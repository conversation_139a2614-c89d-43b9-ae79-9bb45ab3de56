<?xml version="1.0" encoding="UTF-8"?>
<svg version="1.1" viewBox="0 0 2048 2048" width="1280" height="1280" xmlns="http://www.w3.org/2000/svg">
<path transform="translate(946)" d="m0 0h28l4 1h100l14 1v-2h11l1 1 11-1v3l25 2 13 3 13 1 3 1 19 1 5 2 6 2 9 1 2 1 14 1 18 3 17 4 5 3 18 5 13 3 20 6 10 2 27 10 10 4 16 4 9 2 13 8 20 7 20 9 16 7 28 14 17 8 12 6 7 6 34 17 18 13 12 7 7 6 17 8 14 10 2 4 4 2 16 12 4 5 4 3 7 1 4 5 3 5 15 12 13 10 7 8 8 6 17 14 12 11 8 10 13 12 7 9 14 14 8 10 14 15 10 11 11 14 12 15 11 15 10 14 8 11 2 5h2v2h2l6 10 8 11 13 23 9 13 8 14 10 19 6 10 7 13 6 9 1 6 13 29 4 9 6 9 10 28 4 9 4 10 5 12 9 27 4 17 4 3 6 31 9 28 4 17 2 13 1 9 2 10 4 17 3 17 2 15 1 22 1 4 1 21 3 6v8l2 7v58l-1 4 1 2v68l-2 23-3 11-1 22-2 13-1 13-3 17-2 18-4 18-2 13-2 15-4 11-8 33-6 28-4 8-4 7v9l-6 17-11 26-7 20-11 25-6 10v9l-9 16-7 15-4 10-9 11-10 18-5 12-7 12-8 13-11 17-6 9-7 11-2 6-10 13-7 8-4 9-7 9-10 12-9 11-9 10-5 7-3 6-10 9-7 8-9 12-5 10-9 4-5 4-7 9-7 6-6 7-5 5-7 8-4 2-2 5-9 10-7 3-11 9-7 7-16 12-9 7-8 8-3 6-2 2-7 3-5 4-10 4-5 5-5 4-12 7-10 5-4 5-3 3-24 13-14 10-20 12-6 4-38 19-5 9h-12l-24 12-7 4-13 4-10 7-6 3h-9l-8 5-6 3-24 7-10 4-17 6-30 10-27 7-9 2-4 3h-10l-7 2-11 1-5 3-7 3-10 2-12 1-1 1-13 1-10 2-29 3-15 3-24 1-9 1-9 3-10-2v2h-13l-1-2h-3l-1 2h-120v-2h-9l-7-2-21-1-4-1-17-1-24-4-15-1-32-6-6-1-5-3-7-2-15-2-21-5-13-3h-6l-6-2-15-3-6-5-7-3h-12l-6-4-6-3-3-2-8-1-9-5-9-2-19-7-16-6-6-4-15-7-9-2-4-2-5-4-9-5-10-5-9-4-6-2-15-8-10-6-6-4-15-5-9-7-5-3-16-9-11-8-10-7-12-8-9-7-24-16-8-7-4-5-11-7-11-10-22-18-10-9-13-11-6-5-4-6-14-11-7-8-6-4-14-16v-2l-5-2-8-9-7-11-11-12-1-2-8-4-6-9-7-9-8-7-12-21-15-15-4-7-5-3-3-9-6-9-10-18-11-14-7-11-4-9-14-24-13-23-10-16-1-6h-2l-3-4-3-12-5-10-4-7-3-10-8-16-7-16-5-15-6-9-7-28-9-26-5-12-2-14-3-12-4-13-3-15-7-24-6-26-2-12-2-11-1-15-1-2-1-17-1-2-2-31-3-11-1-1v-153h2l1-25 2-6-3-9 3-9 2-9 1-16 3-3 1-16 2-13v-9l2-6 1-15 5-12 3-4 1-13 4-17 4-12 2-4 1-14 6-15 4-15 6-16 8-26 6-10 6-23 3-6 3-5 7-20 7-14 6-9 13-28 15-25 3-10 6-8 6-10 3-9 14-19 7-11 9-11 8-13 2-8 8-6 15-20 7-11 9-11 3-1 2-4 5-9 8-8 6-7 6-5 6-8 8-8 7-8 15-16 10-11 5-4 6-7 11-9 5-5 6-5 7-8 9-8 3-5 21-14 10-9 13-10 9-6 4-5 17-11 10-7 39-26 9-7 7-6 15-6 8-6 22-13 27-12 6-6 13-4 3-5 4-2 7-2 5-4h9l3-3 11-3 24-12 10-3 6-3 23-6 15-6 9-4 9-3 14-4h7l12-4 5-4 9-1 4-2h8l5-2 3-2 18-3 9-1 14-3 10-1 2-1 17-1 13-4 9-1 2-1 19-3 3-1 24-1 11-1 5-2z" fill="#138FB0"/>
<path transform="translate(1327,364)" d="m0 0h43l25 1 40 4 45 8 40 9 34 10 44 15 41 18 21 10 22 12 21 12 16 10 20 14 17 12 15 12 11 8 28 24 13 12 8 7 16 16 7 8 24 28 9 11 12 15 12 17 16 24 7 11 12 20 10 18 16 32 5 11 12 29 10 29 14 43 8 32 6 29 2 10 1 16 3 20 3 25 5 19 2 29 1 7-2 11-1 20-3 14v25l-2 13-2 8-2 16-1 7-2 13-4 13-7 20-6 17-3 7-3 10-8 24-7 17-2 2-2 5-2 3-1 5-3 3-5 13-3 7-4 10-3 6h-2l-1 5-6 9h-2l-1 4-8 13-5 13-8 15-5 10-4 5-6 9-4 4-1 3-2 1-4 9-4 6-12 12-11 16-9 12-5 5-7 8-8 11-6 6h-3v2l-8 5-4 7-8 10-9 9-9 7-3 1v2l-7 5h-2v2l-9 7-10 8-13 10-15 10-10 7-9 6-16 11-6 2v2l-9 4-4 1v2l-12 7-11 7-14 7-19 8-44 17-19 7-7 5-11 4-10 4-7 2-20 6-54 12-34 5-32 3h-14v2h-82l-32-3-30-4-38-7-34-8-39-12-29-10-36-15-33-16-22-12-20-12-22-14-15-10-14-10-28-22-10-9-8-7-10-9-8-7-40-40-9-11-12-14-11-14-16-21-17-26-12-19-12-21-15-28-16-34-15-39-9-25-9-31-7-28-8-43-4-29-3-38-1-26v-35l2-37 4-36 6-37 9-40 7-25 8-26 10-27 13-32 10-22 10-19 14-25 8-13 10-16 18-27 10-13 12-15 7-8 11-13 18-20 14-15 12-12 8-7 12-11 11-9 14-12 16-12 15-11 20-14 22-14 27-16 24-13 29-14 34-14 35-12 35-10 35-8 39-7 31-4 25-2z" fill="#138FB0"/>
<path transform="translate(1172,187)" d="m0 0 81 1 36 3 26 3 21 4 36 7 40 10 41 12 37 13 19 7 11 5 16 7 15 7 26 13 15 8 19 10 15 9 7 4 42 28 19 14 10 8 14 11 12 10 18 14v2l4 2 16 15 12 11 6 5 7 8 14 14 7 8 15 16 10 13 8 8 11 14 18 24 10 14 10 15 17 26 15 26 13 24 8 15 9 20 7 15 5 11 14 34 9 25 8 25 9 31 8 33 5 26 4 26 5 33 3 18 3 31v5h-2l-2-4-6-39-8-39-8-32-17-52-8-22-9-22-4-9-12-25-10-19-9-17-8-13-9-14-16-24-13-18-13-16-9-11-11-13-9-10-7-8-9-10-14-14-8-7-11-10-11-9-15-13-14-10-14-11-33-23-13-8-24-14-26-14-21-10-30-13-21-8-46-15-30-8-44-9-37-6-32-3-25-1h-43l-39 2-36 4-34 6-38 8-36 10-36 12-21 8-25 11-33 16-23 13-23 14-27 18-17 12-17 13-14 11-14 12-15 13-7 7-8 7-14 14-7 8-14 15-9 11-10 11-20 26-10 14-14 22-8 13-12 20-12 22-8 16-12 26-12 30-9 25-10 33-9 36-6 31-5 33-3 28-2 37v35l1 26 3 38 5 35 7 37 9 36 11 35 16 42 11 25 14 29 12 22 13 22 16 25 11 16 16 21 8 10 9 11 12 14 9 10 38 38 8 7 10 9 11 9 12 10 14 11 17 12 15 10 19 12 13 8 21 12 23 12 23 11 27 11 24 9 37 12 34 9 45 9 27 4 28 3 14 1h82l31-2 36-4 23-4 41-9 35-10 26-9 14-5 13-6 20-8 11-5 13-6 18-8 19-10 15-9 21-13 32-22h2v-2l17-12 12-10 11-9 14-12 11-10 4-4h2l2-4h2l2-4 23-23 9-11 8-10h2l2-4 11-13 7-9 11-13 12-17 16-24 6-10 9-16 13-24 7-13 9-17 5-13 4-9 5-9 2 2-3 9-3 10-5 11-6 17-6 14-6 15-7 15-4 7-1 3h-2l-1 6-8 16-10 19-12 20-5 9-7 11-8 12-10 16-13 18-8 11-7 9-11 14-9 10-5 7h-2l-2 4-9 11-5 5-7 8h-2l-2 4h-2l-2 4-12 12-8 7-17 17h-2v2l-8 7-10 9-11 9-8 7-15 12-11 8-17 12-16 11-14 9-11 7-17 11-15 9-21 12-23 12-25 11-15 7-25 10-35 12-30 10-27 8-33 8-31 6-44 7-23 3-24 2-19 1h-79l-39-2-43-5-37-6-40-8-40-10-41-13-39-14-24-10-33-15-36-18-24-14-28-17-24-16-17-12-11-8-13-10-14-11-13-11-10-9-8-7-10-9-13-12-31-31-7-8-24-28-9-10-13-17-10-13-16-23-19-29-17-28-13-23-13-26-10-19-10-23-15-38-13-38-12-41-8-32-7-36-7-49-3-31-2-50v-46l2-43 5-46 7-46 8-37 7-26 13-42 12-34 8-21 12-28 19-39 13-25 14-24 8-14 12-18 10-14 12-17 12-16 10-13 9-11 14-17 14-15 7-8 12-13 11-11 8-7 10-10 8-7 15-14 14-11 14-12 9-7 12-9 13-10 16-11 27-18 29-17 16-9 23-12 33-16 19-8 9-4 20-8 19-7 31-10 31-9 37-9 40-8 18-3 35-4 25-2z" fill="#9EC3D3"/>
<path transform="translate(1522,131)" d="m0 0 9 3 8 5 12 5 17 12 11 7 6 4 6 5 17 8 14 10 2 4 4 2 16 12 4 5 4 3 7 1 4 5 3 5 15 12 13 10 7 8 8 6 17 14 12 11 8 10 13 12 7 9 14 14 8 10 14 15 10 11 11 14 12 15 11 15 10 14 8 11 2 5h2v2h2l6 10 8 11 13 23 9 13 8 14 10 19 6 10 7 13 6 9 1 6 13 29 4 9 6 9 10 28 4 9 4 10 5 12 9 27 4 17 4 3 6 31 9 28 4 17 2 13 1 9 2 10 4 17 3 17 2 15 1 22 1 4 1 21 3 6v8l2 7v58l-1 4 1 2v68l-2 23-3 11-1 22-2 13-1 13-3 17-2 18-4 18-2 13-2 15-4 11-8 33-6 28-4 8-4 7v9l-6 17-11 26-7 20-11 25-6 10v9l-9 16-7 15-4 10-9 11-10 18-5 12-7 12-8 13-11 17-6 9-7 11-2 6-10 13-7 8-4 9-7 9-10 12-9 11-9 10-5 7-3 6-10 9-7 8-9 12-5 10-9 4-5 4-7 9-7 6-6 7-5 5-7 8-4 2-2 5-9 10-7 3-11 9-7 7-16 12-9 7-8 8-3 6-2 2-7 3-5 4-10 4-5 5-5 4-12 7-10 5-4 5-3 3-24 13-14 10-20 12-6 4-38 19-5 9h-12l-24 12-7 4-13 4-10 7-6 3h-9l-8 5-6 3-24 7-10 4-17 6-30 10-27 7-9 2-4 3h-10l-7 2h-10v-1l48-12 42-12 26-9 8-2v-2l5-2 1-2h2l1-2 5-2 6-2 12-3 7-3 5-2 4-3 14-4 5-3 9-3 11-3 10-5 7-4 4-5 10-7 8-3h5v-2l5-2h3v-2l9-5 5-4 5-1 4-3 18-9v-2l6-5 5-3 10-4 11-8 6-4 7-4 7-6 3-5 9-6 10-7 13-10 5-6 7-4 7-3 9-7 2-3h3l2-4 6-3 5-5h3l2-4 2-3h2l1-9-2-12 3-3 6-2 15-8 14-7 11-10 10-7 1-2h2l2-4 7-10 10-7 8-8h2l1-4 10-24 10-13 4-9 2-5 11-11 4-11-13 3-6 7-1 2-12 4-6 5-10 19-11 11-11 7-16 10-3 1-2 4-10 10-11 6-13 8-14 6-12 9-9 8-4 2h-3v2l-9 4-8 6h-2l-1 3-8 5-12 6-10 8-13 8-10 5-12 5-10 4-14 7-5 4-5 1v2l-17 11-10 4-13 1-2 1h-12l-14 4-5 3-4 7-5 1-22 1-13 4v2l-10 7-3 4-12 2-7 1h-9l-5-1-2 1h-21l-14 3-13 1-5 2-25 3-11 2-11 4-8 3-11 4-10-1-22-5-10-1-13-1-55-1-26-2v-1h16l23 1h79l29-2 20-2 31-4 47-8 38-9 23-6 41-13 20-7 28-10 28-12 32-15 19-10 21-12 13-8 11-7 17-11 14-9 20-14 14-10 19-14 12-11 8-6 11-10 8-7 4-2v-2l8-7 17-17 8-7 7-7 5-6h2l2-4 8-9 5-5 8-10h2l2-4 9-11 8-9 6-8 7-9 13-18 14-21 7-11 8-12 7-11 6-11 9-16 13-25 4-8 1-4h2l1-6 4-6 6-13 10-25 7-17 5-14 3-8 3-9 1-4-3 4-5 12-5 10-3 8-8 15-10 19-10 18-12 21-12 18-13 19-11 14-11 13-11 14-4 5-5 5-12 14-7 8-19 19-5 6h-2l-2 4-8 7-14 12-11 9-12 10-13 10-7 5h-2v2l-11 7-14 10-15 10-27 16-18 10-22 10-13 6-25 11-18 7-10 4-28 10-30 9-34 8-15 3-31 5-39 4h-20v-1l27-2 34-4 24-4 53-12 16-5 7-2 12-5 9-3 7-5 27-10 25-10 18-7 15-6 18-10 10-7 6-2v-2l8-4 5-1v-2l9-5 14-10 11-7 10-7 16-11 14-11 11-9 3-1v-2l6-4 3-1v-2l14-11 9-9 5-7 4-7 5-4 4-1v-2l5-3 7-8 8-11h2l2-4 3-4h2l2-4 11-15 8-11 12-12 7-14h2l1-4h2l2-4 7-11 6-9 8-17 4-8 4-9 7-11 1-3h2l7-14h2l2-6 4-10 3-7 5-13 2-3h2v-6l3-3 2-5 6-13 4-11 6-18 5-16 5-12 10-29 3-10 2-14 2-12 2-16 2-6 1-15v-21l3-14 2-26 1-6-1-6-2-27-5-19-5-39-1-6v-8h1l5 31 1 4h2l-1-6-4-36-6-39-3-18-4-25-8-37-8-28-7-24-3-10-3-8-11-30-9-22-14-30-7-16-8-16-13-24-14-24-10-16-16-24-11-16-9-12-10-13-11-14-9-10-10-12-7-8-11-12-14-15-25-25-8-7-14-13-2-1 1-2 8 7 10 9 9 7v2l4 2 9 8v2l3 1 7 8 9 9 7 8 7 7v2h3l10 13 3 4v2h2l1 4 4 2 9 11 11 14 8 10 8 11 7 10 3 3 4 7 8 12 4 5 6 11 6 9 8 14 4 8 10 18 10 19 7 14 10 22 2 2 3 9 9 17 3 7 3 9 10 22 3 9 3 1 1-4-3-17-5-17-4-10-2-8-3-7-4-8-2-15-3-8-3-10-3-5-2-17-2-6h-2l-11-23-4-7-2-8-3-1-3-7-3-6v-2h-2l-5-14-3-3-5-11-3-9-9-17-2-2-2-9-9-14-5-5-7-12-7-11-7-9v-2l-4-2-7-7-9-13-6-11-8-15-6-12-5-6-4-1-7-8-11-11-8-11-9-10-6-7-9-11-2-1v-2l-4-2-4-6-3-3-5-3-12-11-4-6-3-2v-2l-4-2-1-3-3-1-7-8-13-10-5-5-4-5-4-4-1-4-5-2-4-6-9-7v-2l-4-2-7-5-4-4-4-2-10-10-12-7-7-6-31-21-14-10-11-7-10-8-16-10-9-6-12-6-6-4z" fill="#349EBF"/>
<path transform="translate(30,1268)" d="m0 0h1l6 21 7 26 10 33 7 19 7 20 7 16 10 25v2l3 1 1 5h2l6 15 3 5 4 10 4 8 3 10 7 13 9 17 4 6v3l5 1 4 8 2 5 3 5v2h2l5 9v2l3 1 4 9 1 4 3 1 6 11v2l3 1 1 4h2l8 11 6 7 9 11 2 5 3 1 8 11 3 6 10 10 3 5 4 4 4 5v2l3 1 2 5 6 8 12 13 5 5 1 3 3 1v3l3 1 6 9 3 3v2h3l2 6h2l6 7v2l4 2v2l3 1 6 6v2h2l1 4h3l8 8 4 2 1 2 5 3 7 7 3 2v2h3l2 5 4 2 10 9 5 5 7 5v2h4v3l4 1 1 4 5 2 3 2v2l5 2 7 5v2l4 1 4 2 5 5 12 9 6 3 8 6 6 4 11 7 9 6 7 4 5 6 5 3 7 5 5 2 10 7 11 6 2 1v2l4 1 3 1v2h4v2l9 3 7 5 9 4 12 7 5 1 10 4 9 7 14 6 13 6 25 10 10 3 8 4 9 3 20 8 20 7 13 3 7 2 11 4 5 3 27 6 3 2 16 3 5 2 12 3v2l10 1 25 5 19 4 47 7 13 2 10 1v1h-16l-4-1-17-1-24-4-15-1-32-6-6-1-5-3-7-2-15-2-21-5-13-3h-6l-6-2-15-3-6-5-7-3h-12l-6-4-6-3-3-2-8-1-9-5-9-2-19-7-16-6-6-4-15-7-9-2-4-2-5-4-9-5-10-5-9-4-6-2-15-8-10-6-6-4-15-5-9-7-5-3-16-9-11-8-10-7-12-8-9-7-24-16-8-7-4-5-11-7-11-10-22-18-10-9-13-11-6-5-4-6-14-11-7-8-6-4-14-16v-2l-5-2-8-9-7-11-11-12-1-2-8-4-6-9-7-9-8-7-12-21-15-15-4-7-5-3-3-9-6-9-10-18-11-14-7-11-4-9-14-24-13-23-10-16-1-6h-2l-3-4-3-12-5-10-4-7-3-10-8-16-7-16-5-15-6-9-7-28-9-26-5-12-2-14-3-12-4-13z" fill="#349EBF"/>
<path transform="translate(1989,1309)" d="m0 0 2 2-3 9-3 10-5 11-6 17-6 14-6 15-7 15-4 7-1 3h-2l-1 6-8 16-10 19-12 20-5 9-7 11-8 12-10 16-13 18-8 11-7 9-11 14-9 10-5 7h-2l-2 4-9 11-5 5-7 8h-2l-2 4h-2l-2 4-12 12-8 7-17 17h-2v2l-8 7-10 9-11 9-8 7-15 12-11 8-17 12-16 11-14 9-11 7-17 11-15 9-21 12-23 12-25 11-15 7-25 10-35 12-30 10-27 8-33 8-31 6-44 7-23 3-24 2-19 1h-79l-23-1v-1l111-1 27-2 25-3 42-6 24-5 28-6 17-4 16-5 36-11 28-10 27-10 10-5 10-4 29-14 19-9 17-10 10-5 9-6 7-4 9-6 7-4 45-30 8-6h2v-2l11-8 10-8h2l1-3 13-10 3-1v-2l11-9 12-11h2l2-4h2v-2l8-7 10-10 6-5 5-6 7-6 1-2h2l2-4 5-5 9-10 11-13 3-5h2l2-4 12-14 6-8 12-15 12-17 8-11 10-15 11-17 8-14 6-11 8-14 3-8h2l2-6 12-27 3-3 3-9v-3l-1 3h-2v5h-3l-1 5h-2l-1 5-8 12-1 3h-2l-2 6-8 11-9 14h-2l-2 5-6 9h-2v2h-2l-2 4-10 13-5 7-3 3-6 7h-2v3l-5 5-6 7h-2l-2 4-6 7-7 6-5 6-17 17h-2l-2 4-16 14h-2v2l-8 7-10 8-11 9-14 10-18 13-16 10-15 10-14 8-11 7-16 8-23 11-14 7-27 11-20 8-20 7-39 12-29 7-30 6-21 3-25 3-32 2h-77v-1l72-1 31-2 36-4 23-4 41-9 35-10 26-9 14-5 13-6 20-8 11-5 13-6 18-8 19-10 15-9 21-13 32-22h2v-2l17-12 12-10 11-9 14-12 11-10 4-4h2l2-4h2l2-4 23-23 9-11 8-10h2l2-4 11-13 7-9 11-13 12-17 16-24 6-10 9-16 13-24 7-13 9-17 5-13 4-9z" fill="#7DB3C8"/>
<path transform="translate(1172,187)" d="m0 0 81 1 36 3 26 3 21 4 36 7 40 10 41 12 37 13 19 7 11 5 16 7 15 7 26 13 15 8 19 10 15 9 7 4 42 28 19 14 10 8 14 11 12 10 18 14v2l4 2 16 15 12 11 6 5 7 8 14 14 7 8 15 16 10 13 8 8 11 14 18 24 10 14 10 15 17 26 15 26 13 24 8 15 9 20 7 15 5 11 14 34 9 25 8 25 9 31 8 33 5 26 4 26 5 33 3 18 3 31v5h-2l-2-4-6-39-8-39-8-32-17-52-8-22-9-22-4-9-12-25-10-19-9-17-8-13-9-14-16-24-13-18-13-16-9-11-11-13-9-10-7-8-3-5 3 1 7 8 12 13 9 10v2h2l8 10v2h2l4 6v2h2l8 11 8 10 6 8 5 9 3 3v3h2l2 3v3h2l9 15 9 14 5 10 4 7 9 17 14 28 5 13 5 12 3 6 3 9 5 17 3 8 5 18 6 21 5 19 5 24 3 13v4l1-1-3-24-5-25-3-13-3-16-5-24-5-22-3-7-2-8-5-17-5-13-7-21-5-13-6-17-4-8-4-10-10-21-4-9-5-10-8-17v-2h-2l-4-8-7-12v-3h-2l-6-12-4-6v-2h-2l-5-9-10-15-5-6-3-6-3-4v-2h-2l-4-7-4-5-2-6h-2l-4-6-5-6-4-6-1-3h-2l-3-5-8-10-9-10-9-11-7-8-8-8-7-8-1-3-3-1-6-7-7-6-5-5-7-6v-3l-4-1-7-8-5-4-5-5-9-7v-2l-5-2v-2l-5-2-5-5-9-7v-2l-4-2-11-8v-2l-5-2v-2l-5-2-11-8v-2l-5-2-30-20-13-8-10-7-14-8-6-4-16-8-18-10-16-8-25-12-15-6-8-4-26-10-7-2-15-5-46-14-13-4-50-11-10-1-11-3-13-2-27-3-4-1-18-1-5-1-41-2h-91l-51 4h-9v-1l25-3 25-2z" fill="#7DB3C8"/>
<path transform="translate(1522,131)" d="m0 0 9 3 8 5 12 5 17 12 11 7 6 4 6 5 17 8 14 10 2 4 4 2 16 12 4 5 4 3 7 1 4 5 3 5 15 12 13 10 7 8 8 6 17 14 12 11 8 10 13 12 7 9 14 14 8 10 14 15 10 11 11 14 12 15 11 15 10 14 8 11 2 5h2v2h2l6 10 8 11 13 23 9 13 8 14 10 19 6 10 7 13 6 9 1 6 13 29 4 9 6 9 9 25-1 2-6-11v-2h-2l-11-23-4-7-2-8-3-1-3-7-3-6v-2h-2l-5-14-3-3-5-11-3-9-9-17-2-2-2-9-9-14-5-5-7-12-7-11-7-9v-2l-4-2-7-7-9-13-6-11-8-15-6-12-5-6-4-1-7-8-11-11-8-11-9-10-6-7-9-11-2-1v-2l-4-2-4-6-3-3-5-3-12-11-4-6-3-2v-2l-4-2-1-3-3-1-7-8-13-10-5-5-4-5-4-4-1-4-5-2-4-6-9-7v-2l-4-2-7-5-4-4-4-2-10-10-12-7-7-6-31-21-14-10-11-7-10-8-16-10-9-6-12-6-6-4z" fill="#349EBF"/>
<path transform="translate(254,1697)" d="m0 0 4 2 12 14 5 4 9 11 4 5 20 20 8 7 8 8 4 3v2l5 2 8 7 12 12 11 8 9 9 9 7 20 16 5 3 11 8 2 1v2l5 2 11 8 2 1v2l5 2 9 6 15 11 20 13 14 8 12 7 20 12 10 4 9 5 7 3 25 13 16 7 10 5 16 7 17 6 36 14 19 7 17 6 22 7 21 6 28 7 25 5 23 5 23 4 16 3 34 5 13 2 10 1v1h-16l-4-1-17-1-24-4-15-1-32-6-6-1-5-3-7-2-15-2-21-5-13-3h-6l-6-2-15-3-6-5-7-3h-12l-6-4-6-3-3-2-8-1-9-5-9-2-19-7-16-6-6-4-15-7-9-2-4-2-5-4-9-5-10-5-9-4-6-2-15-8-10-6-6-4-15-5-9-7-5-3-16-9-11-8-10-7-12-8-9-7-24-16-8-7-4-5-11-7-11-10-22-18-10-9-13-11-6-5-4-6-14-11-7-8-6-4-14-16v-2l-5-2-8-9-7-11-8-9z" fill="#7DB3C8"/>
<path transform="translate(1522,131)" d="m0 0 9 3 8 5 12 5 17 12 11 7 6 4 6 5 17 8 14 10 2 4 4 2 16 12 4 5 4 3 7 1 4 5 3 5 15 12 13 10 7 8 8 6 17 14 12 11 8 10 13 12 4 6 1 5 4 2 4 4 2 5 12 12 2 5 4 2 11 13 2 4 3 1 8 10 12 14v2h-2l-7-8-11-11-8-11-9-10-6-7-9-11-2-1v-2l-4-2-4-6-3-3-5-3-12-11-4-6-3-2v-2l-4-2-1-3-3-1-7-8-13-10-5-5-4-5-4-4-1-4-5-2-4-6-9-7v-2l-4-2-7-5-4-4-4-2-10-10-12-7-7-6-31-21-14-10-11-7-10-8-16-10-9-6-12-6-6-4z" fill="#349EBF"/>
<path transform="translate(1468,1837)" d="m0 0m-3 1h3l-1 3-4 2-4 7-5 1-22 1-13 4v2l-10 7-3 4-12 2-7 1h-9l-5-1-2 1h-21l-14 3-13 1-5 2-25 3-11 2-11 4-8 3-11 4-10-1-22-5-10-1-13-1-55-1-26-2v-1h16l23 1h79l29-2 20-2 31-4 47-8 38-9 23-6 41-13z" fill="#349EBF"/>
<path transform="translate(1882,1580)" d="m0 0 1 2-7 11-2 6-10 13-7 8-4 9-7 9-10 12-9 11-9 10-5 7-3 6-10 9-7 8-9 12-5 10-9 4-5 4-7 9-7 6-6 7-5 5-7 8-4 2-2 5-9 10-7 3-11 9-7 7-16 12-9 7-8 8-3 6-2 2-7 3-5 4-10 4-5 5-5 4-12 7-4 2-3-1 16-11 10-8 24-18 3-1 1-3 14-10 12-11 9-7 8-6 1-3 7-6 8-7 16-16 8-7 3-1 2-4 6-7 8-7 12-13 5-6 8-7 10-12 8-11 5-5 4-5h2l2-4 7-9 4-5 3-5 11-13 12-16 14-20 7-9 4-8z" fill="#87B8CB"/>
<path transform="translate(1883,1545)" d="m0 0h4l1 7-3 10-3 4-2 5-6 7-7 6-3 5h-2l-2 4-9 10-8 14-3 5-5 2-6-1-1-1 1-9 4-9 6-9 6-7 1-2h2l2-4 4-2v-2h2l2-5 6-12h2l2-4 7-6 4-4z" fill="#138FB0"/>
<path transform="translate(588,1947)" d="m0 0 12 5 10 5 16 7 17 6 36 14 19 7 17 6 22 7 21 6 28 7 25 5 23 5 23 4 16 3 34 5 13 2 10 1v1h-16l-4-1-17-1-24-4-15-1-32-6-6-1-5-3-7-2-15-2-21-5-13-3h-6l-6-2-15-3-6-5-7-3h-12l-6-4-6-3-3-2-8-1-9-5v-2l-4-1-28-11-25-10-26-13z" fill="#95BFD0"/>
<path transform="translate(30,1268)" d="m0 0h1l6 21 7 26 10 33 7 19 7 20 7 16 10 25v2l3 1 1 5h2l6 15 3 5 4 10 4 8 3 10 7 13 9 17 4 6v3l5 1 4 8 2 5 3 5v2h2l5 9v2l3 1 2 9 4 7-1 2-8-10-10-20-8-13-9-16-16-27-3-6v-3h-2l-3-4-3-12-5-10-4-7-3-10-8-16-7-16-5-15-6-9-7-28-9-26-5-12-2-14-3-12-4-13z" fill="#91BDCF"/>
<path transform="translate(1492,1932)" d="m0 0 3 1-5 5-3 6h-12l-24 12-7 4-13 4-10 7-6 3h-9l-8 5-6 3-24 7-10 4-17 6-30 10-27 7-9 2-4 3h-10l-7 2h-10v-1l48-12 42-12 26-9 8-2v-2l5-2 1-2h2l1-2 5-2 6-2 12-3 7-3 5-2 4-3 14-4 5-3 9-3 11-3 10-5 11-6z" fill="#379FBF"/>
<path transform="translate(254,1697)" d="m0 0 4 2 12 14 5 4 9 11 4 5 20 20 8 7 8 8 4 3v2l5 2 8 7 12 12 11 8 9 9 9 7 20 16 5 3 11 8 2 1v2l5 2 11 8 2 1v2l5 2 9 6 15 11 6 4-2 1-11-7-14-10-11-7-11-8-15-11-11-7-10-7-10-9-22-18-10-9-13-11-6-5-4-6-14-11-7-8-6-4-14-16v-2l-5-2-8-9-7-11-8-9z" fill="#82B5C9"/>
<path transform="translate(1522,131)" d="m0 0 9 3 8 5 12 5 17 12 11 7 6 4 6 5 17 8 14 10 2 4 4 2 16 12 4 5 4 3 7 1 4 5 3 5 15 12 13 10 5 7-5-2-15-12v-2l-4-2-7-5-4-4-4-2-10-10-12-7-7-6-31-21-14-10-11-7-10-8-16-10-9-6-12-6-6-4z" fill="#82B5CA"/>
<path transform="translate(2e3 1333)" d="m0 0h1l1 9-6 17-11 26-7 20-11 25-6 10v9l-9 16-7 15-4 10-9 11-6 11-1-3 7-13 2-5 8-16 7-15 11-24 5-11 5-13 4-10 3-5 5-14 11-33 2-7z" fill="#7EB3C8"/>
<path transform="translate(1282,34)" d="m0 0 9 1 24 7 10 2 27 10 10 4 16 4 9 2 13 8 20 7 20 9 16 7 28 14 17 8 12 6 5 6-6-2-20-11-10-5-22-10-17-9-16-5-12-5-21-9-16-6-9-4-14-4-11-4v-2l-5-1-24-7-23-7-8-1z" fill="#399FBF"/>
<path transform="translate(0,1036)" d="m0 0h1l5 79 4 36 8 56 8 40 2 8v5l-2-2-7-27-5-21-2-12-2-11-1-15-1-2-1-17-1-2-2-31-3-11-1-1z" fill="#A1C6D5"/>
<path transform="translate(172,457)" d="m0 0v3l-7 11-5 8-14 23-7 12-12 22-8 14-8 16-4 6-9 21-12 25-8 20-8 18-1-4 6-19 3-3 5-15 6-15 10-16 13-28 15-25 3-10 6-8 6-10 3-9 14-19 7-11z" fill="#84B6CA"/>
<path transform="translate(1763,317)" d="m0 0 7 6 10 10 8 10 14 15 10 11 11 14 12 15 11 15 10 14 8 11 2 5h2v2h2l6 10 8 11 12 21-1 2-7-10-5-8-9-13-13-18-6-8-1-3-3-3-9-13-8-12-4-5-5-5-12-14-6-8-5-5-8-9-4-5-4-4-7-8-2-4-4-2-3-6-6-5v-2l-2-1z" fill="#86B7CB"/>
<path transform="translate(1092)" d="m0 0h11l1 1 11-1v3l25 2 13 3 13 1 3 1 19 1 5 2 6 2 9 1 2 1 14 1 18 3 17 4 5 3 18 5v1l-10-1-23-6-13-2-9-2-25-5-11-1-12-3-15-1-15-3-24-3-26-2-2-1-10-1v-1h5z" fill="#88B8CB"/>
<path transform="translate(1239,2023)" d="m0 0 4 1-5 3-7 3-10 2-12 1-1 1-13 1-10 2-29 3-15 3-24 1-9 1-9 3-10-2v2h-13l-1-3 23-2 14-1 28-4 21-2 26-3 15-3 23-4z" fill="#98C0D1"/>
<path transform="translate(2038,1152)" d="m0 0h2l-1 13-3 17-2 18-4 18-2 13-2 15-4 11-8 33-6 28-4 8-4 5 1-7 3-12 8-28 4-16 2-11 5-25 5-17 4-23 4-31z" fill="#87B8CB"/>
<path transform="translate(1882,1580)" d="m0 0 1 2-7 11-2 6-10 13-7 8-4 9-7 9-10 12-9 11-9 10-5 7-3 6-10 9-7 8h-3l6-9 6-7 2-4 7-6 1-2h2l2-4 7-9 4-5 3-5 11-13 12-16 14-20 7-9 4-8z" fill="#80B4C9"/>
<path transform="translate(1932,691)" d="m0 0 4 4 13 22 6 12 10 19 10 21 6 13 8 20 10 29 14 43 8 32 1 10-2-3-2-5-2-14h-2l-3-15-2-6-3-11-6-20-14-39-5-12-4-9-7-17-9-17-8-18-10-19-10-16z" fill="#349EBF"/>
<path transform="translate(403,1838)" d="m0 0 4 2 9 6 13 10 11 7 11 8 11 7 9 6 7 3 12 8 11 6 12 7 4 4 6 4-4 1-13-5-9-8-16-8-14-10-10-7-12-8-9-7-24-16-8-7z" fill="#9CC2D3"/>
<path transform="translate(2042,930)" d="m0 0 4 5v9l2 7v58l-1 4 1 2v68l-2 23-2 7h-1v-15l2-22v-42l-1-12v-51l-2-30-1-9z" fill="#88B8CC"/>
<path transform="translate(2012,757)" d="m0 0 2 3 8 25 4 17 2 13 1 9 2 10 4 17 3 17 2 15 1 22 1 4v21h-1l-3-23-2-22-2-16-2-12-1-11-3-9-2-15-9-37-4-17-1-3z" fill="#99C1D2"/>
<path transform="translate(588,1947)" d="m0 0 12 5 10 5 16 7 17 6 36 14 19 7 17 6 22 7 15 4v1h-6l-16-5-12-3h-15l-6-4-6-3-3-2-8-1-9-5v-2l-4-1-28-11-25-10-26-13z" fill="#7DB3C8"/>
<path transform="translate(1610,416)" d="m0 0 5 1 23 10 25 12 23 12 27 16 24 16 14 10 9 6 11 9 5 3v2l4 2 14 12 6 4v2l4 2 15 14 6 7-4-2-15-14-11-9-15-13-14-10-14-11-33-23-13-8-24-14-26-14-21-10-25-11z" fill="#7DB3C8"/>
<path transform="translate(172,1591)" d="m0 0 5 5 13 17 6 8 3 3 10 16 9 11 6 7 1 4 4 2 4 6v2h2l9 11 8 9 2 5-4-4-8-4-6-9-7-9-8-7-12-21-15-15-4-7-5-3-3-9-6-9-4-7z" fill="#81B5C9"/>
<path transform="translate(30,1268)" d="m0 0h1l6 21 7 26 10 33 7 19 7 20 7 16 10 25 3 7-1 3-8-16-7-15-6-18-6-9-7-28-9-26-5-12-2-14-3-12-4-13z" fill="#91BDCF"/>
<path transform="translate(267,335)" d="m0 0 1 2-3 5-12 14-7 6-3 4h-2l-2 4-7 10-10 13-6 8h-2l-2 4-6 8-7 10-10 13-4 5-3 7-6 8-3 1 2-4 8-13 2-8 8-6 15-20 7-11 9-11 3-1 2-4 5-9 8-8 6-7 6-5 6-8z" fill="#87B8CB"/>
<path transform="translate(1397,1976)" d="m0 0 3 1-5 4-25 7-9 4-12 4-8 3-30 10-27 7-9 2-4 3h-10l-7 2h-10v-1l48-12 42-12 26-9 25-9z" fill="#97C0D1"/>
<path transform="translate(403,210)" d="m0 0 2 1-7 6-8 6-10 9-9 7h-2v2l-8 6-12 11-8 7-26 24-24 24-7 8-8 7-7 6 2-4 9-9 7-8 7-7 6-7 5-4 6-7 11-9 5-5 6-5 7-8 9-8 3-5 21-14 10-9 13-10z" fill="#86B7CB"/>
<path transform="translate(22,1227)" d="m0 0 2 3 3 15 3 9 1 6 8 32 3 9 10 35 6 16 3 10 4 9 6 19 4 6 3 9 3 10 2 4v3h2l1 8-3-4-11-28-5-11-15-42-9-30-7-26-5-16v-5h-2l-2-13-5-23z" fill="#349EBF"/>
<path transform="translate(1916,522)" d="m0 0 3 4 11 19 7 13 6 9 1 6 13 29 4 9 6 9 9 25-1 2-7-13-4-10-8-16-2-7-6-10-8-15-6-15-10-19-8-17z" fill="#80B4C9"/>
<path transform="translate(1437,90)" d="m0 0 6 1 19 10 14 6 12 6 22 12 6 3 6 2 2 3 12 6 20 12 10 7 13 10 10 6 18 13 15 10-3 1-16-10v-2l-5-2-17-12-16-11-9-7-18-11-14-7-19-11-14-8-11-5-9-4-12-6-9-4-13-7z" fill="#349EBF"/>
<path transform="translate(682,58)" d="m0 0h5l-4 2-20 8-11 4-9 4-18 7-9 4-26 12-18 8-4 3h-3l-1 3-8 1 2-2 6-3 5-5 13-4 3-5 4-2 7-2 5-4h9l3-3 11-3 24-12 10-3 6-3z" fill="#9CC3D3"/>
<path transform="translate(11,878)" d="m0 0 1 4-3 27-7 82-1 40h-1v-76h2l1-25 2-6-3-9 3-9 2-9 1-16z" fill="#85B7CB"/>
<path transform="translate(1701,258)" d="m0 0 5 2 10 9 10 8 12 11 8 10 13 12 4 7-4-2-10-7-5-7-7-5-7-8-13-12-2-1v-2l-4-2-5-5-5-6z" fill="#82B5C9"/>
<path transform="translate(715 2e3)" d="m0 0 11 2 18 5 10 2 42 10v2l8 1v1h-11l-25-6-13-3h-6l-6-2-15-3-6-5-7-3z" fill="#96BFD1"/>
<path transform="translate(0,1036)" d="m0 0h1l5 79 3 28v14l-3-6-2-31-3-11-1-1z" fill="#94BED0"/>
<path transform="translate(946)" d="m0 0h28v2l-11 2-29 2-61 7-11 1 4-2 13-2 2-1 19-3 3-1 24-1 11-1 5-2z" fill="#93BECF"/>
<path transform="translate(1957,1439)" d="m0 0 2 3v7l-9 16-7 15-4 10-9 11-6 11-1-3 7-13 2-5 8-16 7-15 9-20z" fill="#7DB3C8"/>
<path transform="translate(1666,1820)" d="m0 0 2 1-8 8-3 6-2 2-7 3-5 4-10 4-5 5-5 4-12 7-4 2-3-1 16-11 10-8 24-18 6-3z" fill="#9CC3D3"/>
<path transform="translate(1912,518)" d="m0 0 4 4 5 12 16 32 8 18 6 10 5 9 3 9 9 18-1 4-11-23-4-7-2-8-3-1-3-7-3-6v-2h-2l-5-14-3-3-5-11-3-9-9-17-2-2z" fill="#349EBF"/>
<path transform="translate(1412,77)" d="m0 0 5 1 21 9 18 8 28 14 17 8 12 6 5 6-6-2-20-11-10-5-22-10-17-9-9-4-16-7-6-3z" fill="#87B8CB"/>
<path transform="translate(583,1947)" d="m0 0 6 2 24 11 10 5 16 6 28 11 4 3h-7l-15-6-17-6-10-5-5-3-11-5-9-2-4-2-5-4-6-3z" fill="#9FC4D4"/>
<path transform="translate(828,18)" d="m0 0h10v1l-21 5-15 2-13 4-27 6-6 2h-5l5-4 5-3h6l4-2h8l5-2 3-2 18-3 9-1z" fill="#A5C9D7"/>
<path transform="translate(1154,2037)" d="m0 0h7v1h-5v2l-15 3-24 1-9 1-9 3-10-2v2h-13l-1-3 23-2 14-1 28-4z" fill="#80B4C9"/>
<path transform="translate(254,1697)" d="m0 0 4 2 12 14 5 4 9 11 4 5 20 20 7 8 3 4-4-2-10-9-4-4-8-7-7-9-3-3v-2l-5-2-8-9-7-11-8-9z" fill="#97C0D1"/>
<path transform="translate(1742,1750)" d="m0 0m-1 1m-1 1m-1 1m-1 1m-1 1m-1 1v3l-6 6h-2v2l-8 7-10 10-6 5-3 5-8 8-24 18-7 4-3 3h-2l1-3 14-10 12-11 9-7 8-6 1-3 7-6 8-7 16-16z" fill="#9AC1D2"/>
<path transform="translate(1128,2038)" d="m0 0h12v1l-28 4-40 3-1 2h-50l2-2 10-1h13l24-1 43-4z" fill="#46A3C1"/>
<path transform="translate(1847,419)" d="m0 0 4 1 13 18 2 5h2v2h2l6 10 8 11 12 21-1 2-7-10-5-8-9-13-13-18-6-8-1-3-3-3z" fill="#85B7CA"/>
<path transform="translate(1977,650)" d="m0 0 3 4 4 11 4 9 9 25 5 19v5l-2-4-6-16-5-12-3-11-6-17-4-9z" fill="#9AC2D2"/>
<path transform="translate(51,1339)" d="m0 0 3 1 3 9 4 13 4 9 6 19 4 6 3 9 3 10 2 4v3h2l1 8-3-4-11-28-5-11-15-42z" fill="#349EBF"/>
<path transform="translate(96,589)" d="m0 0 1 3-5 11-5 13-11 26-6 14-1-4 6-19 3-3 5-15 6-15z" fill="#9BC2D2"/>
<path transform="translate(1984,1377)" d="m0 0 1 3-9 25-11 25-6 10-2-2 6-13 5-13 4-10 3-5 5-14 3-5z" fill="#88B8CC"/>
<path transform="translate(1217,18)" d="m0 0h7l18 3 17 4 5 3 18 5v1l-10-1-23-6-13-2-9-2-7-2z" fill="#97C0D1"/>
<path transform="translate(1492,1932)" d="m0 0 3 1-5 5-3 6h-12l-24 12-7 4-8 2-3-1 16-8 8-3 8-4 11-6z" fill="#8DBBCD"/>
<path transform="translate(132,1523)" d="m0 0 4 1 4 8 2 5 3 5v2h2l5 9v2l3 1 2 9 4 7-1 2-8-10-10-20-7-11z" fill="#47A4C2"/>
<path transform="translate(1649,215)" d="m0 0 10 2 4 5 3 5 15 12 13 10 5 7-5-2-15-12v-2l-4-2-7-5-4-4-4-2-10-10z" fill="#86B7CB"/>
<path transform="translate(1564,1892)" d="m0 0 2 1-10 7-20 12-6 4-30 15-5 1 1-3 8-4 5-2h3v-2l8-4 13-8 8-3 9-6h3v-2l8-4z" fill="#8BBACD"/>
<path transform="translate(65,665)" d="m0 0 1 2-5 15-9 26-8 25-6 22h-1v-12l6-15 4-15 6-16 8-26z" fill="#8FBCCE"/>
<path transform="translate(2046,1013)" d="m0 0 2 2v68l-2 23-2 7h-1v-15l2-22z" fill="#99C1D1"/>
<path transform="translate(682,58)" d="m0 0h5l-4 2-20 8-11 4-9 4-18 7-3 2-4-1 16-8 17-8 8-3 12-4z" fill="#8EBBCE"/>
<path transform="translate(1741,1754)" d="m0 0v3l-9 10-3 1-2 5-9 10-7 3-6 5h-2l2-4 13-12 8-8h2v-2l8-7z" fill="#A0C4D4"/>
<path transform="translate(323,278)" d="m0 0m-1 1v3l-31 31-7 8-8 7-7 6 2-4 9-9 7-8 7-7 6-7 5-4 6-7z" fill="#87B8CB"/>
<path transform="translate(746,38)" d="m0 0h5l-1 2-6 2-10 2-18 5h-3v2l-13 5h-8v2h-5l4-3 11-4 9-4 9-3 14-4h7z" fill="#87BACD"/>
<path transform="translate(1365,1986)" d="m0 0 3 1-25 9-10 4-17 4-3-1 11-4h2l1-3 7-1 5-4 7-2h9z" fill="#349EBF"/>
<path transform="translate(15,1204)" d="m0 0h2l5 22 6 29v5l-2-2-7-27-5-21v-5z" fill="#A1C6D5"/>
<path transform="translate(125,536)" d="m0 0 1 2-7 12-8 16-4 6-9 21-6 12-1-2 5-11 2-7 16-32 9-16z" fill="#85B7CA"/>
<path transform="translate(86,1430)" d="m0 0 3 3v3h2l6 15 3 5 4 10 4 8 1 6-2-2-8-16v-2h-2l-8-14-3-13z" fill="#399FBF"/>
<path transform="translate(11,878)" d="m0 0 1 4-3 27-1 11-3 4-3-9 3-9 2-9 1-16z" fill="#A3C7D6"/>
<path transform="translate(1597,176)" d="m0 0 9 3 15 10 3 3v2l4 2 12 9-2 1-6-4-11-7-14-10-9-7z" fill="#A2C6D5"/>
<path transform="translate(1918,1520)" d="m0 0 1 2-8 16-11 18-11 17-2 1 2-5 5-9 7-13 8-13z" fill="#87B8CB"/>
<path transform="translate(671,1985)" d="m0 0 7 1 33 11 4 3-12 1-6-4-6-3-3-2-8-1-9-5z" fill="#9FC4D4"/>
<path transform="translate(4,957)" d="m0 0h1v12l-2 25v75h-1l-2-38 1-40z" fill="#349EBF"/>
<path transform="translate(1629,307)" d="m0 0 5 2 18 11 9 7 17 11 16 12 3 4-4-2-18-13-45-30z" fill="#349EBF"/>
<path transform="translate(1282,34)" d="m0 0 9 1 24 7 10 2 24 9v1l-8-1-11-4-15-4-23-7-8-1z" fill="#84B6CA"/>
<path transform="translate(1957,1441)" d="m0 0 2 1v7l-9 16-7 15-4 10-6 7v-3l8-16 9-19 5-12z" fill="#9FC4D4"/>
<path transform="translate(2012,757)" d="m0 0 2 3 8 25 4 17 2 17-2-3-8-33-3-7-3-11z" fill="#9FC4D4"/>
<path transform="translate(2017,781)" d="m0 0 2 2 8 33v3h2v8l4 14 1 19-2-3-1-11-3-9-2-15-9-37z" fill="#81B5C9"/>
<path transform="translate(1397,1976)" d="m0 0 3 1-5 4-25 7-9 4-12 4h-6l3-2 31-11z" fill="#85B7CA"/>
<path transform="translate(30,1268)" d="m0 0h1l6 21 7 26 2 7v7l-2-3-5-12-2-14-3-12-4-13z" fill="#95BFD0"/>
<path transform="translate(1092)" d="m0 0h11l1 1 11-1v3l16 1v2h-21l-11-1-2-1-10-1v-1h5z" fill="#A2C7D6"/>
<path transform="translate(1818,1668)" d="m0 0v3l-5 7-3 6-10 9-7 8h-3l6-9 6-7 2-4 7-6 5-5z" fill="#83B6CA"/>
<path transform="translate(935,2043)" d="m0 0h12l34 3 5 1v1h-35v-2h-9l-7-2z" fill="#95BFD0"/>
<path transform="translate(23,806)" d="m0 0h1l-1 10-7 32-2-2 1-10 1-2 1-15 5-12z" fill="#9DC3D3"/>
<path transform="translate(3,1069)" d="m0 0h1l3 45 3 16 1 16v11h-1l-4-30-3-42z" fill="#349EBF"/>
<path transform="translate(2012,1290)" d="m0 0h1l-1 11-5 20-6 10-1-4 4-15 1-3h2l2-9z" fill="#99C1D2"/>
<path transform="translate(347,258)" d="m0 0 2 1-15 15h-2v2l-11 9-30 30v-3l32-32 8-7z" fill="#349EBF"/>
<path transform="translate(554,114)" d="m0 0 2 1-4 3-16 8-16 9-12 7-2-1 8-6 22-13z" fill="#96BFD1"/>
<path transform="translate(502,143)" d="m0 0 2 1-15 10-10 6-11 8-7 4-2-1 11-8 11-7 8-8z" fill="#93BED0"/>
<path transform="translate(529,1921)" d="m0 0 6 1 28 14 11 6-3 1-11-5-6-2-15-8-10-6z" fill="#9FC4D4"/>
<path transform="translate(1642,1837)" d="m0 0 2 1-10 8-7 8-8 5-9 6-6 1 3-3 14-10 13-10z" fill="#98C0D1"/>
<path transform="translate(2043,932)" d="m0 0 3 3v9l2 7v22l-1 13h-1l-3-51z" fill="#A1C5D5"/>
<path transform="translate(2038,1152)" d="m0 0h2l-1 13-3 17-2 18-2 8h-1l1-16 4-31z" fill="#89B9CC"/>
<path transform="translate(200,419)" d="m0 0v3l-9 11-6 8-3 7-6 8-3 1 2-4 8-13 2-8 8-6 5-5z" fill="#81B5C9"/>
<path transform="translate(1522,131)" d="m0 0 9 3 8 5 12 5 14 10-2 1-7-3-15-9-14-8-5-3z" fill="#95BFD0"/>
<path transform="translate(771,29)" d="m0 0h18v1l-27 6-6 2h-5l5-4 5-3h6z" fill="#98C1D2"/>
<path transform="translate(1622,198)" d="m0 0 5 2 10 8 7 5 6 3 3 5-5-2-6-3-6-4-9-7-5-5z" fill="#349EBF"/>
<path transform="translate(110,1482)" d="m0 0 4 2 3 7 4 6v4h2l3 8 2 1 2 4 2 2 1 3-2 4-4-8-5-8-5-10-7-13z" fill="#349EBF"/>
<path transform="translate(22,1227)" d="m0 0 2 3 3 15 3 9 1 6 3 13-1 3-2-3v-5h-2l-2-13-5-23z" fill="#349EBF"/>
<path transform="translate(1028,2045)" d="m0 0h41v1l-15 2h-33l2-2z" fill="#349EBF"/>
<path transform="translate(1447,1953)" d="m0 0 2 1-16 8-20 8-2-1 5-5 14-4 5-3 9-3z" fill="#349EBF"/>
<path transform="translate(308,1754)" d="m0 0 4 2 12 12 4 3v2l5 2 8 7 5 5 4 5-4-2-8-7-9-8-5-3-5-5-9-10z" fill="#81B5C9"/>
<path transform="translate(2e3 723)" d="m0 0 5 3 5 26v5l-2-3-2-5-3-12-3-9z" fill="#A7CAD8"/>
<path transform="translate(2019,1261)" d="m0 0 1 4-6 25h-2l-2 10-3 9h-1l1-8 6-20 3-13z" fill="#85B7CB"/>
<path transform="translate(96,589)" d="m0 0 1 3-5 11-5 13-2 5-2-3 1-5 5-13z" fill="#99C1D2"/>
<path transform="translate(403,210)" d="m0 0 2 1-7 6-8 6-10 9-9 7h-2v2l-5 2v-3l13-9 11-10z" fill="#87B8CB"/>
<path transform="translate(1740,489)" d="m0 0 5 2 12 9 17 13 9 7 6 5h-3l-14-11-12-9-19-14z" fill="#349EBF"/>
<path transform="translate(1082,2044)" d="m0 0h26l-2 2-10 2-7-2v2h-13l-1-3z" fill="#91BDCF"/>
<path transform="translate(1961,613)" d="m0 0 5 5 10 27-1 2-7-13-4-10-3-7z" fill="#96BFD1"/>
<path transform="translate(1412,77)" d="m0 0 5 1 21 9 18 8-1 2-9-3-14-7-19-8z" fill="#86B7CB"/>
<path transform="translate(1716,270)" d="m0 0 4 2 10 9 8 7 4 5-1 2-5-4v-2l-4-2-10-9-6-6z" fill="#9FC4D4"/>
<path transform="translate(1742,1750)" d="m0 0m-1 1m-1 1m-1 1m-1 1m-1 1m-1 1v3l-6 6h-2v2l-8 7-10 10-6 5-1 2-2-1 1-3 7-6 8-7 16-16z" fill="#7DB3C8"/>
<path transform="translate(1492,1932)" d="m0 0 3 1-5 5-3 6-11-1 2-4z" fill="#9BC2D3"/>
<path transform="translate(1784,1709)" d="m0 0 2 1-7 13-9 4h-3l11-12z" fill="#A3C6D6"/>
<path transform="translate(1855,589)" d="m0 0 6 5 7 8 9 11 11 13-1 2-14-15-6-8-5-5-7-9z" fill="#349EBF"/>
<path transform="translate(900,6)" d="m0 0 10 2v1l-43 5h-5l4-2 13-2 2-1z" fill="#8FBCCE"/>
<path transform="translate(1649,215)" d="m0 0 10 2 4 5 3 5 1 3-5-2-11-9z" fill="#80B4C9"/>
<path transform="translate(1854,1622)" d="m0 0 1 4-8 11-7 8-7 9v-3l14-20 5-7h2z" fill="#9FC4D4"/>
<path transform="translate(1882,1580)" d="m0 0 1 2-7 11-2 6-9 11v-3l5-8 2-5 3-5 4-6z" fill="#9AC1D2"/>
<path transform="translate(2e3 1333)" d="m0 0h1l1 9-6 17-6 14-1-3 10-30z" fill="#9FC4D4"/>
<path transform="translate(1897,491)" d="m0 0 4 4 7 10 8 15-1 2-7-10-7-11-4-7z" fill="#98C0D1"/>
<path transform="translate(433,188)" d="m0 0 2 1-5 5-5 4-5 2-10 8-4 1 2-4 15-10z" fill="#8CBACD"/>
<path transform="translate(975,1)" d="m0 0 8 1v1l-14 2-23 2h-12v-2l29-2z" fill="#359EBF"/>
<path transform="translate(846,2029)" d="m0 0h15l3 1v2l12 1 17 3v1l-12-1-21-3-14-3z" fill="#349EBF"/>
<path transform="translate(1988,682)" d="m0 0 2 3 6 16v2h2l4 15v5l-2-4-6-16-5-12z" fill="#83B6CA"/>
<path transform="translate(1430,1962)" d="m0 0 3 1-14 9-6 2-6-1 4-3 18-7z" fill="#9AC2D2"/>
<path transform="translate(46,1326)" d="m0 0 2 2 7 23 6 16-1 4-1-5h-2l-8-26-3-8z" fill="#8CBACD"/>
<path transform="translate(1521,1916)" d="m0 0 4 2-22 12-8 2 1-3 8-4 5-2h3v-2l8-4z" fill="#89B9CC"/>
<path transform="translate(107,567)" d="m0 0h2l-2 5-9 21-6 12-1-2 5-11 2-7 8-16z" fill="#83B6CA"/>
<path transform="translate(1597,1870)" d="m0 0 2 1-3 5-15 8-7 4-2-1 10-7z" fill="#8BBACD"/>
<path transform="translate(115,1493)" d="m0 0 4 5 8 16 4 6 4 8-1 3-9-16-10-17z" fill="#8BBACD"/>
<path transform="translate(267,335)" d="m0 0 1 2-3 5-12 14-5 4 2-4 4-4 3-7z" fill="#349EBF"/>
<path transform="translate(183,1609)" d="m0 0 5 5 6 8 6 9-1 2-5-5-4-7-5-3z" fill="#9FC4D4"/>
<path transform="translate(1957,1439)" d="m0 0 1 2-4 10-5 13-5 9h-2l2-6 5-11 7-16z" fill="#7DB3C8"/>
<path transform="translate(2042,930)" d="m0 0h1l1 5 2 35v16h-1l-2-25-1-20-1-9z" fill="#7DB3C8"/>
<path transform="translate(254,350)" d="m0 0v3l-8 9-3 4-5 3-6 8-1-2 4-6 7-7 6-7z" fill="#89B9CC"/>
<path transform="translate(1179,13)" d="m0 0h7l13 2 9 1 11 3 1 2-7-1-11-2-11-1-12-3z" fill="#85B7CB"/>
<path transform="translate(1176,2034)" d="m0 0h7l-1 3-14 2h-12v-1h5v-2z" fill="#95BFD0"/>
<path transform="translate(796,2020)" d="m0 0 10 1 24 5 4 2h-8l-8-2-11-1-3-1v-2l-8-1z" fill="#80B4C9"/>
<path transform="translate(1906,652)" d="m0 0 5 5 16 24 5 8-1 2-13-19-12-18z" fill="#349EBF"/>
<path transform="translate(1877,458)" d="m0 0 5 5 10 17 4 7-1 2-7-10-5-8-5-10z" fill="#98C0D1"/>
<path transform="translate(772,2016)" d="m0 0 8 1 24 5v1h-11l-21-5z" fill="#9FC4D4"/>
<path transform="translate(1916,522)" d="m0 0 3 4 11 19 2 6-3-3-5-6-8-17z" fill="#8FBCCE"/>
<path transform="translate(1660,227)" d="m0 0 5 2 8 7 6 4 5 5 7 6h-3l-5-3v-2l-4-2-11-9-8-7z" fill="#349EBF"/>
<path transform="translate(3,952)" d="m0 0h1l-1 13-2 19h-1v-29h2z" fill="#9FC4D4"/>
<path transform="translate(327,1774)" d="m0 0 4 2 8 7 9 8 3 1 3 5 1 2-4-2-13-12-10-8z" fill="#9EC3D3"/>
<path transform="translate(1317,43)" d="m0 0h6l18 7 8 3v1l-8-1-22-8z" fill="#8EBBCE"/>
<path transform="translate(2038,892)" d="m0 0h1l1 12 2 5v21h-1l-3-23z" fill="#86B7CB"/>
<path transform="translate(828,18)" d="m0 0h10v1l-21 5-11 1 1-3 7-1z" fill="#90BCCE"/>
<path transform="translate(254,1697)" d="m0 0 4 2 12 14 5 4 7 8-1 2-6-5-7-8-4-4-7-8z" fill="#81B5C9"/>
<path transform="translate(1371,61)" d="m0 0 7 1 9 2 9 6-2 1-19-7z" fill="#8EBBCE"/>
<path transform="translate(1831,394)" d="m0 0 4 4 11 15 3 6-4-2-9-13-5-6z" fill="#89B9CC"/>
<path transform="translate(1294,39)" d="m0 0 8 1 24 7 6 2-4 1-11-3-8-2-5-2-8-2z" fill="#349EBF"/>
<path transform="translate(1147,2039)" d="m0 0h9l-4 2-11 2h-18v-1z" fill="#9FC4D4"/>
<path transform="translate(1514,128)" d="m0 0 8 2 2 3 12 6 2 3-5-1-19-11z" fill="#399FBF"/>
<path transform="translate(1790,1701)" d="m0 0 1 3-4 5-5 3-11 11-2-1 8-9 8-7z" fill="#7FB4C9"/>
<path transform="translate(75,1406)" d="m0 0 3 4 6 16 4 9-1 3-8-16-4-13z" fill="#89B9CC"/>
<path transform="translate(11,878)" d="m0 0 1 4-3 27h-1v-28z" fill="#8DBBCD"/>
<path transform="translate(579,101)" d="m0 0h5l-4 2-11 6-4 1-1 3-8 1 2-2 6-3 5-5z" fill="#8ABACE"/>
<path transform="translate(147,1551)" d="m0 0 3 3 7 11 4 7-1 2-8-10-5-10z" fill="#8BBACD"/>
<path transform="translate(1984,1377)" d="m0 0 1 3-5 14-3 5-1 2h-2l7-21z" fill="#84B6CA"/>
<path transform="translate(1870,1597)" d="m0 0v3l-5 8-3 7-7 6 1-4 13-19z" fill="#85B7CA"/>
<path transform="translate(642,72)" d="m0 0m-2 1 5 1-4 3-16 6-3 2-4-1 16-8z" fill="#85B7CB"/>
<path transform="translate(915,2038)" d="m0 0 8 1 1 1 19 2v1h-13l-19-2-4-2z" fill="#349EBF"/>
<path transform="translate(1499,117)" d="m0 0 9 3 8 6 2 3-6-2-9-5z" fill="#91BDCF"/>
<path transform="translate(1397,1976)" d="m0 0 3 1-5 4-14 4-3-1 5-1v-2z" fill="#99C1D2"/>
<path transform="translate(485,154)" d="m0 0 2 1-11 7-8 6-7 4-2-1 11-8 11-7z" fill="#85B7CB"/>
<path transform="translate(1409,77)" d="m0 0 4 1 21 9 3 3-10-3-12-5-6-3z" fill="#349EBF"/>
<path transform="translate(157,478)" d="m0 0v3l-9 15-4 6-1-3 4-8z" fill="#9FC4D4"/>
<path transform="translate(1344,54)" d="m0 0h8l10 4 7 2v2l-8-1-17-6z" fill="#4DA6C3"/>
<path transform="translate(567,1937)" d="m0 0 20 9 1 2h-8l-6-4v-2l-6-2z" fill="#82B5CA"/>
<path transform="translate(767,31)" d="m0 0m-6 0 9 2-4 2-10 3h-5l5-4z" fill="#9AC2D2"/>
<path transform="translate(5,1135)" d="m0 0h3l1 8v14l-3-6z" fill="#97C0D1"/>
<path transform="translate(113,561)" d="m0 0 1 2-6 10-4 11-5 9-1-2 5-11 4-10z" fill="#349EBF"/>
<path transform="translate(1678,1809)" d="m0 0 1 2-5 5-5 4-7 4-3 3h-2l1-3 14-10z" fill="#82B5C9"/>
<path transform="translate(2046,1087)" d="m0 0h1l-1 19-2 7h-1v-15h2v-10z" fill="#95BFD0"/>
<path transform="translate(30,1268)" d="m0 0h1l6 21 1 8-2-2-6-20z" fill="#8EBBCE"/>
<path transform="translate(1340 2e3)" d="m0 0h8l-4 2-14 5-6-1 3-2 7-2z" fill="#92BDCF"/>
<path transform="translate(1697,354)" d="m0 0 5 2 11 9 9 8h-3l-13-11-8-6z" fill="#349EBF"/>
<path transform="translate(107,1478)" d="m0 0 3 4 5 9v5l-3-3-5-8-1-5z" fill="#94BED0"/>
<path transform="translate(28,1257)" d="m0 0h2l2 5 2 14-3-3v-5h-2l-1-3z" fill="#43A2C1"/>
<path transform="translate(1064,1)" d="m0 0h14l19 2v1h-23l-10-2z" fill="#5AAAC4"/>
<path transform="translate(267,1713)" d="m0 0 7 6 1 3 4 2 3 3v2l-5-2-8-9z" fill="#9FC4D4"/>
<path transform="translate(1522,131)" d="m0 0 9 3 8 5 6 3 1 3-5-2-14-8-5-3z" fill="#84B6CA"/>
<path transform="translate(1131,5)" d="m0 0h9l9 2v1h-19l-5-1v-1z" fill="#94BED0"/>
<path transform="translate(923,4)" d="m0 0 2 1-1 1-14 2h-9l2-3z" fill="#A5C7D6"/>
<path transform="translate(1471,1941)" d="m0 0 2 1-9 6-9 4-6 1 3-3 9-4z" fill="#349EBF"/>
<path transform="translate(1533,1909)" d="m0 0h3l-1 4-8 4h-5l3-3z" fill="#8BBACD"/>
<path transform="translate(329,1773)" d="m0 0 7 4 7 8 7 6h-3l-10-9-8-7z" fill="#7DB3C8"/>
<path transform="translate(1932,551)" d="m0 0 4 5 7 11v6l-4-5-7-14z" fill="#9FC4D4"/>
<path transform="translate(1888,481)" d="m0 0 4 2 3 5 2 1 2 6 1 5-4-4-7-12z" fill="#379FBF"/>
<path transform="translate(282,1727)" d="m0 0 17 17h-3l-14-13z" fill="#83B6CA"/>
<path transform="translate(1918,1520)" d="m0 0 1 2-6 13-5 2 2-4 7-12z" fill="#86B7CB"/>
<path transform="translate(172,457)" d="m0 0v3l-7 11-4 6-3 1 2-4 6-10z" fill="#8AB9CC"/>
<path transform="translate(525,130)" d="m0 0 3 1-17 9-5 2 5-5 10-6z" fill="#89B9CC"/>
<path transform="translate(710,50)" d="m0 0 3 1-13 5h-8v2h-5l4-3 17-4z" fill="#50A7C3"/>
<path transform="translate(1482,109)" d="m0 0 6 2 11 5 4 5-6-2-11-6-4-2z" fill="#83B6CA"/>
<path transform="translate(654,67)" d="m0 0 2 1-5 5-2 1h-7l4-4z" fill="#9FC5D4"/>
<path transform="translate(69,1395)" d="m0 0 3 4 5 13 2 5-1 3-7-16z" fill="#9FC4D4"/>
<path transform="translate(1169,11)" d="m0 0h19l5 2 1 2-8-1-17-2z" fill="#A3C6D6"/>
<path transform="translate(1642,1837)" d="m0 0 2 1-10 8-4 4-6 2 5-5 9-7z" fill="#7FB4C8"/>
<path transform="translate(236,1678)" d="m0 0 6 5v2l3 1 2 5-5-2-6-9z" fill="#9FC4D4"/>
<path transform="translate(1901,1551)" d="m0 0 1 2-9 14-5 7v-3l6-11 3-5z" fill="#8FBCCE"/>
<path transform="translate(88,1438)" d="m0 0 4 5 4 10-1 3-6-10z" fill="#8FBCCE"/>
<path transform="translate(2006,737)" d="m0 0 4 3 2 12v5l-2-3-2-5-2-8z" fill="#89B9CC"/>
<path transform="translate(1967,628)" d="m0 0 3 1 6 16-1 2-7-13z" fill="#8DBBCD"/>
<path transform="translate(1264,2017)" d="m0 0 3 1-6 3-7 2h-10v-1z" fill="#8EBBCE"/>
<path transform="translate(1449,1955)" d="m0 0 2 1-7 4-8 2-3-1 11-5z" fill="#94BFD0"/>
<path transform="translate(215,1652)" d="m0 0 5 5 8 10v2l-4-2-4-5-5-8z" fill="#9FC4D4"/>
<path transform="translate(361,242)" d="m0 0 2 1-8 8-5 4-2-1 4-6z" fill="#9FC4D4"/>
<path transform="translate(14,849)" d="m0 0h1v14l-2 14h-1v-15z" fill="#6FB2C9"/>
<path transform="translate(17,832)" d="m0 0h2l-2 12-2 4-1-2 1-10z" fill="#93BED0"/>
<path transform="translate(1655,216)" d="m0 0 5 2 3 4-1 3-8-6-1-2z" fill="#9FC4D4"/>
<path transform="translate(86,1430)" d="m0 0 3 3v3h2l4 10-1 3-8-16z" fill="#349EBF"/>
<path transform="translate(40,738)" d="m0 0h1l-1 10-2 7h-1v-12z" fill="#9AC2D2"/>
<path transform="translate(145,502)" d="m0 0 1 2-10 19-3 2 2-5 3-6z" fill="#349EBF"/>
<path transform="translate(132,1523)" d="m0 0 4 1 4 8-1 4-4-3z" fill="#389FBF"/>
<path transform="translate(2038,1152)" d="m0 0h2l-1 13h-2v5h-1v-9z" fill="#88B8CC"/>
<path transform="translate(83,618)" d="m0 0h1v6l-5 11-1-3 3-12z" fill="#8EBBCE"/>
<path transform="translate(1617,1856)" d="m0 0 2 1-4 5-8 4-3-1z" fill="#88B8CC"/>
<path transform="translate(2034,1189)" d="m0 0h1l-1 11-2 8h-1l1-16z" fill="#96BFD0"/>
<path transform="translate(711,47)" d="m0 0h5v2l-13 4-3-1z" fill="#90BCCE"/>
<path transform="translate(1886,473)" d="m0 0h2l8 14-1 2-7-10z" fill="#89B9CC"/>
<path transform="translate(289,1737)" d="m0 0 7 6 5 5 1 2-5-2-8-9z" fill="#9FC4D4"/>
<path transform="translate(1812,1676)" d="m0 0 1 2-3 6-6 4 2-5z" fill="#9FC4D4"/>
<path transform="translate(1882,1580)" d="m0 0 1 2-7 11-2-1 4-8z" fill="#88B8CC"/>
<path transform="translate(1828,395)" d="m0 0 3 1 6 8 2 5-3-2v-2l-4-2-4-6z" fill="#349EBF"/>
<path transform="translate(664,63)" d="m0 0 2 1-1 3-10 3 1-3z" fill="#95BFD0"/>
<path transform="translate(24,1242)" d="m0 0h1l3 13v5l-2-2-3-11z" fill="#8EBBCE"/>
<path transform="translate(2012,757)" d="m0 0 2 3 2 7v9h-1l-3-11z" fill="#85B7CB"/>
<path transform="translate(1977,650)" d="m0 0 3 4 2 7v5l-2-3-4-9z" fill="#87B8CB"/>
<path transform="translate(1897,491)" d="m0 0 4 4 3 4 2 5-1 3-5-7-3-6z" fill="#88B8CC"/>
<path transform="translate(183,443)" d="m0 0v3l-7 10-3 1 2-4 5-8z" fill="#53A8C4"/>
<path transform="translate(787,25)" d="m0 0h8l-3 3-2 1h-7z" fill="#B6D2DE"/>
<path transform="translate(514,1910)" d="m0 0 6 2 7 5 2 4-5-2-1-3-4-2-5-3z" fill="#86B7CB"/>
<path transform="translate(1549,1901)" d="m0 0v3l-11 7h-2l1-3z" fill="#8AB9CC"/>
<path transform="translate(495,1899)" d="m0 0 6 2 12 7 1 2-5-2-11-6z" fill="#7DB3C8"/>
<path transform="translate(55,1353)" d="m0 0 2 4 4 10-1 4-1-5h-2l-2-7z" fill="#85B7CB"/>
<path transform="translate(433,188)" d="m0 0 2 1-5 5-5 4-2-1 4-5z" fill="#5AAAC5"/>
<path transform="translate(1098,2044)" d="m0 0h10l-2 2-10 2-2-3z" fill="#A8C9D8"/>
<path transform="translate(2045,1016)" d="m0 0h2v11l-1 7h-1l-1-12z" fill="#7DB3C8"/>
<path transform="translate(242,362)" d="m0 0v3l-7 8-4 4 2-6z" fill="#9FC4D4"/>
<path transform="translate(267,335)" d="m0 0v3l-11 12v-3l7-8z" fill="#89B9CC"/>
<path transform="translate(1476,1940)" d="m0 0 2 1-5 4-4 2-4-1z" fill="#84B6CA"/>
<path transform="translate(1691,1797)" d="m0 0 2 1-4 5-7 5-2-1z" fill="#7DB3C8"/>
<path transform="translate(1931,1495)" d="m0 0 1 3-8 14-1-3 7-13z" fill="#85B7CB"/>
<path transform="translate(190,429)" d="m0 0v3l-5 7h-1l1-7z" fill="#9FC4D4"/>
<path transform="translate(347,255)" d="m0 0 2 1-9 10-4 2 6-7z" fill="#83B6CA"/>
<path transform="translate(1399,73)" d="m0 0 6 1 7 2-3 2-5-1-5-2z" fill="#51A7C4"/>
<path transform="translate(1202,17)" d="m0 0h8l9 2 1 2-7-1-11-2z" fill="#80B4C9"/>
<path transform="translate(73,642)" d="m0 0h2l-3 9-2 5-1-4 3-9z" fill="#91BDCF"/>
<path transform="translate(1955,599)" d="m0 0 2 3 4 9v5l-3-4-3-9z" fill="#89B9CC"/>
<path transform="translate(642,72)" d="m0 0m-2 1 5 1-4 3-9 1 4-3z" fill="#92BDCF"/>
<path transform="translate(617,83)" d="m0 0 4 1h-3v2l-6 3h-2l2-4z" fill="#91BDCF"/>
</svg>
