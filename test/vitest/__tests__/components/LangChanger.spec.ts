/* eslint-disable @typescript-eslint/no-explicit-any */
import { mount } from '@vue/test-utils'
import LangChanger from '@/components/LangChanger.vue'
// import { useLocaleStore } from '@/stores/LangStore'
import { test, expect, describe, beforeEach, vi } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { createI18n } from 'vue-i18n'
import { config } from '@vue/test-utils'
import { Quasar } from 'quasar'

// Configuração do vue-i18n
const i18n = createI18n({
    legacy: false, // Modo de composição (Vue 3)
    locale: 'en-US', // Idioma padrão
    messages: {
        'en-US': {
            locale: {
                changeLanguage: 'Change Language', // Tradução para o texto usado no componente
                selectLanguage: 'Select Language' // Tradução para o texto usado no componente
            }
        }
    }
})

// Configuração global do Vue Test Utils
config.global.plugins = [i18n]
config.global.mocks.$t = (key: any) => key

// Mock da store useLocaleStore
vi.mock('@/stores/LangStore', () => ({
    useLocaleStore: vi.fn(() => ({
        languages: [
            {
                code: 'en-US',
                name: 'English',
                flag: 'united-states-of-america.png'
            },
            {
                code: 'pt-BR',
                name: 'Português',
                flag: 'brazil-.png'
            },
            {
                code: 'es-PY',
                name: 'Español',
                flag: 'paraguai.png'
            }
        ],
        selectedFlag: 'united-states-of-america.png',
        saveLocale: vi.fn() // Mock da função saveLocale
    }))
}))

// Função para montar o componente
const wrapperFactory = () => {
    return mount(LangChanger, {
        global: {
            plugins: [Quasar, i18n]
        }
    })
}

describe('LangChanger', () => {
    beforeEach(() => {
        const pinia = createPinia()
        setActivePinia(pinia)
    })

    test('should render component', () => {
        const wrapper = wrapperFactory()
        expect(wrapper.html()).toMatchSnapshot()
    })

    // test('should display the correct translation', () => {
    //     const wrapper = wrapperFactory()
    //     const button = wrapper.find('button')
    //     button.trigger('click')
    //     console.log(wrapper.html())
    //     // Verifica se o texto traduzido está sendo exibido corretamente
    //     const tooltip = wrapper.find('q-tooltip')
    //     expect(tooltip.text()).toContain('locale.changeLanguage')
    // })

    // test('should open and close the modal when the button is clicked', async () => {
    //     const wrapper = wrapperFactory()

    //     // Verifica se o modal está fechado inicialmente
    //     expect(wrapper.vm.isModalOpen).toBe(false)

    //     // Clica no botão para abrir o modal
    //     const button = wrapper.find('q-btn')
    //     await button.trigger('click')

    //     // Verifica se o modal foi aberto
    //     expect(wrapper.vm.isModalOpen).toBe(true)

    //     // Clica no botão novamente para fechar o modal
    //     await button.trigger('click')

    //     // Verifica se o modal foi fechado
    //     expect(wrapper.vm.isModalOpen).toBe(false)
    // })

    // test('should call saveConfigLocale when a language is selected', async () => {
    //     const wrapper = wrapperFactory()

    //     // Mock da store
    //     const localeStore = useLocaleStore()
    //     const saveLocaleSpy = vi.spyOn(localeStore, 'saveLocale')

    //     // Abre o modal
    //     const button = wrapper.find('q-btn')
    //     await button.trigger('click')

    //     // Seleciona o primeiro idioma da lista
    //     const firstLanguageItem = wrapper.find('q-item')
    //     await firstLanguageItem.trigger('click')

    //     // Verifica se a função saveConfigLocale foi chamada
    //     expect(saveLocaleSpy).toHaveBeenCalled()
    //     expect(wrapper.vm.isModalOpen).toBe(false)
    // })

    test('should display the correct flag URL', () => {
        const wrapper = wrapperFactory()

        // Verifica se a URL da bandeira está correta
        const flagUrl = (wrapper.vm as any).getFlagUrl(
            'united-states-of-america.png'
        )
        expect(flagUrl).toContain(
            '/src/assets/images/flags/united-states-of-america.png'
        )
    })
})
