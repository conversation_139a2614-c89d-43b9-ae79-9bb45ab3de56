<template>
    <div class="help-viewer-page">
        <!-- Header compacto -->
        <q-header elevated class="bg-primary text-white">
            <q-toolbar class="help-toolbar-compact">
                <q-btn
                    flat
                    round
                    dense
                    icon="arrow_back"
                    @click="goBack"
                    class="q-mr-sm"
                >
                    <q-tooltip class="bg-dark">
                        {{ $t('buttons.back') }}
                    </q-tooltip>
                </q-btn>

                <q-toolbar-title class="text-center">
                    <div class="help-title-compact">
                        <q-icon name="help" size="sm" class="q-mr-xs" />
                        <span class="help-title-text">
                            {{ helpContent?.descricao || $t('help.title') }}
                        </span>
                    </div>
                </q-toolbar-title>

                <div style="width: 40px"></div>
            </q-toolbar>
        </q-header>

        <!-- Conteúdo -->
        <q-page-container>
            <q-page class="help-page-content">
                <div class="q-pa-sm">
                    <q-card class="help-card">
                        <q-scroll-area style="height: calc(100vh - 65px)">
                            <q-card-section>
                                <!-- Loading -->
                                <div v-if="loading" class="text-center q-py-xl">
                                    <q-spinner-dots
                                        size="50px"
                                        color="primary"
                                    />
                                    <div
                                        class="text-subtitle1 q-mt-md text-grey-6"
                                    >
                                        {{ $t('help.loading') }}
                                    </div>
                                </div>

                                <!-- Erro -->
                                <div
                                    v-else-if="error"
                                    class="text-center q-py-xl"
                                >
                                    <q-icon
                                        name="error"
                                        size="50px"
                                        color="negative"
                                    />
                                    <div class="text-h6 q-mt-md text-negative">
                                        {{ $t('help.error') }}
                                    </div>
                                    <div class="text-body2 q-mt-sm text-grey-6">
                                        {{ error }}
                                    </div>
                                    <q-btn
                                        color="primary"
                                        icon="refresh"
                                        :label="$t('buttons.retry')"
                                        @click="loadHelpContent"
                                        class="q-mt-md"
                                    />
                                </div>

                                <!-- Conteúdo Markdown -->
                                <div
                                    v-else-if="helpContent"
                                    class="markdown-content"
                                >
                                    <div
                                        v-html="renderedMarkdown"
                                        class="markdown-body"
                                    ></div>
                                </div>

                                <!-- Sem conteúdo -->
                                <div v-else class="text-center q-py-xl">
                                    <q-icon
                                        name="help_outline"
                                        size="50px"
                                        color="grey-5"
                                    />
                                    <div class="text-h6 q-mt-md text-grey-6">
                                        {{ $t('help.noContent') }}
                                    </div>
                                    <div class="text-body2 q-mt-sm text-grey-5">
                                        {{ $t('help.noContentDescription') }}
                                    </div>
                                </div>
                            </q-card-section>
                        </q-scroll-area>
                    </q-card>
                </div>
            </q-page>
        </q-page-container>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { marked } from 'marked'
import DOMPurify from 'dompurify'

// Interfaces
interface HelpContent {
    id: number
    descricao: string
    conteudo: string
    descricao_rota: string
    ativo: boolean
}

// Composables
const route = useRoute()
const router = useRouter()
const { t } = useI18n()

// Estado
const loading = ref(false)
const error = ref<string | null>(null)
const helpContent = ref<HelpContent | null>(null)

// Computed
const renderedMarkdown = computed(() => {
    if (!helpContent.value?.conteudo) return ''

    try {
        // Renderizar markdown e sanitizar HTML
        const rawHtml = marked.parse(helpContent.value.conteudo)
        // Garantir que é string antes de sanitizar
        const htmlString =
            typeof rawHtml === 'string' ? rawHtml : String(rawHtml)
        return DOMPurify.sanitize(htmlString)
    } catch (err) {
        // eslint-disable-next-line no-console
        console.error('Erro ao renderizar markdown:', err)
        return '<p>Erro ao renderizar conteúdo</p>'
    }
})

// Funções
const goBack = (): void => {
    if (window.history.length > 1) {
        router.go(-1)
    } else {
        router.push('/')
    }
}

const loadHelpContent = async (): Promise<void> => {
    loading.value = true
    error.value = null

    try {
        // Obter a rota do parâmetro da URL
        const routeParam = route.params.route as string | string[]
        let routeName = ''

        if (Array.isArray(routeParam)) {
            routeName = '/' + routeParam.join('/')
        } else if (routeParam) {
            routeName = '/' + decodeURIComponent(routeParam)
        } else {
            routeName = '/dashboard' // Rota padrão
        }

        // TODO: Substituir por chamada real da API
        await mockApiCall(routeName)
    } catch (err) {
        // eslint-disable-next-line no-console
        console.error('Erro ao carregar ajuda:', err)
        error.value = t('help.loadError')
    } finally {
        loading.value = false
    }
}

// Mock da API (remover quando a API real estiver pronta)
const mockApiCall = async (routeName: string): Promise<void> => {
    // Simular delay da API
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Mock de dados baseado na rota
    const mockData: Record<string, HelpContent> = {
        '/dashboard': {
            id: 1,
            descricao: 'Ajuda - Dashboard',
            conteudo: `# 📊 Dashboard - Guia Completo de Uso

<div class="help-intro">
    <p class="lead">Bem-vindo ao <strong>Dashboard</strong>! Esta é sua central de comando para monitorar todos os aspectos importantes do seu negócio em tempo real.</p>
</div>

## 🎯 Visão Geral

O Dashboard foi projetado para fornecer uma **visão panorâmica** de todos os indicadores-chave do seu sistema ERP. Aqui você encontra:

<div class="feature-grid">
    <div class="feature-card">
        <h4>📈 Métricas em Tempo Real</h4>
        <p>Acompanhe vendas, estoque e performance instantaneamente</p>
    </div>
    <div class="feature-card">
        <h4>🎨 Visualizações Interativas</h4>
        <p>Gráficos dinâmicos que respondem aos seus cliques</p>
    </div>
    <div class="feature-card">
        <h4>⚡ Atualizações Automáticas</h4>
        <p>Dados sempre atualizados sem necessidade de refresh</p>
    </div>
</div>

## 🚀 Funcionalidades Principais

### 📊 **Gráficos e Métricas Avançadas**

<div class="metrics-section">
    <div class="metric-item">
        <span class="metric-icon">💰</span>
        <div>
            <strong>Vendas do Mês</strong><br>
            <small>Acompanhe receita, tickets médios e conversões</small>
        </div>
    </div>
    <div class="metric-item">
        <span class="metric-icon">👥</span>
        <div>
            <strong>Clientes Ativos</strong><br>
            <small>Monitore engajamento e retenção</small>
        </div>
    </div>
    <div class="metric-item">
        <span class="metric-icon">📦</span>
        <div>
            <strong>Estoque Inteligente</strong><br>
            <small>Alertas de baixo estoque e reposição</small>
        </div>
    </div>
</div>

### 🔍 **Sistema de Filtros Avançado**

<div class="filter-guide">
    <h4>Como usar os filtros:</h4>
    <ol>
        <li><strong>Período</strong>: Selecione datas específicas ou use presets (hoje, semana, mês)</li>
        <li><strong>Departamentos</strong>: Filtre por setores específicos da empresa</li>
        <li><strong>Regiões</strong>: Analise performance por localização geográfica</li>
        <li><strong>Produtos</strong>: Foque em categorias ou itens específicos</li>
    </ol>
</div>

### 📈 **Relatórios Interativos**

<div class="reports-section">
    <div class="report-type">
        <h5>� Relatórios Rápidos</h5>
        <ul>
            <li>Clique em qualquer gráfico para drill-down</li>
            <li>Hover para ver detalhes instantâneos</li>
            <li>Duplo-clique para expandir visualização</li>
        </ul>
    </div>
    <div class="report-type">
        <h5>📤 Exportação</h5>
        <ul>
            <li><strong>PDF</strong>: Relatórios formatados para impressão</li>
            <li><strong>Excel</strong>: Dados brutos para análise</li>
            <li><strong>PowerBI</strong>: Integração direta com BI</li>
        </ul>
    </div>
</div>

## 💡 Dicas Profissionais

<div class="tips-container">
    <div class="tip-card tip-primary">
        <h4>� Busca Inteligente</h4>
        <p>Use <kbd>Ctrl</kbd> + <kbd>F</kbd> para buscar informações específicas em qualquer relatório. A busca funciona em tempo real!</p>
    </div>

    <div class="tip-card tip-success">
        <h4>⚡ Atualizações Rápidas</h4>
        <p>Configure atualizações automáticas a cada 5, 15 ou 30 minutos para dados sempre frescos.</p>
    </div>

    <div class="tip-card tip-warning">
        <h4>🎯 Alertas Personalizados</h4>
        <p>Defina limites para métricas importantes e receba notificações quando atingidos.</p>
    </div>
</div>

## ⌨️ Atalhos do Teclado

<div class="shortcuts-table">
    <table>
        <thead>
            <tr>
                <th>Atalho</th>
                <th>Ação</th>
                <th>Descrição</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td><kbd>F5</kbd></td>
                <td>Atualizar</td>
                <td>Recarrega todos os dados do dashboard</td>
            </tr>
            <tr>
                <td><kbd>Ctrl</kbd> + <kbd>P</kbd></td>
                <td>Imprimir</td>
                <td>Gera versão para impressão</td>
            </tr>
            <tr>
                <td><kbd>Ctrl</kbd> + <kbd>E</kbd></td>
                <td>Exportar</td>
                <td>Abre opções de exportação</td>
            </tr>
            <tr>
                <td><kbd>Ctrl</kbd> + <kbd>D</kbd></td>
                <td>Download</td>
                <td>Baixa relatório atual</td>
            </tr>
        </tbody>
    </table>
</div>

## 🎨 Personalização

<div class="customization-guide">
    <h4>Torne o Dashboard seu:</h4>
    <ul>
        <li>📌 <strong>Fixe widgets</strong> mais importantes no topo</li>
        <li>🎨 <strong>Escolha cores</strong> que fazem sentido para seu negócio</li>
        <li>📱 <strong>Configure layout</strong> responsivo para mobile</li>
        <li>🔔 <strong>Defina alertas</strong> para métricas críticas</li>
    </ul>
</div>`,
            descricao_rota: 'Dashboard Principal',
            ativo: true
        },
        '/estoque/produto-marca': {
            id: 2,
            descricao: 'MARCA - Gestão de Produtos',
            conteudo: `# 📦 Gestão de Produtos - Guia Completo

<div class="help-intro">
    <p class="lead">Gerencie seu <strong>catálogo de produtos</strong> de forma eficiente com ferramentas avançadas de edição, busca e validação.</p>
</div>

## 🚀 Funcionalidades Principais

<div class="feature-grid">
    <div class="feature-card">
        <h4>➕ Cadastro Rápido</h4>
        <p>Adicione produtos com validação automática e campos inteligentes</p>
    </div>
    <div class="feature-card">
        <h4>✏️ Edição Inline</h4>
        <p>Edite diretamente na tabela com navegação por teclado</p>
    </div>
    <div class="feature-card">
        <h4>🔍 Busca Avançada</h4>
        <p>Encontre produtos rapidamente com filtros inteligentes</p>
    </div>
</div>

## 📋 Como Gerenciar Produtos

### ➕ **Adicionar Novo Produto**

<div class="tip-card tip-success">
    <h4>🎯 Processo Otimizado</h4>
    <ol style="margin: 10px 0;">
        <li><strong>Clique</strong> no botão <kbd>+ Novo Produto</kbd></li>
        <li><strong>Preencha</strong> os campos obrigatórios (marcados com *)</li>
        <li><strong>Adicione</strong> imagens e descrições detalhadas</li>
        <li><strong>Salve</strong> e o produto estará disponível imediatamente</li>
    </ol>
</div>

### ✏️ **Edição Inline Avançada**

<div class="metrics-section">
    <div class="metric-item">
        <span class="metric-icon">🖱️</span>
        <div>
            <strong>Duplo-clique</strong><br>
            <small>Ative a edição diretamente na célula</small>
        </div>
    </div>
    <div class="metric-item">
        <span class="metric-icon">⌨️</span>
        <div>
            <strong>Navegação por Teclado</strong><br>
            <small>Use setas para mover entre campos</small>
        </div>
    </div>
    <div class="metric-item">
        <span class="metric-icon">✅</span>
        <div>
            <strong>Validação Automática</strong><br>
            <small>Erros são detectados em tempo real</small>
        </div>
    </div>
</div>

### 🔍 **Sistema de Busca Inteligente**

<div class="filter-guide">
    <h4>Opções de busca disponíveis:</h4>
    <ul>
        <li><strong>🔤 Busca por Nome</strong>: Digite qualquer parte do nome do produto</li>
        <li><strong>🏷️ Filtro por Categoria</strong>: Selecione uma ou múltiplas categorias</li>
        <li><strong>💰 Faixa de Preço</strong>: Defina valores mínimo e máximo</li>
        <li><strong>📊 Ordenação</strong>: Clique em qualquer coluna para ordenar</li>
    </ul>
</div>

## ✅ Validações e Regras

<div class="tips-container">
    <div class="tip-card tip-primary">
        <h4>📝 Nome do Produto</h4>
        <p><strong>Obrigatório</strong> • Mínimo 3 caracteres • Máximo 100 caracteres</p>
    </div>

    <div class="tip-card tip-warning">
        <h4>💰 Preço</h4>
        <p><strong>Obrigatório</strong> • Deve ser maior que zero • Formato: R$ 0,00</p>
    </div>

    <div class="tip-card tip-success">
        <h4>🔢 Código</h4>
        <p><strong>Único no sistema</strong> • Gerado automaticamente ou manual</p>
    </div>
</div>

## ⌨️ Atalhos de Produtividade

<div class="shortcuts-table">
    <table>
        <thead>
            <tr>
                <th>Atalho</th>
                <th>Ação</th>
                <th>Contexto</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td><kbd>Ctrl</kbd> + <kbd>N</kbd></td>
                <td>Novo Produto</td>
                <td>Qualquer lugar na tela</td>
            </tr>
            <tr>
                <td><kbd>Enter</kbd></td>
                <td>Confirmar Edição</td>
                <td>Durante edição inline</td>
            </tr>
            <tr>
                <td><kbd>Esc</kbd></td>
                <td>Cancelar Edição</td>
                <td>Durante edição inline</td>
            </tr>
            <tr>
                <td><kbd>↑↓</kbd></td>
                <td>Navegar Linhas</td>
                <td>Durante edição inline</td>
            </tr>
        </tbody>
    </table>
</div>`,
            descricao_rota: 'Cadastro de Produtos',
            ativo: true
        },
        '/clientes': {
            id: 3,
            descricao: 'Ajuda - Gestão de Clientes',
            conteudo: `# 👥 Gestão de Clientes - CRM Completo

<div class="help-intro">
    <p class="lead">Gerencie seu <strong>relacionamento com clientes</strong> de forma estratégica e eficiente.</p>
</div>

## 🎯 Funcionalidades Principais

<div class="feature-grid">
    <div class="feature-card">
        <h4>📋 Cadastro Completo</h4>
        <p>Informações detalhadas de contato, histórico e preferências</p>
    </div>
    <div class="feature-card">
        <h4>📞 Histórico de Contatos</h4>
        <p>Registre todas as interações e acompanhe o relacionamento</p>
    </div>
    <div class="feature-card">
        <h4>🎯 Segmentação</h4>
        <p>Organize clientes por categorias e características</p>
    </div>
</div>`,
            descricao_rota: 'Gestão de Clientes',
            ativo: true
        },
        '/financeiro': {
            id: 4,
            descricao: 'Ajuda - Módulo Financeiro',
            conteudo: `# 💰 Módulo Financeiro - Controle Total

<div class="help-intro">
    <p class="lead">Tenha <strong>controle total</strong> sobre as finanças da sua empresa com ferramentas avançadas.</p>
</div>

## 🏦 Funcionalidades

<div class="feature-grid">
    <div class="feature-card">
        <h4>📊 Fluxo de Caixa</h4>
        <p>Acompanhe entradas e saídas em tempo real</p>
    </div>
    <div class="feature-card">
        <h4>📋 Contas a Pagar/Receber</h4>
        <p>Gestão completa de obrigações financeiras</p>
    </div>
</div>`,
            descricao_rota: 'Módulo Financeiro',
            ativo: true
        },
        '/configuracoes': {
            id: 5,
            descricao: 'Ajuda - Configurações do Sistema',
            conteudo: `# ⚙️ Configurações do Sistema

<div class="help-intro">
    <p class="lead">Personalize o <strong>sistema</strong> conforme suas necessidades e preferências.</p>
</div>

## 🎨 Personalização

<div class="tips-container">
    <div class="tip-card tip-primary">
        <h4>🎨 Temas</h4>
        <p>Escolha entre modo claro e escuro para melhor conforto visual</p>
    </div>
    <div class="tip-card tip-success">
        <h4>🌍 Idiomas</h4>
        <p>Sistema disponível em português, inglês e espanhol</p>
    </div>
</div>`,
            descricao_rota: 'Configurações',
            ativo: true
        },
        default: {
            id: 999,
            descricao: 'Ajuda Geral do Sistema',
            conteudo: `# 🏠 Bem-vindo ao CorpERP!

<div class="help-intro">
    <p class="lead">Seja bem-vindo ao <strong>CorpERP</strong>! O sistema ERP mais completo e intuitivo para gerenciar todos os aspectos do seu negócio.</p>
</div>

## 🎯 O que você pode fazer aqui

<div class="feature-grid">
    <div class="feature-card">
        <h4>📊 Dashboard Inteligente</h4>
        <p>Monitore KPIs e métricas em tempo real</p>
    </div>
    <div class="feature-card">
        <h4>📦 Gestão de Produtos</h4>
        <p>Controle completo do seu catálogo</p>
    </div>
    <div class="feature-card">
        <h4>👥 CRM Avançado</h4>
        <p>Relacionamento com clientes otimizado</p>
    </div>
    <div class="feature-card">
        <h4>💰 Financeiro</h4>
        <p>Controle total das finanças</p>
    </div>
    <div class="feature-card">
        <h4>📋 Relatórios</h4>
        <p>Insights poderosos para decisões</p>
    </div>
    <div class="feature-card">
        <h4>⚙️ Configurações</h4>
        <p>Personalize conforme sua necessidade</p>
    </div>
</div>

## 🚀 Primeiros Passos

<div class="tips-container">
    <div class="tip-card tip-primary">
        <h4>1️⃣ Explore o Menu</h4>
        <p>Use o menu lateral para navegar entre os módulos. Cada seção tem suas próprias funcionalidades especializadas.</p>
    </div>

    <div class="tip-card tip-success">
        <h4>2️⃣ Busca Global</h4>
        <p>Use a barra de pesquisa no topo para encontrar rapidamente qualquer funcionalidade ou página do sistema.</p>
    </div>

    <div class="tip-card tip-warning">
        <h4>3️⃣ Personalize</h4>
        <p>Acesse suas configurações no menu do usuário para personalizar tema, idioma e preferências.</p>
    </div>
</div>

## 📞 Suporte e Contato

<div class="customization-guide">
    <h4>🆘 Precisa de ajuda?</h4>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
        <div>
            <strong>📧 E-mail</strong><br>
            <EMAIL>
        </div>
        <div>
            <strong>📱 Telefone</strong><br>
            (11) 99999-9999
        </div>
        <div>
            <strong>🕒 Horário</strong><br>
            Segunda a Sexta, 8h às 18h
        </div>
        <div>
            <strong>� Chat Online</strong><br>
            Disponível no sistema
        </div>
    </div>
</div>

## ⚡ Dicas de Produtividade

<div class="metrics-section">
    <div class="metric-item">
        <span class="metric-icon">🔍</span>
        <div>
            <strong>Busca Rápida</strong><br>
            <small>Use <kbd>Ctrl</kbd> + <kbd>K</kbd> para busca global</small>
        </div>
    </div>
    <div class="metric-item">
        <span class="metric-icon">🎨</span>
        <div>
            <strong>Temas</strong><br>
            <small>Alterne entre modo claro e escuro</small>
        </div>
    </div>
    <div class="metric-item">
        <span class="metric-icon">📱</span>
        <div>
            <strong>Responsivo</strong><br>
            <small>Funciona perfeitamente em mobile</small>
        </div>
    </div>
    <div class="metric-item">
        <span class="metric-icon">🔔</span>
        <div>
            <strong>Notificações</strong><br>
            <small>Configure alertas importantes</small>
        </div>
    </div>
</div>

## 🎓 Recursos de Aprendizado

<div class="filter-guide">
    <h4>📚 Continue aprendendo:</h4>
    <ul>
        <li><strong>🎥 Tutoriais em Vídeo</strong>: Acesse nossa biblioteca de vídeos explicativos</li>
        <li><strong>📖 Documentação</strong>: Guias detalhados para cada módulo</li>
        <li><strong>🎯 Treinamentos</strong>: Sessões ao vivo com nossa equipe</li>
        <li><strong>💡 Dicas Diárias</strong>: Receba dicas por e-mail para otimizar seu uso</li>
    </ul>
</div>`,
            descricao_rota: 'Ajuda Geral',
            ativo: true
        }
    }

    // Buscar conteúdo específico ou usar padrão
    helpContent.value = mockData[routeName] || mockData['default'] || null
}

// Lifecycle
onMounted(() => {
    loadHelpContent()
})
</script>

<style scoped>
.help-viewer-page {
    min-height: 100vh;
}

/* Toolbar compacto */
.help-toolbar-compact {
    min-height: 48px !important;
    padding: 0 12px;
}

/* Título compacto */
.help-title-compact {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 600;
}

.help-title-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300px;
}

/* Conteúdo da página */
.help-page-content {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: calc(100vh - 48px);
}

.help-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    min-height: 400px;
}

/* Responsividade */
@media (max-width: 768px) {
    .help-toolbar-compact {
        min-height: 44px !important;
        padding: 0 8px;
    }

    .help-title-compact {
        font-size: 14px;
    }

    .help-title-text {
        max-width: 200px;
    }

    .help-page-content {
        min-height: calc(100vh - 44px);
    }
}

/* Estilos para o conteúdo Markdown */
.markdown-content {
    line-height: 1.6;
}

.markdown-body h1 {
    color: #1976d2;
    border-bottom: 2px solid #1976d2;
    padding-bottom: 8px;
    margin-bottom: 16px;
}

.markdown-body h2 {
    color: #424242;
    margin-top: 24px;
    margin-bottom: 12px;
}

.markdown-body h3 {
    color: #616161;
    margin-top: 20px;
    margin-bottom: 10px;
}

.markdown-body p {
    margin-bottom: 12px;
    color: #424242;
}

.markdown-body ul,
.markdown-body ol {
    margin-bottom: 12px;
    padding-left: 20px;
}

.markdown-body li {
    margin-bottom: 4px;
}

.markdown-body blockquote {
    border-left: 4px solid #1976d2;
    background: #f5f5f5;
    padding: 12px 16px;
    margin: 16px 0;
    border-radius: 4px;
}

.markdown-body code {
    background: #f5f5f5;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

.markdown-body pre {
    background: #f5f5f5;
    padding: 16px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 16px 0;
}

/* Estilos personalizados para conteúdo de ajuda */
.markdown-body .help-intro {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    margin: 20px 0;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.markdown-body .help-intro .lead {
    font-size: 18px;
    margin: 0;
    line-height: 1.6;
}

.markdown-body .feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.markdown-body .feature-card {
    background: #f8f9ff;
    border: 2px solid #e3f2fd;
    border-radius: 12px;
    padding: 20px;
    transition:
        transform 0.2s ease,
        box-shadow 0.2s ease;
}

.markdown-body .feature-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.markdown-body .feature-card h4 {
    color: #1976d2;
    margin-top: 0;
    margin-bottom: 10px;
}

.markdown-body .metrics-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin: 20px 0;
}

.markdown-body .metric-item {
    display: flex;
    align-items: center;
    background: #fff;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #1976d2;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.markdown-body .metric-icon {
    font-size: 24px;
    margin-right: 15px;
    min-width: 40px;
}

.markdown-body .filter-guide {
    background: #fff8e1;
    border: 2px solid #ffecb3;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
}

.markdown-body .filter-guide h4 {
    color: #f57c00;
    margin-top: 0;
}

.markdown-body .reports-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.markdown-body .report-type {
    background: #f3e5f5;
    border: 2px solid #e1bee7;
    border-radius: 8px;
    padding: 15px;
}

.markdown-body .report-type h5 {
    color: #7b1fa2;
    margin-top: 0;
}

.markdown-body .tips-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin: 20px 0;
}

.markdown-body .tip-card {
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid;
}

.markdown-body .tip-primary {
    background: #e3f2fd;
    border-color: #2196f3;
}

.markdown-body .tip-success {
    background: #e8f5e8;
    border-color: #4caf50;
}

.markdown-body .tip-warning {
    background: #fff3e0;
    border-color: #ff9800;
}

.markdown-body .tip-card h4 {
    margin-top: 0;
    margin-bottom: 10px;
}

.markdown-body .shortcuts-table table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

.markdown-body .shortcuts-table th {
    background: #1976d2;
    color: white;
    padding: 12px;
    text-align: left;
}

.markdown-body .shortcuts-table td {
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.markdown-body .shortcuts-table tr:nth-child(even) {
    background: #f5f5f5;
}

.markdown-body .customization-guide {
    background: linear-gradient(45deg, #4caf50, #45a049);
    color: white;
    padding: 20px;
    border-radius: 12px;
    margin: 20px 0;
}

.markdown-body .customization-guide h4 {
    color: white;
    margin-top: 0;
}

.markdown-body kbd {
    background: #333;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 0.9em;
}
</style>
