<template>
    <crud-template
        table-name="novidades"
        :columns="columns"
        :rows="novidades"
        :loading="loading"
        :filter="tableFilter"
        :pagination="pagination"
        :toolbar-actions="toolbarActions"
        :selected-items="selected"
        :show-modal="showModal"
        :modal-title="modalTitle"
        :modal-saving="saving"
        :filter-columns="filterableColumns"
        v-model:quick-filter-value="columnFilter"
        :show-add-button="permissions.canCreate"
        :show-edit-action="permissions.canEdit"
        :show-delete-action="permissions.canDelete"
        :show-clear-button="true"
        @add-clicked="openCreateModal"
        @refresh-clicked="loadNovidades"
        @table-request="onTableRequest"
        @restore-pagination="onRestorePagination"
        @quick-filter="onColumnFilter"
        @edit-clicked="row => openEditModal(row as unknown as NovidadesData)"
        @delete-clicked="row => confirmDelete(row as unknown as NovidadesData)"
        @inline-edit="handleInlineEdit"
        @update:show-modal="showModal = $event"
        @modal-close="closeModal"
        @modal-cancel="closeModal"
        @modal-clear="clearForm"
        @modal-save="saveNovidade"
        @update:selected-items="
            items => (selected = items as unknown as NovidadesData[])
        "
        :actions-per-row="3"
    >
        <!-- Ações extras na linha -->
        <template #row-extra-actions="{ row }">
            <q-btn
                :icon="row.ativo ? 'visibility_off' : 'visibility'"
                :color="row.ativo ? 'negative' : 'positive'"
                size="sm"
                flat
                round
                @click="toggleItemStatus(row as NovidadesData)"
            >
                <q-tooltip>
                    {{
                        row.ativo
                            ? $t('buttons.deactivate')
                            : $t('buttons.activate')
                    }}
                </q-tooltip>
            </q-btn>
        </template>

        <!-- Conteúdo do modal -->
        <template #modal-content>
            <form-novidades ref="formRef" :initial-values="formInitialValues" />
        </template>
    </crud-template>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Dialog, Notify } from 'quasar'
import CrudTemplate from '@/components/CrudTemplate.vue'
import FormNovidades from 'src/components/forms/Configuracao/FormNovidades.vue'

import { useLoadingOverlay } from '@/composables/loadingOverlay'
import { useActionPermissions } from '@/composables/useActionPermissions'
import { useNovidadesStore } from '@/stores/novidades'
import type {
    TableColumn,
    ActionItem,
    FilterColumn,
    QuickFilterValue
} from '@/types/CrudTemplate'
import type { NovidadesData, FormNovidadesData } from '@/interfaces/Novidades'

const { t: $t } = useI18n()
const { setLoading } = useLoadingOverlay()
const { configuracaoNovidadesPermissions } = useActionPermissions()
const permissions = configuracaoNovidadesPermissions

// Store
const novidadesStore = useNovidadesStore()

// Refs
const formRef = ref<InstanceType<typeof FormNovidades> | null>(null)
const showModal = ref(false)
const saving = ref(false)
const loading = ref(false)
const selected = ref<NovidadesData[]>([])
const currentItem = ref<NovidadesData | null>(null)
const isEditing = ref(false)

// Usar dados do store
const novidades = computed(() => novidadesStore.novidades)

// Filtros e paginação
const tableFilter = ref({})
const pagination = ref({
    sortBy: 'id',
    descending: false,
    page: 1,
    rowsPerPage: 25,
    rowsNumber: novidades.value.length
})
const columnFilter = ref<QuickFilterValue | null>(null)

// Computed
const formInitialValues = computed(() => {
    if (isEditing.value && currentItem.value) {
        return {
            titulo: currentItem.value.titulo,
            descricao: currentItem.value.descricao,
            modulo: currentItem.value.modulo,
            rota: currentItem.value.rota,
            ativo: currentItem.value.ativo
        }
    }
    return {}
})

const modalTitle = computed(() => {
    return isEditing.value
        ? $t('pages.news.editTitle')
        : $t('pages.news.createTitle')
})

// Métodos
const clearForm = () => {
    if (formRef.value) {
        formRef.value.resetForm()
    }
}

const openCreateModal = () => {
    isEditing.value = false
    currentItem.value = null
    showModal.value = true
}

const openEditModal = (item: NovidadesData) => {
    isEditing.value = true
    currentItem.value = item
    showModal.value = true
}

const closeModal = () => {
    showModal.value = false
    currentItem.value = null
    isEditing.value = false
}

const saveNovidade = async () => {
    if (!formRef.value) return

    const validation = await formRef.value.validate()
    if (!validation.valid) {
        Notify.create({
            type: 'negative',
            message: $t('messages.checkRequiredFields')
        })
        return
    }

    saving.value = true
    setLoading(async () => {})

    try {
        const formData: FormNovidadesData = formRef.value.getData()

        if (isEditing.value && currentItem.value) {
            // Editar item existente usando o store
            await novidadesStore.atualizarNovidade(
                currentItem.value.id!,
                formData
            )
            Notify.create({
                type: 'positive',
                message: $t('messages.updatedSuccessfully')
            })
        } else {
            // Criar novo item usando o store
            await novidadesStore.criarNovidade(formData)
            Notify.create({
                type: 'positive',
                message: $t('messages.createdSuccessfully')
            })
        }

        closeModal()
    } catch {
        Notify.create({
            type: 'negative',
            message: $t('messages.errorSaving')
        })
    } finally {
        saving.value = false
        setLoading(async () => {})
    }
}

const confirmDelete = (item: NovidadesData) => {
    Dialog.create({
        title: $t('dialogs.confirmDelete'),
        message: $t('dialogs.confirmDeleteMessage', { item: item.titulo }),
        cancel: true,
        persistent: true
    }).onOk(() => {
        deleteNovidade(item)
    })
}

const deleteNovidade = async (item: NovidadesData) => {
    try {
        await novidadesStore.excluirNovidade(item.id!)
        Notify.create({
            type: 'positive',
            message: $t('messages.deletedSuccessfully')
        })
    } catch {
        Notify.create({
            type: 'negative',
            message: $t('messages.errorDeleting')
        })
    }
}

const toggleItemStatus = async (item: NovidadesData) => {
    try {
        const updatedData = {
            titulo: item.titulo,
            descricao: item.descricao,
            modulo: item.modulo,
            rota: item.rota,
            ativo: !item.ativo
        }

        await novidadesStore.atualizarNovidade(item.id!, updatedData)

        Notify.create({
            type: 'positive',
            message: updatedData.ativo
                ? $t('messages.activatedSuccessfully')
                : $t('messages.deactivatedSuccessfully')
        })
    } catch {
        Notify.create({
            type: 'negative',
            message: $t('messages.errorUpdating')
        })
    }
}

const loadNovidades = async () => {
    try {
        loading.value = true
        await novidadesStore.carregarNovidades()
    } catch {
        Notify.create({
            type: 'negative',
            message: $t('messages.errorLoading')
        })
    } finally {
        loading.value = false
    }
}

const onTableRequest = () => {
    // Implementar lógica de paginação e ordenação se necessário
}

const onRestorePagination = () => {
    // Implementar restauração de paginação se necessário
}

const onColumnFilter = (value: QuickFilterValue | null) => {
    columnFilter.value = value
    // Implementar filtro se necessário
}

const handleInlineEdit = () => {
    // Implementar edição inline se necessário
}

const publishAllDrafts = () => {
    Dialog.create({
        title: $t('dialogs.confirmPublishAll'),
        message: $t('dialogs.confirmPublishAllMessage'),
        cancel: true,
        persistent: true
    }).onOk(async () => {
        try {
            const drafts = novidades.value.filter(n => !n.ativo)

            for (const draft of drafts) {
                const updatedData = {
                    titulo: draft.titulo,
                    descricao: draft.descricao,
                    modulo: draft.modulo,
                    rota: draft.rota,
                    ativo: true
                }
                await novidadesStore.atualizarNovidade(draft.id!, updatedData)
            }

            Notify.create({
                type: 'positive',
                message: $t('messages.publishedAllSuccessfully', {
                    count: drafts.length
                })
            })
        } catch {
            Notify.create({
                type: 'negative',
                message: $t('messages.errorPublishing')
            })
        }
    })
}

// Colunas da tabela
const columns = computed((): TableColumn[] => [
    {
        name: 'id',
        label: $t('forms.labels.id'),
        field: 'id',
        align: 'left' as const,
        sortable: true,
        order: 1
    },
    {
        name: 'titulo',
        label: $t('forms.labels.title'),
        field: 'titulo',
        align: 'left' as const,
        sortable: true,
        order: 2,
        inlineEdit: permissions.value.canEdit,
        fieldType: 'text'
    },
    {
        name: 'descricao',
        label: $t('forms.labels.description'),
        field: 'descricao',
        align: 'left' as const,
        sortable: false,
        order: 3,
        format: (val: string) => {
            if (!val) return ''
            // Remove HTML tags e limita a 100 caracteres
            const texto = val.replace(/<[^>]*>/g, '')
            return texto.length > 100 ? texto.substring(0, 100) + '...' : texto
        }
    },
    {
        name: 'modulo',
        label: $t('forms.labels.module'),
        field: 'modulo',
        align: 'left' as const,
        sortable: true,
        order: 4
    },
    {
        name: 'rota',
        label: $t('forms.labels.route'),
        field: 'rota',
        align: 'left' as const,
        sortable: true,
        order: 5
    },
    {
        name: 'dataPublicacao',
        label: $t('forms.labels.publishDate'),
        field: 'dataPublicacao',
        align: 'center' as const,
        sortable: true,
        order: 6,
        format: (val: string) => {
            if (!val) return ''
            const date = new Date(val)
            return date.toLocaleDateString('pt-BR')
        }
    },
    {
        name: 'ativo',
        label: $t('forms.labels.status'),
        field: 'ativo',
        align: 'center' as const,
        sortable: true,
        order: 7
    },
    {
        name: 'actions',
        label: $t('forms.labels.actions'),
        field: 'actions',
        align: 'center' as const,
        sortable: false,
        order: 8
    }
])

// Colunas filtráveis
const filterableColumns = computed((): FilterColumn[] => [
    {
        label: $t('forms.labels.title'),
        value: 'titulo',
        field: 'titulo',
        type: 'text'
    },
    {
        label: $t('forms.labels.description'),
        value: 'descricao',
        field: 'descricao',
        type: 'text'
    },
    {
        label: $t('forms.labels.module'),
        value: 'modulo',
        field: 'modulo',
        type: 'text'
    },
    {
        label: $t('forms.labels.route'),
        value: 'rota',
        field: 'rota',
        type: 'text'
    },
    {
        label: $t('forms.labels.status'),
        value: 'ativo',
        field: 'ativo',
        type: 'boolean'
    }
])

// Ações da toolbar
const toolbarActions = computed((): ActionItem[] => [
    {
        label: $t('buttons.preview'),
        icon: 'preview',
        active: true,
        action: () => {
            // Abrir preview das novidades publicadas
            window.open('/novidades', '_blank')
        }
    },
    {
        label: $t('buttons.publishAll'),
        icon: 'publish',
        active: novidades.value.some(n => !n.ativo),
        action: () => publishAllDrafts()
    }
])

// Lifecycle
onMounted(() => {
    loadNovidades()
})
</script>
