<template>
    <crud-template
        table-name="help"
        :columns="columns"
        :rows="helps"
        :loading="loading"
        :filter="tableFilter"
        :pagination="pagination"
        :toolbar-actions="toolbarActions"
        :selected-items="selected"
        :show-modal="showModal"
        :modal-title="modalTitle"
        :modal-saving="saving"
        v-model:quick-filter-value="columnFilter"
        :show-add-button="permissions.canCreate"
        :show-edit-action="permissions.canEdit"
        :show-delete-action="permissions.canDelete"
        :show-clear-button="true"
        @add-clicked="openCreateModal"
        @refresh-clicked="loadHelps"
        @table-request="onTableRequest"
        @restore-pagination="onRestorePagination"
        @quick-filter="onColumnFilter"
        @edit-clicked="row => openEditModal(row as unknown as HelpData)"
        @delete-clicked="row => confirmDelete(row as unknown as HelpData)"
        @inline-edit="handleInlineEdit"
        @update:show-modal="showModal = $event"
        @modal-close="closeModal"
        @modal-cancel="closeModal"
        @modal-clear="clearForm"
        @modal-save="saveHelp"
        @update:selected-items="
            items => (selected = items as unknown as HelpData[])
        "
        :actions-per-row="3"
    >
        <!-- Ações extras na linha -->
        <template #row-extra-actions="{ row }">
            <q-btn
                :icon="row.ativo ? 'visibility_off' : 'visibility'"
                :color="row.ativo ? 'negative' : 'positive'"
                size="sm"
                flat
                round
                @click="toggleItemStatus(row as HelpData)"
            >
                <q-tooltip>
                    {{
                        row.ativo
                            ? $t('buttons.deactivate')
                            : $t('buttons.activate')
                    }}
                </q-tooltip>
            </q-btn>
        </template>

        <!-- Formulário no modal -->
        <template #modal-content>
            <FormHelp ref="formRef" :initial-values="formInitialValues" />
        </template>
    </crud-template>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Dialog, Notify } from 'quasar'
import CrudTemplate from '@/components/CrudTemplate.vue'
import FormHelp from 'src/components/forms/Configuracao/FormHelp.vue'

import { useLoadingOverlay } from '@/composables/loadingOverlay'
import { useActionPermissions } from '@/composables/useActionPermissions'
import { useHelpStore } from '@/stores/help'
import type {
    TableColumn,
    ActionItem,
    QuickFilterValue
} from '@/types/CrudTemplate'
import type { HelpData, FormHelpData } from '@/interfaces/Help'

const { t: $t } = useI18n()
const { setLoading } = useLoadingOverlay()
const { configuracaoHelpPermissions } = useActionPermissions()
const permissions = configuracaoHelpPermissions

// Store
const helpStore = useHelpStore()

// Refs
const formRef = ref()

// Estado
const showModal = ref(false)
const saving = ref(false)
const loading = ref(false)
const isEditing = ref(false)
const currentItem = ref<HelpData | null>(null)
const selected = ref<HelpData[]>([])

// Usar dados do store
const helps = computed(() => helpStore.helps)

// Filtros e paginação
const tableFilter = ref({})
const pagination = ref({
    sortBy: 'id',
    descending: false,
    page: 1,
    rowsPerPage: 25,
    rowsNumber: helps.value.length
})
const columnFilter = ref<QuickFilterValue | null>(null)

// Computed
const formInitialValues = computed(() => {
    if (isEditing.value && currentItem.value) {
        return {
            titulo: currentItem.value.titulo,
            conteudo: currentItem.value.conteudo,
            modulo: currentItem.value.modulo,
            rota: currentItem.value.rota,
            ativo: currentItem.value.ativo
        }
    }
    return {}
})

const modalTitle = computed(() => {
    return isEditing.value
        ? $t('pages.help.editTitle')
        : $t('pages.help.createTitle')
})

// Métodos
const clearForm = () => {
    if (formRef.value) {
        formRef.value.resetForm()
    }
}

const openCreateModal = () => {
    isEditing.value = false
    currentItem.value = null
    showModal.value = true
}

const openEditModal = (item: HelpData) => {
    isEditing.value = true
    currentItem.value = item
    showModal.value = true
}

const closeModal = () => {
    showModal.value = false
    currentItem.value = null
    isEditing.value = false
}

const saveHelp = async () => {
    if (!formRef.value) return

    const validation = await formRef.value.validate()
    if (!validation.valid) {
        Notify.create({
            type: 'negative',
            message: $t('messages.checkRequiredFields')
        })
        return
    }

    saving.value = true
    setLoading(async () => {})

    try {
        const formData: FormHelpData = formRef.value.getData()

        if (isEditing.value && currentItem.value) {
            // Editar item existente usando o store
            await helpStore.atualizarHelp(currentItem.value.id!, formData)
            Notify.create({
                type: 'positive',
                message: $t('messages.updatedSuccessfully')
            })
        } else {
            // Criar novo item usando o store
            await helpStore.criarHelp(formData)
            Notify.create({
                type: 'positive',
                message: $t('messages.createdSuccessfully')
            })
        }

        closeModal()
    } catch {
        Notify.create({
            type: 'negative',
            message: $t('messages.errorSaving')
        })
    } finally {
        saving.value = false
        setLoading(async () => {})
    }
}

const confirmDelete = (item: HelpData) => {
    Dialog.create({
        title: $t('dialogs.confirmDelete'),
        message: $t('dialogs.confirmDeleteMessage', { item: item.titulo }),
        cancel: true,
        persistent: true
    }).onOk(() => deleteHelp(item))
}

const deleteHelp = async (item: HelpData) => {
    try {
        await helpStore.excluirHelp(item.id!)
        Notify.create({
            type: 'positive',
            message: $t('messages.deletedSuccessfully')
        })
    } catch {
        Notify.create({
            type: 'negative',
            message: $t('messages.errorDeleting')
        })
    }
}

const toggleItemStatus = async (item: HelpData) => {
    try {
        const updatedData = {
            titulo: item.titulo,
            conteudo: item.conteudo,
            modulo: item.modulo,
            rota: item.rota,
            ativo: !item.ativo
        }

        await helpStore.atualizarHelp(item.id!, updatedData)

        Notify.create({
            type: 'positive',
            message: updatedData.ativo
                ? $t('messages.activatedSuccessfully')
                : $t('messages.deactivatedSuccessfully')
        })
    } catch {
        Notify.create({
            type: 'negative',
            message: $t('messages.errorUpdating')
        })
    }
}

const loadHelps = async () => {
    try {
        loading.value = true
        await helpStore.carregarHelps()
    } catch {
        Notify.create({
            type: 'negative',
            message: $t('messages.errorLoading')
        })
    } finally {
        loading.value = false
    }
}

// Configuração das colunas
const columns: TableColumn[] = [
    {
        name: 'id',
        label: $t('forms.labels.id'),
        field: 'id',
        align: 'left',
        sortable: true,
        style: 'width: 80px'
    },
    {
        name: 'titulo',
        label: $t('forms.labels.title'),
        field: 'titulo',
        align: 'left',
        sortable: true,
        style: 'min-width: 200px'
    },
    {
        name: 'modulo',
        label: $t('forms.labels.module'),
        field: 'modulo',
        align: 'left',
        sortable: true,
        style: 'width: 120px'
    },
    {
        name: 'rota',
        label: $t('forms.labels.route'),
        field: 'rota',
        align: 'left',
        sortable: true,
        style: 'width: 150px'
    },
    {
        name: 'ativo',
        label: $t('forms.labels.status'),
        field: 'ativo',
        align: 'center',
        sortable: true,
        style: 'width: 100px',
        format: (val: boolean) =>
            val ? $t('forms.labels.active') : $t('forms.labels.inactive')
    },
    {
        name: 'dataPublicacao',
        label: $t('forms.labels.publishDate'),
        field: 'dataPublicacao',
        align: 'center',
        sortable: true,
        style: 'width: 120px',
        format: (val: string) => new Date(val).toLocaleDateString('pt-BR')
    },
    {
        name: 'dataAtualizacao',
        label: $t('forms.labels.updateDate'),
        field: 'dataAtualizacao',
        align: 'center',
        sortable: true,
        style: 'width: 120px',
        format: (val: string) => new Date(val).toLocaleDateString('pt-BR')
    }
]

// Métodos de paginação e filtros
const onTableRequest = (props: Record<string, unknown>) => {
    const paginationData = props.pagination as {
        page: number
        rowsPerPage: number
        sortBy: string
        descending: boolean
    }
    const { page, rowsPerPage, sortBy, descending } = paginationData

    pagination.value.page = page
    pagination.value.rowsPerPage = rowsPerPage
    pagination.value.sortBy = sortBy
    pagination.value.descending = descending
}

const onRestorePagination = () => {
    pagination.value = {
        sortBy: 'id',
        descending: false,
        page: 1,
        rowsPerPage: 25,
        rowsNumber: helps.value.length
    }
}

const onColumnFilter = (value: QuickFilterValue | null) => {
    columnFilter.value = value
}

const handleInlineEdit = () => {
    // Implementar edição inline se necessário
}

// Ações da toolbar
const toolbarActions = computed((): ActionItem[] => [
    {
        label: $t('buttons.preview'),
        icon: 'preview',
        active: true,
        action: () => {
            // Abrir preview das ajudas
            window.open('/help', '_blank')
        }
    }
])

// Lifecycle
onMounted(() => {
    loadHelps()
})
</script>
