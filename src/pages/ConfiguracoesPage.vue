<template>
    <q-page class="q-pa-xs">
        <!-- <PERSON><PERSON> de configuração -->
        <q-card class="config-card">
            <q-tabs
                v-model="activeTab"
                dense
                :class="isDarkMode ? 'text-white' : 'text-grey'"
                :active-color="isDarkMode ? 'white' : 'primary'"
                :indicator-color="isDarkMode ? 'white' : 'primary'"
                align="justify"
                narrow-indicator
            >
                <q-tab name="theme" label="Tema" />
                <!-- Futuras abas serão adicionadas aqui -->
            </q-tabs>

            <q-separator />

            <q-tab-panels v-model="activeTab" animated>
                <!-- Aba do Tema -->
                <q-tab-panel name="theme" class="q-pa-none">
                    <ThemeColorsTab />
                </q-tab-panel>

                <!-- Futuras abas serão adicionadas aqui -->
            </q-tab-panels>
        </q-card>
    </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ThemeColorsTab from 'src/components/configuracoes/ThemeColorsTab.vue'
import { useTheme } from 'src/composables/useTheme'

// Meta da página
defineOptions({
    name: 'ConfiguracoesPage'
})

// Composables
const { isDarkMode } = useTheme()

// Estado das abas
const activeTab = ref('theme')
</script>

<style scoped>
/* Card principal */
.config-card {
    min-height: calc(100vh - 120px);
}
</style>
