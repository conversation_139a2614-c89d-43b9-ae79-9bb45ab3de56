<template>
    <div id="FormLogin" class="form-login-container">
        <!-- Título do formulário -->
        <div class="form-header">
            <h1 class="login-title">{{ $t('titles.login') }}</h1>
        </div>

        <!-- Formulário -->
        <div class="form-content">
            <q-form @submit.prevent="handleLogin" class="login-form">
                <div class="form-fields">
                    <q-input
                        v-model="username"
                        name="username"
                        :label="$t('forms.labels.username')"
                        :disable="formLoading"
                        :error="!!errors.username"
                        :error-message="errors.username"
                        outlined
                        class="form-input"
                    >
                        <template v-slot:prepend>
                            <q-icon name="person" color="grey-6" />
                        </template>
                    </q-input>

                    <q-input
                        v-model="password"
                        :label="$t('forms.labels.password')"
                        :disable="formLoading"
                        :error="!!errors.password"
                        :error-message="errors.password"
                        outlined
                        class="form-input"
                        :type="showPass ? 'text' : 'password'"
                    >
                        <template v-slot:prepend>
                            <q-icon name="lock" color="grey-6" />
                        </template>
                        <template v-slot:append>
                            <q-icon
                                :name="
                                    showPass ? 'visibility_off' : 'visibility'
                                "
                                class="cursor-pointer"
                                color="grey-6"
                                @click="toggleShowPass"
                            />
                        </template>
                    </q-input>

                    <q-btn
                        type="submit"
                        :label="$t('buttons.login')"
                        color="primary"
                        :loading="formLoading"
                        class="login-button"
                        size="lg"
                        no-caps
                    />
                </div>
            </q-form>
        </div>

        <!-- Footer com copyright -->
        <div class="form-footer">
            <p class="copyright-text">
                &copy; {{ new Date().getFullYear() }} {{ $t('titles.app') }}.
                {{ $t('titles.copyright') }}
            </p>
        </div>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref } from 'vue'
import yup from 'src/plugins/schemas/validators'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import { useForm } from 'vee-validate'

const loginSchema = yup.object({
    password: yup
        .string()
        .trim()
        .min(5, () => t('validate.invalidPassword'))
        .required(() => t('validate.requiredPassword')),
    username: yup
        .string()
        .trim()
        .min(3, () => t('validate.invalidUsername'))
        .required(() => t('validate.requiredUsername'))
})

const props = defineProps({
    loading: {
        type: Boolean,
        default: false
    }
})

const { handleSubmit, errors, defineField } = useForm({
    validationSchema: loginSchema,
    initialValues: {
        username: '',
        password: ''
    }
})

const [username] = defineField('username', {
    validateOnModelUpdate: false
})
const [password] = defineField('password', {
    validateOnModelUpdate: false
})

const formLoading = ref(props.loading)

const emit = defineEmits(['login'])

const handleLogin = handleSubmit(async values => {
    values.username = values.username.trim().toLowerCase()
    emit('login', values)
})

const showPass = ref<boolean>(false)
const toggleShowPass = () => {
    showPass.value = !showPass.value
}
</script>

<style lang="scss" scoped>
.form-login-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.form-header {
    text-align: center;
    margin-bottom: 2rem;
    width: 100%;
}

.login-title {
    font-size: 2rem;
    font-weight: 600;
    color: #333;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.form-content {
    margin-bottom: 2rem;
    width: 100%;
    max-width: 400px;
}

.form-fields {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-input {
    margin-bottom: 0;
}

.login-button {
    width: 100%;
    height: 48px;
    border-radius: 8px;
    font-weight: 600;
    margin-top: 0.5rem;
}

.form-footer {
    text-align: center;
    margin-top: 2rem;
    width: 100%;
}

.copyright-text {
    color: #999;
    font-size: 0.75rem;
    margin: 0;
    line-height: 1.4;
}

/* Suporte ao modo escuro */
.body--dark {
    .login-title {
        color: #ffffff;
    }

    .copyright-text {
        color: #888888;
    }
}

/* Responsividade */
@media (max-width: 768px) {
    .form-login-container {
        padding: 1rem 0;
    }

    .form-header {
        margin-bottom: 1.5rem;
    }

    .login-title {
        font-size: 1.75rem;
    }

    .form-content {
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 480px) {
    .form-login-container {
        padding: 0.5rem 0;
    }

    .form-header {
        margin-bottom: 1rem;
    }

    .login-title {
        font-size: 1.5rem;
    }

    .form-content {
        margin-bottom: 1rem;
    }

    .form-fields {
        gap: 1rem;
    }

    .login-button {
        height: 44px;
    }

    .form-footer {
        margin-top: 1rem;
    }
}
</style>
