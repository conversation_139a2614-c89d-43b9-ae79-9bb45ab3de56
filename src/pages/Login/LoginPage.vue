<template>
    <div id="LoginPage" class="login-container">
        <!-- Layout principal dividido -->
        <div class="login-layout">
            <!-- Lado esquerdo - Logo personalizada e ilustração -->
            <div class="login-left-side">
                <!-- Nuvens nas bordas -->
                <div class="clouds-container">
                    <div class="cloud cloud-top-left"></div>
                    <div class="cloud cloud-top-right"></div>
                    <div class="cloud cloud-bottom-left"></div>
                    <div class="cloud cloud-bottom-right"></div>
                    <div class="cloud cloud-center-left"></div>
                    <div class="cloud cloud-center-right"></div>
                    <div class="cloud cloud-top-center"></div>
                    <div class="cloud cloud-bottom-center"></div>
                    <div class="cloud cloud-far-left"></div>
                    <div class="cloud cloud-far-right"></div>
                </div>

                <!-- Logo personalizado centralizado -->
                <div class="logo-center-container">
                    <q-img
                        :src="personalizedLogo"
                        alt="personalized-logo"
                        class="personalized-logo-container responsive-logo"
                        fit="contain"
                        spinner-color="white"
                        no-spinner
                    >
                        <template v-slot:error>
                            <div
                                class="text-white text-h5 text-center q-pa-lg personalized-logo-fallback"
                            >
                                LOGO PERSONALIZADO
                            </div>
                        </template>
                    </q-img>
                </div>
            </div>

            <!-- Lado direito - Formulário -->
            <div class="login-right-side">
                <div class="form-container responsive-form">
                    <FormLogin @login="login" :loading="formLoginLoading" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useI18n } from 'vue-i18n'
import { useLoadingOverlay } from '@/composables/loadingOverlay'
import { useEnv } from '@/composables/useEnv'

import FormLogin from './components/FormLogin.vue'

const router = useRouter()
const auth = useAuthStore()
const { t: $t } = useI18n()
const { setLoading } = useLoadingOverlay()
const { getLogoSystem } = useEnv()

const personalizedLogo = getLogoSystem()

const formLoginLoading = ref(false)

function isAlreadyLogged() {
    const hasTokenInStorage = localStorage.getItem('token')
    const hasUserInStorage = localStorage.getItem('user')
    const hasTokenInPinia = auth.token

    // Só fazer logout se realmente houver um token válido (no Pinia ou localStorage)
    if (hasTokenInPinia && hasTokenInStorage && hasUserInStorage) {
        auth.logout()
    }
}

async function authUser(formLogin: { username: string; password: string }) {
    const { token, user } = await auth.authenticateUser(formLogin)
    auth.setToken(token)
    auth.setUser(user)
}

async function login(formLogin: { username: string; password: string }) {
    isAlreadyLogged()

    try {
        await setLoading(async () => {
            await authUser(formLogin)

            if (auth.token) {
                const { data } = await auth.asyncRequest({
                    endpoint:
                        '/api/corpsystem/produto-service/pessoa-usuario/login',
                    method: 'get',
                    customErrorMessages: {
                        400: $t('errors.requests.login.400')
                    }
                })

                const profile = await auth.asyncRequest({
                    endpoint: '/api/corpsystem/produto-service/pn-usuario/only',
                    method: 'get',
                    params: {
                        id_auth_user: auth.user?.id
                    },
                    customErrorMessages: {
                        400: $t('errors.requests.login.400')
                    }
                })

                auth.setProfile(profile.data.results[0])

                // Buscar e salvar permissões do usuário
                const userPermissions = await auth.fetchUserPermissions(
                    profile.data.results[0].id
                )
                auth.setUserPermissions(userPermissions)

                if (
                    (data as { message: string }).message ===
                    'Login realizado com sucesso.'
                ) {
                    router.push('/')
                }
            }
        })
    } catch (error) {
        // Erro capturado - usuário permanece na tela de login
        // O erro já é tratado pelos componentes (notificações, etc.)
        void error // Evita warning de variável não utilizada
    }
}
</script>

<style lang="scss" scoped>
@use 'sass:color';
.login-container {
    height: 100vh;
    min-height: 100vh;
    overflow: hidden;
}

.login-layout {
    display: flex;
    height: 100vh;
    min-height: 100vh;
}

.login-left-side {
    flex: 1;
    background: linear-gradient(
        135deg,
        $primary 0%,
        color.adjust($primary, $lightness: -15%) 100%
    );
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.logo-center-container {
    position: relative;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
}

.personalized-logo-container {
    -webkit-filter: drop-shadow(1px 1px 0 white) drop-shadow(-1px 1px 0 white)
        drop-shadow(1px -1px 0 white) drop-shadow(-1px -1px 0 white);

    filter: drop-shadow(1px 1px 0 white) drop-shadow(-1px 1px 0 white)
        drop-shadow(1px -1px 0 white) drop-shadow(-1px -1px 0 white);
}

.personalized-logo-fallback {
    -webkit-filter: drop-shadow(1px 1px 0 white) drop-shadow(-1px 1px 0 white)
        drop-shadow(1px -1px 0 white) drop-shadow(-1px -1px 0 white);

    filter: drop-shadow(1px 1px 0 white) drop-shadow(-1px 1px 0 white)
        drop-shadow(1px -1px 0 white) drop-shadow(-1px -1px 0 white);
}

.responsive-logo {
    width: 250px;
    height: 100px;
    max-width: 100%;
    max-height: 100%;
}

.login-right-side {
    flex: 1;
    background: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    min-height: 100vh;
}

.body--dark .login-right-side {
    background: #21262c;
}

.form-container {
    width: 100%;
    max-width: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.responsive-form {
    width: 100%;
    max-width: 100%;
    padding: 0;
}

/* Responsividade */
@media (min-width: 769px) {
    .login-layout {
        flex-direction: row;
    }

    .login-left-side,
    .login-right-side {
        flex: 1;
    }
}

@media (max-width: 768px) {
    .login-container {
        height: auto;
        min-height: 100vh;
        overflow: auto;
    }

    .login-layout {
        flex-direction: column;
        height: auto;
        min-height: 100vh;
    }

    .login-left-side {
        flex: 0 0 auto;
        min-height: 120px;
        height: auto;
        padding: 1rem;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .login-right-side {
        flex: 1;
        min-height: auto;
        height: auto;
        padding: 1rem;
        display: flex;
        justify-content: center;
        align-items: flex-start;
    }

    .responsive-logo {
        width: 200px;
        height: 80px;
    }

    .form-container {
        width: 100%;
        max-width: 100%;
        padding: 0;
    }
}
</style>
