<template>
    <q-page class="q-pa-xs column fit">
        <!-- Head<PERSON> <PERSON><PERSON> -->
        <q-banner class="bg-primary text-white q-mb-xs" rounded>
            <template v-slot:avatar>
                <q-icon name="waving_hand" size="md" class="wave-animation" />
            </template>
            <div class="text-h6 q-mb-xs">
                {{ t('pages.home.welcome.title') }}
            </div>
            <div class="text-body2 text-white">
                {{ t('pages.home.welcome.subtitle') }}
            </div>
            <template v-slot:action>
                <q-btn
                    color="white"
                    text-color="primary"
                    icon="dashboard"
                    :label="t('pages.home.welcome.dashboardButton')"
                    dense
                    unelevated
                    class="q-mr-xs"
                />
                <q-btn color="white" icon="help" dense outline>
                    <q-tooltip>{{
                        t('pages.home.welcome.helpButton')
                    }}</q-tooltip>
                </q-btn>
            </template>
        </q-banner>

        <!-- Layout Principal -->
        <div class="row q-col-gutter-xs col-md-grow">
            <!-- Quadro de Avisos -->
            <div class="col-12 col-md-9 col-md-grow column">
                <q-card class="col-md-grow column">
                    <q-card-section class="bg-primary text-white q-pa-xs">
                        <div class="row items-center">
                            <q-icon name="campaign" size="sm" class="q-mr-xs" />
                            <div class="text-h6">
                                {{ t('pages.home.noticeBoard.title') }}
                            </div>
                            <q-space />
                            <q-btn
                                icon="add"
                                color="white"
                                text-color="primary"
                                size="sm"
                                round
                                dense
                                @click="showAddNotice = true"
                            >
                                <q-tooltip>{{
                                    t('pages.home.noticeBoard.addNoticeTooltip')
                                }}</q-tooltip>
                            </q-btn>
                        </div>
                    </q-card-section>

                    <q-separator />

                    <q-card-section class="q-pa-none col-md-grow">
                        <q-scroll-area style="height: 500px; min-height: 300px">
                            <q-list v-if="notices.length > 0" separator>
                                <q-item
                                    v-for="notice in notices"
                                    :key="notice.id"
                                    class="q-pa-xs"
                                >
                                    <q-item-section side top>
                                        <q-icon
                                            :name="
                                                getPriorityIcon(notice.priority)
                                            "
                                            :color="
                                                getPriorityColor(
                                                    notice.priority
                                                )
                                            "
                                            size="sm"
                                        />
                                    </q-item-section>

                                    <q-item-section>
                                        <q-item-label
                                            class="text-weight-medium text-primary"
                                        >
                                            {{ notice.title }}
                                        </q-item-label>
                                        <q-item-label
                                            caption
                                            class="text-body2 q-mt-xs"
                                        >
                                            {{ notice.content }}
                                        </q-item-label>
                                        <div
                                            class="row items-center q-mt-sm q-gutter-xs"
                                            style="flex-wrap: wrap"
                                        >
                                            <q-chip
                                                :color="
                                                    getPriorityColor(
                                                        notice.priority
                                                    )
                                                "
                                                text-color="white"
                                                size="sm"
                                                dense
                                            >
                                                {{
                                                    getPriorityLabel(
                                                        notice.priority
                                                    )
                                                }}
                                            </q-chip>
                                            <q-chip
                                                color="grey-4"
                                                text-color="grey-8"
                                                size="sm"
                                                dense
                                                icon="person"
                                            >
                                                {{ notice.author }}
                                            </q-chip>
                                            <q-chip
                                                color="grey-4"
                                                text-color="grey-8"
                                                size="sm"
                                                dense
                                                icon="schedule"
                                            >
                                                {{ formatDate(notice.date) }}
                                            </q-chip>
                                        </div>
                                    </q-item-section>

                                    <!-- Ações do aviso -->
                                    <q-item-section side>
                                        <div class="row q-gutter-xs">
                                            <q-btn
                                                icon="edit"
                                                color="primary"
                                                flat
                                                round
                                                dense
                                                size="sm"
                                                @click="editNotice(notice)"
                                            >
                                                <q-tooltip>{{
                                                    $t('buttons.edit')
                                                }}</q-tooltip>
                                            </q-btn>
                                            <q-btn
                                                icon="delete"
                                                color="negative"
                                                flat
                                                round
                                                dense
                                                size="sm"
                                                @click="
                                                    confirmDeleteNotice(notice)
                                                "
                                            >
                                                <q-tooltip>{{
                                                    $t('buttons.delete')
                                                }}</q-tooltip>
                                            </q-btn>
                                        </div>
                                    </q-item-section>
                                </q-item>
                            </q-list>

                            <!-- Estado vazio -->
                            <div v-else class="q-pa-xl text-center">
                                <q-icon
                                    name="info"
                                    size="4rem"
                                    color="grey-5"
                                />
                                <div class="text-grey-6 q-mt-md">
                                    {{ t('pages.home.noticeBoard.emptyState') }}
                                </div>
                            </div>
                        </q-scroll-area>
                    </q-card-section>
                </q-card>
            </div>

            <!-- Sidebar com Informações -->
            <div class="col-12 col-md-3 col-md-grow column q-md-gutter-xs">
                <!-- Estatísticas Rápidas -->
                <q-card class="col-md-grow column">
                    <q-card-section class="bg-secondary text-white q-pa-xs">
                        <div class="row items-center">
                            <q-icon
                                name="analytics"
                                size="sm"
                                class="q-mr-xs"
                            />
                            <div class="text-h6">
                                {{ t('pages.home.statistics.title') }}
                            </div>
                        </div>
                    </q-card-section>

                    <q-card-section class="q-pa-xs col-md-grow">
                        <div class="row q-col-gutter-xs full-height">
                            <div class="col-6 col-md-12">
                                <q-card
                                    class="bg-primary text-white text-center q-pa-sm full-height column justify-center"
                                >
                                    <div class="text-h4 text-md-h5">
                                        {{ stats.totalUsers }}
                                    </div>
                                    <div class="text-body2 text-md-caption">
                                        {{ t('pages.home.statistics.users') }}
                                    </div>
                                </q-card>
                            </div>
                            <div class="col-6 col-md-12">
                                <q-card
                                    class="bg-secondary text-white text-center q-pa-sm full-height column justify-center"
                                >
                                    <div class="text-h4 text-md-h5">
                                        {{ stats.totalNotices }}
                                    </div>
                                    <div class="text-body2 text-md-caption">
                                        {{ t('pages.home.statistics.notices') }}
                                    </div>
                                </q-card>
                            </div>
                            <div class="col-6 col-md-12">
                                <q-card
                                    class="bg-warning text-white text-center q-pa-sm full-height column justify-center"
                                >
                                    <div class="text-h4 text-md-h5">
                                        {{ stats.pendingTasks }}
                                    </div>
                                    <div class="text-body2 text-md-caption">
                                        {{ t('pages.home.statistics.pending') }}
                                    </div>
                                </q-card>
                            </div>
                            <div class="col-6 col-md-12">
                                <q-card
                                    class="bg-positive text-white text-center q-pa-sm full-height column justify-center"
                                >
                                    <div class="text-h4 text-md-h5">
                                        {{ stats.completedTasks }}
                                    </div>
                                    <div class="text-body2 text-md-caption">
                                        {{
                                            t('pages.home.statistics.completed')
                                        }}
                                    </div>
                                </q-card>
                            </div>
                        </div>
                    </q-card-section>
                </q-card>
            </div>
        </div>

        <!-- Modal para Adicionar Aviso -->
        <modal-template
            v-model="showAddNotice"
            :title="t('pages.home.addNoticeModal.title')"
            card-style="max-width: 500px"
            card-class="full-width"
        >
            <q-form @submit="addNotice" class="q-gutter-md">
                <q-input
                    v-model="newNotice.title"
                    :label="t('pages.home.addNoticeModal.titleField')"
                    outlined
                    :rules="[
                        val =>
                            !!val ||
                            t(
                                'pages.home.addNoticeModal.validation.titleRequired'
                            )
                    ]"
                />

                <q-select
                    v-model="newNotice.priority"
                    :options="priorityOptions"
                    :label="t('pages.home.addNoticeModal.priorityField')"
                    outlined
                    emit-value
                    map-options
                />

                <q-input
                    v-model="newNotice.content"
                    :label="t('pages.home.addNoticeModal.contentField')"
                    type="textarea"
                    rows="4"
                    outlined
                    :rules="[
                        val =>
                            !!val ||
                            t(
                                'pages.home.addNoticeModal.validation.contentRequired'
                            )
                    ]"
                />

                <q-input
                    v-model="newNotice.author"
                    :label="t('pages.home.addNoticeModal.authorField')"
                    outlined
                    :rules="[
                        val =>
                            !!val ||
                            t(
                                'pages.home.addNoticeModal.validation.authorRequired'
                            )
                    ]"
                />
            </q-form>

            <template #actions>
                <q-btn
                    :label="t('pages.home.addNoticeModal.cancelButton')"
                    color="negative"
                    flat
                    @click="showAddNotice = false"
                />
                <q-btn
                    :label="t('pages.home.addNoticeModal.publishButton')"
                    color="positive"
                    @click="addNotice"
                />
            </template>
        </modal-template>

        <!-- Modal para Editar Aviso -->
        <modal-template
            v-model="showEditNotice"
            :title="t('pages.home.editNoticeModal.title')"
            card-style="max-width: 500px"
            card-class="full-width"
        >
            <q-form @submit="updateNotice" class="q-gutter-md">
                <q-input
                    v-model="editingNotice.title"
                    :label="t('pages.home.editNoticeModal.titleField')"
                    outlined
                    :rules="[
                        val =>
                            !!val ||
                            t(
                                'pages.home.editNoticeModal.validation.titleRequired'
                            )
                    ]"
                />

                <q-select
                    v-model="editingNotice.priority"
                    :options="priorityOptions"
                    :label="t('pages.home.editNoticeModal.priorityField')"
                    outlined
                    emit-value
                    map-options
                />

                <q-input
                    v-model="editingNotice.content"
                    :label="t('pages.home.editNoticeModal.contentField')"
                    type="textarea"
                    rows="4"
                    outlined
                    :rules="[
                        val =>
                            !!val ||
                            t(
                                'pages.home.editNoticeModal.validation.contentRequired'
                            )
                    ]"
                />

                <q-input
                    v-model="editingNotice.author"
                    :label="t('pages.home.editNoticeModal.authorField')"
                    outlined
                    :rules="[
                        val =>
                            !!val ||
                            t(
                                'pages.home.editNoticeModal.validation.authorRequired'
                            )
                    ]"
                />
            </q-form>

            <template #actions>
                <q-btn
                    :label="t('pages.home.editNoticeModal.cancelButton')"
                    color="negative"
                    flat
                    @click="cancelEdit"
                />
                <q-btn
                    :label="t('pages.home.editNoticeModal.saveButton')"
                    color="positive"
                    @click="updateNotice"
                />
            </template>
        </modal-template>
    </q-page>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useQuasar } from 'quasar'
import { useI18n } from 'vue-i18n'
import ModalTemplate from '@/components/ModalTemplate.vue'

const $q = useQuasar()
const { t } = useI18n()

// Interface para os avisos
interface Notice {
    id: number
    title: string
    content: string
    author: string
    date: Date
    priority: string
}

// Estado reativo
const showAddNotice = ref(false)
const showEditNotice = ref(false)
const editingNoticeId = ref<number | null>(null)

// Dados dos avisos
const notices = ref<Notice[]>([
    {
        id: 1,
        title: 'Reunião Geral da Empresa',
        content:
            'Reunião geral marcada para sexta-feira às 14h no auditório principal. Presença obrigatória para todos os colaboradores.',
        author: 'Recursos Humanos',
        date: new Date('2024-01-15'),
        priority: 'high'
    },
    {
        id: 2,
        title: 'Nova Política de Home Office',
        content:
            'A partir do próximo mês, implementaremos uma nova política de trabalho remoto. Consulte o manual de procedimentos para mais detalhes.',
        author: 'Diretoria',
        date: new Date('2024-01-12'),
        priority: 'medium'
    },
    {
        id: 3,
        title: 'Manutenção do Sistema',
        content:
            'Manutenção programada do sistema para este sábado das 8h às 12h. Durante este período, alguns serviços podem ficar indisponíveis.',
        author: 'TI',
        date: new Date('2024-01-10'),
        priority: 'low'
    },
    {
        id: 4,
        title: 'Programa de Treinamento',
        content:
            'Inscrições abertas para o programa de capacitação em novas tecnologias. Vagas limitadas, inscreva-se até o final do mês.',
        author: 'Desenvolvimento',
        date: new Date('2024-01-08'),
        priority: 'medium'
    }
])

// Estatísticas
const stats = reactive({
    totalUsers: 156,
    totalNotices: 12,
    pendingTasks: 8,
    completedTasks: 24
})

// Formulário para novo aviso
const newNotice = reactive({
    title: '',
    content: '',
    author: '',
    priority: 'medium'
})

// Formulário para editar aviso
const editingNotice = reactive({
    title: '',
    content: '',
    author: '',
    priority: 'medium'
})

// Opções de prioridade
const priorityOptions = [
    { label: t('pages.home.noticeBoard.priority.high'), value: 'high' },
    { label: t('pages.home.noticeBoard.priority.medium'), value: 'medium' },
    { label: t('pages.home.noticeBoard.priority.low'), value: 'low' }
]

// Funções auxiliares
const getPriorityIcon = (priority: string) => {
    switch (priority) {
        case 'high':
            return 'priority_high'
        case 'medium':
            return 'remove'
        case 'low':
            return 'keyboard_arrow_down'
        default:
            return 'info'
    }
}

const getPriorityColor = (priority: string) => {
    switch (priority) {
        case 'high':
            return 'negative'
        case 'medium':
            return 'warning'
        case 'low':
            return 'positive'
        default:
            return 'grey'
    }
}

const getPriorityLabel = (priority: string) => {
    switch (priority) {
        case 'high':
            return t('pages.home.noticeBoard.priority.high')
        case 'medium':
            return t('pages.home.noticeBoard.priority.medium')
        case 'low':
            return t('pages.home.noticeBoard.priority.low')
        default:
            return t('pages.home.noticeBoard.priority.medium')
    }
}

const formatDate = (date: Date) => {
    return date.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    })
}

// Adicionar novo aviso
const addNotice = () => {
    const notice = {
        id: Date.now(),
        title: newNotice.title,
        content: newNotice.content,
        author: newNotice.author,
        date: new Date(),
        priority: newNotice.priority
    }

    notices.value.unshift(notice)
    stats.totalNotices++

    // Limpar formulário
    Object.assign(newNotice, {
        title: '',
        content: '',
        author: '',
        priority: 'medium'
    })

    showAddNotice.value = false

    $q.notify({
        type: 'positive',
        message: t('pages.home.addNoticeModal.successMessage'),
        position: 'top'
    })
}

// Editar aviso
const editNotice = (notice: Notice) => {
    editingNoticeId.value = notice.id
    Object.assign(editingNotice, {
        title: notice.title,
        content: notice.content,
        author: notice.author,
        priority: notice.priority
    })
    showEditNotice.value = true
}

// Atualizar aviso
const updateNotice = () => {
    const index = notices.value.findIndex(n => n.id === editingNoticeId.value)

    if (index !== -1 && notices.value[index]) {
        // Atualizar propriedades específicas mantendo id e date
        const notice = notices.value[index]
        notice.title = editingNotice.title
        notice.content = editingNotice.content
        notice.author = editingNotice.author
        notice.priority = editingNotice.priority

        showEditNotice.value = false
        editingNoticeId.value = null

        $q.notify({
            type: 'positive',
            message: t('pages.home.editNoticeModal.successMessage'),
            position: 'top'
        })
    }
}

// Cancelar edição
const cancelEdit = () => {
    showEditNotice.value = false
    editingNoticeId.value = null
    Object.assign(editingNotice, {
        title: '',
        content: '',
        author: '',
        priority: 'medium'
    })
}

// Confirmar exclusão de aviso
const confirmDeleteNotice = (notice: Notice) => {
    $q.dialog({
        title: t('pages.home.deleteNoticeDialog.title'),
        message: t('pages.home.deleteNoticeDialog.message', {
            title: notice.title
        }),
        cancel: {
            label: t('pages.home.deleteNoticeDialog.cancelButton'),
            color: 'primary',
            flat: true
        },
        ok: {
            label: t('pages.home.deleteNoticeDialog.confirmButton'),
            color: 'negative'
        },
        persistent: true
    }).onOk(() => {
        deleteNotice(notice.id)
    })
}

// Deletar aviso
const deleteNotice = (noticeId: number) => {
    const index = notices.value.findIndex(n => n.id === noticeId)

    if (index !== -1) {
        notices.value.splice(index, 1)
        stats.totalNotices--

        $q.notify({
            type: 'positive',
            message: t('pages.home.deleteNoticeDialog.successMessage'),
            position: 'top'
        })
    }
}
</script>

<style lang="scss" scoped>
/* Animação do ícone de aceno */
.wave-animation {
    animation: wave 2s ease-in-out infinite;
}

@keyframes wave {
    0%,
    100% {
        transform: rotate(0deg);
    }
    25% {
        transform: rotate(20deg);
    }
    75% {
        transform: rotate(-10deg);
    }
}

/* Scroll area responsivo */
@media (min-width: 768px) {
    .q-scroll-area {
        height: calc(100vh - 250px) !important;
        min-height: 300px !important;
    }
}

@media (max-width: 767px) {
    .q-scroll-area {
        height: 500px !important;
        min-height: 300px !important;
    }
}
</style>
