<template>
    <q-page class="q-pa-sm">
        <!-- Header e <PERSON> -->
        <div class="q-mb-sm">
            <div class="row q-col-gutter-md q-mb-sm">
                <!-- Filtro de Status -->
                <div class="col-md-3 col-sm-6 col-12">
                    <q-select
                        v-model="filtroStatus"
                        :options="statusOptions"
                        :label="$t('pages.newsView.filters.status')"
                        outlined
                        dense
                        emit-value
                        map-options
                        clearable
                        bg-color="white"
                    >
                        <template v-slot:prepend>
                            <q-icon name="filter_alt" />
                        </template>
                    </q-select>
                </div>

                <!-- Filtro de Módulo -->
                <div class="col-md-3 col-sm-6 col-12">
                    <q-select
                        v-model="filtroModulo"
                        :options="modulosOptions"
                        :label="$t('pages.newsView.filters.module')"
                        outlined
                        dense
                        emit-value
                        map-options
                        clearable
                        bg-color="white"
                    >
                        <template v-slot:prepend>
                            <q-icon name="apps" />
                        </template>
                    </q-select>
                </div>

                <!-- Campo de Busca -->
                <div class="col-md-4 col-sm-8 col-12">
                    <q-input
                        v-model="filtroTitulo"
                        :label="$t('pages.newsView.filters.search')"
                        outlined
                        dense
                        clearable
                        debounce="300"
                        bg-color="white"
                    >
                        <template v-slot:prepend>
                            <q-icon name="search" />
                        </template>
                    </q-input>
                </div>

                <!-- Filtro por Período -->
                <div class="col-md-2 col-sm-4 col-12">
                    <q-select
                        v-model="filtroPeriodo"
                        :options="periodoOptions"
                        :label="$t('pages.newsView.filters.period')"
                        outlined
                        dense
                        emit-value
                        map-options
                        clearable
                        bg-color="white"
                    >
                        <template v-slot:prepend>
                            <q-icon name="date_range" />
                        </template>
                    </q-select>
                </div>
            </div>

            <!-- Segunda linha de filtros -->
            <div class="row q-col-gutter-md q-mb-sm">
                <!-- Ordenação -->
                <div class="col-md-2 col-sm-4 col-12">
                    <q-select
                        v-model="ordenacao"
                        :options="ordenacaoOptions"
                        :label="$t('pages.newsView.filters.sortBy')"
                        outlined
                        dense
                        emit-value
                        map-options
                        bg-color="white"
                    >
                        <template v-slot:prepend>
                            <q-icon name="sort" />
                        </template>
                    </q-select>
                </div>

                <!-- Filtro por Data Início -->
                <div class="col-md-2 col-sm-4 col-12">
                    <q-input
                        v-model="filtroDataInicio"
                        :label="$t('pages.newsView.filters.startDate')"
                        outlined
                        dense
                        type="date"
                        clearable
                        bg-color="white"
                    >
                        <template v-slot:prepend>
                            <q-icon name="event" />
                        </template>
                    </q-input>
                </div>

                <!-- Filtro por Data Fim -->
                <div class="col-md-2 col-sm-4 col-12">
                    <q-input
                        v-model="filtroDataFim"
                        :label="$t('pages.newsView.filters.endDate')"
                        outlined
                        dense
                        type="date"
                        clearable
                        bg-color="white"
                    >
                        <template v-slot:prepend>
                            <q-icon name="event" />
                        </template>
                    </q-input>
                </div>

                <!-- Filtro no Conteúdo -->
                <div class="col-md-3 col-sm-6 col-12">
                    <q-input
                        v-model="filtroConteudo"
                        :label="$t('pages.newsView.filters.searchContent')"
                        outlined
                        dense
                        clearable
                        debounce="300"
                        bg-color="white"
                    >
                        <template v-slot:prepend>
                            <q-icon name="article" />
                        </template>
                    </q-input>
                </div>

                <!-- Botões de Ação -->
                <div class="col-md-3 col-sm-6 col-12">
                    <div class="row q-gutter-sm">
                        <div class="col">
                            <q-btn
                                v-if="novidadesStore.totalNovas > 0"
                                color="primary"
                                icon="done_all"
                                @click="marcarTodasComoVistas"
                                :loading="novidadesStore.loading"
                                class="full-width"
                                size="md"
                                dense
                                :label="
                                    $t('pages.newsView.actions.markAllAsViewed')
                                "
                            />
                        </div>
                        <div class="col">
                            <q-btn
                                color="grey-7"
                                icon="clear_all"
                                @click="limparFiltros"
                                class="full-width"
                                dense
                                :label="
                                    $t('pages.newsView.actions.clearFilters')
                                "
                                outline
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Grid de Novidades -->
        <div class="row q-col-gutter-sm">
            <div
                v-for="novidade in novidadesFiltradas"
                :key="novidade.id || 0"
                class="col-xl-3 col-lg-4 col-md-6 col-12"
            >
                <q-card
                    class="cursor-pointer h-full"
                    flat
                    bordered
                    @click="abrirNovidade(novidade)"
                >
                    <q-card-section>
                        <!-- Cabeçalho do Card -->
                        <div class="row items-center no-wrap q-mb-sm">
                            <div
                                class="col text-h6 text-weight-bold text-grey-9 ellipsis"
                            >
                                {{ novidade.titulo }}
                            </div>
                            <q-chip
                                :color="novidade.vista ? 'green-1' : 'orange-1'"
                                :text-color="
                                    novidade.vista ? 'green' : 'orange'
                                "
                                size="sm"
                                :icon="novidade.vista ? 'done' : 'fiber_new'"
                                class="q-ml-sm"
                            >
                                {{
                                    novidade.vista
                                        ? $t(
                                              'pages.newsView.status.viewedLabel'
                                          )
                                        : $t('pages.newsView.status.newLabel')
                                }}
                            </q-chip>
                        </div>

                        <!-- Metadados -->
                        <div
                            class="row items-center q-gutter-x-md q-mb-sm text-caption text-grey-6"
                        >
                            <div class="row items-center">
                                <q-icon
                                    name="business"
                                    size="16px"
                                    class="q-mr-xs"
                                />
                                {{ novidade.modulo }}
                            </div>
                            <div class="row items-center">
                                <q-icon
                                    name="calendar_today"
                                    size="16px"
                                    class="q-mr-xs"
                                />
                                {{ formatarData(novidade.dataPublicacao) }}
                            </div>
                        </div>

                        <!-- Preview do conteúdo -->
                        <div
                            class="text-body2 text-grey-8 q-mb-sm line-clamp-3"
                        >
                            {{ obterPreview(novidade.descricao) }}
                        </div>
                    </q-card-section>

                    <!-- Ações -->
                    <q-card-actions align="right" class="q-px-md q-pb-md">
                        <q-btn
                            v-if="!novidade.vista"
                            flat
                            color="primary"
                            :label="$t('pages.newsView.actions.markAsViewed')"
                            @click.stop="marcarComoVista(novidade)"
                            icon="done"
                            size="sm"
                            padding="xs sm"
                        />
                        <q-btn
                            flat
                            color="grey-7"
                            :label="$t('pages.newsView.actions.viewDetails')"
                            icon="arrow_forward"
                            size="sm"
                            padding="xs sm"
                        />
                    </q-card-actions>
                </q-card>
            </div>

            <!-- Estado vazio -->
            <div
                v-if="novidadesFiltradas.length === 0"
                class="col-12 text-center q-py-xl"
            >
                <q-icon name="article" size="xl" color="grey-4" />
                <div class="text-h6 text-grey-6 q-mt-md">
                    {{ $t('pages.newsView.noNews') }}
                </div>
                <div class="text-subtitle2 text-grey-5 q-mt-xs">
                    {{ $t('pages.newsView.noNewsDescription') }}
                </div>
            </div>
        </div>

        <!-- Modal de Detalhes -->
        <q-dialog v-model="modalAberto" maximized>
            <q-card class="column">
                <!-- Header do Modal -->
                <q-card-section class="row items-center bg-grey-2 q-pb-sm">
                    <div class="text-h5 text-weight-bold text-grey-9">
                        {{ novidadeSelecionada?.titulo }}
                    </div>
                    <q-space />
                    <q-btn icon="close" flat round dense @click="fecharModal" />
                </q-card-section>

                <!-- Corpo do Modal -->
                <q-card-section class="col q-pt-lg scroll">
                    <!-- Chips de metadados -->
                    <div class="row q-gutter-sm q-mb-lg">
                        <q-chip
                            color="blue-1"
                            text-color="blue"
                            icon="business"
                        >
                            {{ novidadeSelecionada?.modulo }}
                        </q-chip>
                        <q-chip color="teal-1" text-color="teal" icon="route">
                            {{ novidadeSelecionada?.rota }}
                        </q-chip>
                        <q-chip
                            color="grey-2"
                            text-color="grey-7"
                            icon="calendar_today"
                        >
                            {{
                                formatarData(
                                    novidadeSelecionada?.dataPublicacao || ''
                                )
                            }}
                        </q-chip>
                    </div>

                    <q-separator class="q-mb-lg" />

                    <!-- Conteúdo -->
                    <div
                        class="text-body1 text-grey-9 novidade-conteudo"
                        style="line-height: 1.6"
                        v-html="novidadeSelecionada?.descricao"
                    ></div>
                </q-card-section>

                <!-- Footer do Modal -->
                <q-card-actions align="right" class="bg-grey-1 q-pa-md">
                    <q-btn
                        v-if="!novidadeSelecionada?.vista"
                        color="primary"
                        :label="$t('pages.newsView.actions.markAsViewed')"
                        @click="
                            marcarComoVista(
                                novidadeSelecionada as NovidadeExibicao
                            )
                        "
                        icon="done"
                        padding="sm md"
                    />
                    <q-btn
                        flat
                        color="grey-7"
                        :label="$t('pages.newsView.actions.close')"
                        @click="fecharModal"
                        padding="sm md"
                    />
                </q-card-actions>
            </q-card>
        </q-dialog>
    </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { Notify } from 'quasar'

// Composables
import { usePermissions } from '@/composables/usePermissions'

// Store
import { useNovidadesStore } from '@/stores/novidades'

// Types
import type { NovidadeExibicao } from '@/interfaces/Novidades'

const { t: $t } = useI18n()
const { systemModules } = usePermissions()

// Store
const novidadesStore = useNovidadesStore()

// Estado
const modalAberto = ref(false)
const novidadeSelecionada = ref<NovidadeExibicao | null>(null)

// Filtros
const filtroStatus = ref<'all' | 'new' | 'viewed' | null>(null)
const filtroModulo = ref<string | null>(null)
const filtroTitulo = ref('')
const filtroPeriodo = ref<string | null>(null)
const filtroDataInicio = ref('')
const filtroDataFim = ref('')
const filtroConteudo = ref('')
const ordenacao = ref('dataPublicacao-desc')

// Opções para filtros
const statusOptions = [
    { label: $t('pages.newsView.status.all'), value: null },
    { label: $t('pages.newsView.status.new'), value: 'new' },
    { label: $t('pages.newsView.status.viewed'), value: 'viewed' }
]

const modulosOptions = computed(() => {
    const opcoes: Array<{ label: string; value: string | null }> = [
        { label: $t('pages.newsView.filters.allModules'), value: null }
    ]

    systemModules.value
        .filter(module => module.ativo)
        .forEach(module => {
            opcoes.push({
                label: module.descricao,
                value: module.descricao
            })
        })

    return opcoes
})

// Opções para período
const periodoOptions = [
    { label: $t('pages.newsView.periods.all'), value: null },
    { label: $t('pages.newsView.periods.week'), value: 'semana' },
    { label: $t('pages.newsView.periods.month'), value: 'mes' },
    { label: $t('pages.newsView.periods.quarter'), value: 'trimestre' },
    { label: $t('pages.newsView.periods.year'), value: 'ano' }
]

// Opções para ordenação
const ordenacaoOptions = [
    {
        label: $t('pages.newsView.sorting.dateDesc'),
        value: 'dataPublicacao-desc'
    },
    {
        label: $t('pages.newsView.sorting.dateAsc'),
        value: 'dataPublicacao-asc'
    },
    { label: $t('pages.newsView.sorting.titleAsc'), value: 'titulo-asc' },
    { label: $t('pages.newsView.sorting.titleDesc'), value: 'titulo-desc' },
    { label: $t('pages.newsView.sorting.moduleAsc'), value: 'modulo-asc' },
    { label: $t('pages.newsView.sorting.statusNew'), value: 'status-new' }
]

// Computed
const novidadesFiltradas = computed(() => {
    let novidades = novidadesStore.buscarNovidades({
        status: filtroStatus.value || null,
        modulo: filtroModulo.value || null,
        busca: filtroTitulo.value || null,
        ativo: true
    })

    // Filtro por período
    if (filtroPeriodo.value) {
        const agora = new Date()
        let dataLimite: Date

        switch (filtroPeriodo.value) {
            case 'semana':
                dataLimite = new Date(agora.getTime() - 7 * 24 * 60 * 60 * 1000)
                break
            case 'mes':
                dataLimite = new Date(
                    agora.getTime() - 30 * 24 * 60 * 60 * 1000
                )
                break
            case 'trimestre':
                dataLimite = new Date(
                    agora.getTime() - 90 * 24 * 60 * 60 * 1000
                )
                break
            case 'ano':
                dataLimite = new Date(
                    agora.getTime() - 365 * 24 * 60 * 60 * 1000
                )
                break
            default:
                dataLimite = new Date(0)
        }

        novidades = novidades.filter(
            n => new Date(n.dataPublicacao) >= dataLimite
        )
    }

    // Filtro por data específica
    if (filtroDataInicio.value) {
        const dataInicio = new Date(filtroDataInicio.value)
        novidades = novidades.filter(n => {
            const dataPublicacao = new Date(n.dataPublicacao)
            return dataPublicacao >= dataInicio
        })
    }

    if (filtroDataFim.value) {
        const dataFim = new Date(filtroDataFim.value)
        // Adicionar 23:59:59 para incluir todo o dia
        dataFim.setHours(23, 59, 59, 999)
        novidades = novidades.filter(n => {
            const dataPublicacao = new Date(n.dataPublicacao)
            return dataPublicacao <= dataFim
        })
    }

    // Filtro por conteúdo
    if (filtroConteudo.value) {
        const termo = filtroConteudo.value.toLowerCase()
        novidades = novidades.filter(n =>
            n.descricao.toLowerCase().includes(termo)
        )
    }

    // Ordenação
    if (ordenacao.value) {
        const [campo, direcao] = ordenacao.value.split('-')

        novidades.sort((a, b) => {
            let valorA: string | number | Date, valorB: string | number | Date

            switch (campo) {
                case 'titulo':
                    valorA = a.titulo.toLowerCase()
                    valorB = b.titulo.toLowerCase()
                    break
                case 'modulo':
                    valorA = a.modulo.toLowerCase()
                    valorB = b.modulo.toLowerCase()
                    break
                case 'dataPublicacao':
                    valorA = new Date(a.dataPublicacao)
                    valorB = new Date(b.dataPublicacao)
                    break
                case 'status':
                    valorA = a.vista ? 1 : 0
                    valorB = b.vista ? 1 : 0
                    break
                default:
                    return 0
            }

            if (direcao === 'desc') {
                return valorA > valorB ? -1 : valorA < valorB ? 1 : 0
            } else {
                return valorA < valorB ? -1 : valorA > valorB ? 1 : 0
            }
        })
    }

    return novidades
})

// Métodos
const carregarNovidades = async () => {
    try {
        await novidadesStore.carregarNovidades()
    } catch {
        Notify.create({
            type: 'negative',
            message: $t('pages.newsView.errors.loadError')
        })
    }
}

const marcarComoVista = async (novidade: NovidadeExibicao) => {
    if (!novidade.vista && novidade.id) {
        try {
            await novidadesStore.marcarComoVista(novidade.id)

            Notify.create({
                type: 'positive',
                message: $t('pages.newsView.messages.markedAsViewed')
            })
        } catch {
            Notify.create({
                type: 'negative',
                message: $t('pages.newsView.messages.markViewedError')
            })
        }
    }
}

const marcarTodasComoVistas = async () => {
    try {
        await novidadesStore.marcarTodasComoVistas()

        Notify.create({
            type: 'positive',
            message: $t('pages.newsView.messages.allMarkedAsViewed')
        })
    } catch {
        Notify.create({
            type: 'negative',
            message: $t('pages.newsView.messages.markAllViewedError')
        })
    }
}

const abrirNovidade = (novidade: NovidadeExibicao) => {
    novidadeSelecionada.value = novidade
    modalAberto.value = true

    // Marcar como vista automaticamente ao abrir
    if (!novidade.vista) {
        marcarComoVista(novidade)
    }
}

const fecharModal = () => {
    modalAberto.value = false
    novidadeSelecionada.value = null
}

const formatarData = (data: string): string => {
    return new Date(data).toLocaleDateString('pt-BR')
}

const obterPreview = (html: string): string => {
    // Remove tags HTML e limita a 150 caracteres
    const texto = html.replace(/<[^>]*>/g, '')
    return texto.length > 150 ? texto.substring(0, 150) + '...' : texto
}

const limparFiltros = () => {
    filtroStatus.value = null
    filtroModulo.value = null
    filtroTitulo.value = ''
    filtroPeriodo.value = null
    filtroDataInicio.value = ''
    filtroDataFim.value = ''
    filtroConteudo.value = ''
    ordenacao.value = 'dataPublicacao-desc'
}

// Lifecycle
onMounted(() => {
    carregarNovidades()
})
</script>

<style scoped>
/* Estilos simples e funcionais */
.preview-text {
    max-height: 60px;
    overflow: hidden;
    line-height: 1.4;
}

.q-card {
    transition: all 0.3s ease;
}

.q-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Modal Content */
.novidade-conteudo {
    line-height: 1.6;
}

.novidade-conteudo :deep(ul) {
    padding-left: 20px;
}

.novidade-conteudo :deep(li) {
    margin-bottom: 4px;
}

.novidade-conteudo :deep(p) {
    margin-bottom: 12px;
}
</style>
