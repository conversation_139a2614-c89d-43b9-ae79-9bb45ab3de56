<template>
    <crud-template
        table-name="addressType"
        :columns="columns"
        :rows="addressTypes"
        :loading="loading"
        :filter="tableFilter"
        :pagination="pagination"
        :toolbar-actions="toolbarActions"
        :selected-items="selected"
        :show-modal="showModal"
        :modal-title="modalTitle"
        :modal-saving="saving"
        :filter-columns="filterableColumns"
        v-model:quick-filter-value="columnFilter"
        :show-add-button="permissions.canCreate"
        :show-edit-action="permissions.canEdit"
        :show-delete-action="permissions.canDelete"
        :show-clear-button="true"
        @add-clicked="openCreateModal"
        @refresh-clicked="loadAddressType"
        @table-request="onTableRequest"
        @restore-pagination="onRestorePagination"
        @quick-filter="onColumnFilter"
        @edit-clicked="row => openEditModal(row as unknown as AddressType)"
        @delete-clicked="row => confirmDelete(row as unknown as AddressType)"
        @inline-edit="handleInlineEdit"
        @update:show-modal="showModal = $event"
        @modal-close="closeModal"
        @modal-cancel="closeModal"
        @modal-clear="clearForm"
        @modal-save="saveAddressType"
        @update:selected-items="
            items => (selected = items as unknown as AddressType[])
        "
    >
        <!-- Conteúdo do modal -->
        <template #modal-content>
            <form-basico
                ref="formRef"
                :title="$t('pages.addressType.form.title')"
                :enabled-fields="['descricao']"
                :initial-values="formInitialValues"
            />
        </template>
    </crud-template>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Dialog, Notify } from 'quasar'
import CrudTemplate from '@/components/CrudTemplate.vue'
import FormBasico from 'src/components/forms/FormBasico.vue'
import { useAuthStore } from '@/stores/auth'
import { useLoadingOverlay } from '@/composables/loadingOverlay'
import { useActionPermissions } from '@/composables/useActionPermissions'
import type { TableColumn, ActionItem } from '@/types/CrudTemplate'

const { t: $t } = useI18n()
const { setLoading } = useLoadingOverlay()
const { cadastroGeralTipoEnderecoPermissions } = useActionPermissions()
const permissions = cadastroGeralTipoEnderecoPermissions

// Types
interface AddressType {
    id: number
    descricao: string
    // ativo: boolean
}

interface FormAddressTypeInstance {
    validate: () => Promise<{ valid: boolean }>
    getData: () => Record<string, unknown>
    resetForm: () => void
}

interface TableRequestProps {
    pagination: {
        sortBy: string
        descending: boolean
        page: number
        rowsPerPage: number
        rowsNumber?: number
    }
    filter?:
        | {
              contem_descricao: string
              ativo: boolean | null
          }
        | undefined
    getCellValue?: (col: unknown, row: unknown) => unknown
}

// Stores
const authStore = useAuthStore()

// Refs
const addressTypes = ref<AddressType[]>([])
const selected = ref<AddressType[]>([])
const loading = ref(false)
const saving = ref(false)
const showModal = ref(false)
const isEditing = ref(false)
const formRef = ref<FormAddressTypeInstance | null>(null)
const currentItem = ref<AddressType | null>(null)

// Filtros
const columnFilter = ref<{
    column: string
    content: string | number | boolean
} | null>(null)

// Action Collection
const toolbarActions = computed<ActionItem[]>(() => [
    {
        label: $t('buttons.editSelected'),
        active: selected.value.length === 1,
        icon: 'edit',
        description: $t('buttons.editSelectedDescription'),
        action: () => {
            if (selected.value.length === 1 && selected.value[0]) {
                openEditModal(selected.value[0])
            }
        }
    },
    {
        label: $t('buttons.deleteSelected', {
            count: selected.value.length
        }),
        active: selected.value.length > 0,
        icon: 'delete',
        description: $t('buttons.deleteSelectedDescription'),
        action: confirmDeleteMultiple
    }
])

// Paginação
const pagination = ref({
    sortBy: 'id',
    descending: false,
    page: 1,
    rowsPerPage: 25,
    rowsNumber: 0
})

// Computed
const filterableColumns = computed(() => [
    {
        label: $t('forms.labels.description'),
        value: 'descricao',
        field: 'descricao',
        type: 'text' as const
    }
    // {
    //     label: $t('forms.labels.status'),
    //     value: 'ativo',
    //     field: 'ativo',
    //     type: 'boolean' as const
    // }
])

const tableFilter = computed(() => {
    if (
        columnFilter.value &&
        columnFilter.value.column &&
        columnFilter.value.content !== null &&
        columnFilter.value.content !== undefined
    ) {
        try {
            // Converter valor para string para compatibilidade com q-table
            const content = columnFilter.value.content
            const stringValue =
                typeof content === 'string' ? content : String(content)

            // Só retornar filtro se o valor for válido
            if (
                stringValue &&
                stringValue.trim() !== '' &&
                stringValue !== 'undefined' &&
                stringValue !== 'null'
            ) {
                return {
                    [columnFilter.value.column]: stringValue
                }
            }
        } catch {
            // Ignorar erro e retornar filtro vazio
        }
    }
    return {}
})

const formInitialValues = computed(() => {
    if (isEditing.value && currentItem.value) {
        return {
            // ativo: currentItem.value.ativo,
            descricao: currentItem.value.descricao
        }
    }
    return {}
})

const modalTitle = computed(() => {
    return isEditing.value
        ? $t('pages.addressType.editTitle')
        : $t('pages.addressType.createTitle')
})

const clearForm = () => {
    try {
        if (formRef.value) {
            formRef.value.resetForm()
        }
    } catch {
        Notify.create({
            message: 'Erro ao limpar formulário',
            color: 'negative',
            position: 'top',
            timeout: 3000,
            icon: 'error'
        })
    }
}

// Colunas da tabela
const columns: TableColumn[] = [
    {
        name: 'id',
        label: $t('forms.labels.id'),
        field: 'id',
        align: 'left' as const,
        sortable: true,
        order: 1
    },
    {
        name: 'descricao',
        label: $t('forms.labels.description'),
        field: 'descricao',
        align: 'left' as const,
        sortable: true,
        order: 2,
        inlineEdit: permissions.value.canEdit,
        fieldType: 'text'
    },
    {
        name: 'actions',
        label: $t('forms.labels.actions'),
        field: 'actions',
        align: 'center' as const,
        sortable: false,
        order: 5
    }
]

// Métodos
const loadAddressType = async (
    props: Partial<TableRequestProps> | null = null
) => {
    await setLoading(async () => {
        loading.value = true

        const requestPagination = props?.pagination || pagination.value
        const filter = props?.filter || tableFilter.value

        // Construir parâmetros da requisição
        const params: Record<string, unknown> = {
            page: requestPagination.page,
            page_size: requestPagination.rowsPerPage
        }

        // Aplicar filtro de coluna se existir
        if (filter && Object.keys(filter).length > 0) {
            Object.entries(filter).forEach(([key, value]) => {
                if (value !== null && value !== undefined && value !== '') {
                    params[key] = value
                }
            })
        }

        if (requestPagination.sortBy) {
            const sortOrder = requestPagination.descending ? '-' : ''
            params.ordering = `${sortOrder}${requestPagination.sortBy}`
        }

        const response = await authStore.asyncRequest({
            endpoint: '/api/corpsystem/produto-service/tipo-endereco',
            method: 'get',
            params,
            customErrorMessages: {
                400: $t('pages.addressType.errors.loadError'),
                404: $t('pages.addressType.errors.loadError'),
                500: $t('pages.addressType.errors.loadError')
            }
        })

        const data = response.data

        addressTypes.value = data.results
        pagination.value = {
            ...requestPagination,
            rowsNumber: data.count
        }

        loading.value = false
    })
}

const onTableRequest = (requestProps: Record<string, unknown>) => {
    // Converter o requestProps para o formato esperado
    const props: Partial<TableRequestProps> = {}

    if (requestProps.pagination) {
        props.pagination =
            requestProps.pagination as TableRequestProps['pagination']
    }

    if (requestProps.filter) {
        props.filter = requestProps.filter as TableRequestProps['filter']
    }

    loadAddressType(props)
}

const onColumnFilter = (
    filterValue: { column: string; content: string | number | boolean } | null
) => {
    columnFilter.value = filterValue
    pagination.value.page = 1
    loadAddressType()
}

const onRestorePagination = (savedPagination: Record<string, unknown>) => {
    // Restaurar a paginação salva, mas sempre começar na página 1
    pagination.value = {
        ...pagination.value,
        ...savedPagination,
        page: 1 // Sempre começar na página 1
    }
}

// Modal methods
const openCreateModal = () => {
    isEditing.value = false
    currentItem.value = null
    showModal.value = true
}

const openEditModal = async (item: AddressType) => {
    try {
        await setLoading(async () => {
            try {
                // Fazer requisição para obter os dados completos do item
                const fullItemData = await getAddressTypeById(item.id)

                // Atualizar o item atual com os dados completos da API
                currentItem.value = fullItemData
                isEditing.value = true
                showModal.value = true
            } catch {
                // Se falhar ao buscar dados completos, usar os dados do item da lista
                currentItem.value = item
                isEditing.value = true
                showModal.value = true

                Notify.create({
                    message: $t('pages.addressType.errors.loadError'),
                    color: 'warning',
                    position: 'top',
                    timeout: 3000,
                    icon: 'warning'
                })
            }
        })
    } catch {
        // Fallback final
        currentItem.value = item
        isEditing.value = true
        showModal.value = true
    }
}

const closeModal = () => {
    showModal.value = false
    isEditing.value = false
    currentItem.value = null
}

const saveAddressType = async () => {
    try {
        if (!formRef.value) {
            return
        }

        const { valid } = await formRef.value.validate()

        if (!valid) {
            Notify.create({
                message: $t('errors.validationError'),
                color: 'negative',
                position: 'top',
                timeout: 3000,
                icon: 'error'
            })
            return
        }

        await setLoading(async () => {
            try {
                saving.value = true

                if (!formRef.value) {
                    throw new Error('FormRef não está disponível')
                }

                const formData = formRef.value.getData()

                if (isEditing.value && currentItem.value) {
                    await updateAddressType(currentItem.value.id, formData)
                } else {
                    await createAddressType(formData)
                }

                // Sucesso - fechar modal e recarregar dados
                closeModal()
                loadAddressType()

                Notify.create({
                    message: isEditing.value
                        ? $t('pages.addressType.messages.updateSuccess')
                        : $t('pages.addressType.messages.createSuccess'),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } finally {
                saving.value = false
            }
        })
    } catch {
        Notify.create({
            message: $t('pages.addressType.errors.saveError'),
            color: 'negative',
            position: 'top',
            timeout: 3000,
            icon: 'error'
        })
    }
}

// CRUD methods
const getAddressTypeById = async (id: number): Promise<AddressType> => {
    try {
        const response = await authStore.asyncRequest({
            endpoint: `/api/corpsystem/produto-service/tipo-endereco/${id}`,
            method: 'get',
            customErrorMessages: {
                404: $t('pages.addressType.errors.notFound'),
                500: $t('pages.addressType.errors.loadError')
            }
        })
        return response.data
    } catch (error) {
        throw new Error(`Erro ao buscar país ${id}: ${error}`)
    }
}

const createAddressType = async (
    data: Record<string, unknown>
): Promise<AddressType> => {
    try {
        const response = await authStore.asyncRequest({
            endpoint: '/api/corpsystem/produto-service/tipo-endereco',
            method: 'post',
            params: data,
            customErrorMessages: {
                400: $t('pages.addressType.errors.duplicateDescription'),
                500: $t('pages.addressType.errors.saveError')
            }
        })
        return response.data
    } catch (error) {
        throw new Error(`Erro ao criar país: ${error}`)
    }
}

const updateAddressType = async (
    id: number,
    data: Record<string, unknown>
): Promise<AddressType> => {
    try {
        const response = await authStore.asyncRequest({
            endpoint: `/api/corpsystem/produto-service/tipo-endereco/${id}`,
            method: 'put',
            params: data,
            customErrorMessages: {
                400: $t('pages.addressType.errors.duplicateDescription'),
                404: $t('pages.addressType.errors.saveError'),
                500: $t('pages.addressType.errors.saveError')
            }
        })
        return response.data
    } catch (error) {
        throw new Error(`Erro ao atualizar país ${id}: ${error}`)
    }
}

const deleteAddressType = async (id: number): Promise<void> => {
    try {
        await authStore.asyncRequest({
            endpoint: `/api/corpsystem/produto-service/tipo-endereco/${id}`,
            method: 'delete',
            customErrorMessages: {
                400: $t('pages.addressType.errors.duplicateDescription'),
                404: $t('pages.addressType.errors.deleteError'),
                500: $t('pages.addressType.errors.deleteError')
            }
        })
    } catch (error) {
        throw new Error(`Erro ao deletar país ${id}: ${error}`)
    }
}

const deleteMultipleAddressType = async (ids: number[]): Promise<void> => {
    try {
        const deletePromises = ids.map(id =>
            authStore.asyncRequest({
                endpoint: `/api/corpsystem/produto-service/tipo-endereco/${id}`,
                method: 'delete',
                customErrorMessages: {
                    400: $t('pages.addressType.errors.deleteMultipleError'),
                    404: $t('pages.addressType.errors.deleteMultipleError'),
                    500: $t('pages.addressType.errors.deleteMultipleError')
                }
            })
        )

        await Promise.all(deletePromises)
    } catch (error) {
        throw new Error(`Erro ao deletar múltiplos países: ${error}`)
    }
}

// Delete methods
const confirmDelete = (item: AddressType) => {
    Dialog.create({
        title: $t('dialogs.confirmDelete.title'),
        message: $t('dialogs.confirmDelete.message', { item: item.descricao }),
        cancel: true,
        persistent: true,
        ok: {
            label: $t('buttons.delete'),
            color: 'negative'
        }
    }).onOk(async () => {
        await setLoading(async () => {
            try {
                await deleteAddressType(item.id)
                loadAddressType()

                Notify.create({
                    message: $t('pages.addressType.messages.deleteSuccess'),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } catch {
                Notify.create({
                    message: $t('pages.addressType.errors.deleteError'),
                    color: 'negative',
                    position: 'top',
                    timeout: 3000,
                    icon: 'error'
                })
            }
        })
    })
}

const confirmDeleteMultiple = () => {
    Dialog.create({
        title: $t('dialogs.confirmDeleteMultiple.title'),
        message: $t('dialogs.confirmDeleteMultiple.message', {
            count: selected.value.length
        }),
        cancel: true,
        persistent: true,
        ok: {
            label: $t('buttons.delete'),
            color: 'negative'
        }
    }).onOk(async () => {
        await setLoading(async () => {
            try {
                const ids = selected.value.map(item => item.id)
                await deleteMultipleAddressType(ids)
                selected.value = []
                loadAddressType()

                Notify.create({
                    message: $t(
                        'pages.addressType.messages.deleteMultipleSuccess'
                    ),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } catch {
                Notify.create({
                    message: $t('pages.addressType.errors.deleteMultipleError'),
                    color: 'negative',
                    position: 'top',
                    timeout: 3000,
                    icon: 'error'
                })
            }
        })
    })
}
// Inline edit handler
const handleInlineEdit = async (data: {
    row: Record<string, unknown>
    column: string
    value: unknown
    oldValue: unknown
}) => {
    try {
        const addressTypeItem = data.row as unknown as AddressType
        const updateData = {
            descricao: addressTypeItem.descricao,
           // ativo: addressTypeItem.ativo,
            [data.column]: data.value
        }

        // Fazer a chamada para a API
        await updateAddressType(addressTypeItem.id, updateData)

        // Atualizar os dados locais
        const index = addressTypes.value.findIndex(b => b.id === addressTypeItem.id)
        if (index !== -1) {
            const currentItem = addressTypes.value[index]
            if (currentItem) {
                const updatedItem: AddressType = {
                    id: currentItem.id,
                    descricao: currentItem.descricao,
                    //ativo: currentItem.ativo
                }

                if (data.column === 'descricao') {
                    updatedItem.descricao = data.value as string
                }

                addressTypes.value[index] = updatedItem
            }
        }

        Notify.create({
            message: $t('pages.addressType.messages.updateSuccess'),
            color: 'positive',
            position: 'top',
            timeout: 2000,
            icon: 'check_circle'
        })
    } catch {
        Notify.create({
            message: $t('pages.addressType.errors.updateError'),
            color: 'negative',
            position: 'top',
            timeout: 3000,
            icon: 'error'
        })

        // Recarregar dados para reverter a mudança visual
        loadAddressType()
    }
}

// Lifecycle
onMounted(() => {
    loadAddressType()
})
</script>

<style scoped>
.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Estilos para toolbar compacto */
.toolbar-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    flex-wrap: wrap;
}

.toolbar-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
}

/* Responsividade para telas menores */
@media (max-width: 768px) {
    .toolbar-container {
        justify-content: center;
        gap: 6px;
    }

    .toolbar-buttons {
        justify-content: center;
        gap: 6px;
    }
}
</style>
