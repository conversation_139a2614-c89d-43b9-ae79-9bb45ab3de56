<template>
    <crud-template
        table-name="categorias-cnh"
        :columns="columns"
        :rows="categoriasCNH"
        :loading="loading"
        :filter="tableFilter"
        :pagination="pagination"
        :toolbar-actions="toolbarActions"
        :selected-items="selected"
        :show-modal="showModal"
        :modal-title="modalTitle"
        :modal-saving="saving"
        :filter-columns="filterableColumns"
        v-model:quick-filter-value="columnFilter"
        :show-add-button="permissions.canCreate"
        :show-edit-action="permissions.canEdit"
        :show-delete-action="permissions.canDelete"
        :show-clear-button="true"
        @add-clicked="openCreateModal"
        @refresh-clicked="loadCategoriasCNH"
        @table-request="onTableRequest"
        @restore-pagination="onRestorePagination"
        @quick-filter="onColumnFilter"
        @edit-clicked="row => openEditModal(row as unknown as CategoriaCNH)"
        @delete-clicked="row => confirmDelete(row as unknown as CategoriaCNH)"
        @inline-edit="handleInlineEdit"
        @update:show-modal="showModal = $event"
        @modal-close="closeModal"
        @modal-cancel="closeModal"
        @modal-clear="clearForm"
        @modal-save="saveCategoriaCNH"
        @update:selected-items="
            items => (selected = items as unknown as CategoriaCNH[])
        "
    >
        <!-- Ações extras na linha -->
        <template #row-extra-actions="{ row }">
            <q-btn
                icon="visibility"
                color="info"
                flat
                round
                dense
                size="sm"
                @click="viewAttachment(row as unknown as CategoriaCNH)"
                :disable="!row.anexo"
            >
                <q-tooltip>{{ $t('buttons.viewAttachment') }}</q-tooltip>
            </q-btn>
            <q-btn
                :icon="row.ativo ? 'visibility_off' : 'visibility'"
                :color="row.ativo ? 'negative' : 'positive'"
                size="sm"
                flat
                round
                @click="toggleItemStatus(row as CategoriaCNH)"
            >
                <q-tooltip>
                    {{
                        row.ativo
                            ? $t('buttons.deactivate')
                            : $t('buttons.activate')
                    }}
                </q-tooltip>
            </q-btn>
        </template>

        <!-- Conteúdo do modal -->
        <template #modal-content>
            <FormCategoriaCNH
                ref="formRef"
                :initial-values="formInitialValues"
            />
        </template>

        <!-- Modais extras -->
        <template #extra-modals>
            <!-- Image Viewer -->
            <image-viewer
                v-model="showImageViewer"
                :image-url="currentImageUrl"
                :title="currentImageTitle"
            />
        </template>
    </crud-template>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Dialog, Notify } from 'quasar'
import CrudTemplate from '@/components/CrudTemplate.vue'
import FormCategoriaCNH from '@/components/forms/CadastroGeral/FormCategoriaCNH.vue'
import ImageViewer from '@/components/ImageViewer.vue'
import { useAuthStore } from '@/stores/auth'
import { useLoadingOverlay } from '@/composables/loadingOverlay'
import { useActionPermissions } from '@/composables/useActionPermissions'
import type { TableColumn, ActionItem } from '@/types/CrudTemplate'

// Interfaces
import type { CategoriaCNH } from '@/interfaces/CategoriaCNH'

const { t: $t } = useI18n()
const { setLoading } = useLoadingOverlay()
const { cadastroGeralTCategoriasCNHPermissions } = useActionPermissions()
const permissions = cadastroGeralTCategoriasCNHPermissions

// Types

interface TableRequestProps {
    pagination: {
        sortBy: string
        descending: boolean
        page: number
        rowsPerPage: number
        rowsNumber?: number
    }
    filter?:
        | {
              search: string
              tipo: number | null
              ativo: boolean | null
          }
        | undefined
    getCellValue?: (col: unknown, row: unknown) => unknown
}

// Stores
const authStore = useAuthStore()

// Composables

// Refs
const categoriasCNH = ref<CategoriaCNH[]>([])
const selected = ref<CategoriaCNH[]>([])
const loading = ref(false)
const saving = ref(false)
const showModal = ref(false)
const isEditing = ref(false)
const formRef = ref<InstanceType<typeof FormCategoriaCNH> | null>(null)
const currentItem = ref<CategoriaCNH | null>(null)

// Filtros
const columnFilter = ref<{
    column: string
    content: string | number | boolean
} | null>(null)

// Action Collection
const toolbarActions = computed<ActionItem[]>(() => [
    {
        label: $t('buttons.editSelected'),
        active: selected.value.length === 1,
        icon: 'edit',
        description: $t('buttons.editSelectedDescription'),
        action: () => {
            if (selected.value.length === 1 && selected.value[0]) {
                openEditModal(selected.value[0])
            }
        }
    },
    {
        label: $t('buttons.viewAttachment'),
        active:
            selected.value.length === 1 && Boolean(selected.value[0]?.anexo),
        icon: 'visibility',
        description: $t('buttons.viewAttachmentDescription'),
        action: () => {
            if (selected.value.length === 1 && selected.value[0]) {
                viewAttachment(selected.value[0])
            }
        }
    },
    {
        label:
            selected.value.length === 1 && selected.value[0]?.ativo
                ? $t('buttons.deactivate')
                : $t('buttons.activate'),
        active: selected.value.length === 1,
        icon:
            selected.value.length === 1 && selected.value[0]?.ativo
                ? 'visibility_off'
                : 'visibility',
        description:
            selected.value.length === 1 && selected.value[0]?.ativo
                ? $t('buttons.deactivateDescription')
                : $t('buttons.activateDescription'),
        action: () => {
            if (selected.value.length === 1 && selected.value[0]) {
                toggleItemStatus(selected.value[0])
            }
        }
    },
    {
        label: $t('buttons.deleteSelected', {
            count: selected.value.length
        }),
        active: selected.value.length > 0,
        icon: 'delete',
        description: $t('buttons.deleteSelectedDescription'),
        action: confirmDeleteMultiple
    }
])

// Paginação
const pagination = ref({
    sortBy: 'id',
    descending: false,
    page: 1,
    rowsPerPage: 25,
    rowsNumber: 0
})

// Computed
const filterableColumns = computed(() => [
    {
        label: $t('forms.labels.id'),
        value: 'id',
        field: 'id',
        type: 'number' as const
    },
    {
        label: $t('forms.labels.code'),
        value: 'codigo',
        field: 'codigo',
        type: 'text' as const
    },
    {
        label: $t('forms.labels.description'),
        value: 'descricao',
        field: 'descricao',
        type: 'text' as const
    },
    {
        label: $t('forms.labels.status'),
        value: 'ativo',
        field: 'ativo',
        type: 'boolean' as const
    }
])

const tableFilter = computed(() => {
    if (
        columnFilter.value &&
        columnFilter.value.column &&
        columnFilter.value.content !== null &&
        columnFilter.value.content !== undefined
    ) {
        try {
            // Converter valor para string para compatibilidade com q-table
            const content = columnFilter.value.content
            const stringValue =
                typeof content === 'string' ? content : String(content)

            // Só retornar filtro se o valor for válido
            if (
                stringValue &&
                stringValue.trim() !== '' &&
                stringValue !== 'undefined' &&
                stringValue !== 'null'
            ) {
                return {
                    [columnFilter.value.column]: stringValue
                }
            }
        } catch {
            // Ignorar erro e retornar filtro vazio
        }
    }
    return {}
})

const formInitialValues = computed(() => {
    if (isEditing.value && currentItem.value) {
        return {
            codigo: currentItem.value.codigo,
            descricao: currentItem.value.descricao,
            ativo: currentItem.value.ativo,
            anexo: currentItem.value.anexo,
            anexo_base64: currentItem.value.anexo_base64
        }
    }
    return {}
})

const modalTitle = computed(() => {
    return isEditing.value
        ? $t('pages.categoriaCNH.editTitle')
        : $t('pages.categoriaCNH.createTitle')
})

const clearForm = () => {
    if (formRef.value) {
        formRef.value.resetForm()
    }
}

// Colunas da tabela
const columns: TableColumn[] = [
    {
        name: 'id',
        label: $t('forms.labels.id'),
        field: 'id',
        align: 'left' as const,
        sortable: true,
        order: 1
    },
    {
        name: 'codigo',
        label: $t('forms.labels.code'),
        field: 'codigo',
        align: 'left' as const,
        sortable: true,
        order: 2,
        inlineEdit: permissions.value.canEdit,
        fieldType: 'text'
    },
    {
        name: 'descricao',
        label: $t('forms.labels.description'),
        field: 'descricao',
        align: 'left' as const,
        sortable: true,
        order: 3,
        inlineEdit: permissions.value.canEdit,
        fieldType: 'text'
    },
    {
        name: 'ativo',
        label: $t('forms.labels.status'),
        field: 'ativo',
        align: 'center' as const,
        sortable: true,
        order: 4
    },
    {
        name: 'actions',
        label: $t('forms.labels.actions'),
        field: 'actions',
        align: 'center' as const,
        sortable: false,
        order: 5
    }
]

// Image Viewer
const showImageViewer = ref(false)
const currentImageUrl = ref('')
const currentImageTitle = ref('')
const viewAttachment = (item: CategoriaCNH) => {
    if (!item.anexo) return

    currentImageUrl.value = item.anexo
    currentImageTitle.value = `${item.codigo} - ${item.descricao}`
    showImageViewer.value = true
}

// Métodos
const loadCategoriasCNH = async (
    props: Partial<TableRequestProps> | null = null
) => {
    await setLoading(async () => {
        loading.value = true

        const requestPagination = props?.pagination || pagination.value
        const filter = props?.filter || tableFilter.value

        // Construir parâmetros da requisição
        const params: Record<string, unknown> = {
            page: requestPagination.page,
            page_size: requestPagination.rowsPerPage
        }

        // Aplicar filtro de coluna se existir
        if (filter && Object.keys(filter).length > 0) {
            Object.entries(filter).forEach(([key, value]) => {
                if (value !== null && value !== undefined && value !== '') {
                    params[key] = value
                }
            })
        }

        if (requestPagination.sortBy) {
            const sortOrder = requestPagination.descending ? '-' : ''
            params.ordering = `${sortOrder}${requestPagination.sortBy}`
        }

        const response = await authStore.asyncRequest({
            endpoint: '/api/corpsystem/produto-service/categoria-cnh',
            method: 'get',
            params,
            customErrorMessages: {
                400: $t('pages.categoriaCNH.errors.loadError'),
                404: $t('pages.categoriaCNH.errors.loadError'),
                500: $t('pages.categoriaCNH.errors.loadError')
            }
        })

        const data = response.data

        categoriasCNH.value = data.results
        pagination.value = {
            ...requestPagination,
            rowsNumber: data.count
        }

        loading.value = false
    })
}

const onTableRequest = (requestProps: Record<string, unknown>) => {
    // Converter o requestProps para o formato esperado
    const props: Partial<TableRequestProps> = {}

    if (requestProps.pagination) {
        props.pagination =
            requestProps.pagination as TableRequestProps['pagination']
    }

    if (requestProps.filter) {
        props.filter = requestProps.filter as TableRequestProps['filter']
    }

    loadCategoriasCNH(props)
}

const onColumnFilter = (
    filterValue: { column: string; content: string | number | boolean } | null
) => {
    columnFilter.value = filterValue
    pagination.value.page = 1
    loadCategoriasCNH()
}

const onRestorePagination = (savedPagination: Record<string, unknown>) => {
    // Restaurar a paginação salva, mas sempre começar na página 1
    pagination.value = {
        ...pagination.value,
        ...savedPagination,
        page: 1 // Sempre começar na página 1
    }
}

// Modal methods
const openCreateModal = () => {
    isEditing.value = false
    currentItem.value = null
    showModal.value = true
}

const openEditModal = (item: CategoriaCNH) => {
    try {
        setLoading(async () => {
            // Fazer requisição para obter os dados completos do item
            const fullItemData = await getCategoriaCNHById(item.id)

            // Atualizar o item atual com os dados completos da API
            currentItem.value = fullItemData
            isEditing.value = true
            showModal.value = true
        })
    } catch {
        currentItem.value = item
        isEditing.value = true
        showModal.value = true
    }
}

const closeModal = () => {
    showModal.value = false
    isEditing.value = false
    currentItem.value = null
}

const saveCategoriaCNH = async () => {
    if (!formRef.value) return

    const { valid } = await formRef.value.validate()

    if (!valid) {
        Notify.create({
            message: $t('forms.errors.validationError'),
            color: 'negative',
            position: 'top',
            timeout: 3000,
            icon: 'error'
        })
        return
    }

    try {
        saving.value = true

        await setLoading(async () => {
            if (!formRef.value) return
            const formData = formRef.value.getData()

            if (isEditing.value) {
                await updateCategoriaCNH(currentItem.value!.id, formData)
            } else {
                await createCategoriaCNH(formData)
            }

            closeModal()
            loadCategoriasCNH()

            Notify.create({
                message: isEditing.value
                    ? $t('pages.categoriaCNH.messages.updateSuccess')
                    : $t('pages.categoriaCNH.messages.createSuccess'),
                color: 'positive',
                position: 'top',
                timeout: 3000,
                icon: 'check_circle'
            })
        })
    } catch (error) {
        // Erro já é tratado pelas funções CRUD com notificações
        // Apenas garantir que o loading seja removido
        void error
    } finally {
        saving.value = false
    }
}

// CRUD methods
const getCategoriaCNHById = async (id: number) => {
    const response = await authStore.asyncRequest({
        endpoint: `/api/corpsystem/produto-service/categoria-cnh/${id}`,
        method: 'get',
        customErrorMessages: {
            404: $t('pages.categoriaCNH.errors.notFound'),
            500: $t('pages.categoriaCNH.errors.loadError')
        }
    })
    return response.data
}

const createCategoriaCNH = async (data: Record<string, unknown>) => {
    const formData = new FormData()

    formData.append('codigo', data.codigo as string)
    formData.append('descricao', data.descricao as string)
    formData.append('ativo', String(data.ativo))

    if (data.anexo && data.anexo instanceof File) {
        formData.append('anexo', data.anexo)
    }

    const response = await authStore.asyncRequest({
        endpoint: '/api/corpsystem/produto-service/categoria-cnh/create',
        method: 'post',
        params: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        customErrorMessages: {
            400: $t('pages.categoriaCNH.errors.saveError'),
            500: $t('pages.categoriaCNH.errors.saveError')
        }
    })
    return response.data
}

const updateCategoriaCNH = async (
    id: number,
    data: Record<string, unknown>
) => {
    const formData = new FormData()

    formData.append('codigo', data.codigo as string)
    formData.append('descricao', data.descricao as string)
    formData.append('ativo', String(data.ativo))

    if (data.anexo && data.anexo instanceof File) {
        formData.append('anexo', data.anexo)
    }

    const response = await authStore.asyncRequest({
        endpoint: `/api/corpsystem/produto-service/categoria-cnh/update/${id}`,
        method: 'put',
        params: formData,
        customErrorMessages: {
            400: $t('pages.categoriaCNH.errors.saveError'),
            404: $t('pages.categoriaCNH.errors.saveError'),
            500: $t('pages.categoriaCNH.errors.saveError')
        }
    })
    return response.data
}

const deleteCategoriaCNH = async (id: number) => {
    await authStore.asyncRequest({
        endpoint: `/api/corpsystem/produto-service/categoria-cnh/${id}`,
        method: 'delete',
        customErrorMessages: {
            400: $t('pages.categoriaCNH.errors.deleteError'),
            404: $t('pages.categoriaCNH.errors.deleteError'),
            500: $t('pages.categoriaCNH.errors.deleteError')
        }
    })
}

const deleteMultipleCategoriasCNH = async (ids: number[]) => {
    // Fazer múltiplas chamadas individuais para cada ID
    const deletePromises = ids.map(id => deleteCategoriaCNH(id))
    await Promise.all(deletePromises)
}

// Toggle status method
const toggleItemStatus = async (item: CategoriaCNH) => {
    const newStatus = !item.ativo
    const actionText = newStatus
        ? $t('buttons.activate')
        : $t('buttons.deactivate')

    Dialog.create({
        title: $t('dialogs.confirmStatusChange.title'),
        message: $t('dialogs.confirmStatusChange.message', {
            item: item.descricao,
            action: actionText.toLowerCase()
        }),
        cancel: true,
        persistent: true,
        ok: {
            label: actionText,
            color: newStatus ? 'positive' : 'negative'
        }
    }).onOk(async () => {
        await setLoading(async () => {
            saving.value = true

            try {
                // Preparar dados para atualização
                const updateData = {
                    descricao: item.descricao,
                    codigo: item.codigo,
                    ativo: newStatus
                }

                await updateCategoriaCNH(item.id, updateData)

                // Atualizar o item na lista local
                const index = categoriasCNH.value.findIndex(
                    c => c.id === item.id
                )
                if (index !== -1 && categoriasCNH.value[index]) {
                    categoriasCNH.value[index]!.ativo = newStatus
                }

                // Limpar seleção
                selected.value = []

                Notify.create({
                    message: newStatus
                        ? $t('pages.categoriaCNH.messages.activateSuccess')
                        : $t('pages.categoriaCNH.messages.deactivateSuccess'),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } catch {
                // Error handling is done by asyncRequest
            } finally {
                saving.value = false
            }
        })
    })
}

// Delete methods
const confirmDelete = (item: CategoriaCNH) => {
    Dialog.create({
        title: $t('dialogs.confirmDelete.title'),
        message: $t('dialogs.confirmDelete.message', { item: item.descricao }),
        cancel: true,
        persistent: true,
        ok: {
            label: $t('buttons.delete'),
            color: 'negative'
        }
    }).onOk(async () => {
        await setLoading(async () => {
            try {
                await deleteCategoriaCNH(item.id)
                loadCategoriasCNH()

                Notify.create({
                    message: $t('pages.categoriaCNH.messages.deleteSuccess'),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } catch {
                Notify.create({
                    message: $t('pages.categoriaCNH.errors.deleteError'),
                    color: 'negative',
                    position: 'top',
                    timeout: 3000,
                    icon: 'error'
                })
            }
        })
    })
}

const confirmDeleteMultiple = () => {
    Dialog.create({
        title: $t('dialogs.confirmDeleteMultiple.title'),
        message: $t('dialogs.confirmDeleteMultiple.message', {
            count: selected.value.length
        }),
        cancel: true,
        persistent: true,
        ok: {
            label: $t('buttons.delete'),
            color: 'negative'
        }
    }).onOk(async () => {
        await setLoading(async () => {
            try {
                const ids = selected.value.map(item => item.id)
                await deleteMultipleCategoriasCNH(ids)
                selected.value = []
                loadCategoriasCNH()

                Notify.create({
                    message: $t(
                        'pages.categoriaCNH.messages.deleteMultipleSuccess'
                    ),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } catch {
                Notify.create({
                    message: $t(
                        'pages.categoriaCNH.errors.deleteMultipleError'
                    ),
                    color: 'negative',
                    position: 'top',
                    timeout: 3000,
                    icon: 'error'
                })
            }
        })
    })
}

// Inline edit handler
const handleInlineEdit = async (data: {
    row: Record<string, unknown>
    column: string
    value: unknown
    oldValue: unknown
}) => {
    try {
        const CategoriaCNHItem = data.row as unknown as CategoriaCNH
        const updateData = {
            descricao: CategoriaCNHItem.descricao,
            codigo: CategoriaCNHItem.codigo,
            ativo: CategoriaCNHItem.ativo,
            [data.column]: data.value
        }

        // Fazer a chamada para a API
        await updateCategoriaCNH(CategoriaCNHItem.id, updateData)

        // Atualizar os dados locais
        const index = categoriasCNH.value.findIndex(b => b.id === CategoriaCNHItem.id)
        if (index !== -1) {
            const currentItem = categoriasCNH.value[index]
            if (currentItem) {
                const updatedItem: CategoriaCNH = {
                    id: currentItem.id,
                    codigo: currentItem.codigo,
                    descricao: currentItem.descricao,
                    ativo: currentItem.ativo
                }

                if (data.column === 'descricao') {
                    updatedItem.descricao = data.value as string
                }

                categoriasCNH.value[index] = updatedItem
            }
        }

        Notify.create({
            message: $t('pages.categoriaCNH.messages.updateSuccess'),
            color: 'positive',
            position: 'top',
            timeout: 2000,
            icon: 'check_circle'
        })
    } catch {
        Notify.create({
            message: $t('pages.categoriaCNH.errors.updateError'),
            color: 'negative',
            position: 'top',
            timeout: 3000,
            icon: 'error'
        })

        // Recarregar dados para reverter a mudança visual
        loadCategoriasCNH()
    }
}

// Lifecycle
onMounted(() => {
    loadCategoriasCNH()
})
</script>

<style scoped>
.border-bottom {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Estilos para toolbar compacto */
.toolbar-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    flex-wrap: wrap;
}

.toolbar-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
}

/* Responsividade para telas menores */
@media (max-width: 768px) {
    .toolbar-container {
        justify-content: center;
        gap: 6px;
    }

    .toolbar-buttons {
        justify-content: center;
        gap: 6px;
    }
}
</style>
