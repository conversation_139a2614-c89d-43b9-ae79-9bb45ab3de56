<template>
    <crud-template
        table-name="cities"
        :columns="columns"
        :rows="cities"
        :loading="loading"
        :filter="tableFilter"
        :pagination="pagination"
        :toolbar-actions="toolbarActions"
        :selected-items="selected"
        :show-modal="showModal"
        :modal-title="modalTitle"
        :modal-saving="saving"
        :filter-columns="filterableColumns"
        v-model:quick-filter-value="columnFilter"
        @add-clicked="openCreateModal"
        @refresh-clicked="loadCities"
        @table-request="onTableRequest"
        @restore-pagination="onRestorePagination"
        @quick-filter="onColumnFilter"
        @edit-clicked="row => openEditModal(row as unknown as Cida<PERSON>)"
        @delete-clicked="row => confirmDelete(row as unknown as Cidade)"
        @update:show-modal="showModal = $event"
        @modal-close="closeModal"
        @modal-cancel="closeModal"
        @modal-save="saveCity"
        @update:selected-items="
            items => (selected = items as unknown as Cidade[])
        "
        @columns-changed="onColumnsChanged"
    >
        <!-- Conteúdo do modal -->
        <template #modal-content>
            <form-cidade ref="formRef" :initial-values="formInitialValues" />
        </template>
    </crud-template>

    <!-- Modal de Relatório -->
    <report-modal
        v-model="showReportModal"
        :table-title="$t('pages.cities.title')"
        :visible-columns-count="visibleColumnsCount"
        :has-filters="hasActiveFilters"
        :applied-filters="appliedFilters"
        :available-columns="availableColumns"
        :loading="reportLoading"
        @generate="handleGenerateReport"
    />
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import { Dialog, Notify } from 'quasar'
import CrudTemplate from '@/components/CrudTemplate.vue'
import FormCidade from '@/components/forms/CadastroGeral/FormCidade.vue'
import ReportModal from '@/components/ReportModal.vue'
import { useAuthStore } from '@/stores/auth'
import { useLoadingOverlay } from '@/composables/loadingOverlay'
import { useReports } from '@/composables/useReports'
import type { TableColumn, ActionItem } from '@/types/CrudTemplate'
import type { ReportSelection } from '@/components/ReportModal.vue'

// Types
interface Cidade {
    id: number
    descricao: string
    aliq_iss: string
    ibge: string
    id_estado: number
    estado_descricao?: string
}

interface TableRequestProps {
    pagination: {
        page: number
        rowsPerPage: number
        sortBy?: string
        descending?: boolean
        rowsNumber?: number
    }
    filter?: Record<string, unknown>
}

// Internationalization
const { t: $t } = useI18n()
const route = useRoute()
const { setLoading } = useLoadingOverlay()

// Stores
const authStore = useAuthStore()

// Composables
const { generateReport, loading: reportLoading } = useReports()

// Reactive data
const cities = ref<Cidade[]>([])
const selected = ref<Cidade[]>([])
const loading = ref(false)
const saving = ref(false)
const showModal = ref(false)
const showReportModal = ref(false)
const isEditing = ref(false)
const currentItem = ref<Cidade | null>(null)

// Form reference
const formRef = ref<InstanceType<typeof FormCidade> | null>(null)

// Table configuration
const columnFilter = ref<{
    column: string
    content: string | number | boolean
} | null>(null)

const tableFilter = computed(() => {
    if (
        columnFilter.value &&
        columnFilter.value.column &&
        columnFilter.value.content !== null &&
        columnFilter.value.content !== undefined
    ) {
        try {
            // Converter valor para string para compatibilidade com q-table
            const content = columnFilter.value.content
            const stringValue =
                typeof content === 'string' ? content : String(content)

            // Só retornar filtro se o valor for válido
            if (
                stringValue !== '' &&
                stringValue.trim() !== '' &&
                stringValue !== 'undefined' &&
                stringValue !== 'null'
            ) {
                return {
                    [columnFilter.value.column]: stringValue
                }
            }
        } catch {
            // Ignorar erro e retornar filtro vazio
        }
    }
    return {}
})

const pagination = ref({
    page: 1,
    rowsPerPage: 25,
    sortBy: 'id',
    descending: false,
    rowsNumber: 0
})

// Action Collection
const toolbarActions = computed<ActionItem[]>(() => {
    const selectedCount = selected.value.length
    return [
        {
            label: $t('buttons.editSelected'),
            active: selectedCount === 1,
            icon: 'edit',
            description: $t('buttons.editSelectedDescription'),
            action: () => {
                if (selectedCount === 1 && selected.value[0]) {
                    openEditModal(selected.value[0])
                }
            }
        },
        {
            label: $t('buttons.deleteSelected', { count: selectedCount }),
            active: selectedCount > 0,
            icon: 'delete',
            description: $t('buttons.deleteSelectedDescription'),
            action: confirmDeleteMultiple
        },
        {
            label: $t('buttons.generateReport'),
            active: true,
            icon: 'assessment',
            description: $t('buttons.generateReportDescription'),
            action: () => {
                showReportModal.value = true
            }
        }
    ]
})

// Computed properties
const filterableColumns = computed(() => [
    {
        label: $t('forms.labels.description'),
        value: 'descricao',
        field: 'descricao',
        type: 'text' as const
    },
    {
        label: $t('forms.labels.state'),
        value: 'estado_descricao',
        field: 'estado_descricao',
        type: 'text' as const
    },
    {
        label: $t('forms.labels.ibgeCode'),
        value: 'ibge',
        field: 'ibge',
        type: 'text' as const
    },
    {
        label: $t('forms.labels.iss'),
        value: 'aliq_iss',
        field: 'aliq_iss',
        type: 'text' as const
    }
])

const formInitialValues = computed(() => {
    if (isEditing.value && currentItem.value) {
        return {
            descricao: currentItem.value.descricao,
            aliq_iss: currentItem.value.aliq_iss,
            ibge: currentItem.value.ibge,
            id_estado: currentItem.value.id_estado
        }
    }
    return {}
})

const modalTitle = computed(() => {
    return isEditing.value
        ? $t('pages.cities.editTitle')
        : $t('pages.cities.createTitle')
})

// Colunas da tabela
const columns: TableColumn[] = [
    {
        name: 'id',
        label: $t('forms.labels.id'),
        field: 'id',
        align: 'left' as const,
        sortable: true,
        order: 1
    },
    {
        name: 'descricao',
        label: $t('forms.labels.description'),
        field: 'descricao',
        align: 'left' as const,
        sortable: true,
        order: 2
    },
    {
        name: 'estado_descricao',
        label: $t('forms.labels.state'),
        field: 'estado_descricao',
        align: 'left' as const,
        sortable: true,
        order: 3
    },
    {
        name: 'ibge',
        label: $t('forms.labels.ibgeCode'),
        field: 'ibge',
        align: 'left' as const,
        sortable: true,
        order: 4
    },
    {
        name: 'iss',
        label: $t('forms.labels.iss'),
        field: 'aliq_iss',
        align: 'left' as const,
        sortable: true,
        order: 5
    },
    {
        name: 'actions',
        label: $t('forms.labels.actions'),
        field: 'actions',
        align: 'center' as const,
        sortable: false,
        order: 6
    }
]

// Função para obter colunas visíveis do localStorage
const getVisibleColumnsFromStorage = (): string[] => {
    try {
        const TABLES_CONFIG_KEY = 'dataTables_configurations'
        const routeName =
            route.path.replace(/\//g, '-').replace(/^-/, '') || 'default'
        const tableKey = `table-cities-${routeName}`

        const allConfigs = localStorage.getItem(TABLES_CONFIG_KEY)
        if (allConfigs) {
            const configurations = JSON.parse(allConfigs)
            const config = configurations[tableKey]

            if (config && config.columns) {
                return config.columns
                    .filter(
                        (col: { visible?: boolean; name: string }) =>
                            col.visible !== false && col.name !== 'actions'
                    )
                    .map((col: { name: string }) => col.name)
            }
        }
    } catch {
        // Ignorar erros de localStorage
    }
    return []
}

// Estado reativo para contador de colunas
const visibleColumnsCount = ref(0)

// Inicializar contador de colunas
const initializeColumnsCount = () => {
    const savedColumns = getVisibleColumnsFromStorage()
    visibleColumnsCount.value =
        savedColumns.length > 0
            ? savedColumns.length
            : columns.filter(col => col.name !== 'actions').length
}

// Listener para mudanças nas colunas
const onColumnsChanged = (changedColumns: TableColumn[]) => {
    // Filtrar colunas de ações e contar apenas as visíveis
    const visibleColumns = changedColumns.filter(col => col.name !== 'actions')
    visibleColumnsCount.value = visibleColumns.length
}

const hasActiveFilters = computed(() => {
    return Object.keys(tableFilter.value).length > 0
})

const appliedFilters = computed(() => {
    return tableFilter.value
})

const availableColumns = computed(() => {
    return columns
        .filter(col => col.name !== 'actions')
        .map(col => ({
            name: col.name,
            label: col.label
        }))
})

// Métodos
const loadCities = async (props: Partial<TableRequestProps> | null = null) => {
    await setLoading(async () => {
        try {
            loading.value = true

            // Definir valores padrão seguros
            const defaultPagination = {
                page: 1,
                rowsPerPage: 25,
                sortBy: 'id',
                descending: false,
                rowsNumber: 0
            }

            const requestPagination =
                props?.pagination || pagination.value || defaultPagination
            const filter = props?.filter || tableFilter.value

            // Construir parâmetros da requisição
            const params: Record<string, unknown> = {
                page: requestPagination.page,
                page_size: requestPagination.rowsPerPage
            }

            // Aplicar filtro de coluna se existir
            if (filter && Object.keys(filter).length > 0) {
                Object.entries(filter).forEach(([key, value]) => {
                    if (value !== null && value !== undefined && value !== '') {
                        params[key] = value
                    }
                })
            }

            if (requestPagination?.sortBy) {
                const sortOrder = requestPagination.descending ? '-' : ''
                params.ordering = `${sortOrder}${requestPagination.sortBy}`
            }

            const response = await authStore.asyncRequest({
                endpoint: '/api/corpsystem/produto-service/cidade',
                method: 'get',
                params,
                customErrorMessages: {
                    400: $t('pages.cities.errors.loadError'),
                    404: $t('pages.cities.errors.loadError'),
                    500: $t('pages.cities.errors.loadError')
                }
            })

            const data = response.data

            cities.value = data.results || []
            pagination.value = {
                page: requestPagination.page,
                rowsPerPage: requestPagination.rowsPerPage,
                sortBy: requestPagination.sortBy || 'id',
                descending: requestPagination.descending || false,
                rowsNumber: data.count || 0
            }

            loading.value = false
        } catch {
            loading.value = false

            // Mostrar notificação de erro
            Notify.create({
                message: $t('pages.cities.errors.loadError'),
                color: 'negative',
                position: 'top',
                timeout: 5000,
                icon: 'error'
            })
        }
    })
}

const createCity = async (data: {
    descricao: string
    aliq_iss: string
    ibge: string
    id_estado: number
}) => {
    const response = await authStore.asyncRequest({
        endpoint: '/api/corpsystem/produto-service/cidade',
        method: 'post',
        params: data,
        customErrorMessages: {
            400: $t('pages.cities.errors.saveError'),
            500: $t('pages.cities.errors.saveError')
        }
    })
    return response.data
}

const updateCity = async (
    id: number,
    data: {
        descricao: string
        aliq_iss: string
        ibge: string
        id_estado: number
    }
) => {
    const response = await authStore.asyncRequest({
        endpoint: `/api/corpsystem/produto-service/cidade/${id}`,
        method: 'put',
        params: data,
        customErrorMessages: {
            400: $t('pages.cities.errors.saveError'),
            404: $t('pages.cities.errors.saveError'),
            500: $t('pages.cities.errors.saveError')
        }
    })
    return response.data
}

const deleteCity = async (id: number) => {
    await authStore.asyncRequest({
        endpoint: `/api/corpsystem/produto-service/cidade/${id}`,
        method: 'delete',
        customErrorMessages: {
            400: $t('pages.cities.errors.deleteError'),
            404: $t('pages.cities.errors.deleteError'),
            500: $t('pages.cities.errors.deleteError')
        }
    })
}

const deleteMultipleCities = async (ids: number[]) => {
    if (ids.length === 1) {
        const firstId = ids[0]
        if (firstId !== undefined) {
            await deleteCity(firstId)
        }
    } else if (ids.length > 1) {
        const idsString = ids.join(',')
        const endpoint = `/api/corpsystem/produto-service/cidade/bulk/?id=${encodeURIComponent(idsString)}`

        await authStore.asyncRequest({
            endpoint,
            method: 'delete',
            customErrorMessages: {
                400: $t('pages.cities.errors.deleteMultipleError'),
                404: $t('pages.cities.errors.deleteMultipleError'),
                500: $t('pages.cities.errors.deleteMultipleError')
            }
        })
    }
}

// Modal methods
const openCreateModal = () => {
    isEditing.value = false
    currentItem.value = null
    showModal.value = true
}

const openEditModal = (item: Cidade) => {
    isEditing.value = true
    currentItem.value = item
    showModal.value = true
}

const closeModal = () => {
    showModal.value = false
    isEditing.value = false
    currentItem.value = null
    selected.value = []
}

const saveCity = async () => {
    if (!formRef.value) return

    // Validar o formulário
    const { valid } = await formRef.value.validate()
    if (!valid) {
        Notify.create({
            message: $t('notifications.pleaseFixErrors'),
            color: 'negative',
            position: 'top',
            timeout: 3000,
            icon: 'error'
        })
        return
    }

    // Obter dados do formulário
    const formData = formRef.value.getData()

    await setLoading(async () => {
        try {
            saving.value = true

            if (isEditing.value) {
                await updateCity(currentItem.value!.id, formData)
            } else {
                await createCity(formData)
            }

            closeModal()
            loadCities()

            Notify.create({
                message: isEditing.value
                    ? $t('pages.cities.messages.updateSuccess')
                    : $t('pages.cities.messages.createSuccess'),
                color: 'positive',
                position: 'top',
                timeout: 3000,
                icon: 'check_circle'
            })
        } catch {
            // Error handling is done by asyncRequest
        } finally {
            saving.value = false
        }
    })
}

// Delete methods
const confirmDelete = (item: Cidade) => {
    Dialog.create({
        title: $t('dialogs.confirmDelete.title'),
        message: $t('dialogs.confirmDelete.message', { item: item.descricao }),
        cancel: true,
        persistent: true,
        ok: {
            label: $t('buttons.delete'),
            color: 'negative'
        }
    }).onOk(async () => {
        await setLoading(async () => {
            try {
                await deleteCity(item.id)
                loadCities()

                Notify.create({
                    message: $t('pages.cities.messages.deleteSuccess'),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } catch {
                // Error handling is done by asyncRequest
            }
        })
    })
}

const confirmDeleteMultiple = () => {
    Dialog.create({
        title: $t('dialogs.confirmDeleteMultiple.title'),
        message: $t('dialogs.confirmDeleteMultiple.message', {
            count: selected.value.length
        }),
        cancel: true,
        persistent: true,
        ok: {
            label: $t('buttons.delete'),
            color: 'negative'
        }
    }).onOk(async () => {
        await setLoading(async () => {
            try {
                const ids = selected.value.map(item => item.id)
                await deleteMultipleCities(ids)
                selected.value = []
                loadCities()

                Notify.create({
                    message: $t('pages.cities.messages.deleteMultipleSuccess'),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } catch {
                // Error handling is done by asyncRequest
            }
        })
    })
}

// Table event handlers
const onTableRequest = (event: Record<string, unknown>) => {
    const props = event as unknown as TableRequestProps
    loadCities(props)
}

const onRestorePagination = (savedPagination: Record<string, unknown>) => {
    // Restaurar a paginação salva, mas sempre começar na página 1
    pagination.value = {
        ...pagination.value,
        ...savedPagination,
        page: 1 // Sempre começar na página 1
    }
}

const onColumnFilter = (
    filterValue: { column: string; content: string | number | boolean } | null
) => {
    columnFilter.value = filterValue
    pagination.value.page = 1
    loadCities()
}

// Função para gerar relatório
const handleGenerateReport = async (selection: ReportSelection) => {
    try {
        // O useReports agora busca as colunas visíveis diretamente do localStorage
        const reportConfig = {
            title: $t('pages.cities.title'),
            endpoint: '/api/corpsystem/produto-service/cidade',
            tableName: 'cities', // Nome da tabela para buscar configuração no localStorage
            columns: columns,
            visibleColumns: [], // Será ignorado, useReports usa localStorage
            filters: tableFilter.value,
            sortBy: pagination.value.sortBy,
            descending: pagination.value.descending
        }

        await generateReport(reportConfig, selection)

        showReportModal.value = false

        Notify.create({
            message: $t('reports.success.generated'),
            color: 'positive',
            position: 'top',
            timeout: 3000,
            icon: 'check_circle'
        })
    } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Erro ao gerar relatório:', error)
        Notify.create({
            message: $t('reports.errors.generateExcel'),
            color: 'negative',
            position: 'top',
            timeout: 5000,
            icon: 'error'
        })
    }
}

// Lifecycle
onMounted(() => {
    loadCities()
    initializeColumnsCount() // Inicializar contador de colunas
})
</script>

<style scoped>
.border-bottom {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Estilos para toolbar compacto */
.toolbar-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    flex-wrap: wrap;
}

.toolbar-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
}

/* Responsividade para telas menores */
@media (max-width: 768px) {
    .toolbar-container {
        justify-content: center;
        gap: 6px;
    }

    .toolbar-buttons {
        justify-content: center;
        gap: 6px;
    }
}
</style>
