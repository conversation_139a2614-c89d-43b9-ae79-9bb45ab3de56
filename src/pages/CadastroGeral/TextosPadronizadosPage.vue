<template>
    <crud-template
        table-name="textos-padronizados"
        :columns="columns"
        :rows="standardizedTexts"
        :loading="loading"
        :filter="tableFilter"
        :pagination="pagination"
        :toolbar-actions="toolbarActions"
        :selected-items="selected"
        :show-modal="showModal"
        :modal-title="modalTitle"
        :modal-saving="saving"
        :filter-columns="filterableColumns"
        v-model:quick-filter-value="columnFilter"
        :show-add-button="permissions.canCreate"
        :show-edit-action="permissions.canEdit"
        :show-delete-action="permissions.canDelete"
        :show-clear-button="true"
        @add-clicked="openCreateModal"
        @refresh-clicked="loadStandardizedTexts"
        @table-request="onTableRequest"
        @restore-pagination="onRestorePagination"
        @quick-filter="onColumnFilter"
        @edit-clicked="row => openEditModal(row as unknown as StandardizedTexts)"
        @delete-clicked="
            row => confirmDelete(row as unknown as StandardizedTexts)
        "
        @inline-edit="handleInlineEdit"
        @update:show-modal="showModal = $event"
        @modal-close="closeModal"
        @modal-cancel="closeModal"
        @modal-clear="clearForm"
        @modal-save="saveTextoPadronizado"
        @update:selected-items="
            items => (selected = items as unknown as StandardizedTexts[])
        "
        :actions-per-row="3"
    >
        <!-- Ações extras na linha -->
        <template #row-extra-actions="{ row }">
            <q-btn
                :icon="row.ativo ? 'visibility_off' : 'visibility'"
                :color="row.ativo ? 'negative' : 'positive'"
                size="sm"
                flat
                round
                @click="toggleItemStatus(row as StandardizedTexts)"
            >
                <q-tooltip>
                    {{
                        row.ativo
                            ? $t('buttons.deactivate')
                            : $t('buttons.activate')
                    }}
                </q-tooltip>
            </q-btn>
        </template>

        <!-- Conteúdo do modal -->
        <template #modal-content>
            <form-texto-padronizado
                ref="formRef"
                :initial-values="formInitialValues"
            />
        </template>
    </crud-template>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Dialog, Notify } from 'quasar'
import CrudTemplate from '@/components/CrudTemplate.vue'
import FormTextoPadronizado from 'src/components/forms/CadastroGeral/FormTextoPadronizado.vue'
import { useAuthStore } from '@/stores/auth'
import { useLoadingOverlay } from '@/composables/loadingOverlay'
import { useActionPermissions } from '@/composables/useActionPermissions'
import type { TableColumn, ActionItem } from '@/types/CrudTemplate'

const { t: $t } = useI18n()
const { setLoading } = useLoadingOverlay()
const { cadastroGeralTextosPadronizadosPermissions } = useActionPermissions()
const permissions = cadastroGeralTextosPadronizadosPermissions

// Types
interface StandardizedTexts {
    id: number
    descricao: string
    tipo: string
    tipo_id: number
    obs?: string // Campo alternativo para conteúdo
    ativo: boolean
}

interface TableRequestProps {
    pagination: {
        sortBy: string
        descending: boolean
        page: number
        rowsPerPage: number
        rowsNumber?: number
    }
    filter?:
        | {
              search: string
              tipo: number | null
              ativo: boolean | null
          }
        | undefined
    getCellValue?: (col: unknown, row: unknown) => unknown
}

// Stores
const authStore = useAuthStore()

// Composables

// Refs
const standardizedTexts = ref<StandardizedTexts[]>([])
const selected = ref<StandardizedTexts[]>([])
const loading = ref(false)
const saving = ref(false)
const showModal = ref(false)
const isEditing = ref(false)
const formRef = ref<InstanceType<typeof FormTextoPadronizado> | null>(null)
const currentItem = ref<StandardizedTexts | null>(null)

// Filtros
const columnFilter = ref<{
    column: string
    content: string | number | boolean
} | null>(null)

// Action Collection
const toolbarActions = computed<ActionItem[]>(() => [
    {
        label: $t('buttons.editSelected'),
        active: selected.value.length === 1,
        icon: 'edit',
        description: $t('buttons.editSelectedDescription'),
        action: () => {
            if (selected.value.length === 1 && selected.value[0]) {
                openEditModal(selected.value[0])
            }
        }
    },
    {
        label:
            selected.value.length === 1 && selected.value[0]?.ativo
                ? $t('buttons.deactivate')
                : $t('buttons.activate'),
        active: selected.value.length === 1,
        icon:
            selected.value.length === 1 && selected.value[0]?.ativo
                ? 'visibility_off'
                : 'visibility',
        description:
            selected.value.length === 1 && selected.value[0]?.ativo
                ? $t('buttons.deactivateDescription')
                : $t('buttons.activateDescription'),
        action: () => {
            if (selected.value.length === 1 && selected.value[0]) {
                toggleItemStatus(selected.value[0])
            }
        }
    },
    {
        label: $t('buttons.deleteSelected', {
            count: selected.value.length
        }),
        active: selected.value.length > 0,
        icon: 'delete',
        description: $t('buttons.deleteSelectedDescription'),
        action: confirmDeleteMultiple
    }
])

// Paginação
const pagination = ref({
    sortBy: 'id',
    descending: false,
    page: 1,
    rowsPerPage: 25,
    rowsNumber: 0
})

// Computed
const filterableColumns = computed(() => [
    {
        label: $t('forms.labels.id'),
        value: 'id',
        field: 'id',
        type: 'number' as const
    },
    {
        label: $t('forms.labels.description'),
        value: 'descricao',
        field: 'descricao',
        type: 'text' as const
    },
    {
        label: $t('forms.labels.type'),
        value: 'tipo',
        field: 'tipo',
        type: 'text' as const
    },
    {
        label: $t('forms.labels.status'),
        value: 'ativo',
        field: 'ativo',
        type: 'boolean' as const
    }
])

const tableFilter = computed(() => {
    if (
        columnFilter.value &&
        columnFilter.value.column &&
        columnFilter.value.content !== null &&
        columnFilter.value.content !== undefined
    ) {
        try {
            // Converter valor para string para compatibilidade com q-table
            const content = columnFilter.value.content
            const stringValue =
                typeof content === 'string' ? content : String(content)

            // Só retornar filtro se o valor for válido
            if (
                stringValue &&
                stringValue.trim() !== '' &&
                stringValue !== 'undefined' &&
                stringValue !== 'null'
            ) {
                return {
                    [columnFilter.value.column]: stringValue
                }
            }
        } catch {
            // Ignorar erro e retornar filtro vazio
        }
    }
    return {}
})

const formInitialValues = computed(() => {
    if (isEditing.value && currentItem.value) {
        // Encontrar o ID do tipo baseado na string do tipo retornada pela API
        const tipoId = getTipoIdByTipoString(currentItem.value.tipo)

        return {
            descricao: currentItem.value.descricao,
            tipo: tipoId,
            conteudo: currentItem.value.obs, // Usar obs como conteudo
            ativo: currentItem.value.ativo
        }
    }
    return {}
})

const modalTitle = computed(() => {
    return isEditing.value
        ? $t('pages.standardizedTexts.editTitle')
        : $t('pages.standardizedTexts.createTitle')
})

const clearForm = () => {
    if (formRef.value) {
        formRef.value.resetForm()
    }
}

// Função para obter o ID do tipo baseado na string do tipo
const getTipoIdByTipoString = (tipoString: string): number | null => {
    // Mapear as strings de tipo para IDs baseado no composable useTextoPadronizado
    const tipoMap: Record<string, number> = {
        'TEXTO INICIAL': 1,
        'CONDIÇÃO COMERCIAL': 2,
        'INFORMAÇÃO ADICIONAL': 3,
        'TEXTO FINAL': 4,
        'TEXTO CONTRATO COMPRA-VENDA': 5,
        'TEXTO CONTRATO': 6
    }

    return tipoMap[tipoString] || null
}

// Colunas da tabela
const columns: TableColumn[] = [
    {
        name: 'id',
        label: $t('forms.labels.id'),
        field: 'id',
        align: 'left' as const,
        sortable: true,
        order: 1
    },
    {
        name: 'descricao',
        label: $t('forms.labels.description'),
        field: 'descricao',
        align: 'left' as const,
        sortable: true,
        order: 2,
        inlineEdit: permissions.value.canEdit,
        fieldType: 'text'
    },
    {
        name: 'tipo',
        label: $t('forms.labels.type'),
        field: 'tipo',
        align: 'left' as const,
        sortable: true,
        order: 3
    },
    {
        name: 'ativo',
        label: $t('forms.labels.status'),
        field: 'ativo',
        align: 'center' as const,
        sortable: true,
        order: 5
    },
    {
        name: 'actions',
        label: $t('forms.labels.actions'),
        field: 'actions',
        align: 'center' as const,
        sortable: false,
        order: 6
    }
]

// Métodos
const loadStandardizedTexts = async (
    props: Partial<TableRequestProps> | null = null
) => {
    await setLoading(async () => {
        loading.value = true

        const requestPagination = props?.pagination || pagination.value
        const filter = props?.filter || tableFilter.value

        // Construir parâmetros da requisição
        const params: Record<string, unknown> = {
            page: requestPagination.page,
            page_size: requestPagination.rowsPerPage
        }

        // Aplicar filtro de coluna se existir
        if (filter && Object.keys(filter).length > 0) {
            Object.entries(filter).forEach(([key, value]) => {
                if (value !== null && value !== undefined && value !== '') {
                    params[key] = value
                }
            })
        }

        if (requestPagination.sortBy) {
            const sortOrder = requestPagination.descending ? '-' : ''
            params.ordering = `${sortOrder}${requestPagination.sortBy}`
        }

        const response = await authStore.asyncRequest({
            endpoint: '/api/corpsystem/produto-service/texto-padronizado',
            method: 'get',
            params,
            customErrorMessages: {
                400: $t('pages.standardizedTexts.errors.loadError'),
                404: $t('pages.standardizedTexts.errors.loadError'),
                500: $t('pages.standardizedTexts.errors.loadError')
            }
        })

        const data = response.data

        standardizedTexts.value = data.results
        pagination.value = {
            ...requestPagination,
            rowsNumber: data.count
        }

        loading.value = false
    })
}

const onTableRequest = (requestProps: Record<string, unknown>) => {
    // Converter o requestProps para o formato esperado
    const props: Partial<TableRequestProps> = {}

    if (requestProps.pagination) {
        props.pagination =
            requestProps.pagination as TableRequestProps['pagination']
    }

    if (requestProps.filter) {
        props.filter = requestProps.filter as TableRequestProps['filter']
    }

    loadStandardizedTexts(props)
}

const onColumnFilter = (
    filterValue: { column: string; content: string | number | boolean } | null
) => {
    columnFilter.value = filterValue
    pagination.value.page = 1
    loadStandardizedTexts()
}

const onRestorePagination = (savedPagination: Record<string, unknown>) => {
    // Restaurar a paginação salva, mas sempre começar na página 1
    pagination.value = {
        ...pagination.value,
        ...savedPagination,
        page: 1 // Sempre começar na página 1
    }
}

// Modal methods
const openCreateModal = () => {
    isEditing.value = false
    currentItem.value = null
    showModal.value = true
}

const openEditModal = (item: StandardizedTexts) => {
    try {
        setLoading(async () => {
            // Fazer requisição para obter os dados completos do item
            const fullItemData = await getStandardizedTextsById(item.id)

            // Atualizar o item atual com os dados completos da API
            currentItem.value = fullItemData
            isEditing.value = true
            showModal.value = true
        })
    } catch {
        currentItem.value = item
        isEditing.value = true
        showModal.value = true
    }
}

const closeModal = () => {
    showModal.value = false
    isEditing.value = false
    currentItem.value = null
}

const saveTextoPadronizado = async () => {
    if (!formRef.value) return

    const { valid } = await formRef.value.validate()

    if (!valid) {
        Notify.create({
            message: $t('forms.errors.validationError'),
            color: 'negative',
            position: 'top',
            timeout: 3000,
            icon: 'error'
        })
        return
    }

    await setLoading(async () => {
        saving.value = true

        if (!formRef.value) return
        const formData = formRef.value.getData()

        if (isEditing.value) {
            await updateStandardizedTexts(currentItem.value!.id, formData)
        } else {
            await createStandardizedTexts(formData)
        }

        closeModal()
        loadStandardizedTexts()

        Notify.create({
            message: isEditing.value
                ? $t('pages.standardizedTexts.messages.updateSuccess')
                : $t('pages.standardizedTexts.messages.createSuccess'),
            color: 'positive',
            position: 'top',
            timeout: 3000,
            icon: 'check_circle'
        })

        saving.value = false
    })
}

// CRUD methods (preparados para implementação)
const getStandardizedTextsById = async (id: number) => {
    const response = await authStore.asyncRequest({
        endpoint: `/api/corpsystem/produto-service/texto-padronizado/${id}`,
        method: 'get',
        customErrorMessages: {
            404: $t('pages.standardizedTexts.errors.notFound'),
            500: $t('pages.standardizedTexts.errors.loadError')
        }
    })
    return response.data
}

const createStandardizedTexts = async (data: Record<string, unknown>) => {
    // Mapear os dados do formulário para o formato esperado pela API
    const payload = {
        descricao: data.descricao,
        tipo: data.tipo, // Label do tipo (string)
        obs: data.conteudo, // Mapear conteudo para obs
        ativo: data.ativo
    }

    const response = await authStore.asyncRequest({
        endpoint: '/api/corpsystem/produto-service/texto-padronizado',
        method: 'post',
        params: payload,
        customErrorMessages: {
            400: $t('pages.standardizedTexts.errors.saveError'),
            500: $t('pages.standardizedTexts.errors.saveError')
        }
    })
    return response.data
}

const updateStandardizedTexts = async (
    id: number,
    data: Record<string, unknown>
) => {
    // Mapear os dados do formulário para o formato esperado pela API
    const payload = {
        descricao: data.descricao,
        tipo: data.tipo, // Label do tipo (string)
        obs: data.conteudo, // Mapear conteudo para obs
        ativo: data.ativo
    }

    const response = await authStore.asyncRequest({
        endpoint: `/api/corpsystem/produto-service/texto-padronizado/${id}`,
        method: 'put',
        params: payload,
        customErrorMessages: {
            400: $t('pages.standardizedTexts.errors.saveError'),
            404: $t('pages.standardizedTexts.errors.saveError'),
            500: $t('pages.standardizedTexts.errors.saveError')
        }
    })
    return response.data
}

const deleteStandardizedTexts = async (id: number) => {
    // Para DELETE, precisamos enviar os parâmetros como query parameters
    const endpoint = `/api/corpsystem/produto-service/texto-padronizado/bulk/?id=${id}`

    await authStore.asyncRequest({
        endpoint,
        method: 'delete',
        customErrorMessages: {
            400: $t('pages.standardizedTexts.errors.deleteError'),
            404: $t('pages.standardizedTexts.errors.deleteError'),
            500: $t('pages.standardizedTexts.errors.deleteError')
        }
    })
}

const deleteMultiplesStandardizedTexts = async (ids: number[]) => {
    // Para exclusão múltipla, enviar IDs como string separada por vírgula
    // Formato: /bulk/?id=42,39 (conforme curl fornecido)

    if (ids.length === 1) {
        // Se for apenas um ID, usar o método individual
        const firstId = ids[0]
        if (firstId !== undefined) {
            await deleteStandardizedTexts(firstId)
        }
    } else if (ids.length > 1) {
        // Para múltiplos IDs, enviar como string separada por vírgula
        const idsString = ids.join(',')
        const endpoint = `/api/corpsystem/produto-service/texto-padronizado/bulk/?id=${encodeURIComponent(idsString)}`

        await authStore.asyncRequest({
            endpoint,
            method: 'delete',
            customErrorMessages: {
                400: $t('pages.standardizedTexts.errors.deleteMultipleError'),
                404: $t('pages.standardizedTexts.errors.deleteMultipleError'),
                500: $t('pages.standardizedTexts.errors.deleteMultipleError')
            }
        })
    }
}

// Toggle status method
const toggleItemStatus = async (item: StandardizedTexts) => {
    const newStatus = !item.ativo
    const actionText = newStatus
        ? $t('buttons.activate')
        : $t('buttons.deactivate')

    Dialog.create({
        title: $t('dialogs.confirmStatusChange.title'),
        message: $t('dialogs.confirmStatusChange.message', {
            item: item.descricao,
            action: actionText.toLowerCase()
        }),
        cancel: true,
        persistent: true,
        ok: {
            label: actionText,
            color: newStatus ? 'positive' : 'negative'
        }
    }).onOk(async () => {
        await setLoading(async () => {
            saving.value = true

            try {
                // Preparar dados para atualização
                const updateData = {
                    descricao: item.descricao,
                    tipo: getTipoIdByTipoString(item.tipo),
                    conteudo: item.obs,
                    ativo: newStatus
                }

                await updateStandardizedTexts(item.id, updateData)

                // Atualizar o item na lista local
                const index = standardizedTexts.value.findIndex(
                    t => t.id === item.id
                )
                if (index !== -1 && standardizedTexts.value[index]) {
                    standardizedTexts.value[index]!.ativo = newStatus
                }

                // Limpar seleção
                selected.value = []

                Notify.create({
                    message: newStatus
                        ? $t('pages.standardizedTexts.messages.activateSuccess')
                        : $t(
                              'pages.standardizedTexts.messages.deactivateSuccess'
                          ),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } catch {
                // Error handling is done by asyncRequest
            } finally {
                saving.value = false
            }
        })
    })
}

// Delete methods
const confirmDelete = (item: StandardizedTexts) => {
    Dialog.create({
        title: $t('dialogs.confirmDelete.title'),
        message: $t('dialogs.confirmDelete.message', { item: item.descricao }),
        cancel: true,
        persistent: true,
        ok: {
            label: $t('buttons.delete'),
            color: 'negative'
        }
    }).onOk(async () => {
        await setLoading(async () => {
            try {
                await deleteStandardizedTexts(item.id)
                loadStandardizedTexts()

                Notify.create({
                    message: $t(
                        'pages.standardizedTexts.messages.deleteSuccess'
                    ),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } catch {
                Notify.create({
                    message: $t('pages.standardizedTexts.errors.deleteError'),
                    color: 'negative',
                    position: 'top',
                    timeout: 3000,
                    icon: 'error'
                })
            }
        })
    })
}

const confirmDeleteMultiple = () => {
    Dialog.create({
        title: $t('dialogs.confirmDeleteMultiple.title'),
        message: $t('dialogs.confirmDeleteMultiple.message', {
            count: selected.value.length
        }),
        cancel: true,
        persistent: true,
        ok: {
            label: $t('buttons.delete'),
            color: 'negative'
        }
    }).onOk(async () => {
        await setLoading(async () => {
            try {
                const ids = selected.value.map(item => item.id)
                await deleteMultiplesStandardizedTexts(ids)
                selected.value = []
                loadStandardizedTexts()

                Notify.create({
                    message: $t(
                        'pages.standardizedTexts.messages.deleteMultipleSuccess'
                    ),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } catch {
                Notify.create({
                    message: $t(
                        'pages.standardizedTexts.errors.deleteMultipleError'
                    ),
                    color: 'negative',
                    position: 'top',
                    timeout: 3000,
                    icon: 'error'
                })
            }
        })
    })
}
// Inline edit handler
const handleInlineEdit = async (data: {
    row: Record<string, unknown>
    column: string
    value: unknown
    oldValue: unknown
}) => {
    try {
        const standardizedTextsItem = data.row as unknown as StandardizedTexts
        const updateData = {
            descricao: standardizedTextsItem.descricao,
            ativo: standardizedTextsItem.ativo,
            tipo: standardizedTextsItem.tipo,
            tipo_id: standardizedTextsItem.tipo_id,
            [data.column]: data.value
        }

        // Fazer a chamada para a API
        await updateStandardizedTexts(standardizedTextsItem.id, updateData)

        // Atualizar os dados locais
        const index = standardizedTexts.value.findIndex(b => b.id === standardizedTextsItem.id)
        if (index !== -1) {
            const currentItem = standardizedTexts.value[index]
            if (currentItem) {
                const updatedItem: StandardizedTexts = {
                    id: currentItem.id,
                    descricao: currentItem.descricao,
                    ativo: currentItem.ativo,
                    tipo: currentItem.tipo,
                    tipo_id: currentItem.tipo_id,
                }

                if (data.column === 'descricao') {
                    updatedItem.descricao = data.value as string
                }

                standardizedTexts.value[index] = updatedItem
            }
        }

        Notify.create({
            message: $t('pages.standardizedTexts.messages.updateSuccess'),
            color: 'positive',
            position: 'top',
            timeout: 2000,
            icon: 'check_circle'
        })
    } catch {
        Notify.create({
            message: $t('pages.standardizedTexts.errors.updateError'),
            color: 'negative',
            position: 'top',
            timeout: 3000,
            icon: 'error'
        })

        // Recarregar dados para reverter a mudança visual
        loadStandardizedTexts()
    }
}

// Lifecycle
onMounted(() => {
    loadStandardizedTexts()
})
</script>
