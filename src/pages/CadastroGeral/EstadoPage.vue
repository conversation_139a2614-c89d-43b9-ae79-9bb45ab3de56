<template>
    <crud-template
        table-name="states"
        :columns="columns"
        :rows="states"
        :loading="loading"
        :filter="tableFilter"
        :pagination="pagination"
        :toolbar-actions="toolbarActions"
        :selected-items="selected"
        :show-modal="showModal"
        :modal-title="modalTitle"
        :modal-saving="saving"
        :filter-columns="filterableColumns"
        v-model:quick-filter-value="columnFilter"
        @add-clicked="openCreateModal"
        @refresh-clicked="loadStates"
        @table-request="onTableRequest"
        @restore-pagination="onRestorePagination"
        @quick-filter="onColumnFilter"
        @edit-clicked="row => openEditModal(row as unknown as Estado)"
        @delete-clicked="row => confirmDelete(row as unknown as Estado)"
        @update:show-modal="showModal = $event"
        @modal-close="closeModal"
        @modal-cancel="closeModal"
        @modal-save="saveState"
        @update:selected-items="
            items => (selected = items as unknown as Estado[])
        "
    >
        <!-- <PERSON>te<PERSON><PERSON> do modal -->
        <template #modal-content>
            <form-estado ref="formRef" :initial-values="formInitialValues" />
        </template>
    </crud-template>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Dialog, Notify } from 'quasar'
import CrudTemplate from '@/components/CrudTemplate.vue'
import FormEstado from '@/components/forms/CadastroGeral/FormEstado.vue'
import { useAuthStore } from '@/stores/auth'
import { useLoadingOverlay } from '@/composables/loadingOverlay'
import type { TableColumn, ActionItem } from '@/types/CrudTemplate'

// Types
interface Estado {
    id: number
    descricao: string
    sigla: string
    ibge: string
    id_pais: number
    pais_descricao?: string
}

interface TableRequestProps {
    pagination: {
        page: number
        rowsPerPage: number
        sortBy?: string
        descending?: boolean
        rowsNumber?: number
    }
    filter?: Record<string, unknown>
}

// Internationalization
const { t: $t } = useI18n()
const { setLoading } = useLoadingOverlay()

// Stores
const authStore = useAuthStore()

// Composables

// Reactive data
const states = ref<Estado[]>([])
const selected = ref<Estado[]>([])
const loading = ref(false)
const saving = ref(false)
const showModal = ref(false)
const isEditing = ref(false)
const currentItem = ref<Estado | null>(null)

// Form reference
const formRef = ref<InstanceType<typeof FormEstado> | null>(null)

// Table configuration
const columnFilter = ref<{
    column: string
    content: string | number | boolean
} | null>(null)

const tableFilter = computed(() => {
    if (
        columnFilter.value &&
        columnFilter.value.column &&
        columnFilter.value.content !== null &&
        columnFilter.value.content !== undefined
    ) {
        try {
            // Converter valor para string para compatibilidade com q-table
            const content = columnFilter.value.content
            const stringValue =
                typeof content === 'string' ? content : String(content)

            // Só retornar filtro se o valor for válido
            if (
                stringValue !== '' &&
                stringValue.trim() !== '' &&
                stringValue !== 'undefined' &&
                stringValue !== 'null'
            ) {
                return {
                    [columnFilter.value.column]: stringValue
                }
            }
        } catch {
            // Ignorar erro e retornar filtro vazio
        }
    }
    return {}
})
const pagination = ref({
    page: 1,
    rowsPerPage: 25,
    sortBy: 'id',
    descending: false,
    rowsNumber: 0
})

// Action Collection
const toolbarActions = computed<ActionItem[]>(() => {
    const selectedCount = selected.value.length
    return [
        {
            label: $t('buttons.editSelected'),
            active: selectedCount === 1,
            icon: 'edit',
            description: $t('buttons.editSelectedDescription'),
            action: () => {
                if (selectedCount === 1 && selected.value[0]) {
                    openEditModal(selected.value[0])
                }
            }
        },
        {
            label: $t('buttons.deleteSelected', { count: selectedCount }),
            active: selectedCount > 0,
            icon: 'delete',
            description: $t('buttons.deleteSelectedDescription'),
            action: confirmDeleteMultiple
        }
    ]
})

// Computed properties
const filterableColumns = computed(() => [
    {
        label: $t('forms.labels.description'),
        value: 'descricao',
        field: 'descricao',
        type: 'text' as const
    },
    {
        label: $t('forms.labels.stateAbbreviation'),
        value: 'sigla',
        field: 'sigla',
        type: 'text' as const
    },
    {
        label: $t('forms.labels.ibgeCode'),
        value: 'ibge',
        field: 'ibge',
        type: 'text' as const
    },
    {
        label: $t('forms.labels.country'),
        value: 'pais_descricao',
        field: 'pais_descricao',
        type: 'text' as const
    }
])

const formInitialValues = computed(() => {
    if (isEditing.value && currentItem.value) {
        return {
            descricao: currentItem.value.descricao,
            sigla: currentItem.value.sigla,
            ibge: currentItem.value.ibge,
            id_pais: currentItem.value.id_pais
        }
    }
    return {}
})

const modalTitle = computed(() => {
    return isEditing.value
        ? $t('pages.states.editTitle')
        : $t('pages.states.createTitle')
})

// Colunas da tabela
const columns: TableColumn[] = [
    {
        name: 'id',
        label: $t('forms.labels.id'),
        field: 'id',
        align: 'left' as const,
        sortable: true,
        order: 1
    },
    {
        name: 'descricao',
        label: $t('forms.labels.description'),
        field: 'descricao',
        align: 'left' as const,
        sortable: true,
        order: 2
    },
    {
        name: 'sigla',
        label: $t('forms.labels.stateAbbreviation'),
        field: 'sigla',
        align: 'left' as const,
        sortable: true,
        order: 3
    },
    {
        name: 'ibge',
        label: $t('forms.labels.ibgeCode'),
        field: 'ibge',
        align: 'left' as const,
        sortable: true,
        order: 4
    },
    {
        name: 'pais_descricao',
        label: $t('forms.labels.country'),
        field: 'pais_descricao',
        align: 'left' as const,
        sortable: true,
        order: 5
    },
    {
        name: 'actions',
        label: $t('forms.labels.actions'),
        field: 'actions',
        align: 'center' as const,
        sortable: false,
        order: 6
    }
]

// Métodos
const loadStates = async (props: Partial<TableRequestProps> | null = null) => {
    await setLoading(async () => {
        try {
            loading.value = true

            // Definir valores padrão seguros
            const defaultPagination = {
                page: 1,
                rowsPerPage: 25,
                sortBy: 'id',
                descending: false,
                rowsNumber: 0
            }

            const requestPagination =
                props?.pagination || pagination.value || defaultPagination
            const filter = props?.filter || tableFilter.value

            // Construir parâmetros da requisição
            const params: Record<string, unknown> = {
                page: requestPagination.page,
                page_size: requestPagination.rowsPerPage
            }

            // Aplicar filtro de coluna se existir
            if (filter && Object.keys(filter).length > 0) {
                Object.entries(filter).forEach(([key, value]) => {
                    if (value !== null && value !== undefined && value !== '') {
                        params[key] = value
                    }
                })
            }

            if (requestPagination?.sortBy) {
                const sortOrder = requestPagination.descending ? '-' : ''
                params.ordering = `${sortOrder}${requestPagination.sortBy}`
            }

            const response = await authStore.asyncRequest({
                endpoint: '/api/corpsystem/produto-service/estado',
                method: 'get',
                params,
                customErrorMessages: {
                    400: $t('pages.states.errors.loadError'),
                    404: $t('pages.states.errors.loadError'),
                    500: $t('pages.states.errors.loadError')
                }
            })

            const data = response.data

            states.value = data.results || []
            pagination.value = {
                page: requestPagination.page,
                rowsPerPage: requestPagination.rowsPerPage,
                sortBy: requestPagination.sortBy || 'id',
                descending: requestPagination.descending || false,
                rowsNumber: data.count || 0
            }

            loading.value = false
        } catch {
            loading.value = false

            // Mostrar notificação de erro
            Notify.create({
                message: $t('pages.states.errors.loadError'),
                color: 'negative',
                position: 'top',
                timeout: 5000,
                icon: 'error'
            })
        }
    })
}

const createState = async (data: {
    descricao: string
    sigla: string
    ibge: string
    id_pais: number
}) => {
    const response = await authStore.asyncRequest({
        endpoint: '/api/corpsystem/produto-service/estado',
        method: 'post',
        params: data,
        customErrorMessages: {
            400: $t('pages.states.errors.saveError'),
            500: $t('pages.states.errors.saveError')
        }
    })
    return response.data
}

const updateState = async (
    id: number,
    data: { descricao: string; sigla: string; ibge: string; id_pais: number }
) => {
    const response = await authStore.asyncRequest({
        endpoint: `/api/corpsystem/produto-service/estado/${id}`,
        method: 'put',
        params: data,
        customErrorMessages: {
            400: $t('pages.states.errors.saveError'),
            404: $t('pages.states.errors.saveError'),
            500: $t('pages.states.errors.saveError')
        }
    })
    return response.data
}

const deleteState = async (id: number) => {
    await authStore.asyncRequest({
        endpoint: `/api/corpsystem/produto-service/estado/${id}`,
        method: 'delete',
        customErrorMessages: {
            400: $t('pages.states.errors.deleteError'),
            404: $t('pages.states.errors.deleteError'),
            500: $t('pages.states.errors.deleteError')
        }
    })
}

const deleteMultipleStates = async (ids: number[]) => {
    if (ids.length === 1) {
        const firstId = ids[0]
        if (firstId !== undefined) {
            await deleteState(firstId)
        }
    } else if (ids.length > 1) {
        const idsString = ids.join(',')
        const endpoint = `/api/corpsystem/produto-service/estado/bulk/?id=${encodeURIComponent(idsString)}`

        await authStore.asyncRequest({
            endpoint,
            method: 'delete',
            customErrorMessages: {
                400: $t('pages.states.errors.deleteMultipleError'),
                404: $t('pages.states.errors.deleteMultipleError'),
                500: $t('pages.states.errors.deleteMultipleError')
            }
        })
    }
}

// Modal methods
const openCreateModal = () => {
    isEditing.value = false
    currentItem.value = null
    showModal.value = true
}

const openEditModal = (item: Estado) => {
    isEditing.value = true
    currentItem.value = item
    showModal.value = true
}

const closeModal = () => {
    showModal.value = false
    isEditing.value = false
    currentItem.value = null
    selected.value = []
}

const saveState = async () => {
    if (!formRef.value) return

    // Validar o formulário
    const { valid } = await formRef.value.validate()
    if (!valid) {
        Notify.create({
            message: $t('notifications.pleaseFixErrors'),
            color: 'negative',
            position: 'top',
            timeout: 3000,
            icon: 'error'
        })
        return
    }

    // Obter dados do formulário
    const formData = formRef.value.getData()

    await setLoading(async () => {
        try {
            saving.value = true

            if (isEditing.value) {
                await updateState(currentItem.value!.id, formData)
            } else {
                await createState(formData)
            }

            closeModal()
            loadStates()

            Notify.create({
                message: isEditing.value
                    ? $t('pages.states.messages.updateSuccess')
                    : $t('pages.states.messages.createSuccess'),
                color: 'positive',
                position: 'top',
                timeout: 3000,
                icon: 'check_circle'
            })
        } catch {
            // Error handling is done by asyncRequest
        } finally {
            saving.value = false
        }
    })
}

// Delete methods
const confirmDelete = (item: Estado) => {
    Dialog.create({
        title: $t('dialogs.confirmDelete.title'),
        message: $t('dialogs.confirmDelete.message', { item: item.descricao }),
        cancel: true,
        persistent: true,
        ok: {
            label: $t('buttons.delete'),
            color: 'negative'
        }
    }).onOk(async () => {
        await setLoading(async () => {
            try {
                await deleteState(item.id)
                loadStates()

                Notify.create({
                    message: $t('pages.states.messages.deleteSuccess'),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } catch {
                // Error handling is done by asyncRequest
            }
        })
    })
}

const confirmDeleteMultiple = () => {
    Dialog.create({
        title: $t('dialogs.confirmDeleteMultiple.title'),
        message: $t('dialogs.confirmDeleteMultiple.message', {
            count: selected.value.length
        }),
        cancel: true,
        persistent: true,
        ok: {
            label: $t('buttons.delete'),
            color: 'negative'
        }
    }).onOk(async () => {
        await setLoading(async () => {
            try {
                const ids = selected.value.map(item => item.id)
                await deleteMultipleStates(ids)
                selected.value = []
                loadStates()

                Notify.create({
                    message: $t('pages.states.messages.deleteMultipleSuccess'),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } catch {
                // Error handling is done by asyncRequest
            }
        })
    })
}

// Table event handlers
const onTableRequest = (event: Record<string, unknown>) => {
    const props = event as unknown as TableRequestProps
    loadStates(props)
}

const onRestorePagination = (savedPagination: Record<string, unknown>) => {
    // Restaurar a paginação salva, mas sempre começar na página 1
    pagination.value = {
        ...pagination.value,
        ...savedPagination,
        page: 1 // Sempre começar na página 1
    }
}

const onColumnFilter = (
    filterValue: { column: string; content: string | number | boolean } | null
) => {
    columnFilter.value = filterValue
    pagination.value.page = 1
    loadStates()
}

// Lifecycle
onMounted(() => {
    loadStates()
})
</script>

<style scoped>
.border-bottom {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Estilos para toolbar compacto */
.toolbar-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    flex-wrap: wrap;
}

.toolbar-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
}

/* Responsividade para telas menores */
@media (max-width: 768px) {
    .toolbar-container {
        justify-content: center;
        gap: 6px;
    }

    .toolbar-buttons {
        justify-content: center;
        gap: 6px;
    }
}
</style>
