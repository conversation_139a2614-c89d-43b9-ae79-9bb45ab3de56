<template>
    <div id="ErrorNotFound" class="flex flex-center bg-primary">
        <q-card class="q-pa-lg text-center">
            <q-card-section>
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="100"
                    height="100"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    :class="isDarkMode ? 'text-white' : 'text-primary'"
                >
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="8" x2="12" y2="12"></line>
                    <line x1="12" y1="16" x2="12" y2="16"></line>
                </svg>
            </q-card-section>
            <q-card-section>
                <h1
                    class="text-h5 q-mb-xs"
                    :class="isDarkMode ? 'text-white' : 'text-primary'"
                >
                    {{ $t('titles.pageNotFound') }}
                </h1>
                <p class="text-body1 q-mb-lg">
                    {{ $t('messages.pageNotFound') }}
                </p>
                <q-btn
                    :label="$t('buttons.goHome')"
                    color="primary"
                    icon="home"
                    @click="goHome"
                />
            </q-card-section>
        </q-card>
    </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useTheme } from 'src/composables/useTheme'

const router = useRouter()
const { isDarkMode } = useTheme()

function goHome() {
    router.push('/')
}
</script>

<style scoped>
.flex {
    display: flex;
}
.flex-center {
    justify-content: center;
    align-items: center;
    height: 100vh;
}
</style>
