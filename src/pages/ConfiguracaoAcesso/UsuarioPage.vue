<template>
    <q-page class="q-pa-xs q-pb-none">
        <!-- Toolbar compacto -->
        <q-card flat bordered class="toolbar-container q-mb-xs q-pa-xs">
            <div class="toolbar-buttons" style="width: 100%">
                <q-btn
                    :label="$t('buttons.refresh')"
                    color="positive"
                    outline
                    icon="refresh"
                    @click="() => loadUsers()"
                    size="sm"
                    dense
                    class="q-ml-auto q-px-sm"
                />
            </div>
        </q-card>

        <!-- Tabela DataTable -->
        <data-table
            table-name="users"
            v-model:selected="selected"
            :columns="columns"
            :rows="users"
            :loading="loading"
            :filter="tableFilter"
            :pagination="pagination"
            :save-table-options="true"
            :show-toolbar="true"
            :show-quick-filter="true"
            :filter-columns="filterableColumns"
            v-model:quick-filter-value="columnFilter"
            :max-height="'calc(100vh - 150px)'"
            selection="multiple"
            dense
            flat
            bordered
            binary-state-sort
            :rows-per-page-options="[10, 25, 50, 100, 500, 1000]"
            @request="onTableRequest"
            @restore-pagination="onRestorePagination"
            @quick-filter="onColumnFilter"
        >
            <!-- Slot para coluna de status -->
            <template v-slot:body-cell-ativo="props">
                <q-td :props="props">
                    <q-chip
                        :label="getStatusLabel(props.row.ativo)"
                        :color="props.row.ativo ? 'positive' : 'negative'"
                        text-color="white"
                        size="sm"
                    />
                </q-td>
            </template>

            <!-- Slot para quando não há dados -->
            <template v-slot:no-data>
                <div class="full-width row flex-center q-gutter-xs q-pa-lg">
                    <q-icon size="2em" name="sentiment_dissatisfied" />
                    <span class="text-body1">{{
                        $t('forms.labels.noResults')
                    }}</span>
                </div>
            </template>
        </data-table>
    </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores/auth'
import { useLoadingOverlay } from '@/composables/loadingOverlay'
import DataTable from '@/components/DataTable.vue'

const { t: $t } = useI18n()
const { setLoading } = useLoadingOverlay()

// Types
interface User {
    id: number
    nome: string
    sobrenome: string
    email: string
    username: string
    ativo: boolean
    ativo_formatado: string
}

interface TableRequestProps {
    pagination: {
        sortBy: string
        descending: boolean
        page: number
        rowsPerPage: number
        rowsNumber?: number
    }
    filter?:
        | {
              contem_nome_email: string
              ativo: boolean | null
          }
        | undefined
    getCellValue?: (col: unknown, row: unknown) => unknown
}

// Stores
const authStore = useAuthStore()

// Refs
const users = ref<User[]>([])
const selected = ref<User[]>([])
const loading = ref(false)

// Filtros
const columnFilter = ref<{
    column: string
    content: string | number | boolean
} | null>(null)

// Paginação
const pagination = ref({
    sortBy: 'id',
    descending: false,
    page: 1,
    rowsPerPage: 25,
    rowsNumber: 0
})

// Computed
const filterableColumns = computed(() => [
    {
        label: $t('forms.labels.nameEmail'),
        value: 'contem_nome_email',
        field: 'contem_nome_email',
        type: 'text' as const
    },
    {
        label: $t('forms.labels.status'),
        value: 'ativo',
        field: 'ativo',
        type: 'boolean' as const
    }
])

const tableFilter = computed(() => {
    if (
        columnFilter.value &&
        columnFilter.value.column &&
        columnFilter.value.content !== null &&
        columnFilter.value.content !== undefined
    ) {
        try {
            // Converter valor para string para compatibilidade com q-table
            const content = columnFilter.value.content
            const stringValue =
                typeof content === 'string' ? content : String(content)

            // Só retornar filtro se o valor for válido
            if (
                stringValue &&
                stringValue.trim() !== '' &&
                stringValue !== 'undefined' &&
                stringValue !== 'null'
            ) {
                return {
                    [columnFilter.value.column]: stringValue
                }
            }
        } catch {
            // Ignorar erro e retornar filtro vazio
        }
    }
    return {}
})

// Função para formatar o status
const getStatusLabel = (ativo: boolean) => {
    return ativo ? $t('forms.labels.active') : $t('forms.labels.inactive')
}

// Colunas da tabela
const columns = [
    {
        name: 'id',
        label: $t('forms.labels.id'),
        field: 'id',
        align: 'left' as const,
        sortable: true
    },
    {
        name: 'nome',
        label: $t('forms.labels.firstName'),
        field: 'nome',
        align: 'left' as const,
        sortable: true
    },
    {
        name: 'sobrenome',
        label: $t('forms.labels.lastName'),
        field: 'sobrenome',
        align: 'left' as const,
        sortable: true
    },
    {
        name: 'email',
        label: $t('forms.labels.email'),
        field: 'email',
        align: 'left' as const,
        sortable: true
    },
    {
        name: 'username',
        label: $t('forms.labels.username'),
        field: 'username',
        align: 'left' as const,
        sortable: true
    },
    {
        name: 'ativo',
        label: $t('forms.labels.status'),
        field: 'ativo',
        align: 'center' as const,
        sortable: true
    }
]

// Métodos
const loadUsers = async (props: Partial<TableRequestProps> | null = null) => {
    await setLoading(async () => {
        try {
            loading.value = true

            const requestPagination = props?.pagination || pagination.value
            const filter = props?.filter || tableFilter.value

            // Construir parâmetros da requisição
            const params: Record<string, unknown> = {
                page: requestPagination.page,
                page_size: requestPagination.rowsPerPage
            }

            // Aplicar filtro de coluna se existir
            if (filter && Object.keys(filter).length > 0) {
                Object.entries(filter).forEach(([key, value]) => {
                    if (value !== null && value !== undefined && value !== '') {
                        params[key] = value
                    }
                })
            }

            if (requestPagination.sortBy) {
                const sortOrder = requestPagination.descending ? '-' : ''
                params.ordering = `${sortOrder}${requestPagination.sortBy}`
            }

            const response = await authStore.asyncRequest({
                endpoint: '/api/corpsystem/produto-service/pn-usuario',
                method: 'get',
                params,
                customErrorMessages: {
                    400: $t('pages.users.errors.loadError'),
                    404: $t('pages.users.errors.loadError'),
                    500: $t('pages.users.errors.loadError')
                }
            })

            const data = response.data

            users.value = data.results
            pagination.value = {
                ...requestPagination,
                rowsNumber: data.count
            }
        } catch (error) {
            throw new Error(error as string)
        } finally {
            loading.value = false
        }
    })
}

const onTableRequest = (requestProps: Record<string, unknown>) => {
    // Converter o requestProps para o formato esperado
    const props: Partial<TableRequestProps> = {}

    if (requestProps.pagination) {
        props.pagination =
            requestProps.pagination as TableRequestProps['pagination']
    }

    if (requestProps.filter) {
        props.filter = requestProps.filter as TableRequestProps['filter']
    }

    loadUsers(props)
}

const onColumnFilter = (
    filterValue: { column: string; content: string | number | boolean } | null
) => {
    columnFilter.value = filterValue
    pagination.value.page = 1
    loadUsers()
}

const onRestorePagination = (savedPagination: Record<string, unknown>) => {
    // Restaurar a paginação salva, mas sempre começar na página 1
    pagination.value = {
        ...pagination.value,
        ...savedPagination,
        page: 1 // Sempre começar na página 1
    }
}

// Lifecycle
onMounted(() => {
    loadUsers()
})
</script>

<style scoped>
.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Estilos para toolbar compacto */
.toolbar-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    flex-wrap: wrap;
}

.toolbar-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
}

/* Responsividade para telas menores */
@media (max-width: 768px) {
    .toolbar-container {
        justify-content: center;
        gap: 6px;
    }

    .toolbar-buttons {
        justify-content: center;
        gap: 6px;
    }
}
</style>
