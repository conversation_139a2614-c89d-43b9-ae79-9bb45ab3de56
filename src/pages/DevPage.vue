<template>
    <div id="dev-page">
        <p><b> DevPage </b></p>
        <q-tabs
            v-model="tab"
            dense
            class="text-grey"
            :active-color="isDarkMode ? 'white' : 'primary'"
            align="left"
            narrow-indicator
            inline-label
        >
            <q-tab name="trash" icon="delete" label="Trash" />
            <q-tab name="form-pessoa" icon="person" label="Pessoa" />
            <q-tab name="form-usuario" icon="manage_accounts" label="Usuário" />
            <q-tab
                name="form-permissoes"
                icon="lock"
                label="Grupo Permissões"
            />
            <q-tab
                name="form-usuario-permissoes"
                icon="security"
                label="Permissões Usuário"
            />
            <q-tab
                name="form-email-config"
                icon="email"
                label="Config. Email"
            />
            <q-tab name="form-basico" icon="description" label="Form. Básico" />
            <q-tab name="user-credentials" icon="person" label="Credenciais" />
            <q-tab
                name="form-view-restrictions"
                icon="visibility"
                label="Restrições"
            />
            <q-tab name="form-vendedor" icon="person" label="Vendedor" />
            <q-tab
                name="form-comprador"
                icon="shopping_cart"
                label="Comprador"
            />
            <q-tab name="form-cliente" icon="person_outline" label="Cliente" />
            <q-tab name="form-fornecedor" icon="inventory" label="Fornecedor" />
            <q-tab name="form-funcionario" icon="badge" label="Funcionário" />
            <q-tab
                name="form-motorista"
                icon="directions_car"
                label="Motorista"
            />
            <q-tab name="form-tecnico" icon="engineering" label="Técnico" />
            <q-tab name="form-endereco" icon="home" label="Endereço" />
            <q-tab name="form-documento" icon="description" label="Documento" />
            <q-tab name="cnpj-search" icon="search" label="Consulta CNPJ" />
            <q-tab
                name="form-crm-prospect"
                icon="business"
                label="CRM/Prospect"
            />
            <q-tab
                name="form-contato"
                icon="contact_page"
                label="Form. Contato"
            />
            <q-tab
                name="colecao-enderecos"
                icon="location_city"
                label="Col. Endereços"
            />
            <q-tab
                name="colecao-contatos"
                icon="contacts"
                label="Col. Contatos"
            />
            <q-tab name="rich-editor" icon="text_format" label="Rich Editor" />
            <q-tab
                name="advanced-table"
                icon="table_chart"
                label="Tabela Avançada"
            />
            <q-tab
                name="form-texto-padronizado"
                icon="text_snippet"
                label="Texto Padronizado"
            />
        </q-tabs>

        <q-separator />

        <q-tab-panels v-model="tab" animated infinite keep-alive>
            <q-tab-panel name="trash">
                <LangChanger />
                <ThemeChanger />
                <h3>{{ router.currentRoute.value.fullPath }}</h3>
                <h4>{{ $q.screen.lt.sm }}</h4>
                <!-- <h2>WEBSOCKET</h2>
                    <q-btn @click="send">Send</q-btn>
                    <h4>{{ conections }}</h4> -->
            </q-tab-panel>

            <q-tab-panel name="form-usuario">
                <form-usuario ref="formUsuarioRef" />
            </q-tab-panel>

            <q-tab-panel name="form-pessoa">
                <form-pessoa ref="formPessoaRef" />
            </q-tab-panel>

            <q-tab-panel name="form-permissoes">
                <form-grupo-permissoes ref="formGrupoPermissoesRef" />
            </q-tab-panel>

            <q-tab-panel name="form-usuario-permissoes">
                <form-usuario-permissoes ref="formUsuarioPermissoesRef" />
            </q-tab-panel>

            <q-tab-panel name="form-email-config">
                <form-email-config ref="formEmailConfigRef" />
            </q-tab-panel>

            <q-tab-panel name="form-basico">
                <form-basico ref="formBasicoRef" :enabledFields="['email']" />
            </q-tab-panel>

            <q-tab-panel name="user-credentials">
                <div>
                    <div class="row q-mb-md">
                        <div class="col-12 col-md-4">
                            <q-select
                                v-model="userStatus"
                                :label="$t('user.status.label')"
                                :options="statusOptions"
                                outlined
                                dense
                                emit-value
                                map-options
                                class="status-selector"
                            />
                        </div>
                    </div>

                    <!-- Exemplo de uso em cabeçalho de aplicação -->
                    <div class="text-caption text-grey q-mb-xs">
                        Exemplo de uso em cabeçalho:
                    </div>
                    <div class="bg-grey-2 q-pa-xs q-mb-md">
                        <view-user-credentials
                            :photo-url="userPhoto"
                            :first-name="userFirstName"
                            :last-name="userLastName"
                            :username="userUsername"
                            :role="'Administrador'"
                            :role-color="'primary'"
                            :show-status="true"
                            :show-text-status="false"
                            :status="userStatus"
                        />
                    </div>

                    <!-- Exemplo de uso em lista de usuários -->
                    <div class="text-caption text-grey q-mb-xs">
                        Exemplo de uso em lista de usuários:
                    </div>
                    <div class="row q-col-gutter-md">
                        <div class="col-12 col-md-6">
                            <view-user-credentials
                                :photo-url="userPhoto"
                                :first-name="userFirstName"
                                :last-name="userLastName"
                                :username="userUsername"
                                :role="'Administrador'"
                                :role-color="'primary'"
                                :show-status="true"
                                :status="userStatus"
                            >
                                <template #additional-info>
                                    <div class="col-12">
                                        <div class="text-caption text-grey-8">
                                            {{ userEmail }}
                                        </div>
                                    </div>
                                </template>
                            </view-user-credentials>
                        </div>

                        <div class="col-12 col-md-6">
                            <view-user-credentials
                                :photo-url="'https://cdn.quasar.dev/img/avatar2.jpg'"
                                :first-name="'Maria'"
                                :last-name="'Oliveira'"
                                :username="'mariaoliveira'"
                                :role="'Gerente'"
                                :role-color="'secondary'"
                                :show-status="true"
                                :status="'away'"
                            >
                                <template #additional-info>
                                    <div class="col-12">
                                        <div class="text-caption text-grey-8">
                                            <EMAIL>
                                        </div>
                                    </div>
                                </template>
                            </view-user-credentials>
                        </div>
                    </div>
                </div>
            </q-tab-panel>

            <q-tab-panel name="form-view-restrictions">
                <div>
                    <form-view-restrictions ref="formViewRestrictionsRef" />
                </div>
            </q-tab-panel>

            <q-tab-panel name="form-vendedor">
                <div>
                    <form-vendedor ref="formVendedorRef" />
                </div>
            </q-tab-panel>

            <q-tab-panel name="form-comprador">
                <div>
                    <form-comprador ref="formCompradorRef" />
                </div>
            </q-tab-panel>

            <q-tab-panel name="form-cliente">
                <div>
                    <form-cliente
                        ref="formClienteRef"
                        :initialValues="{ nome: 'Cliente Exemplo' }"
                    />
                </div>
            </q-tab-panel>

            <q-tab-panel name="form-fornecedor">
                <div>
                    <form-fornecedor
                        ref="formFornecedorRef"
                        :initialValues="{ nome: 'Fornecedor Exemplo' }"
                    />
                </div>
            </q-tab-panel>

            <q-tab-panel name="form-funcionario">
                <div>
                    <form-funcionario
                        ref="formFuncionarioRef"
                        :initialValues="{ nomeMae: 'Maria da Silva' }"
                    />
                </div>
            </q-tab-panel>

            <q-tab-panel name="form-motorista">
                <div>
                    <form-motorista ref="formMotoristaRef" />
                </div>
            </q-tab-panel>

            <q-tab-panel name="form-tecnico">
                <div>
                    <form-tecnico ref="formTecnicoRef" />
                </div>
            </q-tab-panel>

            <q-tab-panel name="form-endereco">
                <div>
                    <form-endereco ref="formEnderecoRef" />
                </div>
            </q-tab-panel>

            <q-tab-panel name="form-documento">
                <div>
                    <form-documento ref="formDocumentoRef" />
                </div>
            </q-tab-panel>

            <q-tab-panel name="cnpj-search">
                <div class="q-pa-xs">
                    <div class="text-h5 q-mb-md">
                        Componente de Consulta CNPJ
                    </div>

                    <div class="text-body1 q-mb-lg">
                        Este componente permite consultar dados de empresas
                        através do CNPJ. Clique no botão abaixo para testar:
                    </div>

                    <div class="row q-col-gutter-md items-center">
                        <div class="col-auto">
                            <cnpj-search
                                @select="onCNPJSelected"
                                @cancel="onCNPJCanceled"
                                size="lg"
                                color="primary"
                                variant="elevated"
                            />
                        </div>

                        <div class="col-auto">
                            <div class="text-body2">
                                Botão de consulta CNPJ (clique para abrir o
                                modal)
                            </div>
                        </div>
                    </div>

                    <!-- Resultado da consulta -->
                    <div v-if="selectedCNPJData" class="q-mt-xl">
                        <q-separator class="q-mb-md" />
                        <div class="text-h6 q-mb-md">Dados Selecionados:</div>

                        <q-card flat bordered>
                            <q-card-section>
                                <pre class="text-body2">{{
                                    JSON.stringify(selectedCNPJData, null, 2)
                                }}</pre>
                            </q-card-section>
                        </q-card>
                    </div>
                </div>
            </q-tab-panel>

            <q-tab-panel name="form-crm-prospect">
                <div>
                    <form-crm-prospect ref="formCrmProspectRef" />
                </div>
            </q-tab-panel>

            <q-tab-panel name="form-contato">
                <div>
                    <form-contato
                        ref="formContatoRef"
                        :initialValues="{
                            nome: 'João Silva',
                            ocupacao: 'dev_frontend',
                            departamento: 'ti',
                            estado: 'SP',
                            cidade: 'São Paulo',
                            dataNascimento: '1990-05-15',
                            telefones: [
                                {
                                    id: '1',
                                    tipo: 'celular',
                                    codigoPais: 'BR',
                                    ddd: '11',
                                    numero: '99999-9999',
                                    ramal: '',
                                    whatsapp: true
                                }
                            ],
                            emails: [
                                {
                                    id: '1',
                                    email: '<EMAIL>'
                                }
                            ]
                        }"
                    />
                </div>
            </q-tab-panel>

            <q-tab-panel name="colecao-enderecos">
                <div class="q-pa-xs">
                    <div class="text-h6 text-center q-mb-md">
                        Teste - Coleção de Endereços
                    </div>

                    <!-- Componente de Coleção de Endereços -->
                    <form-colecao-enderecos
                        ref="colecaoEnderecosRef"
                        title="Endereços do Cliente"
                        :initial-values="enderecosIniciais"
                        :max-enderecos="5"
                    />
                </div>
            </q-tab-panel>

            <q-tab-panel name="colecao-contatos">
                <div class="q-pa-xs">
                    <div class="text-h6 text-center q-mb-md">
                        Teste - Coleção de Contatos
                    </div>

                    <!-- Componente de Coleção de Contatos -->
                    <form-colecao-contatos
                        ref="colecaoContatosRef"
                        title="Contatos da Empresa"
                        :initial-values="contatosIniciais"
                        :max-contatos="10"
                    />
                </div>
            </q-tab-panel>

            <q-tab-panel name="rich-editor">
                <div class="q-pa-xs">
                    <div class="text-h6 text-center q-mb-md">
                        Teste - Rich Text Editor
                    </div>

                    <div class="text-body1 q-mb-lg">
                        Editor de texto rico com formatação HTML. Suporta
                        negrito, itálico, sublinhado, alinhamento, listas, links
                        e muito mais.
                    </div>

                    <!-- Exemplo básico -->
                    <div class="q-mb-xl">
                        <div class="text-h6 q-mb-md">Exemplo Básico:</div>
                        <quill-rich-text-editor
                            v-model="richTextContent"
                            placeholder="Digite seu texto aqui..."
                            :max-length="1000"
                            :show-char-count="true"
                            min-height="200px"
                            max-height="400px"
                        />
                    </div>

                    <!-- Exemplo readonly -->
                    <div class="q-mb-xl">
                        <div class="text-h6 q-mb-md">Exemplo Readonly:</div>
                        <quill-rich-text-editor
                            v-model="richTextReadonly"
                            :readonly="true"
                            :show-char-count="false"
                            min-height="150px"
                        />
                    </div>

                    <!-- Exemplo compacto -->
                    <div class="q-mb-xl">
                        <div class="text-h6 q-mb-md">Exemplo Compacto:</div>
                        <quill-rich-text-editor
                            v-model="richTextCompact"
                            placeholder="Editor compacto..."
                            :max-length="500"
                            min-height="120px"
                            max-height="200px"
                        />
                    </div>

                    <!-- Exemplo para formulários -->
                    <div class="q-mb-xl">
                        <div class="text-h6 q-mb-md">
                            Exemplo para Formulários:
                        </div>
                        <div class="text-body2 q-mb-xs text-grey-7">
                            Editor integrado em um formulário com validação:
                        </div>
                        <q-form @submit="onFormSubmit" class="q-gutter-md">
                            <q-input
                                v-model="formTitle"
                                label="Título"
                                outlined
                                dense
                                :rules="[
                                    val => !!val || 'Título é obrigatório'
                                ]"
                            />

                            <div>
                                <div class="text-body2 q-mb-xs">
                                    Descrição *
                                </div>
                                <quill-rich-text-editor
                                    v-model="formDescription"
                                    placeholder="Digite a descrição do item..."
                                    :max-length="2000"
                                    min-height="150px"
                                    max-height="300px"
                                />
                                <div
                                    v-if="!formDescription"
                                    class="text-negative text-caption q-mt-xs"
                                >
                                    Descrição é obrigatória
                                </div>
                            </div>

                            <div class="row q-gutter-xs">
                                <q-btn
                                    type="submit"
                                    color="primary"
                                    label="Salvar"
                                    :disable="!formTitle || !formDescription"
                                />
                                <q-btn
                                    type="button"
                                    color="grey"
                                    flat
                                    label="Limpar"
                                    @click="clearForm"
                                />
                            </div>
                        </q-form>
                    </div>

                    <!-- Resultado -->
                    <div class="q-mt-xl">
                        <q-separator class="q-mb-md" />
                        <div class="text-h6 q-mb-md">Conteúdo HTML Gerado:</div>

                        <q-tabs
                            v-model="resultTab"
                            dense
                            class="text-grey"
                            active-color="primary"
                        >
                            <q-tab name="content1" label="Editor Básico" />
                            <q-tab name="content2" label="Editor Compacto" />
                        </q-tabs>

                        <q-tab-panels v-model="resultTab" animated>
                            <q-tab-panel name="content1">
                                <q-card flat bordered>
                                    <q-card-section>
                                        <pre class="text-body2">{{
                                            richTextContent || 'Nenhum conteúdo'
                                        }}</pre>
                                    </q-card-section>
                                </q-card>
                            </q-tab-panel>

                            <q-tab-panel name="content2">
                                <q-card flat bordered>
                                    <q-card-section>
                                        <pre class="text-body2">{{
                                            richTextCompact || 'Nenhum conteúdo'
                                        }}</pre>
                                    </q-card-section>
                                </q-card>
                            </q-tab-panel>
                        </q-tab-panels>
                    </div>
                </div>
            </q-tab-panel>

            <q-tab-panel name="form-texto-padronizado">
                <div class="q-pa-xs">
                    <div class="text-h6 text-center q-mb-md">
                        Teste - Formulário Texto Padronizado
                    </div>

                    <!-- Formulário de Texto Padronizado -->
                    <form-texto-padronizado
                        ref="formTextoPadronizadoRef"
                        :initial-values="textoPadronizadoInitialValues"
                    />
                </div>
            </q-tab-panel>
        </q-tab-panels>

        <q-btn
            :label="$t('buttons.confirm')"
            color="primary"
            class="full-width q-mt-md"
            @click="handleSubmit"
        />
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref, onUnmounted, onMounted } from 'vue'
import { useRouter } from 'vue-router'
// import useWebsocket from '@/services/websocket.service'
// Composables
import { useTheme } from '@/composables/useTheme'
const { isDarkMode } = useTheme()
import { useFormUtils } from '@/composables/formUtils'
const { activateAllTabs } = useFormUtils()
const { validateAll, notifyFieldsErrors } = useFormUtils()
import { useLoadingOverlay } from 'src/composables/loadingOverlay'
// Components
import LangChanger from 'src/components/LangChanger.vue'
import ThemeChanger from 'src/components/ThemeChanger.vue'
import FormPessoa from 'src/components/forms/FormPessoa.vue'
import FormUsuario from 'src/components/forms/FormUsuario.vue'
import FormGrupoPermissoes from 'src/components/forms/FormGrupoPermissoes.vue'
import FormUsuarioPermissoes from 'src/components/forms/FormUsuarioPermissoes.vue'
import FormEmailConfig from 'src/components/forms/FormEmailConfig.vue'
import FormBasico from 'src/components/forms/FormBasico.vue'
import ViewUserCredentials from 'src/components/user/ViewUserCredentials.vue'
import FormViewRestrictions from 'src/components/forms/FormViewRestrictions.vue'
import FormVendedor from 'src/components/forms/FormVendedor.vue'
import FormComprador from 'src/components/forms/FormComprador.vue'
import FormCliente from 'src/components/forms/FormCliente.vue'
import FormFornecedor from 'src/components/forms/FormFornecedor.vue'
import FormFuncionario from 'src/components/forms/FormFuncionario.vue'
import FormMotorista from 'src/components/forms/FormMotorista.vue'
import FormTecnico from 'src/components/forms/FormTecnico.vue'
import FormEndereco from 'src/components/forms/FormEndereco.vue'
import FormDocumento from 'src/components/forms/FormDocumento.vue'
import FormCrmProspect from 'src/components/forms/FormCrmProspect.vue'
import FormContato from 'src/components/forms/FormContato.vue'
import FormColecaoEnderecos from 'src/components/forms/FormColecaoEnderecos.vue'
import FormColecaoContatos from 'src/components/forms/FormColecaoContatos.vue'
import CnpjSearch from 'src/components/CnpjSearch.vue'
import QuillRichTextEditor from 'src/components/QuillRichTextEditor.vue'

import FormTextoPadronizado from 'src/components/forms/CadastroGeral/FormTextoPadronizado.vue'
import type { CNPJData } from 'src/interfaces/CNPJ'
import type { EnderecoData } from 'src/interfaces/FormEndereco'
import type { FormContatoData } from 'src/interfaces/FormContato'

// Interface para o formulário de texto padronizado
interface FormTextoPadronizado {
    descricao: string
    tipo: number | null
    ativo: boolean
    conteudo: string
}

const tab = ref('colecao-contatos')

const router = useRouter()
const { setLoading } = useLoadingOverlay()
// const { connect, sendMessage, conections, disconnect } = useWebsocket()
// connect()
// const send = () => {
//     sendMessage('Hello')
// }

setLoading(async () => {
    await new Promise(resolve => setTimeout(resolve, 1000))
})

const formGrupoPermissoesRef = ref<InstanceType<
    typeof FormGrupoPermissoes
> | null>(null)

const formUsuarioPermissoesRef = ref<InstanceType<
    typeof FormUsuarioPermissoes
> | null>(null)

const formEmailConfigRef = ref<InstanceType<typeof FormEmailConfig> | null>(
    null
)

const formBasicoRef = ref<InstanceType<typeof FormBasico> | null>(null)
const formPessoaRef = ref<InstanceType<typeof FormPessoa> | null>(null)
const formUsuarioRef = ref<InstanceType<typeof FormUsuario> | null>(null)
const formViewRestrictionsRef = ref<InstanceType<
    typeof FormViewRestrictions
> | null>(null)
const formVendedorRef = ref<InstanceType<typeof FormVendedor> | null>(null)
const formCompradorRef = ref<InstanceType<typeof FormComprador> | null>(null)
const formClienteRef = ref<InstanceType<typeof FormCliente> | null>(null)
const formFornecedorRef = ref<InstanceType<typeof FormFornecedor> | null>(null)
const formFuncionarioRef = ref<InstanceType<typeof FormFuncionario> | null>(
    null
)
const formMotoristaRef = ref<InstanceType<typeof FormMotorista> | null>(null)
const formTecnicoRef = ref<InstanceType<typeof FormTecnico> | null>(null)
const formEnderecoRef = ref<InstanceType<typeof FormEndereco> | null>(null)
const formDocumentoRef = ref<InstanceType<typeof FormDocumento> | null>(null)
const formCrmProspectRef = ref<InstanceType<typeof FormCrmProspect> | null>(
    null
)
const formContatoRef = ref<InstanceType<typeof FormContato> | null>(null)
const colecaoEnderecosRef = ref<InstanceType<
    typeof FormColecaoEnderecos
> | null>(null)
const colecaoContatosRef = ref<InstanceType<typeof FormColecaoContatos> | null>(
    null
)

// Dados do usuário para o componente UserCredentials
const userPhoto = ref('https://cdn.quasar.dev/img/boy-avatar.png')
const userFirstName = ref('João')
const userLastName = ref('Silva')
const userUsername = ref('joaosilva')
const userEmail = ref('<EMAIL>')
// Removido userRole pois não está sendo usado no novo design
const userStatus = ref('online') // Pode ser: 'online', 'offline', 'away', 'busy', 'invisible'

// Opções para o select de status
const statusOptions = [
    { label: 'Online', value: 'online' },
    { label: 'Offline', value: 'offline' },
    { label: 'Ausente', value: 'away' },
    { label: 'Ocupado', value: 'busy' },
    { label: 'Invisível', value: 'invisible' }
]

// Removidos os handlers de eventos, pois o componente agora é apenas para exibição

// Estado para o componente CNPJ Search
const selectedCNPJData = ref<CNPJData | null>(null)

// Estados para o Rich Text Editor
const richTextContent = ref(
    '<p>Este é um <strong>exemplo</strong> de texto com <em>formatação</em>.</p><p>Você pode adicionar <u>sublinhado</u>, criar <a href="https://quasar.dev">links</a> e muito mais!</p>'
)
const richTextReadonly = ref(
    '<h3>Conteúdo Readonly</h3><p>Este editor está em modo <strong>somente leitura</strong>.</p><ul><li>Item 1</li><li>Item 2</li><li>Item 3</li></ul>'
)
const richTextCompact = ref(
    '<p>Editor <strong>compacto</strong> para textos menores.</p>'
)
const resultTab = ref('content1')

// Estados para o exemplo de formulário
const formTitle = ref('')
const formDescription = ref('')

// Dados iniciais de exemplo para endereços
const enderecosIniciais: EnderecoData[] = [
    {
        id: '1',
        tipo: 'residential',
        pais: 'BR',
        cep: '01310-100',
        estado: 'SP',
        cidade: 'sao_paulo',
        logradouro: 'Avenida Paulista',
        numero: '1000',
        bairro: 'Bela Vista',
        complemento: 'Apto 101',
        pontoReferencia: 'Próximo ao MASP'
    }
]

// Dados iniciais de exemplo para contatos
const contatosIniciais: FormContatoData[] = [
    {
        id: '1',
        nome: 'Maria Silva Santos',
        ocupacao: 'gerente',
        departamento: 'vendas',
        pais: 'BR',
        estado: 'SP',
        cidade: 'sao_paulo',
        dataNascimento: '1985-03-15',
        telefones: [
            {
                id: '1',
                tipo: 'celular',
                codigoPais: 'BR',
                ddd: '11',
                numero: '99999-8888',
                ramal: '',
                whatsapp: true
            }
        ],
        emails: [
            {
                id: '1',
                email: '<EMAIL>'
            }
        ]
    }
]

// Métodos para o componente CNPJ Search
const onCNPJSelected = (data: CNPJData): void => {
    selectedCNPJData.value = data
    // eslint-disable-next-line no-console
    console.log('CNPJ selecionado:', data)
}

const onCNPJCanceled = (): void => {
    // eslint-disable-next-line no-console
    console.log('Consulta CNPJ cancelada')
}

// Métodos para o exemplo de formulário com Rich Text Editor
const onFormSubmit = (event: Event): void => {
    event.preventDefault()

    if (!formTitle.value || !formDescription.value) {
        return
    }

    // Simular envio do formulário
    // eslint-disable-next-line no-console
    console.log('Formulário enviado:', {
        title: formTitle.value,
        description: formDescription.value
    })

    // Mostrar notificação de sucesso
    import('quasar').then(({ Notify }) => {
        Notify.create({
            message: 'Formulário enviado com sucesso!',
            color: 'positive',
            position: 'bottom',
            timeout: 2000,
            icon: 'check_circle'
        })
    })
}

const clearForm = (): void => {
    formTitle.value = ''
    formDescription.value = ''
}

const handleSubmit = async () => {
    // Verifica qual aba está ativa e valida o formulário correspondente
    const { isValid, errors } = await validateAll([
        formPessoaRef.value!.validate,
        formUsuarioRef.value!.validate,
        formGrupoPermissoesRef.value!.validate,
        formEmailConfigRef.value!.validate,
        formBasicoRef.value!.validate,
        formVendedorRef.value!.validate,
        formCompradorRef.value!.validate,
        formClienteRef.value!.validate,
        formFornecedorRef.value!.validate,
        formFuncionarioRef.value!.validate,
        formMotoristaRef.value!.validate,
        formTecnicoRef.value!.validate,
        formEnderecoRef.value!.validate,
        formDocumentoRef.value!.validate,
        formCrmProspectRef.value!.validate
    ])

    // Validação específica para o formulário de contato
    let contactFormValid = true
    if (formContatoRef.value) {
        const contactResult = await formContatoRef.value.validate()
        contactFormValid = contactResult.valid
    }

    const allFormsValid = isValid && contactFormValid

    if (allFormsValid) {
        // eslint-disable-next-line no-console
        console.log('Form data')
    } else {
        notifyFieldsErrors(errors)
    }
}

onMounted(async () => {
    const allTabs = [
        'form-pessoa',
        'form-usuario',
        'form-permissoes',
        'form-usuario-permissoes',
        'form-email-config',
        'form-basico',
        'user-credentials',
        'form-view-restrictions',
        'form-vendedor',
        'form-comprador',
        'form-cliente',
        'form-fornecedor',
        'form-funcionario',
        'form-motorista',
        'form-tecnico',
        'form-endereco',
        'form-documento',
        'cnpj-search',
        'form-crm-prospect',
        'form-contato',
        'colecao-enderecos',
        'colecao-contatos',
        'rich-editor',

        'form-texto-padronizado'
    ]
    await activateAllTabs({
        allTabs,
        tab
    })
})

// Refs e dados para o formulário de texto padronizado
const formTextoPadronizadoRef = ref<InstanceType<
    typeof FormTextoPadronizado
> | null>(null)
const textoPadronizadoInitialValues = ref({})

onUnmounted(() => {
    // disconnect()
})
</script>

<style scoped>
.q-tab-panel {
    padding: 0;
}

/* Estilos para a demonstração do UserCredentials */
.status-selector {
    max-width: 300px;
}

/* Simula um cabeçalho de aplicação */
.bg-grey-2 {
    border-radius: 4px;
}
</style>
