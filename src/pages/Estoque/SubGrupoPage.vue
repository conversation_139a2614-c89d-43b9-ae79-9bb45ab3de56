<template>
    <crud-template
        table-name="subGroup"
        :columns="columns"
        :rows="subGroup"
        :loading="loading"
        :filter="tableFilter"
        :pagination="pagination"
        :toolbar-actions="toolbarActions"
        :selected-items="selected"
        :show-modal="showModal"
        :modal-title="modalTitle"
        :modal-saving="saving"
        :filter-columns="filterableColumns"
        v-model:quick-filter-value="columnFilter"
        :show-add-button="permissions.canCreate"
        :show-edit-action="permissions.canEdit"
        :show-delete-action="permissions.canDelete"
        :show-clear-button="true"
        @add-clicked="openCreateModal"
        @refresh-clicked="loadSubGroup"
        @table-request="onTableRequest"
        @restore-pagination="onRestorePagination"
        @quick-filter="onColumnFilter"
        @edit-clicked="row => openEditModal(row as unknown as SubGroup)"
        @delete-clicked="row => confirmDelete(row as unknown as SubGroup)"
        @inline-edit="handleInlineEdit"
        @update:show-modal="showModal = $event"
        @modal-close="closeModal"
        @modal-cancel="closeModal"
        @modal-clear="clearForm"
        @modal-save="saveSubGroup"
        @update:selected-items="
            items => (selected = items as unknown as SubGroup[])
        "
    >    <!-- Ações extras na linha -->
        <template #row-extra-actions="{ row }">
            <q-btn
                :icon="row.ativo ? 'visibility_off' : 'visibility'"
                :color="row.ativo ? 'negative' : 'positive'"
                size="sm"
                flat
                round
                @click="toggleItemStatus(row as SubGroup)"
            >
                <q-tooltip>
                    {{
                        row.ativo
                            ? $t('buttons.deactivate')
                            : $t('buttons.activate')
                    }}
                </q-tooltip>
            </q-btn>
        </template>
        <!-- Conteúdo do modal -->
        <template #modal-content>
            <form-basico
                ref="formRef"
                :title="$t('pages.subGroup.form.title')"
                :enabled-fields="['descricao', 'ativo']"
                :initial-values="formInitialValues"
            />
        </template>
    </crud-template>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Dialog, Notify } from 'quasar'
import CrudTemplate from '@/components/CrudTemplate.vue'
import FormBasico from 'src/components/forms/FormBasico.vue'
import { useAuthStore } from '@/stores/auth'
import { useLoadingOverlay } from '@/composables/loadingOverlay'
import { useActionPermissions } from '@/composables/useActionPermissions'
import type { TableColumn, ActionItem } from '@/types/CrudTemplate'

const { t: $t } = useI18n()
const { setLoading } = useLoadingOverlay()
const { produtoSubGrupoPermissions } = useActionPermissions()
const permissions = produtoSubGrupoPermissions

// Types
interface SubGroup {
    id: number
    descricao: string
    ativo: boolean
}

interface FormGroupInstance {
    validate: () => Promise<{ valid: boolean }>
    getData: () => Record<string, unknown>
    resetForm: () => void
}

interface TableRequestProps {
    pagination: {
        sortBy: string
        descending: boolean
        page: number
        rowsPerPage: number
        rowsNumber?: number
    }
    filter?:
        | {
              contem_descricao: string
              ativo: boolean | null
          }
        | undefined
    getCellValue?: (col: unknown, row: unknown) => unknown
}

// Stores
const authStore = useAuthStore()

// Refs
const subGroup = ref<SubGroup[]>([])
const selected = ref<SubGroup[]>([])
const loading = ref(false)
const saving = ref(false)
const showModal = ref(false)
const isEditing = ref(false)
const formRef = ref<FormGroupInstance | null>(null)
const currentItem = ref<SubGroup | null>(null)

// Filtros
const columnFilter = ref<{
    column: string
    content: string | number | boolean
} | null>(null)

// Action Collection
const toolbarActions = computed<ActionItem[]>(() => [
    {
        label: $t('buttons.editSelected'),
        active: selected.value.length === 1,
        icon: 'edit',
        description: $t('buttons.editSelectedDescription'),
        action: () => {
            if (selected.value.length === 1 && selected.value[0]) {
                openEditModal(selected.value[0])
            }
        }
    },
    {
        label: $t('buttons.deleteSelected', {
            count: selected.value.length
        }),
        active: selected.value.length > 0,
        icon: 'delete',
        description: $t('buttons.deleteSelectedDescription'),
        action: confirmDeleteMultiple
    }
])

// Paginação
const pagination = ref({
    sortBy: 'id',
    descending: false,
    page: 1,
    rowsPerPage: 25,
    rowsNumber: 0
})

// Computed
const filterableColumns = computed(() => [
    {
        label: $t('forms.labels.description'),
        value: 'descricao',
        field: 'descricao',
        type: 'text' as const
    },
    {
        label: $t('forms.labels.status'),
        value: 'ativo',
        field: 'ativo',
        type: 'boolean' as const
    }
])

const tableFilter = computed(() => {
    if (
        columnFilter.value &&
        columnFilter.value.column &&
        columnFilter.value.content !== null &&
        columnFilter.value.content !== undefined
    ) {
        try {
            // Converter valor para string para compatibilidade com q-table
            const content = columnFilter.value.content
            const stringValue =
                typeof content === 'string' ? content : String(content)

            // Só retornar filtro se o valor for válido
            if (
                stringValue &&
                stringValue.trim() !== '' &&
                stringValue !== 'undefined' &&
                stringValue !== 'null'
            ) {
                return {
                    [columnFilter.value.column]: stringValue
                }
            }
        } catch {
            // Ignorar erro e retornar filtro vazio
        }
    }
    return {}
})

const formInitialValues = computed(() => {
    if (isEditing.value && currentItem.value) {
        return {
            ativo: currentItem.value.ativo,
            descricao: currentItem.value.descricao
        }
    }
    return {}
})

const modalTitle = computed(() => {
    return isEditing.value
        ? $t('pages.subGroup.editTitle')
        : $t('pages.subGroup.createTitle')
})

const clearForm = () => {
    try {
        if (formRef.value) {
            formRef.value.resetForm()
        }
    } catch {
        Notify.create({
            message: 'Erro ao limpar formulário',
            color: 'negative',
            position: 'top',
            timeout: 3000,
            icon: 'error'
        })
    }
}

// Colunas da tabela
const columns: TableColumn[] = [
    {
        name: 'id',
        label: $t('forms.labels.id'),
        field: 'id',
        align: 'left' as const,
        sortable: true,
        order: 1
    },
    {
        name: 'descricao',
        label: $t('forms.labels.description'),
        field: 'descricao',
        align: 'left' as const,
        sortable: true,
        order: 2,
        inlineEdit: permissions.value.canEdit,
        fieldType: 'text'
    },
    {
        name: 'ativo',
        label: $t('forms.labels.status'),
        field: 'ativo',
        align: 'left' as const,
        sortable: true,
        order: 3
    },
    {
        name: 'actions',
        label: $t('forms.labels.actions'),
        field: 'actions',
        align: 'center' as const,
        sortable: false,
        order: 4
    }
]

// Métodos
const loadSubGroup = async (
    props: Partial<TableRequestProps> | null = null
) => {
    await setLoading(async () => {
        loading.value = true

        const requestPagination = props?.pagination || pagination.value
        const filter = props?.filter || tableFilter.value

        // Construir parâmetros da requisição
        const params: Record<string, unknown> = {
            page: requestPagination.page,
            page_size: requestPagination.rowsPerPage
        }

        // Aplicar filtro de coluna se existir
        if (filter && Object.keys(filter).length > 0) {
            Object.entries(filter).forEach(([key, value]) => {
                if (value !== null && value !== undefined && value !== '') {
                    params[key] = value
                }
            })
        }

        if (requestPagination.sortBy) {
            const sortOrder = requestPagination.descending ? '-' : ''
            params.ordering = `${sortOrder}${requestPagination.sortBy}`
        }

        const response = await authStore.asyncRequest({
            endpoint: '/api/corpsystem/produto-service/produto-subgrupo',
            method: 'get',
            params,
            customErrorMessages: {
                400: $t('pages.subGroup.errors.loadError'),
                404: $t('pages.subGroup.errors.loadError'),
                500: $t('pages.subGroup.errors.loadError')
            }
        })

        const data = response.data

        subGroup.value = data.results
        pagination.value = {
            ...requestPagination,
            rowsNumber: data.count
        }

        loading.value = false
    })
}

const onTableRequest = (requestProps: Record<string, unknown>) => {
    // Converter o requestProps para o formato esperado
    const props: Partial<TableRequestProps> = {}

    if (requestProps.pagination) {
        props.pagination =
            requestProps.pagination as TableRequestProps['pagination']
    }

    if (requestProps.filter) {
        props.filter = requestProps.filter as TableRequestProps['filter']
    }

    loadSubGroup(props)
}

const onColumnFilter = (
    filterValue: { column: string; content: string | number | boolean } | null
) => {
    columnFilter.value = filterValue
    pagination.value.page = 1
    loadSubGroup()
}

const onRestorePagination = (savedPagination: Record<string, unknown>) => {
    // Restaurar a paginação salva, mas sempre começar na página 1
    pagination.value = {
        ...pagination.value,
        ...savedPagination,
        page: 1 // Sempre começar na página 1
    }
}

// Modal methods
const openCreateModal = () => {
    isEditing.value = false
    currentItem.value = null
    showModal.value = true
}

const openEditModal = async (item: SubGroup) => {
    try {
        await setLoading(async () => {
            try {
                // Fazer requisição para obter os dados completos do item
                const fullItemData = await getGroupById(item.id)

                // Atualizar o item atual com os dados completos da API
                currentItem.value = fullItemData
                isEditing.value = true
                showModal.value = true
            } catch {
                // Se falhar ao buscar dados completos, usar os dados do item da lista
                currentItem.value = item
                isEditing.value = true
                showModal.value = true

                Notify.create({
                    message: $t('pages.subGroup.errors.loadError'),
                    color: 'warning',
                    position: 'top',
                    timeout: 3000,
                    icon: 'warning'
                })
            }
        })
    } catch {
        // Fallback final
        currentItem.value = item
        isEditing.value = true
        showModal.value = true
    }
}

const closeModal = () => {
    showModal.value = false
    isEditing.value = false
    currentItem.value = null
}

const saveSubGroup = async () => {
    try {
        if (!formRef.value) {
            return
        }

        const { valid } = await formRef.value.validate()

        if (!valid) {
            Notify.create({
                message: $t('errors.validationError'),
                color: 'negative',
                position: 'top',
                timeout: 3000,
                icon: 'error'
            })
            return
        }

        await setLoading(async () => {
            try {
                saving.value = true

                if (!formRef.value) {
                    throw new Error('FormRef não está disponível')
                }

                const formData = formRef.value.getData()

                if (isEditing.value && currentItem.value) {
                    await updateSubGroup(currentItem.value.id, formData)
                } else {
                    await createSubGroup(formData)
                }

                // Sucesso - fechar modal e recarregar dados
                closeModal()
                loadSubGroup()

                Notify.create({
                    message: isEditing.value
                        ? $t('pages.subGroup.messages.updateSuccess')
                        : $t('pages.subGroup.messages.createSuccess'),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } finally {
                saving.value = false
            }
        })
    } catch {
        Notify.create({
            message: $t('pages.subGroup.errors.saveError'),
            color: 'negative',
            position: 'top',
            timeout: 3000,
            icon: 'error'
        })
    }
}

// CRUD methods
const getGroupById = async (id: number): Promise<SubGroup> => {
    try {
        const response = await authStore.asyncRequest({
            endpoint: `/api/corpsystem/produto-service/produto-subgrupo/${id}`,
            method: 'get',
            customErrorMessages: {
                404: $t('pages.subGroup.errors.notFound'),
                500: $t('pages.subGroup.errors.loadError')
            }
        })
        return response.data
    } catch (error) {
        throw new Error(`Erro ao buscar Subgrupo ${id}: ${error}`)
    }
}

const createSubGroup = async (
    data: Record<string, unknown>
): Promise<SubGroup> => {
    try {
        const response = await authStore.asyncRequest({
            endpoint: '/api/corpsystem/produto-service/produto-subgrupo',
            method: 'post',
            params: data,
            customErrorMessages: {
                400: $t('pages.subGroup.errors.duplicateDescription'),
                500: $t('pages.subGroup.errors.saveError')
            }
        })
        return response.data
    } catch (error) {
        throw new Error(`Erro ao criar Subgrupo: ${error}`)
    }
}

const updateSubGroup = async (
    id: number,
    data: Record<string, unknown>
): Promise<SubGroup> => {
    try {
        const response = await authStore.asyncRequest({
            endpoint: `/api/corpsystem/produto-service/produto-subgrupo/${id}`,
            method: 'put',
            params: data,
            customErrorMessages: {
                400: $t('pages.subGroup.errors.duplicateDescription'),
                404: $t('pages.subGroup.errors.saveError'),
                500: $t('pages.subGroup.errors.saveError')
            }
        })
        return response.data
    } catch (error) {
        throw new Error(`Erro ao atualizar Subgrupo ${id}: ${error}`)
    }
}

const deleteSubGroup = async (id: number): Promise<void> => {
    try {
        await authStore.asyncRequest({
            endpoint: `/api/corpsystem/produto-service/produto-subgrupo/${id}`,
            method: 'delete',
            customErrorMessages: {
                400: $t('pages.subGroup.errors.duplicateDescription'),
                404: $t('pages.subGroup.errors.deleteError'),
                500: $t('pages.subGroup.errors.deleteError')
            }
        })
    } catch (error) {
        throw new Error(`Erro ao deletar Subgrupo ${id}: ${error}`)
    }
}

const deleteMultipleSubGroup = async (ids: number[]): Promise<void> => {
    try {
        const deletePromises = ids.map(id =>
            authStore.asyncRequest({
                endpoint: `/api/corpsystem/produto-service/produto-subgrupo/${id}`,
                method: 'delete',
                customErrorMessages: {
                    400: $t('pages.subGroup.errors.deleteMultipleError'),
                    404: $t('pages.subGroup.errors.deleteMultipleError'),
                    500: $t('pages.subGroup.errors.deleteMultipleError')
                }
            })
        )

        await Promise.all(deletePromises)
    } catch (error) {
        throw new Error(`Erro ao deletar múltiplos Subgrupos: ${error}`)
    }
}

// Toggle status method
const toggleItemStatus = async (item: SubGroup) => {
    const newStatus = !item.ativo
    const actionText = newStatus
        ? $t('buttons.activate')
        : $t('buttons.deactivate')

    Dialog.create({
        title: $t('dialogs.confirmStatusChange.title'),
        message: $t('dialogs.confirmStatusChange.message', {
            item: item.descricao,
            action: actionText.toLowerCase()
        }),
        cancel: true,
        persistent: true,
        ok: {
            label: actionText,
            color: newStatus ? 'positive' : 'negative'
        }
    }).onOk(async () => {
        await setLoading(async () => {
            saving.value = true

            try {
                // Preparar dados para atualização
                const updateData = {
                    descricao: item.descricao,
                    ativo: newStatus
                }

                await updateSubGroup(item.id, updateData)

                // Atualizar o item na lista local
                const index = subGroup.value.findIndex(n => n.id === item.id)
                if (index !== -1 && subGroup.value[index]) {
                    subGroup.value[index]!.ativo = newStatus
                }

                // Limpar seleção
                selected.value = []

                Notify.create({
                    message: newStatus
                        ? $t('pages.subGroup.messages.activateSuccess')
                        : $t('pages.subGroup.messages.deactivateSuccess'),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } catch {
                // Error handling is done by asyncRequest
            } finally {
                saving.value = false
            }
        })
    })
}

// Delete methods
const confirmDelete = (item: SubGroup) => {
    Dialog.create({
        title: $t('dialogs.confirmDelete.title'),
        message: $t('dialogs.confirmDelete.message', { item: item.descricao }),
        cancel: true,
        persistent: true,
        ok: {
            label: $t('buttons.delete'),
            color: 'negative'
        }
    }).onOk(async () => {
        await setLoading(async () => {
            try {
                await deleteSubGroup(item.id)
                loadSubGroup()

                Notify.create({
                    message: $t('pages.subGroup.messages.deleteSuccess'),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } catch {
                Notify.create({
                    message: $t('pages.subGroup.errors.deleteError'),
                    color: 'negative',
                    position: 'top',
                    timeout: 3000,
                    icon: 'error'
                })
            }
        })
    })
}

const confirmDeleteMultiple = () => {
    Dialog.create({
        title: $t('dialogs.confirmDeleteMultiple.title'),
        message: $t('dialogs.confirmDeleteMultiple.message', {
            count: selected.value.length
        }),
        cancel: true,
        persistent: true,
        ok: {
            label: $t('buttons.delete'),
            color: 'negative'
        }
    }).onOk(async () => {
        await setLoading(async () => {
            try {
                const ids = selected.value.map(item => item.id)
                await deleteMultipleSubGroup(ids)
                selected.value = []
                loadSubGroup()

                Notify.create({
                    message: $t(
                        'pages.subGroup.messages.deleteMultipleSuccess'
                    ),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } catch {
                Notify.create({
                    message: $t('pages.subGroup.errors.deleteMultipleError'),
                    color: 'negative',
                    position: 'top',
                    timeout: 3000,
                    icon: 'error'
                })
            }
        })
    })
}

// Inline edit handler
const handleInlineEdit = async (data: {
    row: Record<string, unknown>
    column: string
    value: unknown
    oldValue: unknown
}) => {
    try {
        const subGroupItem = data.row as unknown as SubGroup
        const updateData = {
            descricao: subGroupItem.descricao,
            ativo: subGroupItem.ativo,
            [data.column]: data.value
        }

        // Fazer a chamada para a API
        await updateSubGroup(subGroupItem.id, updateData)

        // Atualizar os dados locais
        const index = subGroup.value.findIndex(b => b.id === subGroupItem.id)
        if (index !== -1) {
            const currentItem = subGroup.value[index]
            if (currentItem) {
                const updatedItem: SubGroup = {
                    id: currentItem.id,
                    descricao: currentItem.descricao,
                    ativo: currentItem.ativo
                }

                if (data.column === 'descricao') {
                    updatedItem.descricao = data.value as string
                }

                subGroup.value[index] = updatedItem
            }
        }

        Notify.create({
            message: $t('pages.subGroup.messages.updateSuccess'),
            color: 'positive',
            position: 'top',
            timeout: 2000,
            icon: 'check_circle'
        })
    } catch {
        Notify.create({
            message: $t('pages.subGroup.errors.updateError'),
            color: 'negative',
            position: 'top',
            timeout: 3000,
            icon: 'error'
        })

        // Recarregar dados para reverter a mudança visual
        loadSubGroup()
    }
}

// Lifecycle
onMounted(() => {
    loadSubGroup()
})
</script>

<style scoped>
.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Estilos para toolbar compacto */
.toolbar-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    flex-wrap: wrap;
}

.toolbar-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
}

/* Responsividade para telas menores */
@media (max-width: 768px) {
    .toolbar-container {
        justify-content: center;
        gap: 6px;
    }

    .toolbar-buttons {
        justify-content: center;
        gap: 6px;
    }
}
</style>
