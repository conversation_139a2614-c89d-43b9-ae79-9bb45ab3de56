<template>
    <crud-template
        table-name="model"
        :columns="columns"
        :rows="models"
        :loading="loading"
        :filter="tableFilter"
        :pagination="pagination"
        :toolbar-actions="toolbarActions"
        :selected-items="selected"
        :show-modal="showModal"
        :modal-title="modalTitle"
        :modal-saving="saving"
        :filter-columns="filterableColumns"
        v-model:quick-filter-value="columnFilter"
        :show-add-button="permissions.canCreate"
        :show-edit-action="permissions.canEdit"
        :show-delete-action="permissions.canDelete"
        :show-clear-button="true"
        @add-clicked="openCreateModal"
        @refresh-clicked="loadModels"
        @table-request="onTableRequest"
        @restore-pagination="onRestorePagination"
        @quick-filter="onColumnFilter"
        @edit-clicked="row => openEditModal(row as unknown as Model)"
        @delete-clicked="row => confirmDelete(row as unknown as Model)"
        @inline-edit="handleInlineEdit"
        @update:show-modal="showModal = $event"
        @modal-close="closeModal"
        @modal-cancel="closeModal"
        @modal-clear="clearForm"
        @modal-save="handleSave"
        @update:selected-items="
            items => (selected = items as unknown as Model[])
        "
        :actions-per-row="3"
    >
        <!-- Ações extras na linha -->
        <template #row-extra-actions="{ row }">
            <q-btn
                :icon="row.ativo ? 'visibility_off' : 'visibility'"
                :color="row.ativo ? 'negative' : 'positive'"
                size="sm"
                flat
                round
                @click="toggleItemStatus(row as Model)"
            >
                <q-tooltip>
                    {{
                        row.ativo
                            ? $t('buttons.deactivate')
                            : $t('buttons.activate')
                    }}
                </q-tooltip>
            </q-btn>
        </template>

        <!-- Conteúdo do modal -->
        <template #modal-content>
            <form-produto-modelo
                ref="formRef"
                :initial-values="formInitialValues"
            />
        </template>
    </crud-template>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Dialog, Notify } from 'quasar'
import CrudTemplate from '@/components/CrudTemplate.vue'
import FormProdutoModelo from '@/components/forms/Estoque/FormProdutoModelo.vue'
import { useAuthStore } from '@/stores/auth'
import { useActionPermissions } from '@/composables/useActionPermissions'
import { useLoadingOverlay } from '@/composables/loadingOverlay'
import type { TableColumn, ActionItem } from '@/types/CrudTemplate'

const { t: $t } = useI18n()
const { setLoading } = useLoadingOverlay()
const { produtoModeloPermissions } = useActionPermissions()
const permissions = produtoModeloPermissions

// Stores
const authStore = useAuthStore()

// Composables

// Types
interface Model {
    id: number
    descricao: string
    ativo: boolean
}

interface TableRequestProps {
    pagination: {
        sortBy: string
        descending: boolean
        page: number
        rowsPerPage: number
        rowsNumber?: number
    }
    filter?:
        | {
              search: string
              ativo: boolean | null
          }
        | undefined
    getCellValue?: (col: unknown, row: unknown) => unknown
}

// Refs
const models = ref<Model[]>([])
const selected = ref<Model[]>([])
const loading = ref(false)
const saving = ref(false)
const showModal = ref(false)
const isEditing = ref(false)
const formRef = ref<InstanceType<typeof FormProdutoModelo> | null>(null)
const currentItem = ref<Model | null>(null)

// Filtros
const columnFilter = ref<{
    column: string
    content: string | number | boolean
} | null>(null)

// Action Collection
const toolbarActions = computed<ActionItem[]>(() => {
    const selectedCount = selected.value.length
    const firstSelected = selected.value[0]
    const isFirstSelectedActive = firstSelected?.ativo

    return [
        {
            label: $t('buttons.editSelected'),
            active: selectedCount === 1 && permissions.value.canEdit,
            icon: 'edit',
            description: $t('buttons.editSelectedDescription'),
            action: () => {
                if (selectedCount === 1 && firstSelected) {
                    openEditModal(firstSelected)
                }
            }
        },
        {
            label:
                selectedCount === 1 && isFirstSelectedActive
                    ? $t('buttons.deactivate')
                    : $t('buttons.activate'),
            active: selectedCount === 1 && permissions.value.canEdit,
            icon:
                selectedCount === 1 && isFirstSelectedActive
                    ? 'visibility_off'
                    : 'visibility',
            description:
                selectedCount === 1 && isFirstSelectedActive
                    ? $t('buttons.deactivateDescription')
                    : $t('buttons.activateDescription'),
            action: () => {
                if (selectedCount === 1 && firstSelected) {
                    toggleItemStatus(firstSelected)
                }
            }
        },
        {
            label: $t('buttons.deleteSelected', { count: selectedCount }),
            active: selectedCount > 0 && permissions.value.canDelete,
            icon: 'delete',
            description: $t('buttons.deleteSelectedDescription'),
            action: confirmDeleteMultiple
        }
    ]
})

// Paginação
const pagination = ref({
    sortBy: 'id',
    descending: false,
    page: 1,
    rowsPerPage: 25,
    rowsNumber: 0
})

// Computed
const filterableColumns = computed(() => [
    {
        label: $t('forms.labels.id'),
        value: 'id',
        field: 'id',
        type: 'number' as const
    },
    {
        label: $t('forms.labels.description'),
        value: 'descricao',
        field: 'descricao',
        type: 'text' as const
    },
    {
        label: $t('forms.labels.status'),
        value: 'ativo',
        field: 'ativo',
        type: 'boolean' as const
    }
])

const tableFilter = computed(() => {
    if (
        columnFilter.value &&
        columnFilter.value.column &&
        columnFilter.value.content !== null &&
        columnFilter.value.content !== undefined
    ) {
        try {
            // Converter valor para string para compatibilidade com q-table
            const content = columnFilter.value.content
            const stringValue =
                typeof content === 'string' ? content : String(content)

            // Só retornar filtro se o valor for válido
            if (
                stringValue &&
                stringValue.trim() !== '' &&
                stringValue !== 'undefined' &&
                stringValue !== 'null'
            ) {
                return {
                    [columnFilter.value.column]: stringValue
                }
            }
        } catch {
            // Ignorar erro e retornar filtro vazio
        }
    }
    return {}
})

const formInitialValues = computed(() => {
    if (isEditing.value && currentItem.value) {
        return {
            descricao: currentItem.value.descricao,
            ativo: currentItem.value.ativo
        }
    }
    return {}
})

const modalTitle = computed(() => {
    return isEditing.value
        ? $t('pages.productModels.editTitle')
        : $t('pages.productModels.createTitle')
})

const clearForm = () => {
    if (formRef.value) {
        formRef.value.resetForm()
    }
}

// Colunas da tabela
const columns: TableColumn[] = [
    {
        name: 'id',
        label: $t('forms.labels.id'),
        field: 'id',
        align: 'left' as const,
        sortable: true,
        order: 1
    },
    {
        name: 'descricao',
        label: $t('forms.labels.description'),
        field: 'descricao',
        align: 'left' as const,
        sortable: true,
        order: 2,
        inlineEdit: permissions.value.canEdit,
        fieldType: 'text'
    },
    {
        name: 'ativo',
        label: $t('forms.labels.status'),
        field: 'ativo',
        align: 'center' as const,
        sortable: true,
        order: 3
    },
    {
        name: 'actions',
        label: $t('forms.labels.actions'),
        field: 'actions',
        align: 'center' as const,
        sortable: false,
        order: 4
    }
]

// Métodos
const loadModels = async (props: Partial<TableRequestProps> | null = null) => {
    await setLoading(async () => {
        loading.value = true

        const requestPagination = props?.pagination || pagination.value
        const filter = props?.filter || tableFilter.value

        // Construir parâmetros da requisição
        const params: Record<string, unknown> = {
            page: requestPagination.page,
            page_size: requestPagination.rowsPerPage
        }

        // Aplicar filtro de coluna se existir
        if (filter && Object.keys(filter).length > 0) {
            Object.entries(filter).forEach(([key, value]) => {
                if (value !== null && value !== undefined && value !== '') {
                    params[key] = value
                }
            })
        }

        if (requestPagination.sortBy) {
            const sortOrder = requestPagination.descending ? '-' : ''
            params.ordering = `${sortOrder}${requestPagination.sortBy}`
        }

        const response = await authStore.asyncRequest({
            endpoint: '/api/corpsystem/produto-service/modelo',
            method: 'get',
            params,
            customErrorMessages: {
                400: $t('pages.productModels.errors.loadError'),
                404: $t('pages.productModels.errors.loadError'),
                500: $t('pages.productModels.errors.loadError')
            }
        })

        const data = response.data

        models.value = data.results
        pagination.value = {
            ...requestPagination,
            rowsNumber: data.count
        }

        loading.value = false
    })
}

const onTableRequest = (requestProps: Record<string, unknown>) => {
    // Converter o requestProps para o formato esperado
    const props: Partial<TableRequestProps> = {}

    if (requestProps.pagination) {
        props.pagination =
            requestProps.pagination as TableRequestProps['pagination']
    }

    if (requestProps.filter) {
        props.filter = requestProps.filter as TableRequestProps['filter']
    }

    loadModels(props)
}

const onColumnFilter = (
    filterValue: { column: string; content: string | number | boolean } | null
) => {
    columnFilter.value = filterValue
    pagination.value.page = 1
    loadModels()
}

const onRestorePagination = (savedPagination: Record<string, unknown>) => {
    // Restaurar a paginação salva, mas sempre começar na página 1
    pagination.value = {
        ...pagination.value,
        ...savedPagination,
        page: 1 // Sempre começar na página 1
    }
}

// Modal methods
const openCreateModal = () => {
    isEditing.value = false
    currentItem.value = null
    showModal.value = true
}

const openEditModal = (item: Model) => {
    try {
        setLoading(async () => {
            // Fazer requisição para obter os dados completos do item
            const fullItemData = await getModelById(item.id)

            // Atualizar o item atual com os dados completos da API
            currentItem.value = fullItemData
            isEditing.value = true
            showModal.value = true
        })
    } catch {
        currentItem.value = item
        isEditing.value = true
        showModal.value = true
    }
}

const closeModal = () => {
    showModal.value = false
    isEditing.value = false
    currentItem.value = null
}

const handleSave = async () => {
    if (!formRef.value) return

    // Validar o formulário
    const { valid } = await formRef.value.validate()
    if (!valid) {
        Notify.create({
            message: $t('notifications.pleaseFixErrors'),
            color: 'negative',
            position: 'top',
            timeout: 3000,
            icon: 'error'
        })
        return
    }

    // Obter dados do formulário
    const formData = formRef.value.getData()

    await setLoading(async () => {
        try {
            saving.value = true

            if (isEditing.value) {
                await updateModel(currentItem.value!.id, formData)
            } else {
                await createModel(formData)
            }

            closeModal()
            loadModels()

            Notify.create({
                message: isEditing.value
                    ? $t('pages.productModels.messages.updateSuccess')
                    : $t('pages.productModels.messages.createSuccess'),
                color: 'positive',
                position: 'top',
                timeout: 3000,
                icon: 'check_circle'
            })
        } catch {
            // Error handling is done by asyncRequest
        } finally {
            saving.value = false
        }
    })
}

// CRUD methods
const getModelById = async (id: number) => {
    const response = await authStore.asyncRequest({
        endpoint: `/api/corpsystem/produto-service/modelo/${id}`,
        method: 'get',
        customErrorMessages: {
            404: $t('pages.productModels.errors.notFound'),
            500: $t('pages.productModels.errors.loadError')
        }
    })
    return response.data
}

const createModel = async (data: { descricao: string; ativo: boolean }) => {
    const response = await authStore.asyncRequest({
        endpoint: '/api/corpsystem/produto-service/modelo',
        method: 'post',
        params: data,
        customErrorMessages: {
            400: $t('pages.productModels.errors.duplicateDescription'),
            500: $t('pages.productModels.errors.saveError')
        }
    })
    return response.data
}

const updateModel = async (
    id: number,
    data: { descricao: string; ativo: boolean }
) => {
    const response = await authStore.asyncRequest({
        endpoint: `/api/corpsystem/produto-service/modelo/${id}`,
        method: 'put',
        params: data,
        customErrorMessages: {
            400: $t('pages.productModels.errors.saveError'),
            404: $t('pages.productModels.errors.saveError'),
            500: $t('pages.productModels.errors.saveError')
        }
    })
    return response.data
}

const deleteModel = async (id: number) => {
    // Para DELETE, precisamos enviar os parâmetros como query parameters
    const endpoint = `/api/corpsystem/produto-service/modelo/bulk/?id=${id}`

    await authStore.asyncRequest({
        endpoint,
        method: 'delete',
        customErrorMessages: {
            400: $t('pages.productModels.errors.deleteError'),
            404: $t('pages.productModels.errors.deleteError'),
            500: $t('pages.productModels.errors.deleteError')
        }
    })
}

const deleteMultipleModels = async (ids: number[]) => {
    // Para exclusão múltipla, enviar IDs como string separada por vírgula
    // Formato: /bulk/?id=42,39 (conforme curl fornecido)

    if (ids.length === 1) {
        // Se for apenas um ID, usar o método individual
        const firstId = ids[0]
        if (firstId !== undefined) {
            await deleteModel(firstId)
        }
    } else if (ids.length > 1) {
        // Para múltiplos IDs, enviar como string separada por vírgula
        const idsString = ids.join(',')
        const endpoint = `/api/corpsystem/produto-service/modelo/bulk/?id=${encodeURIComponent(idsString)}`

        await authStore.asyncRequest({
            endpoint,
            method: 'delete',
            customErrorMessages: {
                400: $t('pages.productModels.errors.deleteMultipleError'),
                404: $t('pages.productModels.errors.deleteMultipleError'),
                500: $t('pages.productModels.errors.deleteMultipleError')
            }
        })
    }
}

// Toggle status method
const toggleItemStatus = async (item: Model) => {
    const newStatus = !item.ativo
    const actionText = newStatus
        ? $t('buttons.activate')
        : $t('buttons.deactivate')

    Dialog.create({
        title: $t('dialogs.confirmStatusChange.title'),
        message: $t('dialogs.confirmStatusChange.message', {
            item: item.descricao,
            action: actionText.toLowerCase()
        }),
        cancel: true,
        persistent: true,
        ok: {
            label: actionText,
            color: newStatus ? 'positive' : 'negative'
        }
    }).onOk(async () => {
        await setLoading(async () => {
            saving.value = true

            try {
                // Preparar dados para atualização
                const updateData = {
                    descricao: item.descricao,
                    ativo: newStatus
                }

                await updateModel(item.id, updateData)

                // Atualizar o item na lista local
                const index = models.value.findIndex(p => p.id === item.id)
                if (index !== -1 && models.value[index]) {
                    models.value[index]!.ativo = newStatus
                }

                // Limpar seleção
                selected.value = []

                Notify.create({
                    message: newStatus
                        ? $t('pages.productModels.messages.activateSuccess')
                        : $t('pages.productModels.messages.deactivateSuccess'),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } catch {
                // Error handling is done by asyncRequest
            } finally {
                saving.value = false
            }
        })
    })
}

// Delete methods
const confirmDelete = (item: Model) => {
    Dialog.create({
        title: $t('dialogs.confirmDelete.title'),
        message: $t('dialogs.confirmDelete.message', { item: item.descricao }),
        cancel: true,
        persistent: true,
        ok: {
            label: $t('buttons.delete'),
            color: 'negative'
        }
    }).onOk(async () => {
        await setLoading(async () => {
            try {
                await deleteModel(item.id)
                loadModels()

                Notify.create({
                    message: $t('pages.productModels.messages.deleteSuccess'),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } catch {
                // Error handling is done by asyncRequest
            }
        })
    })
}

const confirmDeleteMultiple = () => {
    Dialog.create({
        title: $t('dialogs.confirmDeleteMultiple.title'),
        message: $t('dialogs.confirmDeleteMultiple.message', {
            count: selected.value.length
        }),
        cancel: true,
        persistent: true,
        ok: {
            label: $t('buttons.delete'),
            color: 'negative'
        }
    }).onOk(async () => {
        await setLoading(async () => {
            try {
                const ids = selected.value.map(item => item.id)
                await deleteMultipleModels(ids)
                selected.value = []
                loadModels()

                Notify.create({
                    message: $t(
                        'pages.productModels.messages.deleteMultipleSuccess'
                    ),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } catch {
                // Error handling is done by asyncRequest
            }
        })
    })
}
// Inline edit handler
const handleInlineEdit = async (data: {
    row: Record<string, unknown>
    column: string
    value: unknown
    oldValue: unknown
}) => {
    try {
        const modelItem = data.row as unknown as Model
        const updateData = {
            descricao: modelItem.descricao,
            ativo: modelItem.ativo,
            [data.column]: data.value
        }

        // Fazer a chamada para a API
        await updateModel(modelItem.id, updateData)

        // Atualizar os dados locais
        const index = models.value.findIndex(b => b.id === modelItem.id)
        if (index !== -1) {
            const currentItem = models.value[index]
            if (currentItem) {
                const updatedItem: Model = {
                    id: currentItem.id,
                    descricao: currentItem.descricao,
                    ativo: currentItem.ativo
                }

                if (data.column === 'descricao') {
                    updatedItem.descricao = data.value as string
                }

                models.value[index] = updatedItem
            }
        }

        Notify.create({
            message: $t('pages.productModels.messages.updateSuccess'),
            color: 'positive',
            position: 'top',
            timeout: 2000,
            icon: 'check_circle'
        })
    } catch {
        Notify.create({
            message: $t('pages.productModels.errors.updateError'),
            color: 'negative',
            position: 'top',
            timeout: 3000,
            icon: 'error'
        })

        // Recarregar dados para reverter a mudança visual
        loadModels()
    }
}

// Lifecycle
onMounted(() => {
    loadModels()
})
</script>

<style scoped>
.border-bottom {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Estilos para toolbar compacto */
.toolbar-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    flex-wrap: wrap;
}

.toolbar-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
}

/* Responsividade para telas menores */
@media (max-width: 768px) {
    .toolbar-container {
        justify-content: center;
        gap: 6px;
    }

    .toolbar-buttons {
        justify-content: center;
        gap: 6px;
    }
}
</style>
