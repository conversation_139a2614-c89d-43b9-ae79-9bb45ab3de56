<template>
    <crud-template
        table-name="brand"
        :columns="columns"
        :rows="brand"
        :loading="loading"
        :filter="tableFilter"
        :pagination="pagination"
        :toolbar-actions="toolbarActions"
        :selected-items="selected"
        :show-modal="showModal"
        :modal-title="modalTitle"
        :modal-saving="saving"
        :filter-columns="filterableColumns"
        v-model:quick-filter-value="columnFilter"
        :show-add-button="permissions.canCreate"
        :show-edit-action="permissions.canEdit"
        :show-delete-action="permissions.canDelete"
        :show-clear-button="true"
        @add-clicked="openCreateModal"
        @refresh-clicked="loadBrand"
        @table-request="onTableRequest"
        @restore-pagination="onRestorePagination"
        @quick-filter="onColumnFilter"
        @edit-clicked="row => openEditModal(row as unknown as Brand)"
        @delete-clicked="row => confirmDelete(row as unknown as Brand)"
        @inline-edit="handleInlineEdit"
        @update:show-modal="showModal = $event"
        @modal-close="closeModal"
        @modal-cancel="closeModal"
        @modal-clear="clearForm"
        @modal-save="handleSave"
        @update:selected-items="
            items => (selected = items as unknown as Brand[])
        "
        :actions-per-row="4"
    >
        <!-- Ações extras na linha -->
        <template #row-extra-actions="{ row }">
            <q-btn
                icon="link"
                color="info"
                flat
                round
                dense
                size="sm"
                :disable="!permissions.canLink"
                @click="openBrandModelModal(row as unknown as Brand)"
            >
                <q-tooltip>{{ $t('buttons.linkModel') }}</q-tooltip>
            </q-btn>

            <q-btn
                :icon="row.ativo ? 'visibility_off' : 'visibility'"
                :color="row.ativo ? 'negative' : 'positive'"
                size="sm"
                flat
                round
                :disable="!permissions.canEdit"
                @click="toggleItemStatus(row as Brand)"
            >
                <q-tooltip>
                    {{
                        row.ativo
                            ? $t('buttons.deactivate')
                            : $t('buttons.activate')
                    }}
                </q-tooltip>
            </q-btn>
        </template>

        <!-- Conteúdo do modal -->
        <template #modal-content>
            <FormProdutoMarca
                ref="formRef"
                :initial-values="formInitialValues"
            />
        </template>

        <!-- Modais extras -->
        <template #extra-modals>
            <!-- Modal Marca x Modelo -->
            <marca-modelo-modal
                v-model="showMarcaModeloModal"
                :selected-marca="selectedMarcaForModal"
                @success="onMarcaModeloSuccess"
            />
        </template>
    </crud-template>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Dialog, Notify } from 'quasar'
import CrudTemplate from '@/components/CrudTemplate.vue'
import FormProdutoMarca from '@/components/forms/Estoque/FormProdutoMarca.vue'
import MarcaModeloModal from '@/components/modals/MarcaModeloModal.vue'
import { useAuthStore } from '@/stores/auth'
import { useLoadingOverlay } from '@/composables/loadingOverlay'
import { useActionPermissions } from '@/composables/useActionPermissions'
import type { TableColumn, ActionItem } from '@/types/CrudTemplate'

const { t: $t } = useI18n()
const { setLoading } = useLoadingOverlay()
const { produtoMarcaPermissions } = useActionPermissions()

// Permissões para esta rota
const permissions = produtoMarcaPermissions

// Types
interface Brand {
    id: number
    descricao: string
    abreviacao: string
    ativo: boolean
}

interface TableRequestProps {
    pagination: {
        sortBy: string
        descending: boolean
        page: number
        rowsPerPage: number
        rowsNumber?: number
    }
    filter?:
        | {
              search: string
              ativo: boolean | null
          }
        | undefined
    getCellValue?: (col: unknown, row: unknown) => unknown
}

// Stores
const authStore = useAuthStore()

// Refs
const brand = ref<Brand[]>([])
const selected = ref<Brand[]>([])
const loading = ref(false)
const saving = ref(false)
const showModal = ref(false)
const isEditing = ref(false)
const formRef = ref<InstanceType<typeof FormProdutoMarca> | null>(null)
const currentItem = ref<Brand | null>(null)

// Modal Marca x Modelo
const showMarcaModeloModal = ref(false)
const selectedMarcaForModal = ref<{ id: number; descricao: string }>({
    id: 0,
    descricao: ''
})

// Filtros
const columnFilter = ref<{
    column: string
    content: string | number | boolean
} | null>(null)

// Action Collection
const toolbarActions = computed<ActionItem[]>(() => {
    const selectedCount = selected.value.length
    const firstSelected = selected.value[0]
    const isFirstSelectedActive = firstSelected?.ativo

    return [
        {
            label: $t('buttons.deleteSelected', { count: selectedCount }),
            active: selectedCount > 0 && permissions.value.canDelete,
            icon: 'delete',
            description: $t('buttons.deleteSelectedDescription'),
            action: confirmDeleteMultiple
        },
        {
            label: $t('buttons.linkModelToSelected'),
            active: selectedCount === 1,
            icon: 'link',
            description: $t('buttons.linkModelDescription'),
            action: () => {
                if (selectedCount === 1 && firstSelected) {
                    openBrandModelModal(firstSelected)
                }
            }
        },
        {
            label:
                selectedCount === 1 && isFirstSelectedActive
                    ? $t('buttons.deactivate')
                    : $t('buttons.activate'),
            active: selectedCount === 1 && permissions.value.canEdit,
            icon:
                selectedCount === 1 && isFirstSelectedActive
                    ? 'visibility_off'
                    : 'visibility',
            description:
                selectedCount === 1 && isFirstSelectedActive
                    ? $t('buttons.deactivateDescription')
                    : $t('buttons.activateDescription'),
            action: () => {
                if (selectedCount === 1 && firstSelected) {
                    toggleItemStatus(firstSelected)
                }
            }
        }
    ]
})

// Paginação
const pagination = ref({
    sortBy: 'id',
    descending: false,
    page: 1,
    rowsPerPage: 25,
    rowsNumber: 0
})

// Computed
const filterableColumns = computed(() => [
    {
        label: $t('forms.labels.id'),
        value: 'id',
        field: 'id',
        type: 'number' as const
    },
    {
        label: $t('forms.labels.description'),
        value: 'descricao',
        field: 'descricao',
        type: 'text' as const
    },
    {
        label: $t('forms.labels.abbreviation'),
        value: 'abreviacao',
        field: 'abreviacao',
        type: 'text' as const
    },
    {
        label: $t('forms.labels.status'),
        value: 'ativo',
        field: 'ativo',
        type: 'boolean' as const
    }
])

const tableFilter = computed(() => {
    if (
        columnFilter.value &&
        columnFilter.value.column &&
        columnFilter.value.content !== null &&
        columnFilter.value.content !== undefined
    ) {
        try {
            // Converter valor para string para compatibilidade com q-table
            const content = columnFilter.value.content
            const stringValue =
                typeof content === 'string' ? content : String(content)

            // Só retornar filtro se o valor for válido
            if (
                stringValue &&
                stringValue.trim() !== '' &&
                stringValue !== 'undefined' &&
                stringValue !== 'null'
            ) {
                return {
                    [columnFilter.value.column]: stringValue
                }
            }
        } catch {
            // Ignorar erro e retornar filtro vazio
        }
    }
    return {}
})

const formInitialValues = computed(() => {
    if (isEditing.value && currentItem.value) {
        return {
            descricao: currentItem.value.descricao,
            abreviacao: currentItem.value.abreviacao,
            ativo: currentItem.value.ativo
        }
    }
    return {}
})

const modalTitle = computed(() => {
    return isEditing.value
        ? $t('pages.productBrands.editTitle')
        : $t('pages.productBrands.createTitle')
})

const clearForm = () => {
    if (formRef.value) {
        formRef.value.resetForm()
    }
}

// Colunas da tabela
const columns: TableColumn[] = [
    {
        name: 'id',
        label: $t('forms.labels.id'),
        field: 'id',
        align: 'left' as const,
        sortable: true,
        order: 1
    },
    {
        name: 'descricao',
        label: $t('forms.labels.description'),
        field: 'descricao',
        align: 'left' as const,
        sortable: true,
        order: 2,
        inlineEdit: permissions.value.canEdit,
        fieldType: 'text'
    },
    {
        name: 'abreviacao',
        label: $t('forms.labels.abbreviation'),
        field: 'abreviacao',
        align: 'left' as const,
        sortable: true,
        order: 3,
        inlineEdit: permissions.value.canEdit,
        fieldType: 'text'
    },
    {
        name: 'ativo',
        label: $t('forms.labels.status'),
        field: 'ativo',
        align: 'center' as const,
        sortable: true,
        order: 4
    },
    {
        name: 'actions',
        label: $t('forms.labels.actions'),
        field: 'actions',
        align: 'center' as const,
        sortable: false,
        order: 5
    }
]

// Métodos
const loadBrand = async (props: Partial<TableRequestProps> | null = null) => {
    await setLoading(async () => {
        loading.value = true

        const requestPagination = props?.pagination || pagination.value
        const filter = props?.filter || tableFilter.value

        // Construir parâmetros da requisição
        const params: Record<string, unknown> = {
            page: requestPagination.page,
            page_size: requestPagination.rowsPerPage
        }

        // Aplicar filtro de coluna se existir
        if (filter && Object.keys(filter).length > 0) {
            Object.entries(filter).forEach(([key, value]) => {
                if (value !== null && value !== undefined && value !== '') {
                    params[key] = value
                }
            })
        }

        if (requestPagination.sortBy) {
            const sortOrder = requestPagination.descending ? '-' : ''
            params.ordering = `${sortOrder}${requestPagination.sortBy}`
        }

        const response = await authStore.asyncRequest({
            endpoint: '/api/corpsystem/produto-service/produto-marca',
            method: 'get',
            params,
            customErrorMessages: {
                400: $t('pages.productBrands.errors.loadError'),
                404: $t('pages.productBrands.errors.loadError'),
                500: $t('pages.productBrands.errors.loadError')
            }
        })

        const data = response.data

        brand.value = data.results
        pagination.value = {
            ...requestPagination,
            rowsNumber: data.count
        }

        loading.value = false
    })
}

const onTableRequest = (requestProps: Record<string, unknown>) => {
    // Converter o requestProps para o formato esperado
    const props: Partial<TableRequestProps> = {}

    if (requestProps.pagination) {
        props.pagination =
            requestProps.pagination as TableRequestProps['pagination']
    }

    if (requestProps.filter) {
        props.filter = requestProps.filter as TableRequestProps['filter']
    }

    loadBrand(props)
}

const onColumnFilter = (
    filterValue: { column: string; content: string | number | boolean } | null
) => {
    columnFilter.value = filterValue
    pagination.value.page = 1
    loadBrand()
}

const onRestorePagination = (savedPagination: Record<string, unknown>) => {
    // Restaurar a paginação salva, mas sempre começar na página 1
    pagination.value = {
        ...pagination.value,
        ...savedPagination,
        page: 1 // Sempre começar na página 1
    }
}

// Modal methods
const openCreateModal = () => {
    isEditing.value = false
    currentItem.value = null
    showModal.value = true
}

const openEditModal = (item: Brand) => {
    try {
        setLoading(async () => {
            // Fazer requisição para obter os dados completos do item
            const fullItemData = await getBrandById(item.id)

            // Atualizar o item atual com os dados completos da API
            currentItem.value = fullItemData
            isEditing.value = true
            showModal.value = true
        })
    } catch {
        currentItem.value = item
        isEditing.value = true
        showModal.value = true
    }
}

const closeModal = () => {
    showModal.value = false
    isEditing.value = false
    currentItem.value = null
}

const handleSave = async () => {
    if (!formRef.value) return

    // Validar o formulário
    const { valid } = await formRef.value.validate()
    if (!valid) {
        Notify.create({
            message: $t('notifications.pleaseFixErrors'),
            color: 'negative',
            position: 'top',
            timeout: 3000,
            icon: 'error'
        })
        return
    }

    // Obter dados do formulário
    const formData = formRef.value.getData()

    await setLoading(async () => {
        saving.value = true

        try {
            if (isEditing.value) {
                await updateBrand(currentItem.value!.id, formData)
            } else {
                await createBrand(formData)
            }

            closeModal()
            loadBrand()

            Notify.create({
                message: isEditing.value
                    ? $t('pages.productBrands.messages.updateSuccess')
                    : $t('pages.productBrands.messages.createSuccess'),
                color: 'positive',
                position: 'top',
                timeout: 3000,
                icon: 'check_circle'
            })
        } catch {
            // Error handling is done by asyncRequest
        } finally {
            saving.value = false
        }
    })
}

// CRUD methods
const getBrandById = async (id: number) => {
    const response = await authStore.asyncRequest({
        endpoint: `/api/corpsystem/produto-service/produto-marca/${id}`,
        method: 'get',
        customErrorMessages: {
            404: $t('pages.productBrands.errors.notFound'),
            500: $t('pages.productBrands.errors.loadError')
        }
    })
    return response.data
}

const createBrand = async (data: {
    descricao: string
    abreviacao: string
    ativo: boolean
}) => {
    const response = await authStore.asyncRequest({
        endpoint: '/api/corpsystem/produto-service/produto-marca',
        method: 'post',
        params: data,
        customErrorMessages: {
            400: $t('pages.productBrands.errors.duplicateDescription'),
            500: $t('pages.productBrands.errors.saveError')
        }
    })
    return response.data
}

const updateBrand = async (
    id: number,
    data: {
        descricao: string
        abreviacao: string
        ativo: boolean
    }
) => {
    const response = await authStore.asyncRequest({
        endpoint: `/api/corpsystem/produto-service/produto-marca/${id}`,
        method: 'put',
        params: data,
        customErrorMessages: {
            400: $t('pages.productBrands.errors.duplicateDescription'),
            404: $t('pages.productBrands.errors.saveError'),
            500: $t('pages.productBrands.errors.saveError')
        }
    })
    return response.data
}

const deleteBrand = async (id: number) => {
    await authStore.asyncRequest({
        endpoint: `/api/corpsystem/produto-service/produto-marca/bulk/?id=${id}`,
        method: 'delete',
        customErrorMessages: {
            400: $t('pages.productBrands.errors.deleteError'),
            404: $t('pages.productBrands.errors.deleteError'),
            500: $t('pages.productBrands.errors.deleteError')
        }
    })
}

const deleteMultipleBrands = async (ids: number[]) => {
    // Para exclusão múltipla, enviar IDs como string separada por vírgula
    // Formato: /bulk/?id=42,39 (conforme curl fornecido)

    if (ids.length === 1) {
        // Se for apenas um ID, usar o método individual
        const firstId = ids[0]
        if (firstId !== undefined) {
            await deleteBrand(firstId)
        }
    } else if (ids.length > 1) {
        // Para múltiplos IDs, enviar como string separada por vírgula
        const idsString = ids.join(',')
        const endpoint = `/api/corpsystem/produto-service/produto-marca/bulk/?id=${encodeURIComponent(idsString)}`

        await authStore.asyncRequest({
            endpoint,
            method: 'delete',
            customErrorMessages: {
                400: $t('pages.productBrands.errors.deleteMultipleError'),
                404: $t('pages.productBrands.errors.deleteMultipleError'),
                500: $t('pages.productBrands.errors.deleteMultipleError')
            }
        })
    }
}

// Toggle status method
const toggleItemStatus = async (item: Brand) => {
    const newStatus = !item.ativo
    const actionText = newStatus
        ? $t('buttons.activate')
        : $t('buttons.deactivate')

    Dialog.create({
        title: $t('dialogs.confirmStatusChange.title'),
        message: $t('dialogs.confirmStatusChange.message', {
            item: item.descricao,
            action: actionText.toLowerCase()
        }),
        cancel: true,
        persistent: true,
        ok: {
            label: actionText,
            color: newStatus ? 'positive' : 'negative'
        }
    }).onOk(async () => {
        await setLoading(async () => {
            saving.value = true

            try {
                // Preparar dados para atualização
                const updateData = {
                    descricao: item.descricao,
                    abreviacao: item.abreviacao,
                    ativo: newStatus
                }

                await updateBrand(item.id, updateData)

                // Atualizar o item na lista local
                const index = brand.value.findIndex(m => m.id === item.id)
                if (index !== -1 && brand.value[index]) {
                    brand.value[index]!.ativo = newStatus
                }

                // Limpar seleção
                selected.value = []

                Notify.create({
                    message: newStatus
                        ? $t('pages.productBrands.messages.activateSuccess')
                        : $t('pages.productBrands.messages.deactivateSuccess'),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } catch {
                // Error handling is done by asyncRequest
            } finally {
                saving.value = false
            }
        })
    })
}

// Delete methods
const confirmDelete = (item: Brand) => {
    Dialog.create({
        title: $t('dialogs.confirmDelete.title'),
        message: $t('dialogs.confirmDelete.message', {
            item: `${item.descricao} (${item.abreviacao})`
        }),
        cancel: true,
        persistent: true,
        ok: {
            label: $t('buttons.delete'),
            color: 'negative'
        }
    }).onOk(async () => {
        await setLoading(async () => {
            try {
                await deleteBrand(item.id)
                loadBrand()

                Notify.create({
                    message: $t('pages.productBrands.messages.deleteSuccess'),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } catch {
                Notify.create({
                    message: $t('pages.productBrands.errors.deleteError'),
                    color: 'negative',
                    position: 'top',
                    timeout: 3000,
                    icon: 'error'
                })
            }
        })
    })
}

const confirmDeleteMultiple = () => {
    Dialog.create({
        title: $t('dialogs.confirmDeleteMultiple.title'),
        message: $t('dialogs.confirmDeleteMultiple.message', {
            count: selected.value.length
        }),
        cancel: true,
        persistent: true,
        ok: {
            label: $t('buttons.delete'),
            color: 'negative'
        }
    }).onOk(async () => {
        await setLoading(async () => {
            try {
                const ids = selected.value.map(item => item.id)
                await deleteMultipleBrands(ids)
                selected.value = []
                loadBrand()

                Notify.create({
                    message: $t(
                        'pages.productBrands.messages.deleteMultipleSuccess'
                    ),
                    color: 'positive',
                    position: 'top',
                    timeout: 3000,
                    icon: 'check_circle'
                })
            } catch {
                Notify.create({
                    message: $t(
                        'pages.productBrands.errors.deleteMultipleError'
                    ),
                    color: 'negative',
                    position: 'top',
                    timeout: 3000,
                    icon: 'error'
                })
            }
        })
    })
}

// Modal Marca x Modelo methods
const openBrandModelModal = (marca: Brand) => {
    selectedMarcaForModal.value = {
        id: marca.id,
        descricao: marca.descricao
    }
    showMarcaModeloModal.value = true
}

const onMarcaModeloSuccess = () => {
    Notify.create({
        message: $t('modals.marcaModelo.messages.linkSuccess'),
        color: 'positive',
        position: 'top',
        timeout: 3000,
        icon: 'check_circle'
    })
}

// Inline edit handler
const handleInlineEdit = async (data: {
    row: Record<string, unknown>
    column: string
    value: unknown
    oldValue: unknown
}) => {
    try {
        const brandItem = data.row as unknown as Brand
        const updateData = {
            descricao: brandItem.descricao,
            abreviacao: brandItem.abreviacao,
            ativo: brandItem.ativo,
            [data.column]: data.value
        }

        // Fazer a chamada para a API
        await updateBrand(brandItem.id, updateData)

        // Atualizar os dados locais
        const index = brand.value.findIndex(b => b.id === brandItem.id)
        if (index !== -1) {
            const currentItem = brand.value[index]
            if (currentItem) {
                const updatedItem: Brand = {
                    id: currentItem.id,
                    descricao: currentItem.descricao,
                    abreviacao: currentItem.abreviacao,
                    ativo: currentItem.ativo
                }

                if (data.column === 'descricao') {
                    updatedItem.descricao = data.value as string
                } else if (data.column === 'abreviacao') {
                    updatedItem.abreviacao = data.value as string
                }

                brand.value[index] = updatedItem
            }
        }

        Notify.create({
            message: $t('pages.productBrands.messages.updateSuccess'),
            color: 'positive',
            position: 'top',
            timeout: 2000,
            icon: 'check_circle'
        })
    } catch {
        Notify.create({
            message: $t('pages.productBrands.errors.updateError'),
            color: 'negative',
            position: 'top',
            timeout: 3000,
            icon: 'error'
        })

        // Recarregar dados para reverter a mudança visual
        loadBrand()
    }
}

// Lifecycle
onMounted(() => {
    loadBrand()
})
</script>

<style scoped>
.border-bottom {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Estilos para toolbar compacto */
.toolbar-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    flex-wrap: wrap;
}

.toolbar-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
}

/* Responsividade para telas menores */
@media (max-width: 768px) {
    .toolbar-container {
        justify-content: center;
        gap: 6px;
    }

    .toolbar-buttons {
        justify-content: center;
        gap: 6px;
    }
}
</style>
