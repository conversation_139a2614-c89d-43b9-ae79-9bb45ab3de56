<template>
    <div id="usuario-page" class="q-pa-xs" style="width: 100%">
        <div class="text-h6 q-mb-xs">
            {{ $t('forms.titles.userRegistration') }}
        </div>

        <q-separator />

        <q-tabs
            v-model="tab"
            dense
            class="text-grey"
            :active-color="isDarkMode ? 'white' : 'primary'"
            align="left"
            narrow-indicator
            inline-label
        >
            <q-tab
                name="basic"
                :label="$t('forms.titles.basicInfo')"
                icon="fas fa-user"
            />
            <q-tab
                name="access"
                :label="$t('forms.titles.accessAndPermissions')"
                icon="fas fa-lock"
            />
            <q-tab
                name="restrictions"
                :label="$t('forms.titles.restrictions')"
                icon="fas fa-ban"
            />
            <q-tab
                name="email"
                :label="$t('forms.titles.emailConfigs')"
                icon="fas fa-envelope"
            />
        </q-tabs>

        <q-separator />

        <q-tab-panels v-model="tab" animated>
            <q-tab-panel name="basic"></q-tab-panel>

            <q-tab-panel name="access"></q-tab-panel>
        </q-tab-panels>
        <q-btn
            :label="$t('buttons.confirm')"
            color="primary"
            class="full-width q-mt-md"
            @click="handleSubmit"
        />
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref } from 'vue'
// Composables
import { useFormUtils } from 'src/composables/formUtils'
const { validateAll, notifyFieldsErrors } = useFormUtils()
import { useTheme } from '@/composables/useTheme'
const { isDarkMode } = useTheme()
// Components

// Tabs
const tab = ref('basic')
// Basic Info
//const formBasicInfoRef = ref<InstanceType<typeof FormBasicInfo> | null>(null)

const handleSubmit = async () => {
    const { isValid, errors } = await validateAll([
        //formBasicInfoRef.value!.validate
    ])
    if (isValid) {
        // console.log(formBasicInfoRef.value!.getData())
    } else {
        notifyFieldsErrors(errors)
    }
}
</script>
