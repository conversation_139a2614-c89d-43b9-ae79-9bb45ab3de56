<template>
    <router-view />
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useTheme } from './composables/useTheme'
import { useLocaleStore } from './stores/LangStore'
import { usePageReload } from './composables/usePageReload'

const { loadTheme } = useTheme()
const { loadLocale } = useLocaleStore()

// Configurar recarregamento automático após F5
usePageReload()

const loadUserConfig = () => {
    loadTheme()
    loadLocale()
}

onMounted(loadUserConfig)
</script>
