import { defineRouter } from '#q-app/wrappers'
import {
    createMemoryHistory,
    createRouter,
    createWebHashHistory,
    createWebHistory
} from 'vue-router'
import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useRoutePermissions } from '@/composables/useRoutePermissions'
import routes from './routes'

/*
 * If not building with SSR mode, you can
 * directly export the Router instantiation;
 *
 * The function below can be async too; either use
 * async/await or return a Promise which resolves
 * with the Router instance.
 */

export default defineRouter(function (/* { store, ssrContext } */) {
    const createHistory = process.env.SERVER
        ? createMemoryHistory
        : process.env.VUE_ROUTER_MODE === 'history'
          ? createWebHistory
          : createWebHashHistory

    const Router = createRouter({
        scrollBehavior: () => ({ left: 0, top: 0 }),
        routes,

        // Leave this as is and make changes in quasar.conf.js instead!
        // quasar.conf.js -> build -> vueRouterMode
        // quasar.conf.js -> build -> publicPath
        history: createHistory(process.env.VUE_ROUTER_BASE)
    })

    // Guard de autenticação e permissões
    Router.beforeEach(
        async (
            to: RouteLocationNormalized,
            _from: RouteLocationNormalized,
            next: NavigationGuardNext
        ) => {
            const authStore = useAuthStore()
            const { canAccessRoute, requiresAuth, applyKnownRouteIds } =
                useRoutePermissions()

            // Aplicar IDs conhecidos na primeira navegação (apenas uma vez)
            if (
                !(Router as { hasAppliedKnownIds?: boolean }).hasAppliedKnownIds
            ) {
                applyKnownRouteIds()
                ;(
                    Router as { hasAppliedKnownIds?: boolean }
                ).hasAppliedKnownIds = true
            }

            // Recarregar dados do usuário se estiver autenticado (útil após F5)
            if (
                authStore.token &&
                authStore.user?.profile?.id &&
                !authStore.isLoading
            ) {
                // Verificar se as permissões estão carregadas
                const permissions = authStore.getUserPermissions()
                if (!permissions) {
                    try {
                        await authStore.reloadUserData()
                    } catch {
                        // Se falhar ao recarregar, continuar navegação
                    }
                }
            }

            // Verificar se a rota requer autenticação
            if (requiresAuth(to.path) && !authStore.token) {
                // Redirecionar para login se não estiver autenticado
                next('/login')
                return
            }

            // Se estiver na página de login e já estiver autenticado, redirecionar para home
            if (to.path === '/login' && authStore.token) {
                next('/')
                return
            }

            // Verificar permissões de acesso à rota
            if (!canAccessRoute(to.path)) {
                // Redirecionar para home se não tiver permissão
                next('/')
                return
            }

            // Permitir navegação
            next()
        }
    )

    return Router
})
