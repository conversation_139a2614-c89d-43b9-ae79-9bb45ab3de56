import type { RouteRecordRaw } from 'vue-router'

const mainLayoutComponent = () => import('layouts/MainLayout.vue')
const routes: RouteRecordRaw[] = [
    {
        path: '/',
        component: mainLayoutComponent,
        children: [{ path: '', component: () => import('pages/HomePage.vue') }],
        meta: {
            title: 'home'
        }
    },
    {
        path: '/login',
        component: () => import('src/layouts/LoginLayout.vue'),
        children: [
            {
                path: '',
                component: () => import('src/pages/Login/LoginPage.vue')
            }
        ],
        meta: {
            title: 'login'
        }
    },
    {
        path: '/profile',
        component: mainLayoutComponent,
        children: [
            {
                path: '',
                component: () => import('pages/Usuario/UsuarioPage.vue')
            }
        ],
        meta: {
            title: 'profile'
        }
    },

    // Principal
    {
        path: '/principal',
        component: mainLayoutComponent,
        children: []
    },
    // Cadastro Geral
    {
        path: '/cadastro-geral',
        component: mainLayoutComponent,
        children: [
            {
                path: '',
                redirect: '/'
            },
            {
                path: 'textos-padronizados',
                component: () =>
                    import('pages/CadastroGeral/TextosPadronizadosPage.vue'),
                meta: {
                    title: 'standardizedTexts'
                }
            },
            {
                path: 'ncm',
                component: () => import('pages/CadastroGeral/NcmPage.vue'),
                meta: {
                    title: 'ncm'
                }
            },
            {
                path: 'pais',
                component: () => import('pages/CadastroGeral/PaisPage.vue'),
                meta: {
                    title: 'country'
                }
            },
            {
                path: 'categoria-cnh',
                component: () =>
                    import('pages/CadastroGeral/CatergoriaCNHPage.vue'),
                meta: {
                    title: 'categoryCNH'
                }
            },
            {
                path: 'estado',
                component: () => import('pages/CadastroGeral/EstadoPage.vue'),
                meta: {
                    title: 'state'
                }
            },
            {
                path: 'cidade',
                component: () => import('pages/CadastroGeral/CidadePage.vue'),
                meta: {
                    title: 'city'
                }
            },
            {
                path: 'regiao',
                component: () => import('pages/CadastroGeral/RegiaoPage.vue'),
                meta: {
                    title: 'region'
                }
            },
            {
                path: 'tipo-endereco',
                component: () =>
                    import('pages/CadastroGeral/TipoEnderecoPage.vue'),
                meta: {
                    title: 'addressType'
                }
            },
            {
                path: 'tipo-documento',
                component: () =>
                    import('pages/CadastroGeral/TipoDocumentoPage.vue'),
                meta: {
                    title: 'documentType'
                }
            },
            {
                path: 'grupo-forma-pagamento',
                component: () =>
                    import('pages/CadastroGeral/GrupoFormaPagamentoPage.vue'),
                meta: {
                    title: 'paymentGroup'
                }
            },
            {
                path: 'ramo-atividade',
                component: () =>
                    import('pages/CadastroGeral/RamoAtividadePage.vue'),
                meta: {
                    title: 'activitySector'
                }
            },
            {
                path: 'historico-lancamento',
                component: () =>
                    import('pages/CadastroGeral/HistoricoLancamentoPage.vue'),
                meta: {
                    title: 'historicLaunch'
                }
            }
        ]
    },
    // Crm
    {
        path: '/crm',
        component: mainLayoutComponent,
        children: [
            {
                path: '',
                redirect: '/'
            },
            {
                path: 'origem',
                component: () => import('pages/Crm/OrigemPage.vue'),
                meta: {
                    title: 'origin'
                }
            },
            {
                path: 'oportunidade',
                component: () => import('pages/Crm/OportunidadePage.vue'),
                meta: {
                    title: 'opportunity'
                }
            }
        ]
    },
    // Estoque
    {
        path: '/estoque',
        component: mainLayoutComponent,
        children: [
            {
                path: '',
                redirect: '/'
            },
            {
                path: 'produto-familia',
                component: () => import('pages/Estoque/ProdutoFamiliaPage.vue'),
                meta: {
                    title: 'family'
                }
            },
            {
                path: 'produto-grupo',
                component: () => import('pages/Estoque/ProdutoGrupoPage.vue'),
                meta: {
                    title: 'group'
                }
            },
            {
                path: 'sub-grupo',
                component: () => import('pages/Estoque/SubGrupoPage.vue'),
                meta: {
                    title: 'subgroup'
                }
            },
            {
                path: 'produto-marca',
                component: () => import('pages/Estoque/ProdutoMarcaPage.vue'),
                meta: {
                    title: 'productBrand'
                }
            },
            {
                path: 'modelo',
                component: () => import('pages/Estoque/ProdutoModeloPage.vue'),
                meta: {
                    title: 'productModel'
                }
            }
        ]
    },
    // Venda
    {
        path: '/venda',
        component: mainLayoutComponent,
        children: [
            {
                path: '',
                redirect: '/'
            },
            {
                path: 'categoria-cliente',
                component: () => import('pages/Venda/CategoriaClientePage.vue'),
                meta: {
                    title: 'customerCategory'
                }
            },
            {
                path: 'grupo-desconto',
                component: () => import('pages/Venda/GrupoDescontoPage.vue'),
                meta: {
                    title: 'discountGroup'
                }
            }
        ]
    },
    {
        path: '/projeto',
        component: mainLayoutComponent,
        children: [
            {
                path: '',
                redirect: '/'
            },
            {
                path: 'aplicacao',
                component: () => import('pages/Projeto/AplicacaoPage.vue'),
                meta: {
                    title: 'application'
                }
            },
            {
                path: 'tipo-instalacao',
                component: () => import('pages/Projeto/TipoInstalacaoPage.vue'),
                meta: {
                    title: 'installationType'
                }
            },
            {
                path: 'tipo-perfil',
                component: () => import('pages/Projeto/TipoPerfilPage.vue'),
                meta: {
                    title: 'profileType'
                }
            },
            {
                path: 'tipo-material',
                component: () => import('pages/Projeto/TipoMaterialPage.vue'),
                meta: {
                    title: 'materialType'
                }
            },
            {
                path: 'grupo-projeto',
                component: () => import('pages/Projeto/GrupoProjetoPage.vue'),
                meta: {
                    title: 'projectGroup'
                }
            },
            {
                path: 'grupo-material',
                component: () => import('pages/Projeto/GrupoMaterialPage.vue'),
                meta: {
                    title: 'materialGroup'
                }
            }
        ]
    },
    // OS
    {
        path: '/os',
        component: mainLayoutComponent,
        children: [
            {
                path: '',
                redirect: '/'
            },
            {
                path: 'titulo',
                component: () => import('pages/OS/TituloPage.vue'),
                meta: {
                    title: 'title'
                }
            },
            {
                path: 'parte-equipamento',
                component: () => import('pages/OS/ParteEquipamentoPage.vue'),
                meta: {
                    title: 'equipmentPart'
                }
            },
            {
                path: 'defeito',
                component: () => import('pages/OS/DefeitoPage.vue'),
                meta: {
                    title: 'defect'
                }
            },
            {
                path: 'causa',
                component: () => import('pages/GeneralModules/CausaPage.vue'),
                meta: {
                    title: 'cause'
                }
            },
            {
                path: 'familia-equipamento',
                component: () => import('pages/OS/FamiliaEquipamentoPage.vue'),
                meta: {
                    title: 'equipmentFamily'
                }
            }
        ]
    },

    // Financeiro
    {
        path: '/financeiro',
        component: mainLayoutComponent,
        children: [
            {
                path: '',
                redirect: '/'
            },
            {
                path: 'carteira-cobranca',
                component: () =>
                    import('pages/Financeiro/CarteiraCobrancaPage.vue'),
                meta: {
                    title: 'billingPortfolio'
                }
            }
        ]
    },

    // Produção
    {
        path: '/producao',
        component: mainLayoutComponent,
        children: [
            {
                path: '',
                redirect: '/'
            },
            {
                path: 'checklist',
                component: () => import('pages/Producao/ChecklistPage.vue'),
                meta: {
                    title: 'checklist'
                }
            }
        ]
    },

    // Qualidade
    {
        path: '/qualidade',
        component: mainLayoutComponent,
        children: [
            {
                path: '',
                redirect: '/'
            },
            {
                path: 'operacao',
                component: () => import('pages/Qualidade/OperacaoPage.vue'),
                meta: {
                    title: 'operation'
                }
            },
            {
                path: 'nao-conformidade',
                component: () =>
                    import('pages/Qualidade/NaoConformidadePage.vue'),
                meta: {
                    title: 'nonConformity'
                }
            },
            {
                path: 'causa',
                component: () => import('pages/GeneralModules/CausaPage.vue'),
                meta: {
                    title: 'cause'
                }
            }
        ]
    },

    // RH
    {
        path: '/rh',
        component: mainLayoutComponent,
        children: [
            {
                path: '',
                redirect: '/'
            },
            {
                path: 'cargo',
                component: () => import('pages/RH/CargoPage.vue'),
                meta: {
                    title: 'position'
                }
            },
            {
                path: 'escolaridade',
                component: () => import('pages/RH/EscolaridadePage.vue'),
                meta: {
                    title: 'education'
                }
            },
            {
                path: 'especialidade',
                component: () => import('pages/RH/EspecialidadePage.vue'),
                meta: {
                    title: 'specialty'
                }
            },
            {
                path: 'habilidade',
                component: () => import('pages/RH/HabilidadePage.vue'),
                meta: {
                    title: 'skill'
                }
            },
            {
                path: 'departamento',
                component: () => import('pages/RH/DepartamentoPage.vue'),
                meta: {
                    title: 'department'
                }
            }
        ]
    },

    // Configuração
    {
        path: '/configuracao',
        component: mainLayoutComponent,
        children: [
            {
                path: '',
                redirect: '/'
            },
            {
                path: 'perfil-usuario',
                component: () =>
                    import('pages/Configuracao/PerfilUsuarioPage.vue'),
                meta: {
                    title: 'userProfile'
                }
            },
            {
                path: 'modulo',
                component: () => import('pages/Configuracao/ModuloPage.vue'),
                meta: {
                    title: 'module'
                }
            },
            {
                path: 'novidades',
                component: () => import('pages/Configuracao/NovidadesPage.vue'),
                meta: {
                    title: 'news'
                }
            },
            {
                path: 'ajudas',
                component: () => import('pages/Configuracao/HelpPage.vue'),
                meta: {
                    title: 'help'
                }
            }
        ]
    },

    // Novidades (visualização pública)
    {
        path: '/novidades',
        component: mainLayoutComponent,
        children: [
            {
                path: '',
                component: () => import('pages/NovidadesViewPage.vue'),
                meta: {
                    title: 'newsView'
                }
            }
        ]
    },

    // Configurações do Sistema
    {
        path: '/configuracoes',
        component: mainLayoutComponent,
        children: [
            {
                path: '',
                component: () => import('pages/ConfiguracoesPage.vue'),
                meta: {
                    title: 'settings'
                }
            }
        ]
    },
    {
        path: '/help/:route(.*)*',
        component: () => import('src/layouts/BlankLayout.vue'),
        children: [
            {
                path: '',
                component: () => import('src/pages/HelpViewPage.vue')
            }
        ],
        meta: {
            title: 'help',
            requiresAuth: true
        }
    },
    // Always leave this as last one,
    // but you can also remove it
    {
        path: '/:catchAll(.*)*',
        component: () => import('pages/ErrorNotFound.vue'),
        meta: {
            title: 'pageNotFound'
        }
    }
]

if (import.meta.env.MODE === 'development') {
    routes.push({
        path: '/dev',
        component: mainLayoutComponent,
        children: [{ path: '', component: () => import('pages/DevPage.vue') }],
        meta: {
            title: 'dev'
        }
    })
}

export default routes
