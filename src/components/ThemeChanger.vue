<template>
    <div id="theme-changer">
        <q-btn @click="toggleTheme" round flat>
            <i
                v-if="$q.dark.isActive"
                class="fas fa-sun theme-icon theme-icon-dark"
            ></i>
            <i v-else class="fas fa-moon theme-icon theme-icon-light"></i>
            <q-tooltip>
                <span class="text-overline">
                    {{ tooltipInfo }}
                </span>
            </q-tooltip>
        </q-btn>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useTheme } from '../composables/useTheme'
import { useI18n } from 'vue-i18n'

const { toggleTheme, isDark } = useTheme()
const { t: $t } = useI18n()

const darkMode = computed(() => {
    return isDark.value
})
const tooltipInfo = computed(() => {
    return darkMode.value
        ? $t('themes.ChangeToLight')
        : $t('themes.ChangeToDark')
})
</script>

<style scoped>
.theme-icon {
    font-size: 16px;
}

/* Tema escuro ativo - ícone claro com borda escura */
.theme-icon-dark {
    color: white;
    -webkit-filter: drop-shadow(1px 1px 0 #333) drop-shadow(-1px 1px 0 #333)
        drop-shadow(1px -1px 0 #333) drop-shadow(-1px -1px 0 #333);

    filter: drop-shadow(1px 1px 0 #333) drop-shadow(-1px 1px 0 #333)
        drop-shadow(1px -1px 0 #333) drop-shadow(-1px -1px 0 #333);
}

/* Tema claro ativo - ícone escuro com borda clara */
.theme-icon-light {
    color: #333;
    -webkit-filter: drop-shadow(1px 1px 0 white) drop-shadow(-1px 1px 0 white)
        drop-shadow(1px -1px 0 white) drop-shadow(-1px -1px 0 white);

    filter: drop-shadow(1px 1px 0 white) drop-shadow(-1px 1px 0 white)
        drop-shadow(1px -1px 0 white) drop-shadow(-1px -1px 0 white);
}
</style>
