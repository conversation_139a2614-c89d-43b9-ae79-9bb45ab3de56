<template>
    <q-dialog v-model="showModal" persistent>
        <q-card style="min-width: 400px">
            <q-card-section class="row items-center q-pb-none">
                <div class="text-h6">{{ $t('reports.modal.title') }}</div>
                <q-space />
                <q-btn icon="close" flat round dense v-close-popup />
            </q-card-section>

            <q-card-section>
                <q-form @submit.prevent="handleGenerate">
                    <div class="q-gutter-md">
                        <!-- Tipo de Relatório -->
                        <div>
                            <q-field
                                :label="$t('reports.modal.reportType')"
                                stack-label
                                outlined
                                dense
                            >
                                <template v-slot:control>
                                    <div
                                        class="self-center full-width no-outline"
                                        tabindex="0"
                                    >
                                        <q-option-group
                                            v-model="selectedType"
                                            :options="reportTypeOptions"
                                            color="primary"
                                            type="radio"
                                            inline
                                        />
                                    </div>
                                </template>
                            </q-field>
                        </div>

                        <!-- Formato -->
                        <div>
                            <q-field
                                :label="$t('reports.modal.format')"
                                stack-label
                                outlined
                                dense
                            >
                                <template v-slot:control>
                                    <div
                                        class="self-center full-width no-outline"
                                        tabindex="0"
                                    >
                                        <q-option-group
                                            v-model="selectedFormat"
                                            :options="formatOptions"
                                            color="primary"
                                            type="radio"
                                            inline
                                        />
                                    </div>
                                </template>
                            </q-field>
                        </div>

                        <!-- Coluna de Agrupamento (apenas para tipo agrupado) -->
                        <div v-if="selectedType === 'grouped'">
                            <q-select
                                v-model="selectedGroupColumn"
                                :options="groupColumnOptions"
                                :label="$t('reports.modal.groupColumn')"
                                :placeholder="
                                    $t('reports.modal.selectGroupColumn')
                                "
                                outlined
                                dense
                                emit-value
                                map-options
                                clearable
                            />
                        </div>

                        <!-- Coluna para Resumo (apenas para tipo resumido) -->
                        <div v-if="selectedType === 'summary'">
                            <q-select
                                v-model="selectedSummaryColumn"
                                :options="groupColumnOptions"
                                :label="$t('reports.modal.summaryColumn')"
                                :placeholder="
                                    $t('reports.modal.selectSummaryColumn')
                                "
                                outlined
                                dense
                                emit-value
                                map-options
                                clearable
                            />
                        </div>

                        <!-- Informações do Relatório -->
                        <div class="q-mt-md">
                            <q-card flat bordered class="bg-grey-1">
                                <q-card-section class="q-pa-sm">
                                    <div class="text-caption text-grey-7">
                                        {{ $t('reports.modal.info') }}
                                    </div>
                                    <div class="text-body2 q-mt-xs">
                                        <div>
                                            <strong
                                                >{{
                                                    $t('reports.modal.table')
                                                }}:</strong
                                            >
                                            {{ tableTitle }}
                                        </div>
                                        <div>
                                            <strong
                                                >{{
                                                    $t('reports.modal.columns')
                                                }}:</strong
                                            >
                                            {{ visibleColumnsCount }}
                                            {{
                                                $t(
                                                    'reports.modal.columnsVisible'
                                                )
                                            }}
                                        </div>
                                        <div
                                            v-if="
                                                hasFilters &&
                                                formattedFilters.length > 0
                                            "
                                        >
                                            <strong
                                                >{{
                                                    $t('reports.modal.filters')
                                                }}:</strong
                                            >
                                            <div class="q-mt-xs q-gutter-xs">
                                                <q-chip
                                                    v-for="filter in formattedFilters"
                                                    :key="filter"
                                                    size="sm"
                                                    color="positive"
                                                    text-color="white"
                                                    :label="filter"
                                                    dense
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </q-card-section>
                            </q-card>
                        </div>
                    </div>
                </q-form>
            </q-card-section>

            <q-card-actions align="right" class="q-pa-md">
                <q-btn
                    flat
                    :label="$t('buttons.cancel')"
                    color="grey-7"
                    v-close-popup
                />
                <q-btn
                    unelevated
                    :label="$t('reports.modal.generate')"
                    color="primary"
                    :loading="loading"
                    :disable="
                        !selectedType ||
                        !selectedFormat ||
                        (selectedType === 'grouped' && !selectedGroupColumn) ||
                        (selectedType === 'summary' && !selectedSummaryColumn)
                    "
                    @click="handleGenerate"
                />
            </q-card-actions>
        </q-card>
    </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'

export interface ReportModalProps {
    modelValue: boolean
    tableTitle: string
    visibleColumnsCount: number
    hasFilters: boolean
    appliedFilters: Record<string, unknown>
    availableColumns: Array<{ name: string; label: string }>
    loading?: boolean
}

export interface ReportSelection {
    type: 'simple' | 'grouped' | 'summary'
    format: 'excel' | 'pdf'
    groupColumn?: string
    summaryColumn?: string
}

const props = withDefaults(defineProps<ReportModalProps>(), {
    loading: false
})

const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    generate: [selection: ReportSelection]
}>()

const { t } = useI18n()

// Estado local
const selectedType = ref<'simple' | 'grouped' | 'summary'>('simple')
const selectedFormat = ref<'excel' | 'pdf'>('excel')
const selectedGroupColumn = ref<string>('')
const selectedSummaryColumn = ref<string>('')

// Computed
const showModal = computed({
    get: () => props.modelValue,
    set: (value: boolean) => emit('update:modelValue', value)
})

// Opções de tipo de relatório
const reportTypeOptions = computed(() => [
    {
        label: t('reports.types.simple'),
        value: 'simple'
    },
    {
        label: t('reports.types.grouped'),
        value: 'grouped'
    },
    {
        label: t('reports.types.summary'),
        value: 'summary'
    }
])

// Opções de formato
const formatOptions = computed(() => [
    {
        label: t('reports.formats.excel'),
        value: 'excel',
        icon: 'description'
    },
    {
        label: t('reports.formats.pdf'),
        value: 'pdf',
        icon: 'picture_as_pdf'
    }
])

// Opções de coluna de agrupamento
const groupColumnOptions = computed(() =>
    props.availableColumns.map(col => ({
        label: col.label,
        value: col.name
    }))
)

// Formatar filtros aplicados para exibição
const formattedFilters = computed(() => {
    if (!props.hasFilters || !props.appliedFilters) {
        return []
    }

    return Object.entries(props.appliedFilters)
        .filter(
            ([, value]) => value !== null && value !== undefined && value !== ''
        )
        .map(([key, value]) => {
            // Encontrar o label da coluna
            const column = props.availableColumns.find(col => col.name === key)
            const label = column?.label || key

            // Formatar o valor
            let formattedValue = String(value)
            if (Array.isArray(value)) {
                formattedValue = value.join(', ')
            }

            return `${label}: ${formattedValue}`
        })
})

// Métodos
const handleGenerate = () => {
    if (selectedType.value && selectedFormat.value) {
        const selection: ReportSelection = {
            type: selectedType.value,
            format: selectedFormat.value
        }

        if (selectedType.value === 'grouped' && selectedGroupColumn.value) {
            selection.groupColumn = selectedGroupColumn.value
        }

        if (selectedType.value === 'summary' && selectedSummaryColumn.value) {
            selection.summaryColumn = selectedSummaryColumn.value
        }

        emit('generate', selection)
    }
}
</script>

<style scoped>
.q-field__control {
    min-height: 40px;
}

.q-option-group {
    padding: 8px 0;
}

:deep(.q-radio) {
    margin-right: 16px;
}

:deep(.q-radio:last-child) {
    margin-right: 0;
}
</style>
