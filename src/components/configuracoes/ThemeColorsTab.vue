<template>
    <div class="theme-colors-tab q-pa-md">
        <!-- Header com controle de tema -->
        <div class="controls-header q-mb-md">
            <div class="header-content">
                <h6
                    class="controls-title q-ma-none"
                    :class="isDarkMode ? 'text-white' : 'text-dark'"
                >
                    {{ $t('pages.systemConfig.themeBuilder.title') }}
                </h6>
                <p
                    class="controls-subtitle q-ma-none"
                    :class="isDarkMode ? 'text-grey-4' : 'text-grey-6'"
                >
                    {{ $t('pages.systemConfig.themeBuilder.subtitle') }}
                </p>
            </div>
            <ThemeChanger />
        </div>

        <!-- Cores Principais -->
        <div
            class="color-section q-mb-md"
            :class="isDarkMode ? 'bg-dark' : 'bg-white'"
        >
            <h6
                class="section-title q-mb-sm"
                :class="isDarkMode ? 'text-white' : 'text-dark'"
            >
                {{ $t('pages.systemConfig.themeBuilder.sections.mainColors') }}
            </h6>

            <div class="colors-grid">
                <div class="color-item">
                    <label
                        class="color-label"
                        :class="isDarkMode ? 'text-grey-4' : 'text-grey-7'"
                    >
                        {{
                            $t('pages.systemConfig.themeBuilder.colors.primary')
                        }}
                    </label>
                    <div class="color-input-group">
                        <input
                            type="color"
                            v-model="themeColors.primary"
                            @input="updateTheme"
                            class="color-picker"
                        />
                        <q-input
                            v-model="themeColors.primary"
                            @update:model-value="updateTheme"
                            dense
                            outlined
                            class="hex-input"
                            placeholder="#000000"
                            maxlength="7"
                        />
                    </div>
                </div>
                <div class="color-item">
                    <label
                        class="color-label"
                        :class="isDarkMode ? 'text-grey-4' : 'text-grey-7'"
                    >
                        {{
                            $t(
                                'pages.systemConfig.themeBuilder.colors.secondary'
                            )
                        }}
                    </label>
                    <div class="color-input-group">
                        <input
                            type="color"
                            v-model="themeColors.secondary"
                            @input="updateTheme"
                            class="color-picker"
                        />
                        <q-input
                            v-model="themeColors.secondary"
                            @update:model-value="updateTheme"
                            dense
                            outlined
                            class="hex-input"
                            placeholder="#000000"
                            maxlength="7"
                        />
                    </div>
                </div>
                <div class="color-item">
                    <label
                        class="color-label"
                        :class="isDarkMode ? 'text-grey-4' : 'text-grey-7'"
                    >
                        {{
                            $t('pages.systemConfig.themeBuilder.colors.accent')
                        }}
                    </label>
                    <div class="color-input-group">
                        <input
                            type="color"
                            v-model="themeColors.accent"
                            @input="updateTheme"
                            class="color-picker"
                        />
                        <q-input
                            v-model="themeColors.accent"
                            @update:model-value="updateTheme"
                            dense
                            outlined
                            class="hex-input"
                            placeholder="#000000"
                            maxlength="7"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Colors -->
        <div
            class="color-section q-mb-md"
            :class="isDarkMode ? 'bg-dark' : 'bg-white'"
        >
            <h6
                class="section-title q-mb-sm"
                :class="isDarkMode ? 'text-white' : 'text-dark'"
            >
                {{
                    $t('pages.systemConfig.themeBuilder.sections.statusColors')
                }}
            </h6>

            <div class="colors-grid">
                <div class="color-item">
                    <label
                        class="color-label"
                        :class="isDarkMode ? 'text-grey-4' : 'text-grey-7'"
                    >
                        {{
                            $t(
                                'pages.systemConfig.themeBuilder.colors.positive'
                            )
                        }}
                    </label>
                    <div class="color-input-group">
                        <input
                            type="color"
                            v-model="themeColors.positive"
                            @input="updateTheme"
                            class="color-picker"
                        />
                        <q-input
                            v-model="themeColors.positive"
                            @update:model-value="updateTheme"
                            dense
                            outlined
                            class="hex-input"
                            placeholder="#000000"
                            maxlength="7"
                        />
                    </div>
                </div>
                <div class="color-item">
                    <label
                        class="color-label"
                        :class="isDarkMode ? 'text-grey-4' : 'text-grey-7'"
                    >
                        {{
                            $t(
                                'pages.systemConfig.themeBuilder.colors.negative'
                            )
                        }}
                    </label>
                    <div class="color-input-group">
                        <input
                            type="color"
                            v-model="themeColors.negative"
                            @input="updateTheme"
                            class="color-picker"
                        />
                        <q-input
                            v-model="themeColors.negative"
                            @update:model-value="updateTheme"
                            dense
                            outlined
                            class="hex-input"
                            placeholder="#000000"
                            maxlength="7"
                        />
                    </div>
                </div>
                <div class="color-item">
                    <label
                        class="color-label"
                        :class="isDarkMode ? 'text-grey-4' : 'text-grey-7'"
                    >
                        {{
                            $t('pages.systemConfig.themeBuilder.colors.warning')
                        }}
                    </label>
                    <div class="color-input-group">
                        <input
                            type="color"
                            v-model="themeColors.warning"
                            @input="updateTheme"
                            class="color-picker"
                        />
                        <q-input
                            v-model="themeColors.warning"
                            @update:model-value="updateTheme"
                            dense
                            outlined
                            class="hex-input"
                            placeholder="#000000"
                            maxlength="7"
                        />
                    </div>
                </div>
                <div class="color-item">
                    <label
                        class="color-label"
                        :class="isDarkMode ? 'text-grey-4' : 'text-grey-7'"
                    >
                        {{ $t('pages.systemConfig.themeBuilder.colors.info') }}
                    </label>
                    <div class="color-input-group">
                        <input
                            type="color"
                            v-model="themeColors.info"
                            @input="updateTheme"
                            class="color-picker"
                        />
                        <q-input
                            v-model="themeColors.info"
                            @update:model-value="updateTheme"
                            dense
                            outlined
                            class="hex-input"
                            placeholder="#000000"
                            maxlength="7"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- Cores Dark Mode -->
        <div
            class="color-section q-mb-md"
            :class="isDarkMode ? 'bg-dark' : 'bg-white'"
        >
            <h6
                class="section-title q-mb-sm"
                :class="isDarkMode ? 'text-white' : 'text-dark'"
            >
                {{ $t('pages.systemConfig.themeBuilder.sections.darkMode') }}
            </h6>

            <div class="colors-grid">
                <div class="color-item">
                    <label
                        class="color-label"
                        :class="isDarkMode ? 'text-grey-4' : 'text-grey-7'"
                    >
                        {{ $t('pages.systemConfig.themeBuilder.colors.dark') }}
                    </label>
                    <div class="color-input-group">
                        <input
                            type="color"
                            v-model="themeColors.dark"
                            @input="updateTheme"
                            class="color-picker"
                        />
                        <q-input
                            v-model="themeColors.dark"
                            @update:model-value="updateTheme"
                            dense
                            outlined
                            class="hex-input"
                            placeholder="#000000"
                            maxlength="7"
                        />
                    </div>
                </div>
                <div class="color-item">
                    <label
                        class="color-label"
                        :class="isDarkMode ? 'text-grey-4' : 'text-grey-7'"
                    >
                        {{
                            $t(
                                'pages.systemConfig.themeBuilder.colors.darkPage'
                            )
                        }}
                    </label>
                    <div class="color-input-group">
                        <input
                            type="color"
                            v-model="themeColors.darkPage"
                            @input="updateTheme"
                            class="color-picker"
                        />
                        <q-input
                            v-model="themeColors.darkPage"
                            @update:model-value="updateTheme"
                            dense
                            outlined
                            class="hex-input"
                            placeholder="#000000"
                            maxlength="7"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- Presets -->
        <div
            class="presets-section q-mb-md"
            :class="isDarkMode ? 'bg-dark' : 'bg-white'"
        >
            <h6
                class="section-title q-mb-sm"
                :class="isDarkMode ? 'text-white' : 'text-dark'"
            >
                {{ $t('pages.systemConfig.themeBuilder.sections.presets') }}
            </h6>

            <div class="preset-grid">
                <div
                    v-for="preset in themePresets"
                    :key="preset.name"
                    class="preset-item"
                >
                    <q-btn
                        :label="preset.name"
                        @click="applyPreset(preset)"
                        class="preset-btn"
                        outline
                        :color="isDarkMode ? 'white' : 'primary'"
                    />
                    <div class="preset-preview">
                        <div
                            class="color-dot"
                            :style="{ backgroundColor: preset.colors.primary }"
                            :title="'Primary: ' + preset.colors.primary"
                        ></div>
                        <div
                            class="color-dot"
                            :style="{
                                backgroundColor: preset.colors.secondary
                            }"
                            :title="'Secondary: ' + preset.colors.secondary"
                        ></div>
                        <div
                            class="color-dot"
                            :style="{ backgroundColor: preset.colors.accent }"
                            :title="'Accent: ' + preset.colors.accent"
                        ></div>
                        <div
                            class="color-dot"
                            :style="{ backgroundColor: preset.colors.positive }"
                            :title="'Positive: ' + preset.colors.positive"
                        ></div>
                        <div
                            class="color-dot"
                            :style="{ backgroundColor: preset.colors.negative }"
                            :title="'Negative: ' + preset.colors.negative"
                        ></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ações -->
        <div class="actions-section">
            <div class="actions-grid">
                <q-btn
                    @click="saveTheme"
                    :label="$t('pages.systemConfig.themeBuilder.actions.save')"
                    color="primary"
                    class="action-btn"
                    unelevated
                />
                <q-btn
                    @click="resetTheme"
                    :label="$t('pages.systemConfig.themeBuilder.actions.reset')"
                    color="grey-6"
                    outline
                    class="action-btn"
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { reactive, onMounted } from 'vue'
import { Notify } from 'quasar'
import { useI18n } from 'vue-i18n'
import ThemeChanger from 'src/components/ThemeChanger.vue'
import { useTheme } from 'src/composables/useTheme'

// Composables
const { isDarkMode } = useTheme()
const { t } = useI18n()

// Interfaces
interface ThemeColors {
    primary: string
    secondary: string
    accent: string
    positive: string
    negative: string
    warning: string
    info: string
    dark: string
    darkPage: string
}

interface ThemePreset {
    name: string
    colors: ThemeColors
}

interface ThemeSettings {
    colors: ThemeColors
    darkMode: boolean
}

// Estado das cores do tema
const themeColors = reactive<ThemeColors>({
    primary: '#204065',
    secondary: '#bba47a',
    accent: '#e27145',
    positive: '#53a857',
    negative: '#e05d5d',
    warning: '#ffc107',
    info: '#2979ff',
    dark: '#21262c',
    darkPage: '#21262c'
})

// Presets de temas
const themePresets: ThemePreset[] = [
    {
        name: t('pages.systemConfig.themeBuilder.presets.default'),
        colors: {
            primary: '#204065',
            secondary: '#bba47a',
            accent: '#e27145',
            positive: '#53a857',
            negative: '#e05d5d',
            warning: '#ffc107',
            info: '#2979ff',
            dark: '#21262c',
            darkPage: '#21262c'
        }
    },
    {
        name: t('pages.systemConfig.themeBuilder.presets.ocean'),
        colors: {
            primary: '#006064',
            secondary: '#4fc3f7',
            accent: '#ff7043',
            positive: '#66bb6a',
            negative: '#ef5350',
            warning: '#ffca28',
            info: '#42a5f5',
            dark: '#263238',
            darkPage: '#37474f'
        }
    },
    {
        name: t('pages.systemConfig.themeBuilder.presets.forest'),
        colors: {
            primary: '#2e7d32',
            secondary: '#81c784',
            accent: '#ff8f00',
            positive: '#4caf50',
            negative: '#f44336',
            warning: '#ff9800',
            info: '#2196f3',
            dark: '#1b5e20',
            darkPage: '#2e7d32'
        }
    },
    {
        name: t('pages.systemConfig.themeBuilder.presets.sunset'),
        colors: {
            primary: '#d84315',
            secondary: '#ffab40',
            accent: '#7c4dff',
            positive: '#8bc34a',
            negative: '#e91e63',
            warning: '#ffc107',
            info: '#03a9f4',
            dark: '#bf360c',
            darkPage: '#d84315'
        }
    },
    {
        name: t('pages.systemConfig.themeBuilder.presets.night'),
        colors: {
            primary: '#3f51b5',
            secondary: '#9c27b0',
            accent: '#e91e63',
            positive: '#4caf50',
            negative: '#f44336',
            warning: '#ff9800',
            info: '#2196f3',
            dark: '#1a237e',
            darkPage: '#000051'
        }
    }
]

// Funções
const updateTheme = (): void => {
    const root = document.documentElement

    // Aplicar cores CSS customizadas
    root.style.setProperty('--q-primary', themeColors.primary)
    root.style.setProperty('--q-secondary', themeColors.secondary)
    root.style.setProperty('--q-accent', themeColors.accent)
    root.style.setProperty('--q-positive', themeColors.positive)
    root.style.setProperty('--q-negative', themeColors.negative)
    root.style.setProperty('--q-warning', themeColors.warning)
    root.style.setProperty('--q-info', themeColors.info)
    root.style.setProperty('--q-dark', themeColors.dark)
    root.style.setProperty('--q-dark-page', themeColors.darkPage)

    // Aplicar modo escuro
    if (isDarkMode.value) {
        root.classList.add('body--dark')
        document.body.classList.add('body--dark')
    } else {
        root.classList.remove('body--dark')
        document.body.classList.remove('body--dark')
    }
}

const applyPreset = (preset: ThemePreset): void => {
    Object.assign(themeColors, preset.colors)
    updateTheme()

    Notify.create({
        type: 'info',
        message: t('pages.systemConfig.themeBuilder.messages.presetApplied', {
            name: preset.name
        }),
        position: 'top-right'
    })
}

const saveTheme = (): void => {
    // Salvar no localStorage
    const themeSettings: ThemeSettings = {
        colors: { ...themeColors },
        darkMode: isDarkMode.value
    }
    localStorage.setItem('customTheme', JSON.stringify(themeSettings))

    Notify.create({
        type: 'positive',
        message: t('pages.systemConfig.themeBuilder.messages.themeSaved'),
        position: 'top-right'
    })
}

const resetTheme = (): void => {
    // Aplicar preset padrão
    const defaultPreset = themePresets[0]
    if (defaultPreset) {
        Object.assign(themeColors, defaultPreset.colors)
        updateTheme()

        Notify.create({
            type: 'info',
            message: t('pages.systemConfig.themeBuilder.messages.themeReset'),
            position: 'top-right'
        })
    }
}

const loadTheme = (): void => {
    const saved = localStorage.getItem('customTheme')
    if (saved) {
        try {
            const savedTheme = JSON.parse(saved)

            // Verificar se é o formato antigo (só cores) ou novo (com darkMode)
            if (savedTheme.colors) {
                Object.assign(themeColors, savedTheme.colors)
                // O darkMode é gerenciado pelo ThemeChanger globalmente
            } else {
                // Formato antigo - só cores
                Object.assign(themeColors, savedTheme)
            }

            updateTheme()
        } catch {
            // Erro ao carregar tema salvo - usar tema padrão
        }
    }
}

// Lifecycle
onMounted(() => {
    loadTheme()
})
</script>

<style scoped>
.theme-colors-tab {
    max-width: 1000px;
    margin: 0 auto;
}

/* Header dos controles */
.controls-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding-bottom: 16px;
    border-bottom: 1px solid #e9ecef;
}

.header-content {
    flex: 1;
}

.controls-title {
    font-size: 18px;
    font-weight: 600;
    line-height: 1.2;
}

.controls-subtitle {
    font-size: 13px;
    margin-top: 2px;
}

/* Seções de cores */
.color-section {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 16px;
}

.section-title {
    font-size: 14px;
    font-weight: 600;
    margin: 0;
}

.colors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
}

.color-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
}

.color-input-group {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
}

.color-label {
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 2px;
}

.color-picker {
    width: 40px;
    height: 32px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    cursor: pointer;
    background: none;
    padding: 0;
    transition: border-color 0.2s ease;
}

.color-picker:hover {
    border-color: #adb5bd;
}

.color-picker:focus {
    outline: none;
    border-color: var(--q-primary);
    box-shadow: 0 0 0 3px rgba(var(--q-primary-rgb), 0.1);
}

.hex-input {
    flex: 1;
    min-width: 80px;
    max-width: 120px;
}

.hex-input :deep(.q-field__control) {
    height: 32px;
    min-height: 32px;
}

.hex-input :deep(.q-field__native) {
    font-family: 'Courier New', monospace;
    font-size: 11px;
    text-transform: uppercase;
}

/* Seção de presets */
.presets-section {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 16px;
}

.preset-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
}

.preset-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
}

.preset-btn {
    width: 100%;
    height: 32px;
    border-radius: 4px;
    font-weight: 500;
    text-transform: none;
    font-size: 12px;
    transition: all 0.2s ease;
}

.preset-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.preset-preview {
    display: flex;
    gap: 2px;
    justify-content: center;
    align-items: center;
}

.color-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 1px solid rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.2s ease;
}

.color-dot:hover {
    transform: scale(1.2);
}

/* Seção de ações */
.actions-section {
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.actions-grid {
    display: flex;
    gap: 12px;
    justify-content: center;
}

.action-btn {
    min-width: 140px;
    height: 36px;
    font-weight: 500;
    font-size: 13px;
    border-radius: 4px;
    text-transform: none;
    transition: all 0.2s ease;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Responsividade */
@media (max-width: 768px) {
    .theme-colors-tab {
        padding: 12px;
    }

    .controls-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
        padding-bottom: 12px;
    }

    .controls-title {
        font-size: 16px;
    }

    .controls-subtitle {
        font-size: 12px;
    }

    .colors-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 10px;
    }

    .preset-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 8px;
    }

    .actions-grid {
        flex-direction: column;
        align-items: center;
        gap: 8px;
    }

    .action-btn {
        width: 100%;
        max-width: 200px;
        height: 32px;
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .colors-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }

    .preset-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
    }

    .preset-item {
        gap: 4px;
    }

    .color-dot {
        width: 10px;
        height: 10px;
    }

    .color-section,
    .presets-section {
        padding: 12px;
    }
}
</style>
