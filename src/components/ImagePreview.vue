<template>
    <div v-if="showPreview" class="image-preview q-mt-sm">
        <div class="preview-container">
            <q-img
                :src="previewUrl"
                :alt="$t('forms.labels.attachmentPreview')"
                class="preview-image"
                fit="cover"
                loading="lazy"
                @error="onImageError"
            >
                <template v-slot:error>
                    <div class="absolute-full flex flex-center bg-grey-3">
                        <div class="text-center">
                            <q-icon
                                name="broken_image"
                                size="24px"
                                color="grey-6"
                            />
                            <div class="text-caption text-grey-6 q-mt-xs">
                                {{ $t('forms.labels.imageLoadError') }}
                            </div>
                        </div>
                    </div>
                </template>

                <template v-slot:loading>
                    <div class="absolute-full flex flex-center">
                        <q-spinner color="primary" size="24px" />
                    </div>
                </template>
            </q-img>

            <!-- Overlay com ações -->
            <div class="preview-overlay">
                <q-btn
                    icon="visibility"
                    color="primary"
                    round
                    dense
                    size="sm"
                    @click="$emit('view')"
                    class="q-mr-xs"
                >
                    <q-tooltip>{{ $t('buttons.view') }}</q-tooltip>
                </q-btn>

                <q-btn
                    icon="delete"
                    color="negative"
                    round
                    dense
                    size="sm"
                    @click="$emit('remove')"
                >
                    <q-tooltip>{{ $t('buttons.remove') }}</q-tooltip>
                </q-btn>
            </div>
        </div>

        <!-- Informações do arquivo -->
        <div class="file-info q-mt-xs">
            <div class="text-caption text-grey-7">
                <q-icon name="attach_file" size="12px" class="q-mr-xs" />
                {{ fileName }}
                <span v-if="fileSize" class="q-ml-xs">
                    ({{ formatFileSize(fileSize) }})
                </span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores/auth'

const { t: $t } = useI18n()
const authStore = useAuthStore()

// Props
const props = defineProps<{
    file?: File | null
    existingUrl?: string | null
    existingBase64?: string | null
    fileName?: string | null
}>()

// Emits
defineEmits<{
    view: []
    remove: []
}>()

// Computed
const showPreview = computed(() => {
    return !!(props.file || props.existingUrl || props.existingBase64)
})

const previewUrl = computed(() => {
    if (props.file && props.file instanceof File) {
        return URL.createObjectURL(props.file)
    }

    // Priorizar base64 se disponível
    if (props.existingBase64) {
        return props.existingBase64
    }

    if (props.existingUrl) {
        // Se a URL já contém parâmetros, adicionar o token com &
        // Se não contém parâmetros, adicionar o token com ?
        const separator = props.existingUrl.includes('?') ? '&' : '?'
        const token = authStore.token

        if (token) {
            return `${props.existingUrl}${separator}token=${encodeURIComponent(token)}`
        }

        return props.existingUrl
    }

    return ''
})

const fileName = computed(() => {
    if (props.file && props.file instanceof File) {
        return props.file.name
    }
    if (props.fileName) {
        return props.fileName
    }
    if (props.existingBase64) {
        return props.fileName || 'anexo'
    }
    if (props.existingUrl) {
        // Extrair nome do arquivo da URL
        const urlParts = props.existingUrl.split('/')
        const fileName = urlParts[urlParts.length - 1]
        // Remover query parameters se houver
        return fileName?.split('?')[0] || 'arquivo'
    }
    return ''
})

const fileSize = computed(() => {
    return props.file && props.file instanceof File ? props.file.size : null
})

// Methods
const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'

    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const onImageError = () => {
    // Erro ao carregar imagem - silencioso
}
</script>

<style scoped>
.image-preview {
    max-width: 200px;
}

.preview-container {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.12);
}

.preview-image {
    width: 100%;
    height: 120px;
    background-color: #f5f5f5;
}

.preview-overlay {
    position: absolute;
    top: 4px;
    right: 4px;
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.preview-container:hover .preview-overlay {
    opacity: 1;
}

.file-info {
    padding: 4px 8px;
    background-color: rgba(0, 0, 0, 0.04);
    border-radius: 0 0 8px 8px;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-top: none;
}

.file-info .text-caption {
    font-size: 11px;
    line-height: 1.2;
}
</style>
