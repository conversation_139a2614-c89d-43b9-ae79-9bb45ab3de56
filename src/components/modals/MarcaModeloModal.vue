<template>
    <modal-template
        :model-value="modelValue"
        :title="$t('modals.marcaModelo.title')"
        full-width
        full-height
        maximized
        transition-show="slide-up"
        transition-hide="slide-down"
        card-class="marca-modelo-modal"
        content-class="q-pa-xs table-section"
        @update:model-value="
            (value: boolean) => emit('update:modelValue', value)
        "
        @close="closeModal"
    >
        <!-- Header com informações da marca -->
        <template #subheader>
            <div class="row items-center q-gutter-md">
                <div class="col">
                    <div class="text-h6">
                        {{ $t('modals.marcaModelo.selectedBrand') }}:
                        {{ selectedMarca.descricao }}
                    </div>
                    <div class="text-caption text-grey-6">
                        {{ $t('modals.marcaModelo.manageModels') }}
                    </div>
                </div>
                <div class="col-auto">
                    <q-btn
                        :label="$t('buttons.linkModel')"
                        color="primary"
                        icon="add"
                        @click="openVincularModal"
                        :disable="!selectedMarca || !selectedMarca.id"
                    />
                </div>
            </div>
        </template>

        <!-- Toolbar compacto -->
        <template #toolbar>
            <div class="toolbar-buttons" style="width: 100%">
                <action-collection
                    :actions="toolbarActions"
                    :label="$t('buttons.actions')"
                    color="accent"
                    icon="arrow_drop_down_circle"
                    size="sm"
                    outline
                    dense
                    menu-anchor="bottom left"
                    menu-self="top left"
                    class="q-px-sm"
                />

                <q-btn
                    :label="$t('buttons.refresh')"
                    color="positive"
                    outline
                    icon="refresh"
                    @click="() => loadMarcaModelos()"
                    size="sm"
                    dense
                    class="q-ml-auto q-px-sm"
                />
            </div>
        </template>

        <!-- Tabela DataTable -->
        <data-table
            table-name="marca-modelos"
            v-model:selected="selected"
            :columns="columns"
            :rows="marcaModelos"
            :loading="loadingTable"
            :filter="tableFilter"
            :pagination="pagination"
            :save-table-options="true"
            :show-toolbar="true"
            :show-quick-filter="true"
            :filter-columns="filterableColumns"
            v-model:quick-filter-value="columnFilter"
            :max-height="'calc(100vh - 210px)'"
            selection="multiple"
            dense
            flat
            bordered
            binary-state-sort
            :rows-per-page-options="[10, 25, 50, 100, 500, 1000]"
            @request="onTableRequest"
            @restore-pagination="onRestorePagination"
            @quick-filter="onColumnFilter"
        >
            <!-- Slot para coluna de ações -->
            <template v-slot:body-cell-actions="props">
                <q-td :props="props">
                    <div class="row q-gutter-xs no-wrap justify-center">
                        <q-btn
                            icon="visibility"
                            color="info"
                            flat
                            round
                            dense
                            size="sm"
                            @click="viewAttachment(props.row)"
                            :disable="
                                !props.row.anexo ||
                                !permissions.canViewLinkImage
                            "
                        >
                            <q-tooltip>{{
                                $t('buttons.viewAttachment')
                            }}</q-tooltip>
                        </q-btn>
                        <q-btn
                            icon="edit"
                            color="primary"
                            flat
                            round
                            dense
                            size="sm"
                            :disable="!permissions.canEditLink"
                            @click="editMarcaModelo(props.row)"
                        >
                            <q-tooltip>{{ $t('buttons.edit') }}</q-tooltip>
                        </q-btn>
                        <q-btn
                            icon="delete"
                            color="negative"
                            flat
                            round
                            dense
                            size="sm"
                            :disable="!permissions.canDeleteLink"
                            @click="deleteMarcaModelo(props.row)"
                        >
                            <q-tooltip>{{ $t('buttons.delete') }}</q-tooltip>
                        </q-btn>
                    </div>
                </q-td>
            </template>

            <!-- Slot para quando não há dados -->
            <template v-slot:no-data>
                <div class="full-width row flex-center q-gutter-xs q-pa-lg">
                    <q-icon size="2em" name="sentiment_dissatisfied" />
                    <span class="text-body1">{{
                        $t('forms.labels.noResults')
                    }}</span>
                </div>
            </template>
        </data-table>
    </modal-template>

    <!-- Image Viewer -->
    <image-viewer
        v-model="showImageViewer"
        :image-url="currentImageUrl"
        :title="currentImageTitle"
    />

    <!-- Modal de Vincular Modelo -->
    <vincular-modelo-modal
        v-model="showVincularModal"
        :selected-marca="selectedMarca"
        @success="onVincularSuccess"
    />

    <!-- Modal de Editar Modelo -->
    <vincular-modelo-modal
        v-if="currentEditItem"
        v-model="showEditModal"
        :selected-marca="selectedMarca"
        :initial-values="editInitialValues"
        :is-editing="true"
        :editing-id="currentEditItem.id"
        @success="onEditSuccess"
    />
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Notify } from 'quasar'
import { useAuthStore } from '@/stores/auth'
import { useLoadingOverlay } from '@/composables/loadingOverlay'
import ModalTemplate from '@/components/ModalTemplate.vue'
import DataTable from '@/components/DataTable.vue'
import ImageViewer from '@/components/ImageViewer.vue'
import ActionCollection from '@/components/ActionCollection.vue'
import VincularModeloModal from '@/components/modals/VincularModeloModal.vue'
import { useActionPermissions } from '@/composables/useActionPermissions'

const { t: $t } = useI18n()
const { setLoading } = useLoadingOverlay()
const authStore = useAuthStore()
const { produtoMarcaPermissions } = useActionPermissions()

// Permissões para esta rota
const permissions = produtoMarcaPermissions

// Props
interface Props {
    modelValue: boolean
    selectedMarca: {
        id: number
        descricao: string
    }
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    selectedMarca: () => ({
        id: 0,
        descricao: ''
    })
})

// Emits
const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    success: []
}>()

// Types
interface MarcaModelo {
    id: number
    id_marca_descricao: string
    id_modelo_descricao: string
    nivel_dificuldade: number
    anexo: string | null
    anexo_base64?: string
    id_marca: number
    id_modelo: number
}

interface TableRequestProps {
    paginationData: {
        sortBy: string
        descending: boolean
        page: number
        rowsPerPage: number
        rowsNumber?: number
    }
    filter?:
        | {
              search: string
              ativo: boolean | null
          }
        | undefined
    getCellValue?: (col: unknown, row: unknown) => unknown
}

// Refs
const loadingTable = ref(false)
const selected = ref<MarcaModelo[]>([])
const showVincularModal = ref(false)
const showEditModal = ref(false)
const currentEditItem = ref<MarcaModelo | null>(null)

// Marca-Modelos
const marcaModelos = ref<MarcaModelo[]>([])
const pagination = ref({
    sortBy: 'id',
    descending: false,
    page: 1,
    rowsPerPage: 25,
    rowsNumber: 0
})

// Image Viewer
const showImageViewer = ref(false)
const currentImageUrl = ref('')
const currentImageTitle = ref('')

// Filtros
const columnFilter = ref<{
    column: string
    content: string | number | boolean
} | null>(null)

// Computed
const filterableColumns = computed(() => [
    {
        label: $t('forms.labels.id'),
        value: 'id',
        field: 'id',
        type: 'number' as const
    },
    {
        label: $t('forms.labels.brand'),
        value: 'id_marca_descricao',
        field: 'id_marca_descricao',
        type: 'text' as const
    },
    {
        label: $t('forms.labels.model'),
        value: 'id_modelo_descricao',
        field: 'id_modelo_descricao',
        type: 'text' as const
    },
    {
        label: $t('forms.labels.difficultyLevel'),
        value: 'nivel_dificuldade',
        field: 'nivel_dificuldade',
        type: 'number' as const
    }
])

const toolbarActions = computed(() => [
    {
        label: $t('buttons.editSelected'),
        active: selected.value.length === 1 && permissions.value.canEdit,
        icon: 'edit',
        description: $t('buttons.editSelectedDescription'),
        action: () => {
            if (selected.value.length === 1 && selected.value[0]) {
                editMarcaModelo(selected.value[0])
            }
        }
    },
    {
        label: $t('buttons.deleteSelected', {
            count: selected.value.length
        }),
        active: selected.value.length > 0 && permissions.value.canDelete,
        icon: 'delete',
        description: $t('buttons.deleteSelectedDescription'),
        action: confirmDeleteMultiple
    }
])

const tableFilter = computed(() => {
    if (
        columnFilter.value &&
        columnFilter.value.column &&
        columnFilter.value.content !== null &&
        columnFilter.value.content !== undefined
    ) {
        try {
            // Converter valor para string para compatibilidade com q-table
            const content = columnFilter.value.content
            const stringValue =
                typeof content === 'string' ? content : String(content)

            // Só retornar filtro se o valor for válido
            if (
                stringValue &&
                stringValue.trim() !== '' &&
                stringValue !== 'undefined' &&
                stringValue !== 'null'
            ) {
                return {
                    [columnFilter.value.column]: stringValue
                }
            }
        } catch {
            // Ignorar erro e retornar filtro vazio
        }
    }
    return {}
})

const editInitialValues = computed(() => {
    if (currentEditItem.value) {
        return {
            modelo: currentEditItem.value.id_modelo,
            nivelDificuldade: currentEditItem.value.nivel_dificuldade,
            anexo: currentEditItem.value.anexo || undefined,
            anexo_base64: currentEditItem.value.anexo_base64 || undefined
        }
    }
    return {}
})

const columns = [
    {
        name: 'id',
        label: $t('forms.labels.id'),
        field: 'id',
        align: 'left' as const,
        sortable: true,
        order: 1
    },
    {
        name: 'id_marca_descricao',
        label: $t('forms.labels.brand'),
        field: 'id_marca_descricao',
        align: 'left' as const,
        sortable: true,
        order: 2
    },
    {
        name: 'id_modelo_descricao',
        label: $t('forms.labels.model'),
        field: 'id_modelo_descricao',
        align: 'left' as const,
        sortable: true,
        order: 3
    },
    {
        name: 'nivel_dificuldade',
        label: $t('forms.labels.difficultyLevel'),
        field: 'nivel_dificuldade',
        align: 'center' as const,
        sortable: true,
        order: 4
    },
    {
        name: 'actions',
        label: $t('forms.labels.actions'),
        field: 'actions',
        align: 'center' as const,
        sortable: false,
        order: 5
    }
]

// Watch
watch(
    () => props.modelValue,
    newVal => {
        if (newVal) {
            loadMarcaModelos()
            // Abrir automaticamente o modal de cadastro se há uma marca selecionada
            if (props.selectedMarca && props.selectedMarca.id) {
                openVincularModal()
            }
        }
    }
)

// Methods
const closeModal = () => {
    emit('update:modelValue', false)
}

const openVincularModal = () => {
    showVincularModal.value = true
}

const onVincularSuccess = () => {
    // Recarregar dados da tabela quando uma vinculação for criada
    loadMarcaModelos()
    emit('success')
}

const onEditSuccess = () => {
    // Recarregar dados da tabela quando uma vinculação for editada
    loadMarcaModelos()
    showEditModal.value = false
    currentEditItem.value = null
    emit('success')
}

// Métodos para carregar marca-modelos
const loadMarcaModelos = async (
    requestProps: Partial<TableRequestProps> | null = null
) => {
    await setLoading(async () => {
        try {
            loadingTable.value = true

            const requestPagination =
                requestProps?.paginationData || pagination.value
            const filter = requestProps?.filter || tableFilter.value

            // Construir parâmetros da requisição
            const params: Record<string, unknown> = {
                page: requestPagination.page,
                page_size: requestPagination.rowsPerPage,
                id_marca: props.selectedMarca.id // Sempre filtrar pela marca selecionada
            }

            // Aplicar filtro de coluna se existir
            if (filter && Object.keys(filter).length > 0) {
                Object.entries(filter).forEach(([key, value]) => {
                    if (value !== null && value !== undefined && value !== '') {
                        params[key] = value
                    }
                })
            }

            if (requestPagination.sortBy) {
                const sortOrder = requestPagination.descending ? '-' : ''
                params.ordering = `${sortOrder}${requestPagination.sortBy}`
            }

            const response = await authStore.asyncRequest({
                endpoint: '/api/corpsystem/produto-service/marca-modelo',
                method: 'get',
                params,
                customErrorMessages: {
                    400: $t('modals.marcaModelo.errors.loadError'),
                    500: $t('modals.marcaModelo.errors.loadError')
                }
            })

            const data = response.data

            marcaModelos.value = data.results
            pagination.value = {
                ...requestPagination,
                rowsNumber: data.count
            }
        } catch (error) {
            throw new Error(error as string)
        } finally {
            loadingTable.value = false
        }
    })
}

const onTableRequest = (requestProps: Record<string, unknown>) => {
    // Converter o requestProps para o formato esperado
    const props: Partial<TableRequestProps> = {}

    if (requestProps.pagination) {
        props.paginationData =
            requestProps.pagination as TableRequestProps['paginationData']
    }

    if (requestProps.filter) {
        props.filter = requestProps.filter as TableRequestProps['filter']
    }

    loadMarcaModelos(props)
}

// Métodos para ações da tabela
const viewAttachment = (item: MarcaModelo) => {
    if (!item.anexo) return

    currentImageUrl.value = item.anexo
    currentImageTitle.value = `${item.id_marca_descricao} - ${item.id_modelo_descricao}`
    showImageViewer.value = true
}

const editMarcaModelo = async (item: MarcaModelo) => {
    try {
        await setLoading(async () => {
            // Fazer requisição para obter os dados completos do item
            const fullItemData = await getMarcaModeloById(item.id)

            // Atualizar o item atual com os dados completos da API
            currentEditItem.value = fullItemData
            showEditModal.value = true
        })
    } catch {
        // Em caso de erro, usar os dados da tabela
        currentEditItem.value = item
        showEditModal.value = true
    }
}

const deleteMarcaModelo = (item: MarcaModelo) => {
    import('quasar').then(({ Dialog }) => {
        Dialog.create({
            title: $t('dialogs.confirmDelete.title'),
            message: $t('dialogs.confirmDelete.message', {
                item: `${item.id_marca_descricao} - ${item.id_modelo_descricao}`
            }),
            cancel: true,
            persistent: true,
            ok: {
                label: $t('buttons.delete'),
                color: 'negative'
            }
        }).onOk(async () => {
            await setLoading(async () => {
                try {
                    await deleteMarcaModeloById(item.id)
                    loadMarcaModelos()

                    Notify.create({
                        message: $t(
                            'modals.marcaModelo.messages.deleteSuccess'
                        ),
                        color: 'positive',
                        position: 'top',
                        timeout: 3000,
                        icon: 'check_circle'
                    })
                } catch {
                    Notify.create({
                        message: $t('modals.marcaModelo.errors.deleteError'),
                        color: 'negative',
                        position: 'top',
                        timeout: 3000,
                        icon: 'error'
                    })
                }
            })
        })
    })
}

// Métodos para filtros
const onColumnFilter = (
    filterValue: { column: string; content: string | number | boolean } | null
) => {
    columnFilter.value = filterValue
    pagination.value.page = 1
    loadMarcaModelos()
}

const onRestorePagination = (savedPagination: Record<string, unknown>) => {
    pagination.value = {
        ...pagination.value,
        ...savedPagination,
        page: 1
    }
}

// CRUD methods
const getMarcaModeloById = async (id: number) => {
    const response = await authStore.asyncRequest({
        endpoint: `/api/corpsystem/produto-service/marca-modelo/${id}`,
        method: 'get',
        customErrorMessages: {
            404: $t('modals.marcaModelo.errors.notFound'),
            500: $t('modals.marcaModelo.errors.loadError')
        }
    })
    return response.data
}

const deleteMarcaModeloById = async (id: number) => {
    await authStore.asyncRequest({
        endpoint: `/api/corpsystem/produto-service/marca-modelo/${id}`,
        method: 'delete',
        customErrorMessages: {
            400: $t('modals.marcaModelo.errors.deleteError'),
            404: $t('modals.marcaModelo.errors.deleteError'),
            500: $t('modals.marcaModelo.errors.deleteError')
        }
    })
}

const deleteMultipleMarcaModelos = async (ids: number[]) => {
    if (ids.length === 1) {
        const firstId = ids[0]
        if (firstId !== undefined) {
            await deleteMarcaModeloById(firstId)
        }
    } else if (ids.length > 1) {
        const idsString = ids.join(',')
        const endpoint = `/api/corpsystem/produto-service/marca-modelo/?id=${encodeURIComponent(idsString)}`

        await authStore.asyncRequest({
            endpoint,
            method: 'delete',
            customErrorMessages: {
                400: $t('modals.marcaModelo.errors.deleteMultipleError'),
                404: $t('modals.marcaModelo.errors.deleteMultipleError'),
                500: $t('modals.marcaModelo.errors.deleteMultipleError')
            }
        })
    }
}

// Métodos para ações múltiplas
const confirmDeleteMultiple = () => {
    import('quasar').then(({ Dialog }) => {
        Dialog.create({
            title: $t('dialogs.confirmDeleteMultiple.title'),
            message: $t('dialogs.confirmDeleteMultiple.message', {
                count: selected.value.length
            }),
            cancel: true,
            persistent: true,
            ok: {
                label: $t('buttons.delete'),
                color: 'negative'
            }
        }).onOk(async () => {
            await setLoading(async () => {
                try {
                    const ids = selected.value.map(item => item.id)
                    await deleteMultipleMarcaModelos(ids)
                    selected.value = []
                    loadMarcaModelos()

                    Notify.create({
                        message: $t(
                            'modals.marcaModelo.messages.deleteMultipleSuccess'
                        ),
                        color: 'positive',
                        position: 'top',
                        timeout: 3000,
                        icon: 'check_circle'
                    })
                } catch {
                    Notify.create({
                        message: $t(
                            'modals.marcaModelo.errors.deleteMultipleError'
                        ),
                        color: 'negative',
                        position: 'top',
                        timeout: 3000,
                        icon: 'error'
                    })
                }
            })
        })
    })
}
</script>

<style scoped>
.marca-modelo-modal {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.marca-modelo-modal .q-card__section:last-child {
    flex: 1;
    overflow: hidden;
}

.border-bottom {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.table-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Estilos para toolbar */
.toolbar-container {
    margin-bottom: 16px;
}

.toolbar-buttons {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    flex-wrap: wrap;
}

.toolbar-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

/* Responsividade para telas menores */
@media (max-width: 768px) {
    .toolbar-buttons {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .toolbar-actions {
        justify-content: center;
        gap: 6px;
    }
}
</style>
