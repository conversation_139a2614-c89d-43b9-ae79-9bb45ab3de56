<template>
    <modal-template
        :model-value="modelValue"
        @update:model-value="
            (value: boolean) => emit('update:modelValue', value)
        "
        @close="clearForm"
    >
        <template #title>
            <div class="text-h6">
                {{
                    isEditing
                        ? $t('modals.marcaModelo.form.editTitle')
                        : $t('modals.marcaModelo.form.title')
                }}
            </div>
            <div class="text-subtitle2 text-grey-6">
                {{ $t('forms.labels.brand') }}: {{ selectedMarca.descricao }}
            </div>
        </template>
        <!-- Conteúdo do modal -->
        <form-marca-modelo
            ref="formRef"
            :selected-marca="selectedMarca"
            :initial-values="formInitialValues"
        />

        <!-- Botões do modal -->
        <template #actions>
            <q-btn
                :label="$t('buttons.cancel')"
                color="negative"
                flat
                @click="closeModal"
                :disable="saving"
            />
            <q-btn
                :label="$t('buttons.clear')"
                color="grey-6"
                outline
                @click="clearForm"
                :disable="saving"
            />
            <q-btn
                :label="$t('buttons.save')"
                color="positive"
                @click="handleSave"
                :loading="saving"
            />
        </template>
    </modal-template>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Notify } from 'quasar'
import { useAuthStore } from '@/stores/auth'
import { useLoadingOverlay } from '@/composables/loadingOverlay'
import ModalTemplate from '@/components/ModalTemplate.vue'
import FormMarcaModelo from '@/components/forms/FormMarcaModelo.vue'

const { t: $t } = useI18n()
const { setLoading } = useLoadingOverlay()
const authStore = useAuthStore()

// Props
interface Props {
    modelValue: boolean
    selectedMarca: {
        id: number
        descricao: string
    }
    initialValues?: {
        modelo?: number | undefined
        nivelDificuldade?: number | undefined
        anexo?: File | null | string | undefined
        anexo_base64?: string | undefined
    }
    isEditing?: boolean
    editingId?: number
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    selectedMarca: () => ({
        id: 0,
        descricao: ''
    }),
    initialValues: () => ({}),
    isEditing: false
})

// Emits
const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    success: []
}>()

// Refs
const saving = ref(false)
const formRef = ref<InstanceType<typeof FormMarcaModelo> | null>(null)

// Computed
const formInitialValues = computed(() => {
    const values: {
        modelo?: number | undefined
        nivelDificuldade?: number | undefined
        anexo?: File | null | string | undefined
        anexo_base64?: string | undefined
    } = {
        nivelDificuldade: 1,
        anexo: null,
        ...props.initialValues
    }

    return values
})

// Methods
const closeModal = () => {
    emit('update:modelValue', false)
    clearForm()
}

const clearForm = () => {
    if (formRef.value) {
        formRef.value.resetForm()
    }
}

const handleSave = async () => {
    if (!formRef.value) return

    const { valid } = await formRef.value.validate()

    if (!valid) {
        Notify.create({
            message: $t('notifications.pleaseFixErrors'),
            color: 'negative',
            position: 'top',
            timeout: 3000,
            icon: 'error'
        })
        return
    }

    await setLoading(async () => {
        saving.value = true

        try {
            // Obter FormData do formulário (já formatado conforme a API)
            const formDataToSend = formRef.value!.getData()

            // Verificar se modelo foi selecionado (validação adicional)
            if (!formDataToSend.get('id_modelo')) {
                Notify.create({
                    message: $t('validate.requiredModel'),
                    color: 'negative',
                    position: 'top',
                    timeout: 3000,
                    icon: 'error'
                })
                return
            }

            if (props.isEditing && props.editingId) {
                // Edição - usar PUT
                await authStore.asyncRequest({
                    endpoint: `/api/corpsystem/produto-service/marca-modelo/update/${props.editingId}`,
                    method: 'put',
                    params: formDataToSend,
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    },
                    customErrorMessages: {
                        400: $t('modals.marcaModelo.errors.updateError'),
                        404: $t('modals.marcaModelo.errors.notFound'),
                        500: $t('modals.marcaModelo.errors.updateError')
                    }
                })
            } else {
                // Criação - usar POST
                await authStore.asyncRequest({
                    endpoint:
                        '/api/corpsystem/produto-service/marca-modelo/create',
                    method: 'post',
                    params: formDataToSend,
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    },
                    customErrorMessages: {
                        400: $t('modals.marcaModelo.errors.saveError'),
                        500: $t('modals.marcaModelo.errors.saveError')
                    }
                })
            }

            clearForm()
            emit('success')
            closeModal()

            Notify.create({
                message: props.isEditing
                    ? $t('modals.marcaModelo.messages.updateSuccess')
                    : $t('modals.marcaModelo.messages.createSuccess'),
                color: 'positive',
                position: 'top',
                timeout: 3000,
                icon: 'check_circle'
            })
        } catch {
            // Error handling is done by asyncRequest
        } finally {
            saving.value = false
        }
    })
}
</script>

<style scoped>
.q-card {
    border-radius: 8px;
}
</style>
