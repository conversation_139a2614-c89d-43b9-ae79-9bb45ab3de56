<template>
    <div class="table-column-filter">
        <div class="filter-row">
            <q-select
                v-model="selectedColumn"
                :options="columnOptions"
                :label="selectedColumn ? '' : $t('forms.labels.column')"
                clearable
                emit-value
                map-options
                dense
                class="filter-column-select input-box"
                @update:model-value="onColumnChange"
            >
                <template v-slot:prepend>
                    <q-icon name="view_column" />
                </template>
            </q-select>

            <div class="filter-content">
                <!-- Input de texto padrão -->
                <q-input
                    v-if="currentInputType === 'text'"
                    :model-value="filterContent?.toString() || ''"
                    :label="filterContent ? '' : $t('forms.labels.content')"
                    clearable
                    dense
                    :disable="!selectedColumn"
                    class="filter-content-input input-box"
                    @update:model-value="onContentChange"
                    @keyup.enter="applyFilter"
                >
                    <template v-slot:prepend>
                        <q-icon name="search" />
                    </template>
                </q-input>

                <!-- Input numérico -->
                <q-input
                    v-else-if="currentInputType === 'number'"
                    :model-value="filterContent?.toString() || ''"
                    :label="filterContent ? '' : $t('forms.labels.content')"
                    type="number"
                    dense
                    clearable
                    :disable="!selectedColumn"
                    class="filter-content-input input-box"
                    @update:model-value="onContentChange"
                    @keyup.enter="applyFilter"
                >
                    <template v-slot:prepend>
                        <q-icon name="tag" />
                    </template>
                </q-input>

                <!-- Select para boolean -->
                <q-select
                    v-else-if="currentInputType === 'boolean'"
                    v-model="filterContent"
                    :options="booleanOptions"
                    :label="
                        filterContent !== null && filterContent !== undefined
                            ? ''
                            : $t('forms.labels.content')
                    "
                    clearable
                    emit-value
                    map-options
                    dense
                    :disable="!selectedColumn"
                    class="filter-content-input input-box"
                    @update:model-value="onContentChange"
                >
                    <template v-slot:prepend>
                        <q-icon name="check_circle" />
                    </template>
                </q-select>

                <!-- Select customizado -->
                <q-select
                    v-else-if="currentInputType === 'select'"
                    v-model="filterContent"
                    :options="currentSelectOptions"
                    :label="filterContent ? '' : $t('forms.labels.content')"
                    dense
                    clearable
                    emit-value
                    map-options
                    :disable="!selectedColumn"
                    class="filter-content-input input-box"
                    @update:model-value="onContentChange"
                >
                    <template v-slot:prepend>
                        <q-icon name="list" />
                    </template>
                </q-select>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'

// Types
interface ColumnOption {
    label: string
    value: string
    field: string
    type?: 'text' | 'number' | 'boolean' | 'select'
    options?: Array<{ label: string; value: string | number | boolean }>
}

interface FilterValue {
    column: string
    content: string | number | boolean
}

// Props
interface Props {
    columns: ColumnOption[]
    modelValue?: FilterValue | null
}

// Emits
interface Emits {
    (e: 'update:modelValue', value: FilterValue | null): void
    (e: 'filter', value: FilterValue | null): void
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: null
})

const emit = defineEmits<Emits>()

// Composables
const { t: $t } = useI18n()

// Reactive data
const selectedColumn = ref<string>('')
const filterContent = ref<string | number | boolean>('')

// Computed
const columnOptions = computed(() => {
    return props.columns.map(col => ({
        label: col.label,
        value: col.field,
        field: col.field,
        type: col.type,
        options: col.options
    }))
})

const currentColumn = computed(() => {
    return props.columns.find(col => col.field === selectedColumn.value)
})

const currentInputType = computed(() => {
    return currentColumn.value?.type || 'text'
})

const booleanOptions = computed(() => [
    { label: $t('forms.labels.active'), value: true },
    { label: $t('forms.labels.inactive'), value: false }
])

const currentSelectOptions = computed(() => {
    return currentColumn.value?.options || []
})

// Methods
const onColumnChange = (value: string | null) => {
    selectedColumn.value = value || ''
    if (!value) {
        filterContent.value = ''
        clearFilter()
    } else {
        // Reset content when column changes
        filterContent.value = ''
    }
}

const filterTimeout = ref<number | null>(null)
const onContentChange = (value: string | number | boolean | null) => {
    const timeDelay = 1000

    // Handle different input types
    if (currentInputType.value === 'number') {
        filterContent.value = value ? Number(value) : ''
    } else if (currentInputType.value === 'boolean') {
        filterContent.value = value !== null ? value : ''
    } else {
        filterContent.value = value?.toString() || ''
    }

    if (!value && value !== false && value !== 0) {
        clearFilter()
        return
    }

    if (filterTimeout.value) {
        clearTimeout(filterTimeout.value)
    }
    filterTimeout.value = window.setTimeout(() => {
        applyFilter()
    }, timeDelay)
}

const applyFilter = () => {
    if (
        selectedColumn.value &&
        filterContent.value !== '' &&
        filterContent.value !== null &&
        filterContent.value !== undefined
    ) {
        const filterValue: FilterValue = {
            column: selectedColumn.value,
            content: filterContent.value
        }
        emit('update:modelValue', filterValue)
        emit('filter', filterValue)
    }
}

const clearFilter = () => {
    emit('update:modelValue', null)
    emit('filter', null)
}

// Watch for external changes
watch(
    () => props.modelValue,
    newValue => {
        if (newValue) {
            selectedColumn.value = newValue.column
            filterContent.value = newValue.content
        } else {
            selectedColumn.value = ''
            filterContent.value = ''
        }
    },
    { immediate: true }
)
</script>

<style scoped>
.table-column-filter {
    width: 100%;
    max-width: 500px;
}

.filter-row {
    display: flex;
    gap: 2px; /* Gap bem reduzido */
    align-items: flex-end;
    flex-wrap: wrap;
}

.filter-column-select {
    min-width: 150px; /* Largura menor */
    max-width: 180px; /* Largura menor */
    flex: 0 0 auto;
}

.filter-content {
    flex: 1;
    min-width: 120px; /* Largura mínima menor */
}

.filter-content-input {
    width: 100%;
}

/* Responsividade para telas menores */
@media (max-width: 600px) {
    .filter-row {
        flex-direction: column;
        gap: 6px;
    }

    .filter-column-select {
        min-width: 100%;
        max-width: 100%;
    }

    .filter-content {
        min-width: 100%;
    }
}

/* Responsividade para telas médias */
@media (min-width: 601px) and (max-width: 900px) {
    .filter-column-select {
        min-width: 100px;
        max-width: 130px;
    }

    .filter-content {
        min-width: 140px;
    }
}

/* Manter apenas correções essenciais para evitar vazamento de conteúdo */
.table-column-filter :deep(.q-field__native) {
    overflow: hidden !important; /* Evitar conteúdo fora da caixa */
    text-overflow: ellipsis !important; /* Adicionar ... quando necessário */
    white-space: nowrap !important; /* Não quebrar linha */
}

.table-column-filter :deep(.q-select__selected) {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}
</style>
