<template>
    <div id="form-template">
        <q-card flat bordered class="my-card">
            <q-card-section
                v-if="title || $slots.header"
                class="bg-primary q-pa-xs"
            >
                <div class="row col-12 justify-between">
                    <div
                        v-if="title"
                        class="text-caption q-my-none text-white col-auto"
                    >
                        {{ title }}
                    </div>
                    <slot v-if="$slots.header" name="header"></slot>
                </div>
            </q-card-section>

            <q-card-section v-if="$slots.body" class="q-pa-xs">
                <slot name="body"></slot>
            </q-card-section>
        </q-card>
    </div>
</template>

<script setup lang="ts">
defineProps({
    title: {
        type: String,
        default: null
    }
})
</script>

<style scoped>
.my-card {
    border-radius: 8px;
}
</style>
