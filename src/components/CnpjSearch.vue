<template>
    <div class="cnpj-search">
        <!-- Bo<PERSON><PERSON> de pesquisa -->
        <q-btn
            :size="size"
            :color="color"
            :flat="variant === 'flat'"
            :outline="variant === 'outline'"
            :unelevated="variant === 'unelevated'"
            round
            icon="search"
            @click="openSearchDialog"
            :loading="loading"
        >
            <q-tooltip>{{ tooltipText || $t('cnpjSearch.tooltip') }}</q-tooltip>
        </q-btn>

        <!-- Modal de pesquisa -->
        <modal-template
            v-model="dialogOpen"
            :title="$t('cnpjSearch.title')"
            card-style="min-width: 320px; max-width: 800px; width: 95vw"
            card-class="cnpj-search-modal"
            @close="closeDialog"
        >
            <!-- Campo de pesquisa -->
            <div class="q-mb-xs">
                <q-input
                    v-model="cnpjInput"
                    :label="$t('cnpjSearch.inputLabel')"
                    :mask="$t('forms.masks.docLegalPerson')"
                    outlined
                    dense
                    :loading="loading"
                    :disable="loading"
                    @keyup.enter="searchCNPJ"
                    :error="!!inputError"
                    :error-message="inputError"
                    @blur="validateCNPJField"
                >
                    <template v-slot:prepend>
                        <q-icon name="business" />
                    </template>
                    <template v-slot:append>
                        <!-- Botão limpar -->
                        <q-btn
                            v-if="cnpjInput && !loading"
                            icon="clear"
                            flat
                            round
                            dense
                            @click="clearInput"
                            :disable="loading"
                            class="q-mr-xs"
                        >
                            <q-tooltip>{{ $t('cnpjSearch.clear') }}</q-tooltip>
                        </q-btn>

                        <!-- Botão pesquisar -->
                        <q-btn
                            icon="search"
                            flat
                            round
                            dense
                            @click="searchCNPJ"
                            :loading="loading"
                            :disable="!cnpjInput || loading || !!inputError"
                        />
                    </template>
                </q-input>
                <div class="text-caption text-grey q-mt-xs">
                    {{ $t('cnpjSearch.hint') }}
                </div>
            </div>

            <!-- Resultados da pesquisa -->
            <div v-if="searchResult && !loading" class="q-mt-sm">
                <q-separator class="q-mb-xs" />

                <div class="text-subtitle1 q-mb-xs">
                    {{ $t('cnpjSearch.results') }}
                </div>

                <!-- Dados da empresa -->
                <div class="row q-col-gutter-xs">
                    <!-- Informações básicas -->
                    <div class="col-12">
                        <q-card flat bordered>
                            <q-card-section dense class="q-pa-xs">
                                <div class="text-subtitle2 q-mb-xs">
                                    {{ $t('cnpjSearch.basicInfo') }}
                                </div>

                                <div class="row q-col-gutter-xs">
                                    <div class="col-12 col-sm-6">
                                        <div class="text-caption text-grey">
                                            {{ $t('cnpjSearch.fields.cnpj') }}
                                        </div>
                                        <div class="text-body2">
                                            {{ formatCNPJ(searchResult.taxId) }}
                                        </div>
                                    </div>

                                    <div class="col-12 col-sm-6">
                                        <div class="text-caption text-grey">
                                            {{
                                                $t(
                                                    'cnpjSearch.fields.companyName'
                                                )
                                            }}
                                        </div>
                                        <div class="text-body2">
                                            {{
                                                searchResult.company?.name ||
                                                'N/A'
                                            }}
                                        </div>
                                    </div>

                                    <div
                                        v-if="searchResult.alias"
                                        class="col-12 col-sm-6"
                                    >
                                        <div class="text-caption text-grey">
                                            {{
                                                $t(
                                                    'cnpjSearch.fields.tradeName'
                                                )
                                            }}
                                        </div>
                                        <div class="text-body2">
                                            {{ searchResult.alias }}
                                        </div>
                                    </div>

                                    <div
                                        v-if="searchResult.status"
                                        class="col-12 col-sm-6"
                                    >
                                        <div class="text-caption text-grey">
                                            {{ $t('cnpjSearch.fields.status') }}
                                        </div>
                                        <div class="text-body2">
                                            <q-chip
                                                :color="
                                                    searchResult.status.id === 2
                                                        ? 'positive'
                                                        : 'negative'
                                                "
                                                text-color="white"
                                                size="sm"
                                            >
                                                {{ searchResult.status.text }}
                                            </q-chip>
                                        </div>
                                    </div>

                                    <div
                                        v-if="searchResult.founded"
                                        class="col-12 col-sm-6"
                                    >
                                        <div class="text-caption text-grey">
                                            {{
                                                $t(
                                                    'cnpjSearch.fields.foundingDate'
                                                )
                                            }}
                                        </div>
                                        <div class="text-body2">
                                            {{
                                                $d(
                                                    new Date(
                                                        searchResult.founded
                                                    ),
                                                    'short'
                                                )
                                            }}
                                        </div>
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>

                    <!-- Endereço -->
                    <div v-if="searchResult.address" class="col-12">
                        <q-card flat bordered>
                            <q-card-section dense class="q-pa-xs">
                                <div class="text-subtitle2 q-mb-xs">
                                    {{ $t('cnpjSearch.address') }}
                                </div>

                                <div class="row q-col-gutter-xs">
                                    <div
                                        v-if="searchResult.address.street"
                                        class="col-12 col-md-8"
                                    >
                                        <div class="text-caption text-grey">
                                            {{ $t('cnpjSearch.fields.street') }}
                                        </div>
                                        <div class="text-body2">
                                            {{ searchResult.address.street }}
                                            {{
                                                searchResult.address.number
                                                    ? `, ${searchResult.address.number}`
                                                    : ''
                                            }}
                                        </div>
                                    </div>

                                    <div
                                        v-if="searchResult.address.zip"
                                        class="col-12 col-md-4"
                                    >
                                        <div class="text-caption text-grey">
                                            {{
                                                $t('cnpjSearch.fields.zipCode')
                                            }}
                                        </div>
                                        <div class="text-body2">
                                            {{
                                                formatCEP(
                                                    searchResult.address.zip
                                                )
                                            }}
                                        </div>
                                    </div>

                                    <div
                                        v-if="searchResult.address.district"
                                        class="col-12 col-md-4"
                                    >
                                        <div class="text-caption text-grey">
                                            {{
                                                $t(
                                                    'cnpjSearch.fields.neighborhood'
                                                )
                                            }}
                                        </div>
                                        <div class="text-body2">
                                            {{ searchResult.address.district }}
                                        </div>
                                    </div>

                                    <div
                                        v-if="searchResult.address.city"
                                        class="col-12 col-md-4"
                                    >
                                        <div class="text-caption text-grey">
                                            {{ $t('cnpjSearch.fields.city') }}
                                        </div>
                                        <div class="text-body2">
                                            {{ searchResult.address.city }}
                                            {{
                                                searchResult.address.state
                                                    ? ` - ${searchResult.address.state}`
                                                    : ''
                                            }}
                                        </div>
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>

                    <!-- Informações de contato -->
                    <div
                        v-if="
                            (searchResult.phones &&
                                searchResult.phones.length > 0) ||
                            (searchResult.emails &&
                                searchResult.emails.length > 0)
                        "
                        class="col-12"
                    >
                        <q-card flat bordered>
                            <q-card-section dense class="q-pa-xs">
                                <div class="text-subtitle2 q-mb-xs">
                                    {{ $t('cnpjSearch.contact') }}
                                </div>

                                <div class="row q-col-gutter-xs">
                                    <!-- Telefones -->
                                    <div
                                        v-if="
                                            searchResult.phones &&
                                            searchResult.phones.length > 0
                                        "
                                        class="col-12 col-md-6"
                                    >
                                        <div class="text-caption text-grey">
                                            {{ $t('cnpjSearch.fields.phone') }}
                                        </div>
                                        <div class="text-body2">
                                            <div
                                                v-for="(
                                                    phone, index
                                                ) in searchResult.phones"
                                                :key="index"
                                            >
                                                {{
                                                    formatPhone(
                                                        phone.area,
                                                        phone.number
                                                    )
                                                }}
                                                <span
                                                    v-if="phone.type"
                                                    class="text-caption text-grey q-ml-sm"
                                                >
                                                    ({{ phone.type }})
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- E-mails -->
                                    <div
                                        v-if="
                                            searchResult.emails &&
                                            searchResult.emails.length > 0
                                        "
                                        class="col-12 col-md-6"
                                    >
                                        <div class="text-caption text-grey">
                                            {{ $t('cnpjSearch.fields.email') }}
                                        </div>
                                        <div class="text-body2">
                                            <div
                                                v-for="(
                                                    email, index
                                                ) in searchResult.emails"
                                                :key="index"
                                            >
                                                {{ email.address }}
                                                <span
                                                    v-if="email.ownership"
                                                    class="text-caption text-grey q-ml-sm"
                                                >
                                                    ({{ email.ownership }})
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>

                    <!-- Atividade principal -->
                    <div v-if="searchResult.mainActivity" class="col-12">
                        <q-card flat bordered>
                            <q-card-section dense class="q-pa-xs">
                                <div class="text-subtitle2 q-mb-xs">
                                    {{ $t('cnpjSearch.mainActivity') }}
                                </div>
                                <div class="text-body2">
                                    {{ searchResult.mainActivity.text }}
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>
                </div>
            </div>

            <!-- Estado de loading -->
            <div v-if="loading" class="text-center q-py-lg">
                <q-spinner-dots size="40px" color="primary" />
                <div class="q-mt-md text-body2">
                    {{ $t('cnpjSearch.searching') }}
                </div>
            </div>

            <!-- Mensagem de erro -->
            <div v-if="errorMessage && !loading" class="q-mt-sm">
                <q-banner class="bg-negative text-white" rounded dense>
                    <template v-slot:avatar>
                        <q-icon name="error" />
                    </template>
                    {{ errorMessage }}
                </q-banner>
            </div>

            <!-- Ações -->
            <template #actions>
                <q-btn
                    flat
                    color="negative"
                    :label="$t('buttons.cancel')"
                    @click="closeDialog"
                    :disable="loading"
                />
                <q-btn
                    v-if="searchResult && !loading"
                    color="positive"
                    :label="$t('buttons.confirm')"
                    @click="confirmSelection"
                />
            </template>
        </modal-template>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import ModalTemplate from '@/components/ModalTemplate.vue'
import { axiosCnpj } from '@/services/consultacnpj'
import type { CNPJData, CNPJSearchProps } from '@/interfaces/CNPJ'
// Função local para validar CNPJ
const validateCNPJLocal = (cnpj: string): boolean => {
    const numbers = cnpj.replace(/[^\d]+/g, '')
    if (numbers.length !== 14 || !!numbers.match(/(\d)\1{13}/)) return false

    const digits = numbers.split('').map(x => parseInt(x))
    const calc = (x: number) => {
        const slice = digits.slice(0, x)
        let factor = x - 7
        let sum = 0

        for (let i = x; i >= 1; i--) {
            const n = slice[x - i] ?? 0
            sum += n * factor--
            if (factor < 2) factor = 9
        }

        const result = 11 - (sum % 11)
        return result > 9 ? 0 : result
    }
    return calc(12) === digits[12] && calc(13) === digits[13]
}

const { t: $t, d: $d } = useI18n()

// Props
const props = withDefaults(defineProps<CNPJSearchProps>(), {
    buttonOnly: true,
    size: 'md',
    color: 'primary',
    variant: 'flat'
})

// Emits
const emit = defineEmits<{
    select: [data: CNPJData]
    cancel: []
}>()

// Estado do componente
const dialogOpen = ref(false)
const loading = ref(false)
const cnpjInput = ref('')
const searchResult = ref<CNPJData | null>(null)
const errorMessage = ref('')
const inputError = ref('')

// Computed
const tooltipText = computed(() => {
    return props.tooltipText || $t('cnpjSearch.tooltip')
})

// Métodos
const openSearchDialog = (): void => {
    dialogOpen.value = true
    cnpjInput.value = ''
    searchResult.value = null
    errorMessage.value = ''
    inputError.value = ''
}

const closeDialog = (): void => {
    if (loading.value) return

    dialogOpen.value = false
    cnpjInput.value = ''
    searchResult.value = null
    errorMessage.value = ''
    inputError.value = ''

    if (props.onCancel) {
        props.onCancel()
    }
    emit('cancel')
}

const formatCNPJ = (cnpj: string): string => {
    // Remove caracteres não numéricos
    const numbers = cnpj.replace(/\D/g, '')

    // Aplica a máscara XX.XXX.XXX/XXXX-XX
    if (numbers.length === 14) {
        return numbers.replace(
            /(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/,
            '$1.$2.$3/$4-$5'
        )
    }

    return cnpj
}

const formatCEP = (cep: string): string => {
    // Remove caracteres não numéricos
    const numbers = cep.replace(/\D/g, '')

    // Aplica a máscara XXXXX-XXX
    if (numbers.length === 8) {
        return numbers.replace(/(\d{5})(\d{3})/, '$1-$2')
    }

    return cep
}

const formatPhone = (area: string, number: string): string => {
    try {
        // Remove caracteres não numéricos
        const cleanArea = area.replace(/\D/g, '')
        const cleanNumber = number.replace(/\D/g, '')

        // Formata o telefone baseado no tamanho
        if (cleanNumber.length === 9) {
            // Celular: (XX) 9XXXX-XXXX
            return `(${cleanArea}) ${cleanNumber.replace(
                /(\d{5})(\d{4})/,
                '$1-$2'
            )}`
        } else if (cleanNumber.length === 8) {
            // Fixo: (XX) XXXX-XXXX
            return `(${cleanArea}) ${cleanNumber.replace(
                /(\d{4})(\d{4})/,
                '$1-$2'
            )}`
        } else {
            // Formato genérico se não seguir padrões conhecidos
            return `(${cleanArea}) ${cleanNumber}`
        }
    } catch {
        return `(${area}) ${number}`
    }
}

const validateCNPJ = (cnpj: string): boolean => {
    const numbers = cnpj.replace(/\D/g, '')

    if (numbers.length !== 14) {
        inputError.value = $t('cnpjSearch.errors.invalidFormat')
        return false
    }

    // Usar a função de validação completa
    if (!validateCNPJLocal(cnpj)) {
        inputError.value = $t('cnpjSearch.errors.invalidCNPJ')
        return false
    }

    inputError.value = ''
    return true
}

const validateCNPJField = (): void => {
    if (cnpjInput.value.trim()) {
        validateCNPJ(cnpjInput.value)
    }
}

const clearInput = (): void => {
    cnpjInput.value = ''
    inputError.value = ''
    searchResult.value = null
    errorMessage.value = ''
}

const searchCNPJ = async (): Promise<void> => {
    if (!cnpjInput.value.trim()) {
        inputError.value = $t('cnpjSearch.errors.required')
        return
    }

    if (!validateCNPJ(cnpjInput.value)) {
        return
    }

    loading.value = true
    errorMessage.value = ''
    searchResult.value = null

    try {
        const cnpjNumbers = cnpjInput.value.replace(/\D/g, '')
        const response = await axiosCnpj.get(`/${cnpjNumbers}`)

        if (response.data) {
            searchResult.value = response.data as CNPJData
        } else {
            errorMessage.value = $t('cnpjSearch.errors.notFound')
        }
    } catch (error: unknown) {
        // eslint-disable-next-line no-console
        console.error('Erro na consulta CNPJ:', error)
        const err = error as { response?: { data?: { message?: string } } }
        errorMessage.value =
            err.response?.data?.message || $t('cnpjSearch.errors.searchError')
    } finally {
        loading.value = false
    }
}

const confirmSelection = (): void => {
    if (searchResult.value) {
        if (props.onSelect) {
            props.onSelect(searchResult.value)
        }
        emit('select', searchResult.value)
        closeDialog()
    }
}

// Expor métodos para uso programático
defineExpose({
    openSearchDialog,
    closeDialog,
    searchCNPJ
})
</script>

<style scoped>
.cnpj-search {
    display: inline-block;
}
</style>
