<template>
    <div class="rich-text-editor">
        <q-card flat bordered>
            <!-- Quill Editor Container -->
            <div
                ref="editorContainer"
                class="quill-editor-container"
                :style="{ minHeight: minHeight, maxHeight: maxHeight }"
            ></div>

            <!-- Contador de caracteres e palavras -->
            <q-card-section class="q-pa-xs bg-grey-2" v-if="showCharCount">
                <div class="text-caption text-right">
                    <span class="q-mr-md text-grey-7">
                        {{ wordCount }} {{ $t('richEditor.words') }}
                    </span>
                    <span
                        :class="{
                            'text-grey-7':
                                !maxLength || characterCount <= maxLength,
                            'text-orange':
                                maxLength &&
                                characterCount > maxLength * 0.8 &&
                                characterCount <= maxLength,
                            'text-negative':
                                maxLength && characterCount > maxLength
                        }"
                    >
                        {{ characterCount }} {{ $t('richEditor.characters') }}
                        <span v-if="maxLength"> / {{ maxLength }}</span>
                    </span>

                    <!-- Barra de progresso para limite de caracteres -->
                    <div v-if="maxLength" class="q-mt-xs">
                        <q-linear-progress
                            :value="characterCount / maxLength"
                            :color="
                                characterCount > maxLength
                                    ? 'negative'
                                    : characterCount > maxLength * 0.8
                                      ? 'orange'
                                      : 'primary'
                            "
                            size="2px"
                            rounded
                        />
                    </div>
                </div>
            </q-card-section>
        </q-card>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import Quill from 'quill'
import 'quill/dist/quill.snow.css'

// Types
interface RichTextEditorProps {
    modelValue: string
    placeholder?: string
    readonly?: boolean
    maxLength?: number
    showCharCount?: boolean
    minHeight?: string
    maxHeight?: string
    rows?: number
}

interface RichTextEditorEmits {
    (e: 'update:modelValue', value: string): void
    (e: 'change', value: string): void
    (e: 'focus'): void
    (e: 'blur'): void
}

// Props & Emits
const props = withDefaults(defineProps<RichTextEditorProps>(), {
    modelValue: '',
    placeholder: '',
    readonly: false,
    showCharCount: true,
    minHeight: '200px',
    maxHeight: '400px',
    rows: 10
})

const emit = defineEmits<RichTextEditorEmits>()

// Composables
const { t } = useI18n()

// Refs
const editorContainer = ref<HTMLElement | null>(null)
const quillInstance = ref<Quill | null>(null)
const content = ref(props.modelValue)

// Computed
const characterCount = computed(() => {
    if (!quillInstance.value) return 0
    const text = quillInstance.value.getText()
    return Math.max(0, text.length - 1) // Quill adiciona um \n no final
})

const wordCount = computed(() => {
    if (!quillInstance.value) return 0
    const text = quillInstance.value.getText().trim()
    if (!text) return 0
    const words = text.split(/\s+/).filter(word => word.length > 0)
    return words.length
})

// Quill Configuration
const getQuillConfig = () => ({
    theme: 'snow',
    placeholder: props.placeholder || t('richEditor.placeholder'),
    readOnly: props.readonly,
    modules: {
        toolbar: [
            // Fonte e tamanho
            [{ font: [] }, { size: ['small', false, 'large', 'huge'] }],

            // Formatação básica
            ['bold', 'italic', 'underline', 'strike'],

            // Cores
            [{ color: [] }, { background: [] }],

            // Scripts
            [{ script: 'sub' }, { script: 'super' }],

            // Títulos
            [{ header: [1, 2, 3, 4, 5, 6, false] }],

            // Alinhamento
            [{ align: [] }],

            // Listas
            [{ list: 'ordered' }, { list: 'bullet' }],

            // Indentação
            [{ indent: '-1' }, { indent: '+1' }],

            // Citação e código
            ['blockquote', 'code-block'],

            // Links e imagens
            ['link', 'image'],

            // Utilitários
            ['clean']
        ]
    }
})

// Methods
const initializeQuill = (): void => {
    if (!editorContainer.value) return

    quillInstance.value = new Quill(editorContainer.value, getQuillConfig())

    // Definir conteúdo inicial
    if (props.modelValue) {
        quillInstance.value.root.innerHTML = props.modelValue
    }

    // Força direção LTR
    quillInstance.value.root.dir = 'ltr'
    quillInstance.value.root.style.direction = 'ltr'
    quillInstance.value.root.style.textAlign = 'left'

    // Event listeners
    quillInstance.value.on('text-change', handleTextChange)
    quillInstance.value.on('selection-change', handleSelectionChange)

    // Aplicar limite de caracteres se definido
    if (props.maxLength) {
        quillInstance.value.on('text-change', handleMaxLength)
    }
}

const handleTextChange = (): void => {
    if (!quillInstance.value) return

    const html = quillInstance.value.root.innerHTML
    content.value = html
    emit('update:modelValue', html)
    emit('change', html)
}

const handleSelectionChange = (
    range: { index: number; length: number } | null
): void => {
    if (range) {
        emit('focus')
    } else {
        emit('blur')
    }
}

const handleMaxLength = (): void => {
    if (!quillInstance.value || !props.maxLength) return

    const text = quillInstance.value.getText()
    if (text.length > props.maxLength) {
        quillInstance.value.deleteText(props.maxLength, text.length)
    }
}

// Lifecycle
onMounted(() => {
    nextTick(() => {
        initializeQuill()
    })
})

onUnmounted(() => {
    if (quillInstance.value) {
        quillInstance.value.off('text-change')
        quillInstance.value.off('selection-change')
    }
})

// Watch
watch(
    () => props.modelValue,
    newValue => {
        if (quillInstance.value && newValue !== content.value) {
            quillInstance.value.root.innerHTML = newValue
            content.value = newValue
        }
    }
)

watch(
    () => props.readonly,
    newValue => {
        if (quillInstance.value) {
            quillInstance.value.enable(!newValue)
        }
    }
)

// Expose methods
defineExpose({
    focus: () => quillInstance.value?.focus(),
    blur: () => quillInstance.value?.blur(),
    getContent: () => content.value,
    setContent: (newContent: string) => {
        if (quillInstance.value) {
            quillInstance.value.root.innerHTML = newContent
            content.value = newContent
        }
    },
    insertText: (text: string) => {
        if (quillInstance.value && !props.readonly) {
            const selection = quillInstance.value.getSelection()
            if (selection) {
                quillInstance.value.insertText(selection.index, text)
            }
        }
    },
    clear: () => {
        if (quillInstance.value) {
            quillInstance.value.setContents([])
            content.value = ''
            emit('update:modelValue', '')
            emit('change', '')
        }
    }
})
</script>

<style scoped>
.rich-text-editor {
    width: 100%;
}

.quill-editor-container {
    border: none;
}

/* Força direção LTR no Quill */
:deep(.ql-editor) {
    direction: ltr !important;
    text-align: left !important;
    unicode-bidi: normal !important;
    font-family: 'Roboto', sans-serif;
    line-height: 1.5;
}

:deep(.ql-editor *) {
    direction: ltr !important;
    text-align: inherit !important;
}

/* Mantém apenas o estilo padrão do Quill com pequenos ajustes */
:deep(.ql-toolbar) {
    flex-wrap: wrap;
}

:deep(.ql-container) {
    font-family: 'Roboto', sans-serif;
}

/* Estilos para elementos do editor */
:deep(.ql-editor h1) {
    font-size: 2.5em;
    margin: 0.5em 0;
}

:deep(.ql-editor h2) {
    font-size: 2em;
    margin: 0.5em 0;
}

:deep(.ql-editor h3) {
    font-size: 1.75em;
    margin: 0.5em 0;
}

:deep(.ql-editor blockquote) {
    border-left: 4px solid #1976d2;
    padding-left: 1em;
    margin: 1em 0;
    background-color: #f5f5f5;
    font-style: italic;
}

:deep(.ql-editor pre) {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 12px;
    margin: 1em 0;
}

:deep(.ql-editor img) {
    max-width: 100%;
    height: auto;
}
</style>
