<template>
    <div class="data-table-container">
        <q-table
            v-bind="$attrs"
            :columns="visibleColumns"
            :rows="rows"
            :loading="loading"
            :pagination="currentPagination"
            @update:pagination="onPaginationUpdate"
            @request="onRequest"
            row-key="id"
            class="data-table"
            :style="{ height: tableHeight }"
            :rows-per-page-label="$t('dataTable.pagination.rowsPerPageLabel')"
            :selected-rows-label="selectedRowsLabelFunction"
            :pagination-label="paginationLabelFunction"
            separator="horizontal"
        >
            <!-- Passar todos os slots para o q-table -->
            <template v-for="(_, slot) in $slots" v-slot:[slot]="scope">
                <slot :name="slot" v-bind="scope" />
            </template>

            <!-- Template para células editáveis -->
            <template #body-cell="props">
                <q-td :props="props">
                    <!-- <PERSON><PERSON><PERSON><PERSON> editável -->
                    <div
                        v-if="
                            isColumnEditable(props.col) &&
                            isEditing(props.row.id, props.col.name)
                        "
                        class="inline-edit-container"
                    >
                        <!-- Campo de input baseado no tipo -->
                        <q-input
                            v-if="getFieldType(props.col) === 'text'"
                            v-model="editingTextValue"
                            :mask="props.col.mask"
                            :error="
                                !!validationError &&
                                isEditing(props.row.id, props.col.name)
                            "
                            :error-message="validationError || undefined"
                            dense
                            outlined
                            autofocus
                            @keyup.enter="handleEnterKey(props.row, props.col)"
                            @keyup.escape="cancelEdit"
                            @keyup.arrow-down="
                                handleArrowDown(props.row, props.col)
                            "
                            @keyup.arrow-up="
                                handleArrowUp(props.row, props.col)
                            "
                            @keyup.shift.arrow-right="
                                handleShiftRight(props.row, props.col)
                            "
                            @keyup.shift.arrow-left="
                                handleShiftLeft(props.row, props.col)
                            "
                            @blur="cancelEdit"
                            class="inline-edit-input"
                        >
                            <template #append>
                                <q-btn
                                    icon="check"
                                    size="xs"
                                    flat
                                    round
                                    color="positive"
                                    @click="confirmEdit(props.row, props.col)"
                                />
                                <q-btn
                                    icon="close"
                                    size="xs"
                                    flat
                                    round
                                    color="negative"
                                    @click="cancelEdit"
                                />
                            </template>
                        </q-input>

                        <q-input
                            v-else-if="getFieldType(props.col) === 'number'"
                            v-model.number="editingNumberValue"
                            type="number"
                            :error="
                                !!validationError &&
                                isEditing(props.row.id, props.col.name)
                            "
                            :error-message="validationError || undefined"
                            dense
                            outlined
                            autofocus
                            @keyup.enter="handleEnterKey(props.row, props.col)"
                            @keyup.escape="cancelEdit"
                            @keyup.arrow-down="
                                handleArrowDown(props.row, props.col)
                            "
                            @keyup.arrow-up="
                                handleArrowUp(props.row, props.col)
                            "
                            @keyup.shift.arrow-right="
                                handleShiftRight(props.row, props.col)
                            "
                            @keyup.shift.arrow-left="
                                handleShiftLeft(props.row, props.col)
                            "
                            @blur="cancelEdit"
                            class="inline-edit-input"
                        >
                            <template #append>
                                <q-btn
                                    icon="check"
                                    size="xs"
                                    flat
                                    round
                                    color="positive"
                                    @click="confirmEdit(props.row, props.col)"
                                />
                                <q-btn
                                    icon="close"
                                    size="xs"
                                    flat
                                    round
                                    color="negative"
                                    @click="cancelEdit"
                                />
                            </template>
                        </q-input>

                        <q-toggle
                            v-else-if="getFieldType(props.col) === 'boolean'"
                            v-model="editingBooleanValue"
                            @update:model-value="
                                confirmEdit(props.row, props.col)
                            "
                            class="inline-edit-toggle"
                        />

                        <q-select
                            v-else-if="getFieldType(props.col) === 'select'"
                            v-model="editingValue"
                            :options="getSelectOptions(props.col)"
                            dense
                            outlined
                            autofocus
                            @update:model-value="
                                confirmEdit(props.row, props.col)
                            "
                            @keyup.escape="cancelEdit"
                            @keyup.arrow-down="
                                handleArrowDown(props.row, props.col)
                            "
                            @keyup.arrow-up="
                                handleArrowUp(props.row, props.col)
                            "
                            @keyup.shift.arrow-right="
                                handleShiftRight(props.row, props.col)
                            "
                            @keyup.shift.arrow-left="
                                handleShiftLeft(props.row, props.col)
                            "
                            @blur="cancelEdit"
                            class="inline-edit-select"
                        />
                    </div>

                    <!-- Célula normal (não editável ou não em edição) -->
                    <div
                        v-else
                        @dblclick="startEdit(props.row, props.col)"
                        :class="{
                            'editable-cell': isColumnEditable(props.col)
                        }"
                    >
                        {{ props.value }}
                    </div>
                </q-td>
            </template>

            <!-- Toolbar personalizada -->
            <template v-slot:top v-if="showToolbar">
                <div class="table-toolbar-container">
                    <!-- Primeira linha: Título e botão de colunas -->
                    <div
                        class="row items-center q-gutter-xs full-width"
                        v-if="title"
                    >
                        <div class="col-grow">
                            <div class="text-h6">{{ title }}</div>
                        </div>

                        <!-- Botão de gerenciar colunas -->
                        <q-btn
                            flat
                            round
                            dense
                            icon="view_column"
                            size="sm"
                            @click="showColumnManager = true"
                            :color="isDarkMode ? 'grey-4' : 'grey-7'"
                        >
                            <q-tooltip>{{
                                $t('dataTable.manageColumns')
                            }}</q-tooltip>
                        </q-btn>
                    </div>

                    <!-- Segunda linha: Botões de ação, Filtro rápido e botão de colunas -->
                    <div class="row items-center q-gutter-xs full-width">
                        <!-- Slot para botões de ação (adicionar, ações, refresh) -->
                        <div class="row items-center q-gutter-xs">
                            <slot name="toolbar-buttons"></slot>
                        </div>

                        <!-- Espaçador para empurrar filtro para direita no desktop -->
                        <div class="col-grow gt-sm"></div>

                        <!-- Filtro rápido - na direita no desktop, embaixo no mobile -->
                        <div
                            class="col-12 col-md-auto"
                            v-if="
                                showQuickFilter &&
                                filterColumns &&
                                filterColumns.length > 0
                            "
                        >
                            <table-column-filter
                                :model-value="quickFilterValue || null"
                                :columns="filterColumns"
                                @update:model-value="onQuickFilterUpdate"
                                @filter="onQuickFilter"
                            />
                        </div>

                        <!-- Botão de gerenciar colunas (sempre visível) -->
                        <div class="q-ml-xs">
                            <q-btn
                                flat
                                round
                                dense
                                icon="view_column"
                                size="sm"
                                @click="showColumnManager = true"
                                :color="isDarkMode ? 'grey-4' : 'grey-7'"
                            >
                                <q-tooltip>{{
                                    $t('dataTable.manageColumns')
                                }}</q-tooltip>
                            </q-btn>
                        </div>
                    </div>
                </div>
            </template>
        </q-table>
    </div>

    <!-- Modal de gerenciamento de colunas -->
    <q-dialog v-model="showColumnManager" persistent>
        <q-card class="column-manager-card">
            <!-- Header com ícone e título -->
            <q-card-section class="row items-center q-pb-none">
                <q-avatar
                    size="40px"
                    :color="isDarkMode ? 'grey-8' : 'grey-2'"
                    text-color="primary"
                >
                    <q-icon name="view_column" size="20px" />
                </q-avatar>
                <div class="q-ml-md">
                    <div class="text-h6 text-weight-medium">
                        {{ $t('dataTable.manageColumns') }}
                    </div>
                    <div class="text-caption text-grey-6">
                        {{ $t('dataTable.dragToReorder') }}
                    </div>
                </div>
                <q-space />
                <q-btn
                    icon="close"
                    flat
                    round
                    dense
                    @click="showColumnManager = false"
                    :color="isDarkMode ? 'grey-4' : 'grey-7'"
                />
            </q-card-section>

            <q-separator class="q-my-sm" />

            <!-- Lista de colunas com design melhorado -->
            <q-card-section class="q-pt-sm">
                <div ref="dragContainer" class="column q-gutter-xs">
                    <q-card
                        v-for="(column, index) in managedColumnsFiltered"
                        :key="column.name"
                        :data-column-index="index"
                        draggable="true"
                        @dragstart="onDragStart($event, index)"
                        @dragover="onDragOver"
                        @drop="onDrop($event, index)"
                        @dragend="onDragEnd"
                        class="drag-item column-item"
                        flat
                        bordered
                    >
                        <q-card-section class="column-item-content">
                            <!-- Drag handle com visual melhorado -->
                            <div class="drag-handle">
                                <q-icon
                                    name="drag_indicator"
                                    :color="isDarkMode ? 'grey-4' : 'grey-6'"
                                    size="20px"
                                />
                            </div>

                            <!-- Ícone da coluna -->
                            <q-avatar
                                size="32px"
                                :color="
                                    column.visible
                                        ? 'primary'
                                        : isDarkMode
                                          ? 'grey-7'
                                          : 'grey-4'
                                "
                                :text-color="
                                    column.visible
                                        ? 'white'
                                        : isDarkMode
                                          ? 'grey-4'
                                          : 'grey-6'
                                "
                                class="column-avatar"
                            >
                                <q-icon
                                    :name="getColumnIcon(column.name)"
                                    size="16px"
                                />
                            </q-avatar>

                            <!-- Informações da coluna -->
                            <div class="column-info">
                                <div class="column-title">
                                    {{ column.label }}
                                </div>
                                <div class="column-description">
                                    {{ getColumnDescription(column.name) }}
                                </div>
                            </div>

                            <!-- Toggle de visibilidade -->
                            <q-toggle
                                v-model="column.visible"
                                :color="isDarkMode ? 'primary' : 'primary'"
                                size="md"
                                :icon="
                                    column.visible
                                        ? 'visibility'
                                        : 'visibility_off'
                                "
                                class="column-toggle"
                            />
                        </q-card-section>
                    </q-card>
                </div>
            </q-card-section>

            <q-separator />

            <!-- Footer com botões melhorados -->
            <q-card-actions class="column-manager-footer">
                <div class="row justify-end items-center q-gutter-x-xs">
                    <q-btn
                        :label="$t('buttons.reset')"
                        icon="refresh"
                        :color="isDarkMode ? 'grey-4' : 'grey-7'"
                        outline
                        @click="resetColumns"
                        class="footer-btn footer-btn-reset"
                    />
                    <div class="footer-spacer"></div>
                    <q-btn
                        :label="$t('buttons.cancel')"
                        :color="isDarkMode ? 'grey-4' : 'grey-7'"
                        flat
                        @click="showColumnManager = false"
                        class="footer-btn footer-btn-cancel"
                    />
                    <q-btn
                        :label="$t('buttons.apply')"
                        color="primary"
                        icon="check"
                        @click="applyColumnChanges"
                        unelevated
                        class="footer-btn footer-btn-apply"
                    />
                </div>
            </q-card-actions>
        </q-card>
    </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { Notify } from 'quasar'
import { useTheme } from 'src/composables/useTheme'
import TableColumnFilter from '@/components/filters/TableColumnFilter.vue'
import * as yup from 'yup'
// Removendo @formkit/drag-and-drop por problemas de compatibilidade
// Vamos usar HTML5 drag and drop nativo

// Interfaces
interface TableColumn {
    name: string
    label: string
    field: string | ((row: Record<string, unknown>) => unknown)
    align?: 'left' | 'right' | 'center'
    sortable?: boolean
    visible?: boolean
    order?: number
    // Propriedades para edição inline
    inlineEdit?: boolean
    fieldType?: 'text' | 'number' | 'select' | 'boolean'
    selectOptions?: Array<{ label: string; value: unknown }>
    // Propriedades para máscaras e validação
    mask?: string
    validation?: unknown // Schema Yup
    validationMessage?: string
    [key: string]: unknown
}

interface TableConfig {
    columns: Array<{
        name: string
        label: string
        field: string | ((row: Record<string, unknown>) => unknown)
        visible: boolean
        order: number
    }>
    pagination: {
        rowsPerPage: number
        page: number
        sortBy: string
        descending: boolean
    }
}

// Props
interface Props {
    columns: TableColumn[]
    rows: Record<string, unknown>[]
    title?: string
    showToolbar?: boolean
    loading?: boolean
    pagination?: Record<string, unknown>
    filter?: Record<string, unknown>
    tableName?: string
    saveTableOptions?: boolean
    maxHeight?: string
    height?: string
    showQuickFilter?: boolean
    filterColumns?: Array<{
        label: string
        value: string
        field: string
        type: 'text' | 'number' | 'boolean' | 'select'
    }>
    quickFilterValue?: {
        column: string
        content: string | number | boolean
    } | null
    actionColumnWidth?: number | 'auto'
    actionsPerRow?: number
}

const props = withDefaults(defineProps<Props>(), {
    title: '',
    showToolbar: true,
    loading: false,
    saveTableOptions: true,
    maxHeight: 'calc(100vh - 200px)',
    height: 'auto',
    showQuickFilter: false,
    filterColumns: () => [],
    quickFilterValue: null,
    actionColumnWidth: 'auto',
    actionsPerRow: 3
})

// Emits
const emit = defineEmits<{
    'update:pagination': [pagination: Record<string, unknown>]
    request: [requestProps: Record<string, unknown>]
    'columns-changed': [columns: TableColumn[]]
    'restore-pagination': [pagination: Record<string, unknown>]
    'update:quickFilterValue': [
        value: { column: string; content: string | number | boolean } | null
    ]
    'quick-filter': [
        value: { column: string; content: string | number | boolean } | null
    ]
    'inline-edit': [
        data: {
            row: Record<string, unknown>
            column: string
            value: unknown
            oldValue: unknown
        }
    ]
}>()

// Composables
const route = useRoute()
const { t } = useI18n()
const { isDarkMode } = useTheme()

// State
const showColumnManager = ref(false)
const managedColumns = ref<TableColumn[]>([])
const internalPagination = ref({
    rowsPerPage: 10,
    page: 1,
    sortBy: '',
    descending: false
})
const isInitializing = ref(true)
const dragContainer = ref<HTMLElement | null>(null)

// Inline editing state
const editingCell = ref<{ rowId: string | number; columnName: string } | null>(
    null
)
const editingValue = ref<string | number | boolean | null>(null)
const originalValue = ref<unknown>(null)
const validationError = ref<string | null>(null)
const isValidating = ref(false)

// Computed
const currentPagination = computed(() => {
    return props.pagination || internalPagination.value
})

const dynamicStorageKey = computed(() => {
    if (props.tableName) {
        const routeName =
            route.path.replace(/\//g, '-').replace(/^-/, '') || 'default'
        return `table-${props.tableName}-${routeName}`
    }
    return `data-table-${route.path}`
})

// Não usar filtro interno do q-table para evitar erro "toLowerCase is not a function"
// A filtragem é feita via API através do evento @request

// Separar colunas de ações das demais
const actionColumns = computed(() => {
    return props.columns.filter(
        col =>
            col.name === 'actions' ||
            col.name === 'acoes' ||
            col.field === 'actions' ||
            col.field === 'acoes'
    )
})

const managedColumnsFiltered = computed(() => {
    return managedColumns.value.filter(
        col =>
            col.name !== 'actions' &&
            col.name !== 'acoes' &&
            col.field !== 'actions' &&
            col.field !== 'acoes'
    )
})

// Computed para calcular a largura da coluna de ações
const actionColumnWidth = computed(() => {
    if (props.actionColumnWidth !== 'auto') {
        return props.actionColumnWidth
    }

    // Calcular largura baseada no número de ações com espaçamento adequado
    // Cada botão redondo tem 24px + gap de 4px entre eles + padding lateral confortável
    const buttonWidth = 24 // Tamanho real dos botões redondos (.q-btn--round)
    const gapWidth = 4 // Espaçamento mais confortável entre botões
    const lateralPadding = 12 // Padding lateral mais generoso

    const calculatedWidth =
        props.actionsPerRow * buttonWidth +
        (props.actionsPerRow - 1) * gapWidth +
        lateralPadding

    // Largura mínima de 70px (para 1 botão) e máxima de 160px (para muitos botões)
    // Valores aumentados para dar mais espaço aos botões
    return Math.max(70, Math.min(160, calculatedWidth))
})

// Computed para estilos da coluna de ações baseado no tema
const actionColumnStyles = computed(() => {
    const width = `${actionColumnWidth.value}px`

    return {
        headerStyle: `position: sticky; right: 0; z-index: 2; background: ${isDarkMode.value ? '#424242' : '#f5f5f5'}; border-left: 1px solid ${isDarkMode.value ? '#333' : '#e0e0e0'}; min-width: ${width}; max-width: ${width}; width: ${width};`,
        style: `position: sticky; right: 0; z-index: 2; border-left: 1px solid ${isDarkMode.value ? '#333' : '#e0e0e0'}; min-width: ${width}; max-width: ${width}; width: ${width};`
    }
})

const visibleColumns = computed(() => {
    // Colunas gerenciáveis (sem ações)
    const managedCols = managedColumnsFiltered.value
        .filter(col => col.visible !== false)
        .sort((a, b) => (a.order || 0) - (b.order || 0))

    // Adicionar colunas de ações sempre no final com estilos dinâmicos baseados no tema
    const actionCols = actionColumns.value.map(col => ({
        ...col,
        headerStyle: actionColumnStyles.value.headerStyle,
        style: actionColumnStyles.value.style,
        classes: 'action-column',
        headerClasses: 'action-column-header'
    }))

    return [...managedCols, ...actionCols]
})

const tableHeight = computed(() => {
    if (props.height !== 'auto') {
        return props.height
    }
    return props.maxHeight
})

// Computed properties para edição inline
const editingTextValue = computed({
    get: () =>
        typeof editingValue.value === 'string' ? editingValue.value : '',
    set: (value: string) => {
        editingValue.value =
            typeof value === 'string'
                ? value.toUpperCase()
                : String(value || '').toUpperCase()
    }
})

const editingNumberValue = computed({
    get: () =>
        typeof editingValue.value === 'number' ? editingValue.value : 0,
    set: (value: number) => {
        editingValue.value = value
    }
})

const editingBooleanValue = computed({
    get: () =>
        typeof editingValue.value === 'boolean' ? editingValue.value : false,
    set: (value: boolean) => {
        editingValue.value = value
    }
})

// Função para tradução de linhas selecionadas
const selectedRowsLabelFunction = computed(() => {
    return (count: number) => {
        if (count === 0) {
            return t('dataTable.pagination.selectedRowsLabel.none')
        } else if (count === 1) {
            return t('dataTable.pagination.selectedRowsLabel.one')
        } else {
            return t('dataTable.pagination.selectedRowsLabel.many', { count })
        }
    }
})

// Função para tradução do label de paginação "1-5 of 10"
const paginationLabelFunction = computed(() => {
    return (
        firstRowIndex: number,
        endRowIndex: number,
        totalRowsNumber: number
    ) => {
        return t('dataTable.pagination.paginationLabel', {
            first: firstRowIndex,
            last: endRowIndex,
            total: totalRowsNumber
        })
    }
})

// Computed properties removidas para simplificar

// Methods
const initializeDefaultColumns = (): TableColumn[] => {
    return props.columns
        .filter(
            col =>
                col.name !== 'actions' &&
                col.name !== 'acoes' &&
                col.field !== 'actions' &&
                col.field !== 'acoes'
        )
        .map((col, index) => ({
            ...col,
            visible: col.visible !== false,
            order: col.order || index
        }))
}

// Constante para a chave centralizada do localStorage
const TABLES_CONFIG_KEY = 'dataTables_configurations'

// Interface para o objeto centralizado
interface TablesConfigurations {
    [tableKey: string]: TableConfig
}

// Função utilitária para obter todas as configurações (para debug)
const getAllTableConfigurations = (): TablesConfigurations => {
    try {
        const allConfigs = localStorage.getItem(TABLES_CONFIG_KEY)
        return allConfigs
            ? (JSON.parse(allConfigs) as TablesConfigurations)
            : {}
    } catch {
        return {}
    }
}

// Função utilitária para limpar configuração específica
const clearTableConfiguration = (tableKey: string): void => {
    try {
        const allConfigurations = getAllTableConfigurations()
        delete allConfigurations[tableKey]
        localStorage.setItem(
            TABLES_CONFIG_KEY,
            JSON.stringify(allConfigurations)
        )
    } catch {
        // Ignorar erros
    }
}

// Função utilitária para limpar todas as configurações
const clearAllTableConfigurations = (): void => {
    try {
        localStorage.removeItem(TABLES_CONFIG_KEY)
    } catch {
        // Ignorar erros
    }
}

// Função para migrar configurações antigas para o novo formato
const migrateOldConfig = (tableKey: string): void => {
    try {
        // Verificar se existe configuração antiga
        const oldConfig = localStorage.getItem(tableKey)

        if (oldConfig) {
            const config = JSON.parse(oldConfig) as TableConfig

            // Carregar configurações centralizadas existentes
            let allConfigurations: TablesConfigurations = {}
            const existingConfigs = localStorage.getItem(TABLES_CONFIG_KEY)

            if (existingConfigs) {
                allConfigurations = JSON.parse(
                    existingConfigs
                ) as TablesConfigurations
            }

            // Adicionar configuração migrada
            allConfigurations[tableKey] = config

            // Salvar no novo formato
            localStorage.setItem(
                TABLES_CONFIG_KEY,
                JSON.stringify(allConfigurations)
            )

            // Remover configuração antiga
            localStorage.removeItem(tableKey)
        }
    } catch {
        // Ignorar erros de migração
    }
}

const loadTableConfig = (): TableConfig | null => {
    if (!props.saveTableOptions) return null

    try {
        const tableKey = dynamicStorageKey.value

        // Tentar migrar configuração antiga primeiro
        migrateOldConfig(tableKey)

        const allConfigs = localStorage.getItem(TABLES_CONFIG_KEY)

        if (allConfigs) {
            const configurations = JSON.parse(
                allConfigs
            ) as TablesConfigurations
            const config = configurations[tableKey]

            if (config) {
                // Validar se as colunas salvas ainda existem
                const currentColumnNames = props.columns.map(col => col.name)
                const validColumns = config.columns.filter(col =>
                    currentColumnNames.includes(col.name)
                )

                if (validColumns.length > 0) {
                    return {
                        ...config,
                        columns: validColumns
                    }
                }
            }
        }
    } catch {
        // Ignorar erro e usar configuração padrão
    }

    return null
}

const saveTableConfig = (): void => {
    if (!props.saveTableOptions) return

    try {
        const tableKey = dynamicStorageKey.value

        // Usar paginação externa se disponível, senão usar interna
        const currentPagination = props.pagination || internalPagination.value

        const config: TableConfig = {
            columns: managedColumns.value.map(col => ({
                name: col.name,
                label: col.label,
                field: col.field,
                visible: col.visible !== false,
                order: col.order || 0
            })),
            pagination: {
                rowsPerPage: Number(currentPagination.rowsPerPage) || 25,
                // Não salvar a página atual - sempre começar na página 1
                page: 1,
                sortBy: String(currentPagination.sortBy || ''),
                descending: Boolean(currentPagination.descending)
            }
        }

        // Carregar configurações existentes ou criar objeto vazio
        let allConfigurations: TablesConfigurations = {}
        const existingConfigs = localStorage.getItem(TABLES_CONFIG_KEY)

        if (existingConfigs) {
            allConfigurations = JSON.parse(
                existingConfigs
            ) as TablesConfigurations
        }

        // Atualizar configuração desta tabela específica
        allConfigurations[tableKey] = config

        // Salvar todas as configurações de volta
        localStorage.setItem(
            TABLES_CONFIG_KEY,
            JSON.stringify(allConfigurations)
        )
    } catch {
        // Ignorar erro de salvamento
    }
}

const initializeColumns = (): void => {
    isInitializing.value = true

    const savedConfig = loadTableConfig()

    if (savedConfig && savedConfig.columns.length > 0) {
        // Usar configuração salva
        const savedColumns = savedConfig.columns
            .map(savedCol => {
                const originalCol = props.columns.find(
                    col => col.name === savedCol.name
                )
                return originalCol
                    ? {
                          ...originalCol,
                          visible: savedCol.visible,
                          order: savedCol.order
                      }
                    : null
            })
            .filter(Boolean) as TableColumn[]

        // Adicionar colunas novas que não estavam salvas
        const savedColumnNames = savedColumns.map(col => col.name)
        const newColumns = props.columns
            .filter(col => !savedColumnNames.includes(col.name))
            .map((col, index) => ({
                ...col,
                visible: col.visible !== false,
                order: savedColumns.length + index
            }))

        managedColumns.value = [...savedColumns, ...newColumns]

        // Restaurar paginação
        if (savedConfig.pagination) {
            if (!props.pagination) {
                // Se não há paginação externa, usar interna
                internalPagination.value = { ...savedConfig.pagination }
            } else {
                // Se há paginação externa, emitir para a página pai
                emit('restore-pagination', savedConfig.pagination)
            }
        }
    } else {
        // Usar configuração padrão
        managedColumns.value = initializeDefaultColumns()
    }

    // Finalizar inicialização para permitir que o watch funcione
    setTimeout(() => {
        isInitializing.value = false
    }, 100)
}

const onPaginationUpdate = (newPagination: Record<string, unknown>): void => {
    if (!props.pagination) {
        internalPagination.value = {
            ...internalPagination.value,
            ...newPagination
        }
    }
    emit('update:pagination', newPagination)
}

const onRequest = (requestProps: Record<string, unknown>): void => {
    emit('request', requestProps)
}

// Quick filter methods
const onQuickFilterUpdate = (
    value: { column: string; content: string | number | boolean } | null
): void => {
    emit('update:quickFilterValue', value)
}

const onQuickFilter = (
    value: { column: string; content: string | number | boolean } | null
): void => {
    emit('quick-filter', value)
}

const applyColumnChanges = (): void => {
    emit('columns-changed', visibleColumns.value)
    showColumnManager.value = false

    Notify.create({
        message: t('dataTable.notifications.configApplied'),
        color: 'positive',
        position: 'bottom',
        timeout: 2000,
        icon: 'check_circle'
    })
}

const resetColumns = (): void => {
    managedColumns.value = initializeDefaultColumns()

    if (!props.pagination) {
        internalPagination.value = {
            rowsPerPage: 10,
            page: 1,
            sortBy: '',
            descending: false
        }
    }

    emit('columns-changed', visibleColumns.value)
    showColumnManager.value = false

    Notify.create({
        message: t('dataTable.notifications.configReset'),
        color: 'info',
        position: 'bottom',
        timeout: 2000,
        icon: 'refresh'
    })
}

// Drag and Drop nativo HTML5
let draggedIndex: number | null = null

const onDragStart = (event: DragEvent, index: number): void => {
    draggedIndex = index
    if (event.dataTransfer) {
        event.dataTransfer.effectAllowed = 'move'
        event.dataTransfer.setData('text/html', index.toString())
    }

    // Adicionar classe visual
    const target = event.target as HTMLElement
    target.classList.add('dragging')
}

const onDragOver = (event: DragEvent): void => {
    event.preventDefault()
    if (event.dataTransfer) {
        event.dataTransfer.dropEffect = 'move'
    }
}

const onDrop = (event: DragEvent, dropIndex: number): void => {
    event.preventDefault()

    if (draggedIndex !== null && draggedIndex !== dropIndex) {
        // Reordenar apenas as colunas gerenciáveis (sem ações)
        const filteredColumns = [...managedColumnsFiltered.value]
        const draggedColumn = filteredColumns[draggedIndex]

        if (draggedColumn) {
            // Remover da posição original
            filteredColumns.splice(draggedIndex, 1)

            // Inserir na nova posição
            filteredColumns.splice(dropIndex, 0, draggedColumn)

            // Atualizar a propriedade order
            filteredColumns.forEach((column, index) => {
                column.order = index
            })

            // Atualizar managedColumns mantendo as colunas de ações
            const actionCols = managedColumns.value.filter(
                col =>
                    col.name === 'actions' ||
                    col.name === 'acoes' ||
                    col.field === 'actions' ||
                    col.field === 'acoes'
            )

            managedColumns.value = [...filteredColumns, ...actionCols]
        }
    }
}

const onDragEnd = (event: DragEvent): void => {
    // Remover classe visual
    const target = event.target as HTMLElement
    target.classList.remove('dragging')

    draggedIndex = null
}

// Métodos para melhorar a aparência visual
const getColumnIcon = (columnName: string): string => {
    const iconMap: Record<string, string> = {
        id: 'tag',
        descricao: 'description',
        tipo: 'category',
        conteudo: 'article',
        ativo: 'toggle_on',
        status: 'toggle_on',
        actions: 'settings',
        acoes: 'settings',
        nome: 'person',
        email: 'email',
        telefone: 'phone',
        data: 'event',
        valor: 'attach_money',
        quantidade: 'numbers',
        observacoes: 'note',
        created_at: 'schedule',
        updated_at: 'update'
    }

    return iconMap[columnName] || 'view_column'
}

const getColumnDescription = (columnName: string): string => {
    const descriptionMap: Record<string, string> = {
        id: t('forms.labels.uniqueIdentifier'),
        descricao: t('forms.labels.itemDescription'),
        tipo: t('forms.labels.categoryType'),
        conteudo: t('forms.labels.textContent'),
        ativo: t('forms.labels.activeStatus'),
        status: t('forms.labels.currentStatus'),
        actions: t('forms.labels.availableActions'),
        acoes: t('forms.labels.availableActions'),
        nome: t('forms.labels.fullName'),
        email: t('forms.labels.emailAddress'),
        telefone: t('forms.labels.phoneNumber'),
        data: t('forms.labels.dateField'),
        valor: t('forms.labels.monetaryValue'),
        quantidade: t('forms.labels.numericQuantity'),
        observacoes: t('forms.labels.additionalNotes'),
        created_at: t('forms.labels.creationDate'),
        updated_at: t('forms.labels.lastUpdate')
    }

    return descriptionMap[columnName] || t('forms.labels.tableColumn')
}

// Funções para edição inline
const isColumnEditable = (column: TableColumn): boolean => {
    return !!column.inlineEdit
}

const getFieldType = (column: TableColumn): string => {
    return (column.fieldType as string) || 'text'
}

const getSelectOptions = (
    column: TableColumn
): Array<{ label: string; value: unknown }> => {
    return (
        (column.selectOptions as Array<{ label: string; value: unknown }>) || []
    )
}

// Funções para validação
const validateField = async (
    value: unknown,
    column: TableColumn
): Promise<boolean> => {
    if (!column.validation) return true

    try {
        isValidating.value = true
        validationError.value = null

        // Validar usando o schema Yup
        await (column.validation as yup.Schema).validate(value)
        return true
    } catch (error) {
        if (error instanceof yup.ValidationError) {
            validationError.value = column.validationMessage || error.message
        } else {
            validationError.value = 'Erro de validação'
        }
        return false
    } finally {
        isValidating.value = false
    }
}

const isEditing = (rowId: string | number, columnName: string): boolean => {
    return (
        editingCell.value?.rowId === rowId &&
        editingCell.value?.columnName === columnName
    )
}

const startEdit = (row: Record<string, unknown>, column: TableColumn): void => {
    if (!isColumnEditable(column)) return

    const fieldName =
        typeof column.field === 'string' ? column.field : column.name
    const currentValue = row[fieldName]

    editingCell.value = {
        rowId: row.id as string | number,
        columnName: column.name
    }
    editingValue.value = currentValue as string | number | boolean | null
    originalValue.value = currentValue
}

const confirmEdit = async (
    row: Record<string, unknown>,
    column: TableColumn
): Promise<void> => {
    if (!editingCell.value) return

    const fieldName =
        typeof column.field === 'string' ? column.field : column.name
    const newValue = editingValue.value
    const oldValue = originalValue.value

    // Validar campo se houver validação configurada
    if (column.validation) {
        const isValid = await validateField(newValue, column)
        if (!isValid) {
            // Mostrar erro de validação
            Notify.create({
                type: 'negative',
                message: validationError.value || 'Valor inválido',
                position: 'top'
            })
            return // Não confirma a edição se inválido
        }
    }

    // Emitir evento de edição
    emit('inline-edit', {
        row,
        column: fieldName,
        value: newValue,
        oldValue
    })

    // Limpar estado de edição
    cancelEdit()
}

const cancelEdit = (): void => {
    editingCell.value = null
    editingValue.value = null
    originalValue.value = null
    validationError.value = null
    isValidating.value = false
}

// Funções de navegação por teclado
const handleEnterKey = (
    row: Record<string, unknown>,
    column: TableColumn
): void => {
    // Apenas confirmar edição atual (sem navegar)
    confirmEdit(row, column)
}

const handleArrowDown = (
    row: Record<string, unknown>,
    column: TableColumn
): void => {
    // Confirmar edição atual
    confirmEdit(row, column)

    // Mover para próxima linha na mesma coluna
    moveToNextRow(row, column)
}

const handleArrowUp = (
    row: Record<string, unknown>,
    column: TableColumn
): void => {
    // Confirmar edição atual
    confirmEdit(row, column)

    // Mover para linha anterior na mesma coluna
    moveToPreviousRow(row, column)
}

const handleShiftRight = (
    row: Record<string, unknown>,
    column: TableColumn
): void => {
    // Confirmar edição atual
    confirmEdit(row, column)

    // Mover para próxima coluna editável na mesma linha
    moveToNextEditableColumn(row, column)
}

const handleShiftLeft = (
    row: Record<string, unknown>,
    column: TableColumn
): void => {
    // Confirmar edição atual
    confirmEdit(row, column)

    // Mover para coluna anterior editável na mesma linha
    moveToPreviousEditableColumn(row, column)
}

const moveToNextEditableColumn = (
    row: Record<string, unknown>,
    column: TableColumn
): void => {
    const editableColumns = visibleColumns.value.filter(col =>
        isColumnEditable(col)
    )
    const currentIndex = editableColumns.findIndex(
        col => col.name === column.name
    )

    if (currentIndex < editableColumns.length - 1) {
        // Próxima coluna na mesma linha
        const nextColumn = editableColumns[currentIndex + 1]
        if (nextColumn) {
            nextTick(() => {
                startEdit(row, nextColumn)
            })
        }
    }
}

const moveToPreviousEditableColumn = (
    row: Record<string, unknown>,
    column: TableColumn
): void => {
    const editableColumns = visibleColumns.value.filter(col =>
        isColumnEditable(col)
    )
    const currentIndex = editableColumns.findIndex(
        col => col.name === column.name
    )

    if (currentIndex > 0) {
        // Coluna anterior na mesma linha
        const previousColumn = editableColumns[currentIndex - 1]
        if (previousColumn) {
            nextTick(() => {
                startEdit(row, previousColumn)
            })
        }
    }
}

const moveToNextRow = (
    row: Record<string, unknown>,
    column: TableColumn
): void => {
    const currentRowIndex = props.rows.findIndex(r => r.id === row.id)

    if (currentRowIndex < props.rows.length - 1) {
        // Próxima linha na mesma coluna
        const nextRow = props.rows[currentRowIndex + 1]
        if (nextRow) {
            nextTick(() => {
                startEdit(nextRow, column)
            })
        }
    }
}

const moveToPreviousRow = (
    row: Record<string, unknown>,
    column: TableColumn
): void => {
    const currentRowIndex = props.rows.findIndex(r => r.id === row.id)

    if (currentRowIndex > 0) {
        // Linha anterior na mesma coluna
        const previousRow = props.rows[currentRowIndex - 1]
        if (previousRow) {
            nextTick(() => {
                startEdit(previousRow, column)
            })
        }
    }
}

// Watch para salvar automaticamente
watch(
    () => [managedColumns.value, internalPagination.value, props.pagination],
    () => {
        if (!isInitializing.value) {
            saveTableConfig()
        }
    },
    { deep: true }
)

// Watch para mudanças nas props.columns
watch(
    () => props.columns,
    () => {
        initializeColumns()
    },
    { deep: true }
)

// Drag and drop nativo não precisa de configuração especial

// Lifecycle
onMounted(() => {
    initializeColumns()
})

// Expor funções utilitárias para debug e manutenção
defineExpose({
    getAllTableConfigurations,
    clearTableConfiguration,
    clearAllTableConfigurations,
    getCurrentTableKey: () => dynamicStorageKey.value
})
</script>

<style scoped>
/* Garantir que inputs e selects tenham exatamente o mesmo tamanho */
:deep(.input-box.q-field .q-field__control),
:deep(.input-box.q-select .q-field__control) {
    height: 32px !important;
    min-height: 32px !important;
    max-height: 32px !important;
    font-size: 16px;
}

:deep(.input-box.q-field .q-field__marginal),
:deep(.input-box.q-select .q-field__marginal) {
    height: 32px !important;
    font-size: 16px;
}

/* Controlar elementos internos do select para igualar ao input */
:deep(.input-box.q-select .q-field__native) {
    height: 30px !important;
    min-height: 30px !important;
    line-height: 30px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
}

:deep(.input-box.q-select .q-field__control-container) {
    height: 30px !important;
    min-height: 30px !important;
    display: flex !important;
    align-items: center !important;
}

:deep(.input-box.q-select .q-select__selected) {
    height: 30px !important;
    line-height: 30px !important;
    display: flex !important;
    align-items: center !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Forçar alinhamento do texto selecionado */
:deep(.input-box.q-select .q-select__selected span) {
    line-height: 30px !important;
    display: flex !important;
    align-items: center !important;
    height: 30px !important;
}

/* Ajustar posicionamento do label para ficar alinhado */
:deep(.input-box .q-field__label) {
    top: 50% !important;
    transform: translateY(-50%) !important;
    line-height: 1 !important;
}

/* Quando o campo tem valor, manter label no topo */
:deep(.input-box.q-field--float .q-field__label) {
    top: 0 !important;
    transform: translateY(-50%) !important;
}

/* Correção específica para select com valor */
:deep(.input-box.q-select.q-field--float .q-field__label),
:deep(.input-box.q-select.q-field--filled .q-field__label) {
    top: 0 !important;
    transform: translateY(-50%) !important;
    font-size: 12px !important;
}

/* Garantir que o conteúdo do select fique centralizado */
:deep(.input-box.q-select.q-field--float .q-field__native),
:deep(.input-box.q-select.q-field--filled .q-field__native) {
    padding-top: 6px !important;
    display: flex !important;
    align-items: center !important;
}

/* Ajustar também o container de controle */
:deep(.input-box.q-select.q-field--float .q-field__control-container),
:deep(.input-box.q-select.q-field--filled .q-field__control-container) {
    padding-top: 1px !important;
    display: flex !important;
    align-items: center !important;
}

/* Ajustar o texto selecionado especificamente */
:deep(.input-box.q-select.q-field--float .q-select__selected),
:deep(.input-box.q-select.q-field--filled .q-select__selected) {
    margin-top: 1px !important;
    height: 24px !important;
    line-height: 24px !important;
    display: flex !important;
    align-items: center !important;
}
/* Estilos para coluna de ações - versão simplificada */
.data-table :deep(th.action-column-header),
.data-table :deep(th:last-child) {
    position: sticky;
    right: 0;
    z-index: 3;
    background-color: #f5f5f5 !important;
    border-left: 1px solid #e0e0e0 !important;
    text-align: center;
}

.data-table :deep(td.action-column),
.data-table :deep(td:last-child) {
    position: sticky;
    right: 0;
    z-index: 3;
    border-left: 1px solid #e0e0e0 !important;
    text-align: center;
}

/* Zebrado simplificado */
.data-table :deep(tbody tr:nth-child(odd) td) {
    background: #ffffff !important;
}

.data-table :deep(tbody tr:nth-child(even) td) {
    background: #f8f8f8 !important;
}

.data-table :deep(tbody tr:hover td) {
    background: rgba(0, 0, 0, 0.08) !important;
}

/* Manter background da coluna de ações durante hover */
.data-table :deep(tbody tr:hover td.action-column),
.data-table :deep(tbody tr:hover td:last-child) {
    background: #f5f5f5 !important;
}

/* Modo escuro simplificado */
.body--dark .data-table :deep(th.action-column-header),
.body--dark .data-table :deep(th:last-child) {
    background-color: #424242 !important;
    border-left: 1px solid #333 !important;
    color: #ffffff !important;
}

.body--dark .data-table :deep(td.action-column),
.body--dark .data-table :deep(td:last-child) {
    border-left: 1px solid #333 !important;
}

.body--dark .data-table :deep(tbody tr:nth-child(odd) td) {
    background: #1e1e1e !important;
}

.body--dark .data-table :deep(tbody tr:nth-child(even) td) {
    background: #2a2a2a !important;
}

.body--dark .data-table :deep(tbody tr:hover td) {
    background: rgba(255, 255, 255, 0.1) !important;
}

/* Manter background da coluna de ações durante hover no modo escuro */
.body--dark .data-table :deep(tbody tr:hover td.action-column),
.body--dark .data-table :deep(tbody tr:hover td:last-child) {
    background: #424242 !important;
}

.body--dark .data-table :deep(thead tr) {
    background-color: #424242 !important;
}

.body--dark .data-table :deep(thead th) {
    background-color: #424242 !important;
    color: #ffffff !important;
}

/* Toolbar container */
.table-toolbar-container {
    width: 100%;
    padding: 4px 0;
}

.table-toolbar-container .row {
    margin: 0;
}

/* Layout responsivo para filtro */
@media (max-width: 1023px) {
    /* Mobile: filtro ocupa linha inteira */
    .table-toolbar-container .row {
        flex-wrap: wrap;
    }
}

@media (min-width: 1024px) {
    /* Desktop: filtro na direita */
    .table-toolbar-container .row {
        flex-wrap: nowrap;
    }
}

/* Modal de gerenciamento de colunas */
.column-manager-card {
    width: 100%;
    max-width: 600px;
    min-width: 320px;
    margin: 16px;
}

.column-manager-header {
    padding: 16px;
    font-weight: 500;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.column-manager-footer {
    padding: 12px 16px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    border-top: 1px solid rgba(0, 0, 0, 0.12);
}

.footer-btn {
    min-width: 100px;
}

.footer-btn-cancel {
    border: 1px solid rgba(0, 0, 0, 0.12);
}

.footer-btn-reset {
    background: rgba(var(--q-warning-rgb), 0.1);
}

.footer-btn-apply {
    background: var(--q-primary);
    color: white;
}

/* Estilos para itens de coluna */
.column-item {
    margin-bottom: 8px;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.column-item:hover {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.column-item-content {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    gap: 12px;
}

.drag-handle {
    cursor: grab;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.05);
}

.column-info {
    flex: 1;
    min-width: 0;
}

.column-title {
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.column-description {
    font-size: 0.8rem;
    color: rgba(0, 0, 0, 0.6);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.column-toggle {
    margin-left: auto;
}

/* Classe para item sendo arrastado */
.dragging {
    opacity: 0.5;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Estilos para reduzir o tamanho das linhas da tabela */
.data-table :deep(.q-table__container) {
    font-size: 0.8rem;
}

.data-table :deep(thead tr) {
    height: 32px !important;
}

.data-table :deep(tbody tr) {
    height: 28px !important;
}

.data-table :deep(th) {
    padding: 4px 6px !important;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1.1;
    vertical-align: middle;
    height: 32px !important;
}

.data-table :deep(td) {
    padding: 2px 6px !important;
    font-size: 0.8rem;
    line-height: 1.2;
    vertical-align: middle;
    height: 28px !important;
}

/* Reduzir tamanho dos elementos de UI */
.data-table :deep(.q-checkbox) {
    font-size: 14px;
}

.data-table :deep(.q-checkbox__inner) {
    width: 14px;
    height: 14px;
    min-width: 14px;
    min-height: 14px;
}

.data-table :deep(.q-btn) {
    min-height: 24px;
    padding: 2px 6px;
    font-size: 0.7rem;
}

.data-table :deep(.q-btn--round) {
    width: 24px;
    height: 24px;
    min-width: 24px;
    min-height: 24px;
}

.data-table :deep(.q-btn .q-icon) {
    font-size: 14px;
}

/* Header da tabela acinzentado */
.data-table :deep(thead tr) {
    background-color: #f5f5f5 !important;
}

.data-table :deep(thead th) {
    background-color: #f5f5f5 !important;
    color: #424242 !important;
}

/* Otimizações para telas menores */
@media (max-width: 768px) {
    .data-table :deep(thead tr) {
        height: 28px;
    }

    .data-table :deep(tbody tr) {
        height: 24px;
    }

    .data-table :deep(th) {
        padding: 2px 4px;
        font-size: 0.7rem;
    }

    .data-table :deep(td) {
        padding: 1px 4px;
        font-size: 0.75rem;
        line-height: 1.1;
    }

    .data-table :deep(.q-btn--round) {
        width: 20px;
        height: 20px;
        min-width: 20px;
        min-height: 20px;
    }

    .data-table :deep(.q-btn .q-icon) {
        font-size: 12px;
    }
}

/* Melhorias para modo escuro */
.body--dark .column-manager-card .column-item {
    border-color: rgba(255, 255, 255, 0.1);
    background: var(--q-dark-page);
}

.body--dark .column-manager-header,
.body--dark .column-manager-footer {
    border-color: rgba(255, 255, 255, 0.1);
}

.body--dark .column-description {
    color: rgba(255, 255, 255, 0.6);
}

.body--dark .drag-handle {
    background: rgba(255, 255, 255, 0.1);
}

.body--dark .footer-btn-cancel {
    border-color: rgba(255, 255, 255, 0.2);
    background: var(--q-dark-page);
}

/* Animações */
.column-item,
.footer-btn {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.footer-btn:hover {
    transform: translateY(-1px);
}

.footer-btn-apply:hover {
    box-shadow: 0 8px 25px rgba(var(--q-primary-rgb), 0.5);
}

/* Título da tabela */
.table-toolbar-container .text-h6 {
    font-size: 1rem;
    line-height: 1.2;
    background-color: #f5f5f5;
    padding: 6px 12px;
    border-radius: 4px;
    margin-bottom: 2px;
}

.body--dark .table-toolbar-container .text-h6 {
    background-color: #424242;
}

/* Estilos para edição inline */
.editable-cell {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.editable-cell:hover {
    background-color: rgba(25, 118, 210, 0.08);
}

.inline-edit-container {
    width: 100%;
    min-width: 120px;
}

.inline-edit-input {
    min-width: 120px;
}

.inline-edit-input :deep(.q-field__control) {
    min-height: 32px;
}

.inline-edit-input :deep(.q-field__append) {
    padding-left: 4px;
}

.inline-edit-toggle {
    margin: 0;
}

.inline-edit-select {
    min-width: 120px;
}

.inline-edit-select :deep(.q-field__control) {
    min-height: 32px;
}
</style>
