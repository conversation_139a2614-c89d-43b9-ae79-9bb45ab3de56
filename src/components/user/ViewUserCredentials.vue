<template>
    <div class="user-credentials">
        <q-card flat bordered class="q-pa-xs">
            <div class="row items-center q-col-gutter-xs">
                <!-- Foto do usuário -->
                <div class="col-auto">
                    <div class="avatar-container">
                        <q-avatar size="64px" class="shadow-1">
                            <img v-if="photoUrl" :src="photoUrl" />
                            <q-icon
                                v-else
                                name="person"
                                size="40px"
                                color="grey-6"
                            />
                        </q-avatar>
                        <div
                            v-if="showStatus"
                            class="status-indicator"
                            :class="[`status-${status}`]"
                        >
                            <q-tooltip>
                                {{ statusText }}
                            </q-tooltip>
                        </div>
                    </div>
                </div>

                <!-- Informações do usuário -->
                <div class="col">
                    <div class="row q-col-gutter-xs">
                        <!-- Nome completo -->
                        <div class="col-12">
                            <div
                                class="text-subtitle2 text-weight-medium q-mb-none"
                            >
                                {{ fullName || $t('user.notAvailable') }}
                            </div>
                            <div
                                class="text-caption text-grey-8 row items-center"
                            >
                                <span
                                    >@{{
                                        username || $t('user.notAvailable')
                                    }}</span
                                >
                                <span
                                    v-if="showStatus && showTextStatus"
                                    class="status-text q-ml-xs"
                                >
                                    • {{ statusText }}
                                </span>
                            </div>
                        </div>

                        <!-- Informações adicionais (slot) -->
                        <slot name="additional-info"></slot>
                    </div>
                </div>

                <!-- Função do usuário (se disponível) -->
                <div v-if="role" class="col-auto self-start">
                    <q-chip
                        dense
                        size="sm"
                        :color="roleColor"
                        text-color="white"
                        class="role-chip"
                    >
                        {{ role }}
                    </q-chip>
                </div>
            </div>
        </q-card>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

const { t: $t } = useI18n()

// Props
const props = defineProps({
    photoUrl: {
        type: String,
        default: ''
    },
    firstName: {
        type: String,
        default: ''
    },
    lastName: {
        type: String,
        default: ''
    },
    username: {
        type: String,
        default: ''
    },
    role: {
        type: String,
        default: ''
    },
    roleColor: {
        type: String,
        default: 'primary'
    },
    showStatus: {
        type: Boolean,
        default: false
    },
    showTextStatus: {
        type: Boolean,
        default: true
    },
    status: {
        type: String,
        default: 'offline',
        validator: (value: string) =>
            ['online', 'offline', 'away', 'busy', 'invisible'].includes(value)
    }
})

// Não há mais eventos, pois o componente é apenas para exibição

// Computed
const fullName = computed(() => {
    if (props.firstName || props.lastName) {
        return `${props.firstName} ${props.lastName}`.trim()
    }
    return ''
})

// Removida a propriedade statusColor, agora usando classes CSS

// Status text for tooltip
const statusText = computed(() => {
    return $t(`user.status.${props.status}`)
})
</script>

<style scoped>
.avatar-container {
    position: relative;
    display: inline-block;
}

.status-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    border: 1px solid white;
    transition: all 0.3s ease;
}

/* Status colors */
.status-online {
    background-color: #21ba45; /* verde */
}

.status-offline {
    background-color: #9e9e9e; /* cinza */
}

.status-away {
    background-color: #f2c037; /* amarelo */
}

.status-busy {
    background-color: #c10015; /* vermelho */
}

.status-invisible {
    background-color: #9e9e9e; /* cinza */
    opacity: 0.5;
}

:deep(.q-avatar) {
    background-color: #f5f5f5;
}

/* Status text style */
.status-text {
    font-size: 0.7rem;
    opacity: 0.8;
}

/* Role chip style */
.role-chip {
    font-size: 0.65rem;
    height: 18px;
    padding: 0 6px;
}
</style>
