<template>
    <div class="module-permissions">
        <div class="text-caption q-mb-xs text-center">
            {{ $t('forms.titles.modulePermissions') }}
        </div>

        <q-card flat bordered>
            <q-tabs
                v-model="moduloAtivo"
                dense
                class="text-grey"
                :active-color="isDarkMode ? 'white' : 'primary'"
                :indicator-color="isDarkMode ? 'white' : 'primary'"
                align="justify"
                narrow-indicator
            >
                <q-tab
                    v-for="modulo in modulos"
                    :key="modulo.value"
                    :name="modulo.value"
                    :label="modulo.label"
                />
            </q-tabs>

            <q-separator />

            <q-tab-panels v-model="moduloAtivo" animated>
                <q-tab-panel
                    v-for="modulo in modulos"
                    :key="modulo.value"
                    :name="modulo.value"
                >
                    <div class="row q-col-gutter-xs">
                        <div class="col-12">
                            <q-list bordered separator>
                                <q-expansion-item
                                    v-for="rota in getRotasPorModulo(
                                        modulo.value
                                    )"
                                    :key="rota.value"
                                    :label="rota.label"
                                    :header-class="
                                        isDarkMode
                                            ? 'text-white'
                                            : 'text-primary'
                                    "
                                    :expand-icon-class="
                                        isDarkMode
                                            ? 'text-white'
                                            : 'text-primary'
                                    "
                                    group="rotas"
                                >
                                    <q-card>
                                        <q-card-section>
                                            <div class="row q-col-gutter-xs">
                                                <div
                                                    class="col-12 col-sm-6 col-md-3"
                                                    v-for="acao in acoes"
                                                    :key="acao.value"
                                                >
                                                    <q-checkbox
                                                        :model-value="
                                                            getPermissaoValue(
                                                                modulo.value,
                                                                rota.value,
                                                                acao.value
                                                            )
                                                        "
                                                        @update:model-value="
                                                            setPermissaoValue(
                                                                modulo.value,
                                                                rota.value,
                                                                acao.value,
                                                                $event
                                                            )
                                                        "
                                                        :label="acao.label"
                                                        color="primary"
                                                    />
                                                </div>
                                            </div>
                                        </q-card-section>
                                    </q-card>
                                </q-expansion-item>
                            </q-list>
                        </div>
                    </div>
                </q-tab-panel>
            </q-tab-panels>
        </q-card>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'

const { t: $t } = useI18n()

// Props
const props = defineProps({
    // Módulos disponíveis
    modulos: {
        type: Array as () => Array<{ label: string; value: string }>,
        required: true
    },
    // Rotas disponíveis
    rotas: {
        type: Array as () => Array<{
            label: string
            value: string
            modulo: string
        }>,
        required: true
    },
    // Ações disponíveis
    acoes: {
        type: Array as () => Array<{ label: string; value: string }>,
        required: true
    },
    // Permissões atuais
    permissoes: {
        type: Object as () => Record<
            string,
            Record<string, Record<string, boolean>>
        >,
        required: true
    }
})

// Composables
import { useTheme } from '@/composables/useTheme'
const { isDarkMode } = useTheme()

// Emits
const emit = defineEmits(['update:permissoes'])

// Estado local
const moduloAtivo = ref('')

// Inicialização
onMounted(() => {
    // Define o primeiro módulo como ativo
    if (props.modulos && props.modulos.length > 0) {
        const primeiroModulo = props.modulos[0]
        if (primeiroModulo && primeiroModulo.value) {
            moduloAtivo.value = primeiroModulo.value
        }
    }
})

// Filtra as rotas por módulo
const getRotasPorModulo = (modulo: string) => {
    return props.rotas.filter(rota => rota.modulo === modulo)
}

// Métodos para manipular as permissões de forma segura
const getPermissaoValue = (
    modulo: string,
    rota: string,
    acao: string
): boolean => {
    if (!props.permissoes[modulo]) return false
    if (!props.permissoes[modulo][rota]) return false
    if (props.permissoes[modulo][rota][acao] === undefined) return false
    return props.permissoes[modulo][rota][acao]
}

const setPermissaoValue = (
    modulo: string,
    rota: string,
    acao: string,
    value: boolean
): void => {
    try {
        // Cria uma cópia profunda do objeto de permissões
        const novasPermissoes = JSON.parse(JSON.stringify(props.permissoes))

        // Garante que a estrutura existe
        if (!novasPermissoes[modulo]) {
            novasPermissoes[modulo] = {}
        }
        if (!novasPermissoes[modulo][rota]) {
            novasPermissoes[modulo][rota] = {}
        }

        // Define o valor
        novasPermissoes[modulo][rota][acao] = value

        // Emite o evento para atualizar o valor no componente pai
        emit('update:permissoes', novasPermissoes)
    } catch (error) {
        // Silencia o erro em produção
        if (process.env.NODE_ENV !== 'production') {
            // eslint-disable-next-line no-console
            console.error('Erro ao atualizar permissão:', error)
        }
    }
}
</script>

<style scoped>
.module-permissions {
    width: 100%;
}
</style>
