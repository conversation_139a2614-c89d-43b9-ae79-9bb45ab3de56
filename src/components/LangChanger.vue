<template>
    <div id="lang-changer">
        <q-btn @click="setModal" round flat color="primary">
            <img
                :src="getFlagUrl(selectedFlag)"
                alt="Selected Language Flag"
                class="flag-icon"
            />

            <q-tooltip>
                <span class="text-overline">
                    {{ $t('locale.changeLanguage') }}
                </span>
            </q-tooltip>
        </q-btn>

        <q-dialog v-model="isModalOpen">
            <q-card class="q-dialog-plugin">
                <q-card-section class="row items-center q-pb-none">
                    <q-card-section>
                        {{ $t('locale.changeLanguage') }}
                    </q-card-section>
                    <q-space />
                    <q-btn icon="close" flat round dense v-close-popup />
                </q-card-section>

                <q-card-section class="row items-center block">
                    <q-list>
                        <q-item
                            v-for="lang in languages"
                            :key="lang.flag"
                            clickable
                            @click="saveConfigLocale(lang)"
                        >
                            <q-item-section avatar>
                                <q-avatar>
                                    <img
                                        :src="getFlagUrl(lang.flag)"
                                        alt="Language Flag"
                                    />
                                </q-avatar>
                            </q-item-section>

                            <q-item-section>
                                <q-item-label>{{ lang.name }}</q-item-label>
                            </q-item-section>
                        </q-item>
                    </q-list>
                </q-card-section>
            </q-card>
        </q-dialog>
    </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useLocaleStore } from '../stores/LangStore'
import type { ILocaleConfig } from 'src/interfaces/Lang'

const selectedFlag = computed(() => useLocaleStore().selectedFlag)
const languages = computed(() => useLocaleStore().languages)

const getFlagUrl = (flag: string) => {
    return new URL(`/src/assets/images/flags/${flag}`, import.meta.url).href
}

const saveConfigLocale = (locale: ILocaleConfig) => {
    useLocaleStore().saveLocale(locale)
    setModal()
}

const isModalOpen = ref<boolean>(false)
const setModal = () => {
    isModalOpen.value = !isModalOpen.value
}
</script>

<style scoped>
.flag-icon {
    width: 15px;
    height: 15px;
    -webkit-filter: drop-shadow(1px 1px 0 white) drop-shadow(-1px 1px 0 white)
        drop-shadow(1px -1px 0 white) drop-shadow(-1px -1px 0 white);

    filter: drop-shadow(1px 1px 0 white) drop-shadow(-1px 1px 0 white)
        drop-shadow(1px -1px 0 white) drop-shadow(-1px -1px 0 white);
}
</style>
