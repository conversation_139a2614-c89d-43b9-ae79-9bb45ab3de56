<template>
    <q-dialog
        v-model="showDialog"
        maximized
        transition-show="slide-up"
        transition-hide="slide-down"
        class="image-viewer-dialog"
    >
        <q-card class="bg-black text-white">
            <!-- Header -->
            <q-card-section class="row items-center q-pa-sm bg-dark">
                <div class="text-h6">
                    {{ title || $t('imageViewer.title') }}
                </div>
                <q-space />

                <!-- Controles de zoom -->
                <q-btn
                    flat
                    round
                    icon="zoom_out"
                    @click="zoomOut"
                    :disable="scale <= minScale"
                    class="q-mr-xs"
                >
                    <q-tooltip>{{ $t('imageViewer.zoomOut') }}</q-tooltip>
                </q-btn>

                <div class="text-caption q-mx-sm">
                    {{ Math.round(scale * 100) }}%
                </div>

                <q-btn
                    flat
                    round
                    icon="zoom_in"
                    @click="zoomIn"
                    :disable="scale >= maxScale"
                    class="q-mr-xs"
                >
                    <q-tooltip>{{ $t('imageViewer.zoomIn') }}</q-tooltip>
                </q-btn>

                <q-btn
                    flat
                    round
                    icon="fit_screen"
                    @click="resetZoom"
                    class="q-mr-xs"
                >
                    <q-tooltip>{{ $t('imageViewer.fitScreen') }}</q-tooltip>
                </q-btn>

                <q-btn
                    flat
                    round
                    icon="fullscreen"
                    @click="toggleFullscreen"
                    class="q-mr-xs"
                >
                    <q-tooltip>{{ $t('imageViewer.fullscreen') }}</q-tooltip>
                </q-btn>

                <q-btn flat round icon="close" @click="closeViewer">
                    <q-tooltip>{{ $t('buttons.close') }}</q-tooltip>
                </q-btn>
            </q-card-section>

            <!-- Área da imagem -->
            <q-card-section class="image-container q-pa-none">
                <div
                    ref="imageContainer"
                    class="image-wrapper"
                    @wheel="handleWheel"
                    @mousedown="startPan"
                    @mousemove="handlePan"
                    @mouseup="endPan"
                    @mouseleave="endPan"
                >
                    <q-img
                        ref="imageElement"
                        :src="imageNormalized"
                        class="viewer-image"
                        :style="imageStyle"
                        @load="onImageLoad"
                        @error="onImageError"
                        :draggable="false"
                        fit="contain"
                        no-spinner
                        no-transition
                    />
                </div>

                <!-- Loading -->
                <div v-if="loading" class="absolute-center">
                    <q-spinner-dots size="50px" color="white" />
                </div>

                <!-- Erro -->
                <div v-if="error" class="absolute-center text-center">
                    <q-icon name="error" size="50px" color="negative" />
                    <div class="q-mt-md">{{ $t('imageViewer.loadError') }}</div>
                </div>
            </q-card-section>
        </q-card>
    </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { useFormUtils } from '@/composables/formUtils'
import { useAuthStore } from '@/stores/auth'

const { t: $t } = useI18n()
const { normalizeUrls } = useFormUtils()
const authStore = useAuthStore()

// Props
interface Props {
    modelValue: boolean
    imageUrl: string
    title?: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
    'update:modelValue': [value: boolean]
}>()

// Refs
const showDialog = ref(false)
const imageContainer = ref<HTMLElement>()
const imageElement = ref<HTMLImageElement>()
const loading = ref(true)
const error = ref(false)

// Zoom e Pan
const scale = ref(1)
const translateX = ref(0)
const translateY = ref(0)
const minScale = 0.1
const maxScale = 5

// Pan state
const isPanning = ref(false)
const lastPanPoint = ref({ x: 0, y: 0 })

// Computed
const imageStyle = computed(() => ({
    transform: `scale(${scale.value}) translate(${translateX.value}px, ${translateY.value}px)`,
    transformOrigin: 'center center',
    transition: isPanning.value ? 'none' : 'transform 0.2s ease'
}))

const imageNormalized = computed(() => {
    const normalizedUrl = normalizeUrls(props.imageUrl)

    if (normalizedUrl) {
        // Se a URL já contém parâmetros, adicionar o token com &
        // Se não contém parâmetros, adicionar o token com ?
        const separator = normalizedUrl.includes('?') ? '&' : '?'
        const token = authStore.token

        if (token) {
            return `${normalizedUrl}${separator}token=${encodeURIComponent(token)}`
        }

        return normalizedUrl
    }

    return ''
})

// Watch
watch(
    () => props.modelValue,
    newVal => {
        showDialog.value = newVal
        if (newVal) {
            resetViewer()
        }
    }
)

watch(showDialog, newVal => {
    emit('update:modelValue', newVal)
})

// Methods
const closeViewer = () => {
    showDialog.value = false
}

const resetViewer = () => {
    loading.value = true
    error.value = false
    scale.value = 1
    translateX.value = 0
    translateY.value = 0
}

const onImageLoad = () => {
    loading.value = false
    error.value = false
    nextTick(() => {
        fitToScreen()
    })
}

const onImageError = () => {
    loading.value = false
    error.value = true
}

const fitToScreen = () => {
    if (!imageContainer.value) return

    // Para q-img, vamos resetar para escala 1 que já faz o fit automaticamente
    scale.value = 1
    translateX.value = 0
    translateY.value = 0
}

const zoomIn = () => {
    const newScale = Math.min(scale.value * 1.2, maxScale)
    scale.value = newScale
}

const zoomOut = () => {
    const newScale = Math.max(scale.value / 1.2, minScale)
    scale.value = newScale
}

const resetZoom = () => {
    fitToScreen()
}

const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen()
    } else {
        document.exitFullscreen()
    }
}

const handleWheel = (event: WheelEvent) => {
    event.preventDefault()

    if (event.deltaY < 0) {
        zoomIn()
    } else {
        zoomOut()
    }
}

const startPan = (event: MouseEvent) => {
    if (scale.value <= 1) return

    isPanning.value = true
    lastPanPoint.value = { x: event.clientX, y: event.clientY }
}

const handlePan = (event: MouseEvent) => {
    if (!isPanning.value) return

    const deltaX = event.clientX - lastPanPoint.value.x
    const deltaY = event.clientY - lastPanPoint.value.y

    translateX.value += deltaX / scale.value
    translateY.value += deltaY / scale.value

    lastPanPoint.value = { x: event.clientX, y: event.clientY }
}

const endPan = () => {
    isPanning.value = false
}
</script>

<style scoped>
.image-viewer-dialog .q-dialog__inner {
    padding: 0;
}

.image-container {
    height: calc(100vh - 60px);
    overflow: hidden;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #000;
}

.image-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: grab;
}

.image-wrapper:active {
    cursor: grabbing;
}

.viewer-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    user-select: none;
}
</style>
