<template>
    <q-page class="q-pa-xs q-pb-none">
        <!-- Slot para conteúdo antes da tabela -->
        <slot name="before-table"></slot>

        <!-- Tabela DataTable -->
        <data-table
            :table-name="tableName"
            v-model:selected="selectedItems"
            :columns="columns"
            :rows="rows"
            :loading="loading || false"
            :filter="filter || {}"
            :pagination="pagination || {}"
            :save-table-options="saveTableOptions ?? true"
            :show-toolbar="showTableToolbar ?? true"
            :show-quick-filter="showQuickFilter ?? true"
            :filter-columns="filterColumns || []"
            v-model:quick-filter-value="quickFilterValueModel"
            :max-height="tableMaxHeight || 'calc(100vh - 95px)'"
            :selection="tableSelection || 'multiple'"
            :dense="tableDense ?? true"
            :flat="tableFlat ?? true"
            :bordered="tableBordered ?? true"
            :binary-state-sort="tableBinaryStateSort ?? true"
            :rows-per-page-options="
                rowsPerPageOptions || [10, 25, 50, 100, 500, 1000]
            "
            @request="$emit('table-request', $event)"
            @restore-pagination="$emit('restore-pagination', $event)"
            @quick-filter="$emit('quick-filter', $event)"
            @inline-edit="$emit('inline-edit', $event)"
            @columns-changed="$emit('columns-changed', $event)"
            v-bind="tableProps || {}"
            :actions-per-row="actionsPerRow ?? 2"
        >
            <!-- Botões da toolbar movidos para dentro da tabela -->
            <template #toolbar-buttons>
                <!-- Slot para botão de adicionar customizado -->
                <slot name="add-button">
                    <q-btn
                        v-if="showAddButton"
                        :label="addButtonLabel || $t('buttons.add')"
                        color="primary"
                        icon="add"
                        @click="$emit('add-clicked')"
                        size="sm"
                        dense
                        class="q-mr-xs"
                    />
                </slot>

                <!-- Slot para ações customizadas da toolbar -->
                <slot name="toolbar-actions">
                    <action-collection
                        v-if="toolbarActions && toolbarActions.length > 0"
                        :actions="toolbarActions"
                        :label="$t('buttons.actions')"
                        color="accent"
                        icon="arrow_drop_down_circle"
                        size="sm"
                        outline
                        dense
                        menu-anchor="bottom left"
                        menu-self="top left"
                        class="q-mr-xs"
                    />
                </slot>

                <!-- Slot para botões adicionais da toolbar -->
                <slot name="toolbar-extra-buttons"></slot>

                <!-- Botão de refresh -->
                <slot name="refresh-button">
                    <q-btn
                        :label="refreshButtonLabel || $t('buttons.refresh')"
                        color="positive"
                        outline
                        icon="refresh"
                        @click="$emit('refresh-clicked')"
                        size="sm"
                        dense
                        class="q-mr-xs"
                    />
                </slot>
            </template>

            <!-- Slots da tabela - passados diretamente para o DataTable -->
            <template
                v-for="(_, name) in $slots"
                :key="name"
                #[name]="slotData"
            >
                <slot
                    v-if="
                        typeof name === 'string' &&
                        (name.startsWith('body-cell-') ||
                            name.startsWith('header-cell-') ||
                            name === 'no-data')
                    "
                    :name="name"
                    v-bind="slotData"
                ></slot>
            </template>

            <!-- Slot padrão para coluna de ações se não fornecido -->
            <template
                v-if="!$slots['body-cell-actions'] && showDefaultActions"
                #body-cell-actions="props"
            >
                <q-td :props="props">
                    <div class="row q-gutter-xs no-wrap justify-center">
                        <q-btn
                            v-if="showEditAction"
                            icon="edit"
                            color="primary"
                            flat
                            round
                            dense
                            size="sm"
                            @click="$emit('edit-clicked', props.row)"
                        >
                            <q-tooltip>{{ $t('buttons.edit') }}</q-tooltip>
                        </q-btn>
                        <q-btn
                            v-if="showDeleteAction"
                            icon="delete"
                            color="negative"
                            flat
                            round
                            dense
                            size="sm"
                            @click="$emit('delete-clicked', props.row)"
                        >
                            <q-tooltip>{{ $t('buttons.delete') }}</q-tooltip>
                        </q-btn>
                        <!-- Slot para ações extras na linha -->
                        <slot name="row-extra-actions" :row="props.row"></slot>
                    </div>
                </q-td>
            </template>

            <!-- Slot padrão para coluna de status se não fornecido -->
            <template
                v-if="!$slots['body-cell-ativo'] && showDefaultStatusColumn"
                #body-cell-ativo="props"
            >
                <q-td :props="props">
                    <q-chip
                        :label="getStatusLabel(props.row.ativo)"
                        :color="props.row.ativo ? 'positive' : 'negative'"
                        text-color="white"
                        size="sm"
                    />
                </q-td>
            </template>

            <!-- Slot padrão para quando não há dados -->
            <template v-if="!$slots['no-data']" #no-data>
                <div class="full-width row flex-center q-gutter-xs q-pa-lg">
                    <q-icon size="2em" name="sentiment_dissatisfied" />
                    <span class="text-body1">{{
                        $t('forms.labels.noResults') ||
                        $t('forms.noResults') ||
                        'Nenhum resultado encontrado'
                    }}</span>
                </div>
            </template>
        </data-table>

        <!-- Slot para conteúdo após a tabela -->
        <slot name="after-table"></slot>

        <!-- Modal do formulário -->
        <modal-template
            v-if="showModal"
            :model-value="showModal"
            :title="modalTitle || ''"
            :card-style="
                modalCardStyle ||
                'min-width: 320px; max-width: 1200px; width: 95vw'
            "
            @update:model-value="$emit('update:show-modal', $event)"
            @close="$emit('modal-close')"
        >
            <!-- Slot para conteúdo do modal -->
            <slot name="modal-content"></slot>

            <!-- Footer do modal -->
            <template #actions>
                <slot name="modal-actions">
                    <!-- Ações padrão do modal -->
                    <q-btn
                        :label="$t('buttons.cancel')"
                        color="negative"
                        outline
                        @click="$emit('modal-cancel')"
                    />
                    <q-btn
                        v-if="showClearButton"
                        :label="$t('buttons.clear')"
                        color="grey-6"
                        outline
                        @click="$emit('modal-clear')"
                        :disable="modalSaving"
                    />
                    <q-btn
                        :label="$t('buttons.save')"
                        color="positive"
                        @click="$emit('modal-save')"
                        :loading="modalSaving"
                    />
                </slot>
            </template>
        </modal-template>

        <!-- Slot para modais adicionais -->
        <slot name="extra-modals"></slot>
    </q-page>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import ModalTemplate from '@/components/ModalTemplate.vue'
import DataTable from '@/components/DataTable.vue'
import ActionCollection from '@/components/ActionCollection.vue'
import type { CrudTemplateProps, CrudTemplateEmits } from '@/types/CrudTemplate'

const { t: $t } = useI18n()

// Props - usando CrudTemplateProps diretamente
type Props = CrudTemplateProps

const props = withDefaults(defineProps<Props>(), {
    loading: false,
    saveTableOptions: true,
    showTableToolbar: true,
    showQuickFilter: true,
    quickFilterValue: null,
    tableMaxHeight: 'calc(100vh - 95px)',
    tableSelection: 'multiple',
    tableDense: true,
    tableFlat: true,
    tableBordered: true,
    tableBinaryStateSort: true,
    rowsPerPageOptions: () => [10, 25, 50, 100, 500, 1000],
    tableProps: () => ({}),
    showAddButton: true,
    showDefaultActions: true,
    showEditAction: true,
    showDeleteAction: true,
    showDefaultStatusColumn: true,
    showModal: false,
    modalCardStyle: 'min-width: 320px; max-width: 1200px; width: 95vw',
    modalSaving: false,
    showClearButton: false,
    actionsPerRow: 2
})

// Emits
const emit = defineEmits<CrudTemplateEmits>()

// Computed
const selectedItems = computed({
    get: () => props.selectedItems || [],
    set: value => emit('update:selected-items', value)
})

const quickFilterValueModel = computed({
    get: () => props.quickFilterValue,
    set: value => emit('update:quick-filter-value', value)
})

// Methods
const getStatusLabel = (ativo: boolean) => {
    return ativo ? $t('forms.labels.active') : $t('forms.labels.inactive')
}
</script>

<style scoped>
.toolbar-container {
    border-radius: 8px;
}

.toolbar-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
}
</style>
