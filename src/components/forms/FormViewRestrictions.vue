<template>
    <div id="form-view-restrictions">
        <form-template :title="$t('forms.titles.viewRestrictions')">
            <template #body>
                <div class="row q-col-gutter-md">
                    <!-- Visualizar Dados Apenas do Vendedor -->
                    <div class="col-12 col-sm-6 col-md-4" v-if="showSeller">
                        <q-card flat bordered class="q-pa-xs">
                            <div class="row q-col-gutter-xs">
                                <div class="col-12">
                                    <q-toggle
                                        v-model="viewOnlySeller"
                                        :label="
                                            $t('forms.labels.viewOnlySeller')
                                        "
                                        color="primary"
                                        class="q-mb-xs"
                                    />
                                </div>
                                <div class="col-12">
                                    <q-select
                                        v-model="selectedSeller"
                                        :label="$t('forms.labels.selectSeller')"
                                        :options="sellersOptions"
                                        outlined
                                        dense
                                        stack-label
                                        emit-value
                                        map-options
                                        options-dense
                                        :error="!!errors.selectedSeller"
                                        :error-message="errors.selectedSeller"
                                    />
                                </div>
                            </div>
                        </q-card>
                    </div>

                    <!-- Visualizar Dados Apenas do Comprador -->
                    <div class="col-12 col-sm-6 col-md-4" v-if="showBuyer">
                        <q-card flat bordered class="q-pa-xs">
                            <div class="row q-col-gutter-xs">
                                <div class="col-12">
                                    <q-toggle
                                        v-model="viewOnlyBuyer"
                                        :label="
                                            $t('forms.labels.viewOnlyBuyer')
                                        "
                                        color="primary"
                                        class="q-mb-xs"
                                    />
                                </div>
                                <div class="col-12">
                                    <q-select
                                        v-model="selectedBuyer"
                                        :label="$t('forms.labels.selectBuyer')"
                                        :options="buyersOptions"
                                        outlined
                                        dense
                                        stack-label
                                        emit-value
                                        map-options
                                        options-dense
                                        :error="!!errors.selectedBuyer"
                                        :error-message="errors.selectedBuyer"
                                    />
                                </div>
                            </div>
                        </q-card>
                    </div>

                    <!-- Visualizar Dados Apenas do Caixa -->
                    <div class="col-12 col-sm-6 col-md-4" v-if="showCashier">
                        <q-card flat bordered class="q-pa-xs">
                            <div class="row q-col-gutter-xs">
                                <div class="col-12">
                                    <q-toggle
                                        v-model="viewOnlyCashier"
                                        :label="
                                            $t('forms.labels.viewOnlyCashier')
                                        "
                                        color="primary"
                                        class="q-mb-xs"
                                    />
                                </div>
                                <div class="col-12">
                                    <q-select
                                        v-model="selectedCashier"
                                        :label="
                                            $t('forms.labels.selectCashier')
                                        "
                                        :options="cashiersOptions"
                                        outlined
                                        dense
                                        stack-label
                                        emit-value
                                        map-options
                                        options-dense
                                        :error="!!errors.selectedCashier"
                                        :error-message="errors.selectedCashier"
                                    />
                                </div>
                            </div>
                        </q-card>
                    </div>

                    <!-- Visualizar Dados Apenas da Empresa -->
                    <div class="col-12 col-sm-6 col-md-4" v-if="showCompany">
                        <q-card flat bordered class="q-pa-xs">
                            <div class="row q-col-gutter-xs">
                                <div class="col-12">
                                    <q-toggle
                                        v-model="viewOnlyCompany"
                                        :label="
                                            $t('forms.labels.viewOnlyCompany')
                                        "
                                        color="primary"
                                        class="q-mb-xs"
                                    />
                                </div>
                                <div class="col-12">
                                    <q-select
                                        v-model="selectedCompany"
                                        :label="
                                            $t('forms.labels.selectCompany')
                                        "
                                        :options="companiesOptions"
                                        outlined
                                        dense
                                        stack-label
                                        emit-value
                                        map-options
                                        options-dense
                                        :error="!!errors.selectedCompany"
                                        :error-message="errors.selectedCompany"
                                    />
                                </div>
                            </div>
                        </q-card>
                    </div>

                    <!-- Visualizar Dados Apenas do Representante -->
                    <div
                        class="col-12 col-sm-6 col-md-4"
                        v-if="showRepresentative"
                    >
                        <q-card flat bordered class="q-pa-xs">
                            <div class="row q-col-gutter-xs">
                                <div class="col-12">
                                    <q-toggle
                                        v-model="viewOnlyRepresentative"
                                        :label="
                                            $t(
                                                'forms.labels.viewOnlyRepresentative'
                                            )
                                        "
                                        color="primary"
                                        class="q-mb-xs"
                                    />
                                </div>
                                <div class="col-12">
                                    <q-select
                                        v-model="selectedRepresentative"
                                        :label="
                                            $t(
                                                'forms.labels.selectRepresentative'
                                            )
                                        "
                                        :options="representativesOptions"
                                        outlined
                                        dense
                                        stack-label
                                        emit-value
                                        map-options
                                        options-dense
                                        :error="!!errors.selectedRepresentative"
                                        :error-message="
                                            errors.selectedRepresentative
                                        "
                                    />
                                </div>
                            </div>
                        </q-card>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import FormTemplate from '@/components/FormTemplate.vue'

const { t: $t } = useI18n()

// Props
const props = defineProps({
    // Controla quais restrições são exibidas
    showSeller: {
        type: Boolean,
        default: true
    },
    showBuyer: {
        type: Boolean,
        default: true
    },
    showCashier: {
        type: Boolean,
        default: true
    },
    showCompany: {
        type: Boolean,
        default: true
    },
    showRepresentative: {
        type: Boolean,
        default: true
    },
    // Valores iniciais
    initialValues: {
        type: Object,
        default: () => ({})
    },
    // Opções para os selects
    sellersOptions: {
        type: Array,
        default: () => [
            { label: 'Vendedor 1', value: 'seller1' },
            { label: 'Vendedor 2', value: 'seller2' }
        ]
    },
    buyersOptions: {
        type: Array,
        default: () => [
            { label: 'Comprador 1', value: 'buyer1' },
            { label: 'Comprador 2', value: 'buyer2' }
        ]
    },
    cashiersOptions: {
        type: Array,
        default: () => [
            { label: 'Caixa 1', value: 'cashier1' },
            { label: 'Caixa 2', value: 'cashier2' }
        ]
    },
    companiesOptions: {
        type: Array,
        default: () => [
            { label: 'Empresa 1', value: 'company1' },
            { label: 'Empresa 2', value: 'company2' }
        ]
    },
    representativesOptions: {
        type: Array,
        default: () => [
            { label: 'Representante 1', value: 'representative1' },
            { label: 'Representante 2', value: 'representative2' }
        ]
    }
})

// Estado do formulário
const viewOnlySeller = ref(props.initialValues.viewOnlySeller || false)
const viewOnlyBuyer = ref(props.initialValues.viewOnlyBuyer || false)
const viewOnlyCashier = ref(props.initialValues.viewOnlyCashier || false)
const viewOnlyCompany = ref(props.initialValues.viewOnlyCompany || false)
const viewOnlyRepresentative = ref(
    props.initialValues.viewOnlyRepresentative || false
)

const selectedSeller = ref(props.initialValues.selectedSeller || null)
const selectedBuyer = ref(props.initialValues.selectedBuyer || null)
const selectedCashier = ref(props.initialValues.selectedCashier || null)
const selectedCompany = ref(props.initialValues.selectedCompany || null)
const selectedRepresentative = ref(
    props.initialValues.selectedRepresentative || null
)

// Erros de validação
const errors = ref({
    selectedSeller: '',
    selectedBuyer: '',
    selectedCashier: '',
    selectedCompany: '',
    selectedRepresentative: ''
})

// Limpar seleção quando o toggle é desativado
watch(viewOnlySeller, newValue => {
    if (!newValue) selectedSeller.value = null
})

watch(viewOnlyBuyer, newValue => {
    if (!newValue) selectedBuyer.value = null
})

watch(viewOnlyCashier, newValue => {
    if (!newValue) selectedCashier.value = null
})

watch(viewOnlyCompany, newValue => {
    if (!newValue) selectedCompany.value = null
})

watch(viewOnlyRepresentative, newValue => {
    if (!newValue) selectedRepresentative.value = null
})

// Validação do formulário
const validate = async () => {
    let isValid = true
    errors.value = {
        selectedSeller: '',
        selectedBuyer: '',
        selectedCashier: '',
        selectedCompany: '',
        selectedRepresentative: ''
    }

    // Validar seleção de vendedor quando viewOnlySeller está ativo
    if (viewOnlySeller.value && !selectedSeller.value) {
        errors.value.selectedSeller = $t('validate.requiredSeller')
        isValid = false
    }

    // Validar seleção de comprador quando viewOnlyBuyer está ativo
    if (viewOnlyBuyer.value && !selectedBuyer.value) {
        errors.value.selectedBuyer = $t('validate.requiredBuyer')
        isValid = false
    }

    // Validar seleção de caixa quando viewOnlyCashier está ativo
    if (viewOnlyCashier.value && !selectedCashier.value) {
        errors.value.selectedCashier = $t('validate.requiredCashier')
        isValid = false
    }

    // Validar seleção de empresa quando viewOnlyCompany está ativo
    if (viewOnlyCompany.value && !selectedCompany.value) {
        errors.value.selectedCompany = $t('validate.requiredCompany')
        isValid = false
    }

    // Validar seleção de representante quando viewOnlyRepresentative está ativo
    if (viewOnlyRepresentative.value && !selectedRepresentative.value) {
        errors.value.selectedRepresentative = $t(
            'validate.requiredRepresentative'
        )
        isValid = false
    }

    return {
        valid: isValid,
        errors: errors.value
    }
}

// Obter dados do formulário
const getData = () => {
    return {
        viewOnlySeller: viewOnlySeller.value,
        viewOnlyBuyer: viewOnlyBuyer.value,
        viewOnlyCashier: viewOnlyCashier.value,
        viewOnlyCompany: viewOnlyCompany.value,
        viewOnlyRepresentative: viewOnlyRepresentative.value,
        selectedSeller: selectedSeller.value,
        selectedBuyer: selectedBuyer.value,
        selectedCashier: selectedCashier.value,
        selectedCompany: selectedCompany.value,
        selectedRepresentative: selectedRepresentative.value
    }
}

// Resetar formulário
const resetForm = () => {
    viewOnlySeller.value = false
    viewOnlyBuyer.value = false
    viewOnlyCashier.value = false
    viewOnlyCompany.value = false
    viewOnlyRepresentative.value = false
    selectedSeller.value = null
    selectedBuyer.value = null
    selectedCashier.value = null
    selectedCompany.value = null
    selectedRepresentative.value = null
    errors.value = {
        selectedSeller: '',
        selectedBuyer: '',
        selectedCashier: '',
        selectedCompany: '',
        selectedRepresentative: ''
    }
}

// Expor métodos e dados
defineExpose({
    validate,
    getData,
    resetForm
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}
</style>
