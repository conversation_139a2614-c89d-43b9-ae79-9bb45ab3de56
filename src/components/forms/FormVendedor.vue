<template>
    <div id="form-vendedor">
        <form-template :title="$t('forms.titles.sellerForm')">
            <template #header>
                <q-toggle
                    v-model="status"
                    :label="$t('forms.labels.active')"
                    color="secondary"
                    class="no-bottom-spacing text-white"
                    dense
                />
            </template>

            <template #body>
                <div class="row q-col-gutter-xs">
                    <!-- <PERSON><PERSON><PERSON><PERSON> (%) -->
                    <div class="col-12 col-sm-4">
                        <q-input
                            v-model.number="comissao"
                            :label="$t('forms.labels.commission')"
                            type="number"
                            outlined
                            dense
                            stack-label
                            :error="!!errors.comissao"
                            :error-message="errors.comissao"
                        >
                            <template #append>
                                <q-icon name="percent" />
                            </template>
                        </q-input>
                    </div>

                    <!-- Percentual de Desconto Permitido -->
                    <div class="col-12 col-sm-4">
                        <q-input
                            v-model.number="percentualDesconto"
                            :label="$t('forms.labels.discountPercentage')"
                            type="number"
                            outlined
                            dense
                            stack-label
                            :error="!!errors.percentualDesconto"
                            :error-message="errors.percentualDesconto"
                        >
                            <template #append>
                                <q-icon name="percent" />
                            </template>
                        </q-input>
                    </div>

                    <!-- Valor de Desconto Permitido -->
                    <div class="col-12 col-sm-4">
                        <q-input
                            v-model.number="valorDesconto"
                            :label="$t('forms.labels.discountValue')"
                            type="number"
                            :prefix="$t('forms.labels.currencySymbol')"
                            outlined
                            dense
                            stack-label
                            :error="!!errors.valorDesconto"
                            :error-message="errors.valorDesconto"
                        >
                            <template #prepend>
                                <q-icon name="attach_money" />
                            </template>
                        </q-input>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'

const { t: $t } = useI18n()
// Components
import FormTemplate from '@/components/FormTemplate.vue'

// Props
const props = defineProps({
    initialValues: {
        type: Object,
        default: () => ({})
    }
})

// Form Vendedor Schema
const vendedorSchema = yup.object({
    comissao: yup
        .number()
        .typeError($t('validate.invalidCommission'))
        .min(0, $t('validate.minCommission'))
        .max(100, $t('validate.maxCommission'))
        .required($t('validate.requiredCommission')),
    percentualDesconto: yup
        .number()
        .typeError($t('validate.invalidDiscountPercentage'))
        .min(0, $t('validate.minDiscountPercentage'))
        .max(100, $t('validate.maxDiscountPercentage'))
        .required($t('validate.requiredDiscountPercentage')),
    valorDesconto: yup
        .number()
        .typeError($t('validate.invalidDiscountValue'))
        .min(0, $t('validate.minDiscountValue'))
        .required($t('validate.requiredDiscountValue'))
})

// Inicializa o formulário com valores padrão ou iniciais
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: vendedorSchema,
    initialValues: {
        comissao: props.initialValues.comissao || 0,
        percentualDesconto: props.initialValues.percentualDesconto || 0,
        valorDesconto: props.initialValues.valorDesconto || 0
    },
    validateOnMount: false
})

// Define fields
const [comissao] = defineField('comissao')
const [percentualDesconto] = defineField('percentualDesconto')
const [valorDesconto] = defineField('valorDesconto')

// Campo adicional não incluído na validação
const status = ref(
    props.initialValues.status !== undefined ? props.initialValues.status : true
)

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: () => {
        return {
            ...values,
            status: status.value
        }
    },
    resetForm
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
