<template>
    <div id="form-fornecedor" class="q-pa-xs">
        <form-template :title="$t('forms.titles.supplierForm')">
            <template #header>
                <div class="row q-col-gutter-xs">
                    <div class="col-auto">
                        <q-toggle
                            v-model="status"
                            :label="$t('forms.labels.active')"
                            color="secondary"
                            class="no-bottom-spacing text-white"
                            dense
                        />
                    </div>
                </div>
            </template>

            <template #body>
                <div class="row q-col-gutter-xs">
                    <!-- Informações Básicas -->
                    <div class="col-12">
                        <div class="row q-col-gutter-xs">
                            <!-- Nome (readonly e disabled) -->
                            <div class="col-12 col-md-3 col-lg-2">
                                <q-input
                                    v-model="nome"
                                    :label="$t('forms.labels.name')"
                                    outlined
                                    dense
                                    stack-label
                                    readonly
                                    disable
                                    :error="!!errors.nome"
                                    :error-message="errors.nome"
                                />
                            </div>

                            <!-- Transação -->
                            <div class="col-12 col-md-3 col-lg-2">
                                <q-select
                                    v-model="transacao"
                                    :label="$t('forms.labels.transaction')"
                                    :options="transacoesFilteredOptions"
                                    outlined
                                    dense
                                    stack-label
                                    emit-value
                                    map-options
                                    options-dense
                                    use-input
                                    input-debounce="300"
                                    @filter="filterTransacao"
                                    :error="!!errors.transacao"
                                    :error-message="errors.transacao"
                                >
                                    <template v-slot:no-option>
                                        <q-item>
                                            <q-item-section class="text-grey">
                                                {{ $t('forms.noResults') }}
                                            </q-item-section>
                                        </q-item>
                                    </template>
                                </q-select>
                            </div>

                            <!-- Condição de pagamento -->
                            <div class="col-12 col-md-3 col-lg-2">
                                <q-select
                                    v-model="condicaoPagamento"
                                    :label="$t('forms.labels.paymentCondition')"
                                    :options="condicoesPagamentoFilteredOptions"
                                    outlined
                                    dense
                                    stack-label
                                    emit-value
                                    map-options
                                    options-dense
                                    use-input
                                    input-debounce="300"
                                    @filter="filterCondicaoPagamento"
                                    :error="!!errors.condicaoPagamento"
                                    :error-message="errors.condicaoPagamento"
                                >
                                    <template v-slot:no-option>
                                        <q-item>
                                            <q-item-section class="text-grey">
                                                {{ $t('forms.noResults') }}
                                            </q-item-section>
                                        </q-item>
                                    </template>
                                </q-select>
                            </div>

                            <!-- Modalidade de pagamento -->
                            <div class="col-12 col-md-3 col-lg-2">
                                <q-select
                                    v-model="modalidadePagamento"
                                    :label="$t('forms.labels.paymentModality')"
                                    :options="
                                        modalidadesPagamentoFilteredOptions
                                    "
                                    outlined
                                    dense
                                    stack-label
                                    emit-value
                                    map-options
                                    options-dense
                                    use-input
                                    input-debounce="300"
                                    @filter="filterModalidadePagamento"
                                    :error="!!errors.modalidadePagamento"
                                    :error-message="errors.modalidadePagamento"
                                >
                                    <template v-slot:no-option>
                                        <q-item>
                                            <q-item-section class="text-grey">
                                                {{ $t('forms.noResults') }}
                                            </q-item-section>
                                        </q-item>
                                    </template>
                                </q-select>
                            </div>

                            <!-- Comprador -->
                            <div class="col-12 col-md-3 col-lg-2">
                                <q-select
                                    v-model="comprador"
                                    :label="$t('forms.labels.buyer')"
                                    :options="compradoresFilteredOptions"
                                    outlined
                                    dense
                                    stack-label
                                    emit-value
                                    map-options
                                    options-dense
                                    use-input
                                    input-debounce="300"
                                    @filter="filterComprador"
                                    :error="!!errors.comprador"
                                    :error-message="errors.comprador"
                                >
                                    <template v-slot:no-option>
                                        <q-item>
                                            <q-item-section class="text-grey">
                                                {{ $t('forms.noResults') }}
                                            </q-item-section>
                                        </q-item>
                                    </template>
                                </q-select>
                            </div>

                            <!-- Conta Financeira -->
                            <div class="col-12 col-md-3 col-lg-2">
                                <q-select
                                    v-model="contaFinanceira"
                                    :label="$t('forms.labels.financialAccount')"
                                    :options="contasFinanceirasFilteredOptions"
                                    outlined
                                    dense
                                    stack-label
                                    emit-value
                                    map-options
                                    options-dense
                                    use-input
                                    input-debounce="300"
                                    @filter="filterContaFinanceira"
                                    :error="!!errors.contaFinanceira"
                                    :error-message="errors.contaFinanceira"
                                >
                                    <template v-slot:no-option>
                                        <q-item>
                                            <q-item-section class="text-grey">
                                                {{ $t('forms.noResults') }}
                                            </q-item-section>
                                        </q-item>
                                    </template>
                                </q-select>
                            </div>

                            <!-- Grupo Forma de Pagamento -->
                            <div class="col-12 col-md-3 col-lg-2">
                                <q-select
                                    v-model="grupoFormaPagamento"
                                    :label="
                                        $t('forms.labels.paymentMethodGroup')
                                    "
                                    :options="
                                        gruposFormaPagamentoFilteredOptions
                                    "
                                    outlined
                                    dense
                                    stack-label
                                    emit-value
                                    map-options
                                    options-dense
                                    use-input
                                    input-debounce="300"
                                    @filter="filterGrupoFormaPagamento"
                                    :error="!!errors.grupoFormaPagamento"
                                    :error-message="errors.grupoFormaPagamento"
                                >
                                    <template v-slot:no-option>
                                        <q-item>
                                            <q-item-section class="text-grey">
                                                {{ $t('forms.noResults') }}
                                            </q-item-section>
                                        </q-item>
                                    </template>
                                </q-select>
                            </div>

                            <!-- Tipo de Entrega -->
                            <div class="col-12 col-md-3 col-lg-2">
                                <q-select
                                    v-model="tipoEntrega"
                                    :label="$t('forms.labels.deliveryType')"
                                    :options="tiposEntregaFilteredOptions"
                                    outlined
                                    dense
                                    stack-label
                                    emit-value
                                    map-options
                                    options-dense
                                    use-input
                                    input-debounce="300"
                                    @filter="filterTipoEntrega"
                                    :error="!!errors.tipoEntrega"
                                    :error-message="errors.tipoEntrega"
                                >
                                    <template v-slot:no-option>
                                        <q-item>
                                            <q-item-section class="text-grey">
                                                {{ $t('forms.noResults') }}
                                            </q-item-section>
                                        </q-item>
                                    </template>
                                </q-select>
                            </div>

                            <!-- Transportadora (Checkbox) -->
                            <div class="col-12 col-md-3 col-lg-2">
                                <q-checkbox
                                    v-model="transportadora"
                                    :label="$t('forms.labels.carrier')"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'

const { t: $t } = useI18n()
// Components
import FormTemplate from '@/components/FormTemplate.vue'

// Props
const props = defineProps({
    initialValues: {
        type: Object,
        default: () => ({})
    }
})

// Opções para os selects (mock - deve vir do banco)
const transacoesOptions = [
    { label: 'Compra', value: 'compra' },
    { label: 'Devolução', value: 'devolucao' }
]

const condicoesPagamentoOptions = [
    { label: 'À Vista', value: 'avista' },
    { label: 'Parcelado', value: 'parcelado' }
]

const modalidadesPagamentoOptions = [
    { label: 'Dinheiro', value: 'dinheiro' },
    { label: 'Cartão', value: 'cartao' },
    { label: 'Boleto', value: 'boleto' },
    { label: 'Transferência', value: 'transferencia' }
]

const compradoresOptions = [
    { label: 'Comprador 1', value: 'comp1' },
    { label: 'Comprador 2', value: 'comp2' }
]

const contasFinanceirasOptions = [
    { label: 'Conta 1', value: 'conta1' },
    { label: 'Conta 2', value: 'conta2' }
]

const gruposFormaPagamentoOptions = [
    { label: 'Grupo 1', value: 'grupo1' },
    { label: 'Grupo 2', value: 'grupo2' }
]

const tiposEntregaOptions = [
    { label: 'Entrega Padrão', value: 'padrao' },
    { label: 'Entrega Expressa', value: 'expressa' }
]

// Form Fornecedor Schema
const fornecedorSchema = yup.object({
    nome: yup.string().required($t('validate.requiredName')),
    transacao: yup.string().required($t('validate.requiredTransaction')),
    condicaoPagamento: yup
        .string()
        .required($t('validate.requiredPaymentCondition')),
    modalidadePagamento: yup
        .string()
        .required($t('validate.requiredPaymentModality')),
    comprador: yup.string().required($t('validate.requiredBuyer')),
    contaFinanceira: yup
        .string()
        .required($t('validate.requiredFinancialAccount')),
    grupoFormaPagamento: yup
        .string()
        .required($t('validate.requiredPaymentMethodGroup')),
    tipoEntrega: yup.string().required($t('validate.requiredDeliveryType'))
})

// Inicializa o formulário com valores padrão ou iniciais
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: fornecedorSchema,
    initialValues: {
        nome: props.initialValues.nome || '',
        transacao: props.initialValues.transacao || '',
        condicaoPagamento: props.initialValues.condicaoPagamento || '',
        modalidadePagamento: props.initialValues.modalidadePagamento || '',
        comprador: props.initialValues.comprador || '',
        contaFinanceira: props.initialValues.contaFinanceira || '',
        grupoFormaPagamento: props.initialValues.grupoFormaPagamento || '',
        tipoEntrega: props.initialValues.tipoEntrega || ''
    },
    validateOnMount: false
})

// Define fields
const [nome] = defineField('nome')
const [transacao] = defineField('transacao')
const [condicaoPagamento] = defineField('condicaoPagamento')
const [modalidadePagamento] = defineField('modalidadePagamento')
const [comprador] = defineField('comprador')
const [contaFinanceira] = defineField('contaFinanceira')
const [grupoFormaPagamento] = defineField('grupoFormaPagamento')
const [tipoEntrega] = defineField('tipoEntrega')

// Variáveis para armazenar as opções filtradas
const transacoesFilteredOptions = ref([...transacoesOptions])
const condicoesPagamentoFilteredOptions = ref([...condicoesPagamentoOptions])
const modalidadesPagamentoFilteredOptions = ref([
    ...modalidadesPagamentoOptions
])
const compradoresFilteredOptions = ref([...compradoresOptions])
const contasFinanceirasFilteredOptions = ref([...contasFinanceirasOptions])
const gruposFormaPagamentoFilteredOptions = ref([
    ...gruposFormaPagamentoOptions
])
const tiposEntregaFilteredOptions = ref([...tiposEntregaOptions])

// Métodos de filtro para os selects
const filterTransacao = (val: string, update: (fn: () => void) => void) => {
    if (val === '') {
        update(() => {
            transacoesFilteredOptions.value = transacoesOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        transacoesFilteredOptions.value = transacoesOptions.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

const filterCondicaoPagamento = (
    val: string,
    update: (fn: () => void) => void
) => {
    if (val === '') {
        update(() => {
            condicoesPagamentoFilteredOptions.value = condicoesPagamentoOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        condicoesPagamentoFilteredOptions.value =
            condicoesPagamentoOptions.filter(
                v => v.label.toLowerCase().indexOf(needle) > -1
            )
    })
}

const filterModalidadePagamento = (
    val: string,
    update: (fn: () => void) => void
) => {
    if (val === '') {
        update(() => {
            modalidadesPagamentoFilteredOptions.value =
                modalidadesPagamentoOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        modalidadesPagamentoFilteredOptions.value =
            modalidadesPagamentoOptions.filter(
                v => v.label.toLowerCase().indexOf(needle) > -1
            )
    })
}

const filterComprador = (val: string, update: (fn: () => void) => void) => {
    if (val === '') {
        update(() => {
            compradoresFilteredOptions.value = compradoresOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        compradoresFilteredOptions.value = compradoresOptions.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

const filterContaFinanceira = (
    val: string,
    update: (fn: () => void) => void
) => {
    if (val === '') {
        update(() => {
            contasFinanceirasFilteredOptions.value = contasFinanceirasOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        contasFinanceirasFilteredOptions.value =
            contasFinanceirasOptions.filter(
                v => v.label.toLowerCase().indexOf(needle) > -1
            )
    })
}

const filterGrupoFormaPagamento = (
    val: string,
    update: (fn: () => void) => void
) => {
    if (val === '') {
        update(() => {
            gruposFormaPagamentoFilteredOptions.value =
                gruposFormaPagamentoOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        gruposFormaPagamentoFilteredOptions.value =
            gruposFormaPagamentoOptions.filter(
                v => v.label.toLowerCase().indexOf(needle) > -1
            )
    })
}

const filterTipoEntrega = (val: string, update: (fn: () => void) => void) => {
    if (val === '') {
        update(() => {
            tiposEntregaFilteredOptions.value = tiposEntregaOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        tiposEntregaFilteredOptions.value = tiposEntregaOptions.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

// Campos adicionais não incluídos na validação
const status = ref(
    props.initialValues.status !== undefined ? props.initialValues.status : true
)
const transportadora = ref(props.initialValues.transportadora || false)

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: () => {
        return {
            ...values,
            status: status.value,
            transportadora: transportadora.value
        }
    },
    resetForm
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
