<template>
    <div id="form-endereco">
        <form-template :title="$t('forms.titles.addressForm')">
            <template #body>
                <div class="row q-col-gutter-xs">
                    <!-- Linha 1: Tipo, País, CEP, Estado -->
                    <div class="col-12 col-sm-3">
                        <q-select
                            v-model="tipo"
                            :label="$t('forms.labels.addressType')"
                            :options="tiposEnderecoOptions"
                            outlined
                            dense
                            stack-label
                            emit-value
                            map-options
                            :error="!!errors.tipo"
                            :error-message="errors.tipo"
                            clearable
                        />
                    </div>

                    <div class="col-12 col-sm-3">
                        <q-select
                            v-model="pais"
                            :label="$t('forms.labels.country')"
                            :options="paisesFilteredOptions"
                            outlined
                            dense
                            stack-label
                            emit-value
                            map-options
                            use-input
                            input-debounce="300"
                            @filter="filterPais"
                            @update:model-value="atualizarEstados"
                            :error="!!errors.pais"
                            :error-message="errors.pais"
                            clearable
                        >
                            <template v-slot:no-option>
                                <q-item>
                                    <q-item-section class="text-grey">
                                        {{ $t('forms.labels.noResults') }}
                                    </q-item-section>
                                </q-item>
                            </template>
                        </q-select>
                    </div>

                    <div class="col-12 col-sm-3">
                        <q-input
                            v-model="cep"
                            :label="$t('forms.labels.zipCode')"
                            :mask="getCepMask()"
                            outlined
                            dense
                            stack-label
                            :error="!!errors.cep"
                            :error-message="errors.cep"
                            :loading="loadingCep"
                            :disable="pais !== 'BR'"
                        >
                            <template #append>
                                <q-icon v-if="!loadingCep" name="location_on" />
                                <q-spinner-dots
                                    v-else
                                    size="20px"
                                    color="primary"
                                />
                            </template>
                            <template #hint v-if="loadingCep">
                                {{ $t('cepSearch.searchingAddress') }}
                            </template>
                            <template #hint v-else-if="pais !== 'BR'">
                                {{ $t('cepSearch.onlyBrazil') }}
                            </template>
                        </q-input>
                    </div>

                    <div class="col-12 col-sm-3">
                        <q-select
                            v-model="estado"
                            :label="$t('forms.labels.state')"
                            :options="estadosFilteredOptions"
                            outlined
                            dense
                            stack-label
                            emit-value
                            map-options
                            use-input
                            input-debounce="300"
                            @filter="filterEstado"
                            @update:model-value="atualizarCidades"
                            :error="!!errors.estado"
                            :error-message="errors.estado"
                            :loading="loadingCep"
                            :disable="!pais"
                            clearable
                        >
                            <template v-slot:no-option>
                                <q-item>
                                    <q-item-section class="text-grey">
                                        {{ $t('forms.labels.noResults') }}
                                    </q-item-section>
                                </q-item>
                            </template>
                        </q-select>
                    </div>

                    <!-- Linha 2: Cidade, Logradouro, Número -->
                    <div class="col-12 col-sm-3">
                        <q-select
                            v-model="cidade"
                            :label="$t('forms.labels.city')"
                            :options="cidadesOptions"
                            outlined
                            dense
                            stack-label
                            emit-value
                            map-options
                            use-input
                            input-debounce="300"
                            @filter="filterCidade"
                            :error="!!errors.cidade"
                            :error-message="errors.cidade"
                            :disable="!estado"
                            :loading="loadingCep"
                            clearable
                        >
                            <template v-slot:no-option>
                                <q-item>
                                    <q-item-section class="text-grey">
                                        {{ $t('forms.labels.noResults') }}
                                    </q-item-section>
                                </q-item>
                            </template>
                        </q-select>
                    </div>

                    <div class="col-12 col-sm-6">
                        <q-input
                            v-model="logradouro"
                            :label="$t('forms.labels.street')"
                            outlined
                            dense
                            stack-label
                            :error="!!errors.logradouro"
                            :error-message="errors.logradouro"
                            :loading="loadingCep"
                        />
                    </div>

                    <div class="col-12 col-sm-3">
                        <q-input
                            v-model="numero"
                            :label="$t('forms.labels.number')"
                            outlined
                            dense
                            stack-label
                            :error="!!errors.numero"
                            :error-message="errors.numero"
                        />
                    </div>

                    <!-- Linha 3: Bairro, Complemento, Ponto de Referência -->
                    <div class="col-12 col-sm-4">
                        <q-input
                            v-model="bairro"
                            :label="$t('forms.labels.neighborhood')"
                            outlined
                            dense
                            stack-label
                            :error="!!errors.bairro"
                            :error-message="errors.bairro"
                            :loading="loadingCep"
                        />
                    </div>

                    <div class="col-12 col-sm-4">
                        <q-input
                            v-model="complemento"
                            :label="$t('forms.labels.complement')"
                            outlined
                            dense
                            stack-label
                        />
                    </div>

                    <div class="col-12 col-sm-4">
                        <q-input
                            v-model="pontoReferencia"
                            :label="$t('forms.labels.referencePoint')"
                            outlined
                            dense
                            stack-label
                        />
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref, watch } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'
import { Notify } from 'quasar'

const { t: $t, locale } = useI18n()
// Components
import FormTemplate from '@/components/FormTemplate.vue'
// Services
import { axiosCep } from '@/services/consultacep'
import {
    getCountries,
    getStatesByCountry,
    getCitiesByState
} from '@/services/locationService'
// Types
import type { CEPData } from '@/interfaces/CEP'

// Props
const props = defineProps({
    initialValues: {
        type: Object,
        default: () => ({})
    }
})

// Definição de tipos
interface OptionItem {
    label: string
    value: string
}

// Opções para os selects
const tiposEnderecoOptions: OptionItem[] = [
    { label: $t('forms.addressTypes.residential'), value: 'residential' },
    { label: $t('forms.addressTypes.commercial'), value: 'commercial' },
    { label: $t('forms.addressTypes.delivery'), value: 'delivery' },
    { label: $t('forms.addressTypes.billing'), value: 'billing' },
    { label: $t('forms.addressTypes.other'), value: 'other' }
]

// Dados dinâmicos da API
const paisesOptions = ref<OptionItem[]>([])
const estadosOptions = ref<OptionItem[]>([])
const cidadesOptions = ref<OptionItem[]>([])

// Opções filtradas
const paisesFilteredOptions = ref<OptionItem[]>([])
const estadosFilteredOptions = ref<OptionItem[]>([])

// País selecionado (Brasil como padrão)
const paisSelecionado = ref('BR')

// Carregar dados da API
const carregarPaises = (): void => {
    paisesOptions.value = getCountries(locale.value)
    paisesFilteredOptions.value = [...paisesOptions.value]
}

const carregarEstados = (paisCodigo: string): void => {
    estadosOptions.value = getStatesByCountry(paisCodigo)
    estadosFilteredOptions.value = [...estadosOptions.value]
}

const carregarCidades = (paisCodigo: string, estadoCodigo: string): void => {
    cidadesOptions.value = getCitiesByState(paisCodigo, estadoCodigo)
}

// Inicializar dados
carregarPaises()
carregarEstados(paisSelecionado.value)

// Form Endereco Schema
const enderecoSchema = yup.object({
    tipo: yup.string().required($t('validate.requiredAddressType')),
    pais: yup.string().required($t('validate.requiredCountry')),
    cep: yup.string().when('pais', {
        is: 'BR',
        then: schema => schema.required($t('validate.requiredZipCode')),
        otherwise: schema => schema.notRequired()
    }),
    estado: yup.string().required($t('validate.requiredState')),
    cidade: yup.string().required($t('validate.requiredCity')),
    logradouro: yup.string().required($t('validate.requiredStreet')),
    numero: yup.string().required($t('validate.requiredNumber')),
    bairro: yup.string().required($t('validate.requiredNeighborhood')),
    complemento: yup.string(),
    pontoReferencia: yup.string()
})

// Inicializa o formulário com valores padrão ou iniciais
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: enderecoSchema,
    initialValues: {
        tipo: props.initialValues.tipo || '',
        pais: props.initialValues.pais || 'BR',
        cep: props.initialValues.cep || '',
        estado: props.initialValues.estado || '',
        cidade: props.initialValues.cidade || '',
        logradouro: props.initialValues.logradouro || '',
        numero: props.initialValues.numero || '',
        bairro: props.initialValues.bairro || '',
        complemento: props.initialValues.complemento || '',
        pontoReferencia: props.initialValues.pontoReferencia || ''
    },
    validateOnMount: false
})

// Define fields
const [tipo] = defineField('tipo')
const [pais] = defineField('pais')
const [cep] = defineField('cep')
const [estado] = defineField('estado')
const [cidade] = defineField('cidade')
const [logradouro] = defineField('logradouro')
const [numero] = defineField('numero')
const [bairro] = defineField('bairro')
const [complemento] = defineField('complemento')
const [pontoReferencia] = defineField('pontoReferencia')

// Estados para pesquisa de CEP
const loadingCep = ref(false)
const cepSearchTimeout = ref<number | null>(null)

// Função para validar CEP
const isValidCep = (cep: string): boolean => {
    const cleanCep = cep.replace(/\D/g, '')
    return cleanCep.length === 8 && /^\d{8}$/.test(cleanCep)
}

// Função para buscar CEP
const searchCep = async (cepValue: string): Promise<void> => {
    const cleanCep = cepValue.replace(/\D/g, '')

    if (!isValidCep(cleanCep)) {
        return
    }

    loadingCep.value = true

    try {
        const response = await axiosCep.get(`${cleanCep}/json/`)

        if (response.data && !response.data.erro) {
            const cepData: CEPData = response.data

            // Preencher os campos automaticamente
            logradouro.value = cepData.logradouro || ''
            bairro.value = cepData.bairro || ''

            // Buscar o estado correspondente
            const estadoEncontrado = estadosOptions.value.find(
                est => est.value === cepData.uf
            )
            if (estadoEncontrado) {
                estado.value = estadoEncontrado.value
                atualizarCidades()

                // Buscar a cidade correspondente na API
                const todasCidades = getCitiesByState(pais.value, cepData.uf)
                const cidadeEncontrada = todasCidades.find(
                    (cid: OptionItem) =>
                        cid.label.toLowerCase() ===
                        cepData.localidade.toLowerCase()
                )
                if (cidadeEncontrada) {
                    cidade.value = cidadeEncontrada.value
                } else {
                    // Se não encontrar a cidade na API, adicionar dinamicamente
                    const novaCidade = {
                        label: cepData.localidade,
                        value: cepData.localidade
                            .toLowerCase()
                            .replace(/\s+/g, '_')
                    }
                    cidadesOptions.value.push(novaCidade)
                    cidade.value = novaCidade.value
                }
            }

            // Notificar sucesso
            Notify.create({
                message: $t('cepSearch.success.addressFound'),
                color: 'positive',
                position: 'bottom',
                timeout: 2000,
                icon: 'check_circle'
            })
        } else {
            Notify.create({
                message: $t('cepSearch.errors.notFound'),
                color: 'negative',
                position: 'bottom',
                timeout: 2000,
                icon: 'error'
            })
        }
    } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Erro na consulta CEP:', error)
        Notify.create({
            message: $t('cepSearch.errors.searchError'),
            color: 'negative',
            position: 'bottom',
            timeout: 2000,
            icon: 'error'
        })
    } finally {
        loadingCep.value = false
    }
}

// Watch para detectar mudanças no CEP e fazer a pesquisa automaticamente
watch(cep, (newCep: string) => {
    // Limpar timeout anterior
    if (cepSearchTimeout.value) {
        window.clearTimeout(cepSearchTimeout.value)
    }

    // Se o CEP está vazio ou incompleto, não fazer nada
    if (!newCep || newCep.replace(/\D/g, '').length < 8) {
        return
    }

    // Debounce de 500ms para evitar muitas requisições
    cepSearchTimeout.value = window.setTimeout(() => {
        searchCep(newCep)
    }, 500)
})

// Métodos de filtro para os selects
const filterPais = (val: string, update: (fn: () => void) => void): void => {
    if (val === '') {
        update(() => {
            paisesFilteredOptions.value = paisesOptions.value
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        paisesFilteredOptions.value = paisesOptions.value.filter(
            (v: OptionItem) => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

const filterEstado = (val: string, update: (fn: () => void) => void): void => {
    if (val === '') {
        update(() => {
            estadosFilteredOptions.value = estadosOptions.value
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        estadosFilteredOptions.value = estadosOptions.value.filter(
            (v: OptionItem) => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

const filterCidade = (val: string, update: (fn: () => void) => void): void => {
    if (val === '') {
        update(() => {
            if (estado.value) {
                carregarCidades(pais.value, estado.value)
            }
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        const todasCidades = getCitiesByState(pais.value, estado.value)
        cidadesOptions.value = todasCidades.filter(
            (v: OptionItem) => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

// Atualizar estados quando o país muda
const atualizarEstados = (): void => {
    estado.value = ''
    cidade.value = ''
    cidadesOptions.value = []
    paisSelecionado.value = pais.value
    carregarEstados(pais.value)
}

// Atualizar cidades quando o estado muda
const atualizarCidades = (): void => {
    cidade.value = ''
    if (estado.value) {
        carregarCidades(pais.value, estado.value)
    } else {
        cidadesOptions.value = []
    }
}

// Função para obter máscara do CEP baseada no país
const getCepMask = (): string => {
    return pais.value === 'BR' ? '#####-###' : '##########'
}

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: () => {
        return {
            ...values
        }
    },
    resetForm
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
