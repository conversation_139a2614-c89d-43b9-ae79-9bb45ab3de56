<template>
    <div id="form-usuario-permissoes" class="q-pa-xs">
        <form-template :title="$t('forms.titles.userPermissions')">
            <template #header>
                <q-toggle
                    v-model="status"
                    :label="$t('forms.labels.active')"
                    color="secondary"
                    class="no-bottom-spacing text-white"
                    dense
                />
            </template>

            <template #body>
                <div class="row q-col-gutter-md justify-center">
                    <!-- Seção: Configurações Básicas -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.basicInfo') }}
                        </div>
                        <q-card flat bordered class="q-pa-xs row items-center">
                            <!-- Seção: Credenciais do Usuário -->
                            <div class="col-12 col-md-4 col-lg-3 q-pa-xs">
                                <view-user-credentials
                                    :first-name="userCredentials.name"
                                    :last-name="userCredentials.surname"
                                    :username="userCredentials.username"
                                    :role="userCredentials.role"
                                    :email="userCredentials.email"
                                    :show-status="false"
                                >
                                    <template #additional-info>
                                        <div class="col-12">
                                            <div
                                                class="text-caption text-grey-8"
                                            >
                                                {{ userCredentials.email }}
                                            </div>
                                        </div>
                                    </template>
                                </view-user-credentials>
                            </div>

                            <div
                                class="row col-12 col-md-8 col-lg-9 q-col-gutter-xs"
                            >
                                <!-- Administrador -->
                                <div class="col-12 col-sm-6">
                                    <q-toggle
                                        v-model="isAdmin"
                                        :label="$t('forms.labels.admin')"
                                        color="primary"
                                    />
                                </div>

                                <!-- Obrigar fechamento do Operador -->
                                <div class="col-12 col-sm-6">
                                    <q-toggle
                                        v-model="forceOperatorClosure"
                                        :label="
                                            $t(
                                                'forms.labels.forceOperatorClosure'
                                            )
                                        "
                                        color="primary"
                                    />
                                </div>

                                <!-- Função do Usuário -->
                                <div class="col-12 col-sm-6">
                                    <q-select
                                        v-model="userCredentials.role"
                                        :label="$t('forms.labels.role')"
                                        :options="[
                                            'Administrador',
                                            'Gerente',
                                            'Vendedor',
                                            'Financeiro',
                                            'Estoque',
                                            'Suporte'
                                        ]"
                                        outlined
                                        dense
                                        stack-label
                                        emit-value
                                        map-options
                                        options-dense
                                        :error="false"
                                        error-message=""
                                    />
                                </div>

                                <!-- Dias para expirar senha -->
                                <div class="col-12 col-sm-6">
                                    <q-input
                                        v-model.number="passwordExpirationDays"
                                        :label="
                                            $t(
                                                'forms.labels.passwordExpirationDays'
                                            )
                                        "
                                        type="number"
                                        outlined
                                        dense
                                        stack-label
                                        :error="!!errors.passwordExpirationDays"
                                        :error-message="
                                            errors.passwordExpirationDays
                                        "
                                        class=""
                                    />
                                </div>
                            </div>
                        </q-card>
                    </div>

                    <!-- Seção: Restrições de Visualização -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.viewRestrictions') }}
                        </div>
                        <form-view-restrictions
                            ref="viewRestrictionsRef"
                            :sellersOptions="sellersOptions"
                            :buyersOptions="buyersOptions"
                            :cashiersOptions="cashiersOptions"
                            :companiesOptions="companiesOptions"
                            :representativesOptions="representativesOptions"
                            :initialValues="{
                                viewOnlySeller,
                                viewOnlyBuyer,
                                viewOnlyCashier,
                                viewOnlyCompany,
                                viewOnlyRepresentative,
                                selectedSeller,
                                selectedBuyer,
                                selectedCashier,
                                selectedCompany,
                                selectedRepresentative
                            }"
                        />
                    </div>

                    <!-- Seção: Grupos de Permissões -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.permissionGroups') }}
                        </div>
                        <q-card flat bordered class="q-pa-xs q-mb-lg">
                            <q-select
                                v-model="permissionGroups"
                                :label="$t('forms.labels.permissionGroups')"
                                :options="permissionGroupsOptions"
                                outlined
                                dense
                                stack-label
                                multiple
                                use-chips
                                clearable
                                use-input
                                @filter="filterPermissionGroups"
                                emit-value
                                map-options
                                options-dense
                                :error="!!errors.permissionGroups"
                                :error-message="errors.permissionGroups"
                                class="q-mb-md"
                            />
                        </q-card>
                    </div>

                    <!-- Seção: Permissões Avulsas -->
                    <div class="col-12">
                        <q-card flat bordered class="q-pa-xs">
                            <module-permissions
                                v-model:permissoes="customPermissions"
                                :modulos="modulos"
                                :rotas="rotas"
                                :acoes="acoes"
                            />
                        </q-card>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref, onMounted } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'

const { t: $t } = useI18n()
// Components
import FormTemplate from '@/components/FormTemplate.vue'
import ModulePermissions from '@/components/permissions/ModulePermissions.vue'
import ViewUserCredentials from '@/components/user/ViewUserCredentials.vue'
import FormViewRestrictions from '@/components/forms/FormViewRestrictions.vue'

// Form Usuário Permissões
const usuarioPermissoesSchema = yup.object({
    passwordExpirationDays: yup
        .number()
        .nullable()
        .transform(value => (isNaN(value) ? null : value))
        .min(0, $t('validate.invalidPasswordExpirationDays')),
    permissionGroups: yup.array().of(yup.string()).nullable(),
    selectedSeller: yup.string().nullable(),
    selectedBuyer: yup.string().nullable(),
    selectedCashier: yup.string().nullable(),
    selectedCompany: yup.string().nullable(),
    selectedRepresentative: yup.string().nullable()
})

const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: usuarioPermissoesSchema,
    initialValues: {
        passwordExpirationDays: 90,
        permissionGroups: null,
        selectedSeller: null,
        selectedBuyer: null,
        selectedCashier: null,
        selectedCompany: null,
        selectedRepresentative: null
    },
    validateOnMount: false
})

// Define fields
const [passwordExpirationDays] = defineField('passwordExpirationDays')
const [permissionGroups] = defineField('permissionGroups')
const [selectedSeller] = defineField('selectedSeller')
const [selectedBuyer] = defineField('selectedBuyer')
const [selectedCashier] = defineField('selectedCashier')
const [selectedCompany] = defineField('selectedCompany')
const [selectedRepresentative] = defineField('selectedRepresentative')

// Additional fields not in validation
const status = ref(true)
const isAdmin = ref(false)
const forceOperatorClosure = ref(false)
const viewOnlySeller = ref(false)
const viewOnlyBuyer = ref(false)
const viewOnlyCashier = ref(false)
const viewOnlyCompany = ref(false)
const viewOnlyRepresentative = ref(false)

// Credenciais do usuário (mock - deve vir do banco)
const userCredentials = ref({
    name: 'João',
    surname: 'Silva',
    username: 'joaosilva',
    email: '<EMAIL>',
    role: 'Gerente'
})

// Referência para o componente de restrições de visualização
const viewRestrictionsRef = ref<InstanceType<
    typeof FormViewRestrictions
> | null>(null)

// Grupos de Permissões (mock - deve vir do banco)
const permissionGroupsOptions = ref([
    { label: 'Administradores', value: 'admin' },
    { label: 'Gerentes', value: 'gerentes' },
    { label: 'Vendedores', value: 'vendedores' },
    { label: 'Financeiro', value: 'financeiro' },
    { label: 'Estoque', value: 'estoque' },
    { label: 'Suporte', value: 'suporte' }
])

// Opções para os selects (mock - deve vir do banco)
const sellersOptions = ref([
    { label: 'João Silva', value: '1' },
    { label: 'Maria Oliveira', value: '2' },
    { label: 'Pedro Santos', value: '3' }
])

const buyersOptions = ref([
    { label: 'Carlos Mendes', value: '1' },
    { label: 'Ana Souza', value: '2' },
    { label: 'Roberto Alves', value: '3' }
])

const cashiersOptions = ref([
    { label: 'Fernanda Lima', value: '1' },
    { label: 'Ricardo Gomes', value: '2' },
    { label: 'Juliana Costa', value: '3' }
])

const companiesOptions = ref([
    { label: 'Matriz', value: '1' },
    { label: 'Filial SP', value: '2' },
    { label: 'Filial RJ', value: '3' }
])

const representativesOptions = ref([
    { label: 'Marcos Pereira', value: '1' },
    { label: 'Luciana Ferreira', value: '2' },
    { label: 'Eduardo Martins', value: '3' }
])

// Módulos (mock - deve vir do banco ou das rotas)
const modulos = ref([
    { label: 'Dashboard', value: 'dashboard' },
    { label: 'Usuários', value: 'usuarios' },
    { label: 'Clientes', value: 'clientes' },
    { label: 'Produtos', value: 'produtos' },
    { label: 'Vendas', value: 'vendas' },
    { label: 'Financeiro', value: 'financeiro' },
    { label: 'Relatórios', value: 'relatorios' },
    { label: 'Configurações', value: 'configuracoes' }
])

// Rotas por módulo (mock - deve vir do banco ou das rotas)
const rotas = ref([
    {
        label: 'Visualizar Dashboard',
        value: 'dashboard_view',
        modulo: 'dashboard'
    },
    { label: 'Listar Usuários', value: 'usuarios_list', modulo: 'usuarios' },
    { label: 'Criar Usuário', value: 'usuarios_create', modulo: 'usuarios' },
    { label: 'Editar Usuário', value: 'usuarios_edit', modulo: 'usuarios' },
    { label: 'Excluir Usuário', value: 'usuarios_delete', modulo: 'usuarios' },
    { label: 'Listar Clientes', value: 'clientes_list', modulo: 'clientes' },
    { label: 'Criar Cliente', value: 'clientes_create', modulo: 'clientes' },
    { label: 'Editar Cliente', value: 'clientes_edit', modulo: 'clientes' },
    { label: 'Excluir Cliente', value: 'clientes_delete', modulo: 'clientes' },
    { label: 'Listar Produtos', value: 'produtos_list', modulo: 'produtos' },
    { label: 'Criar Produto', value: 'produtos_create', modulo: 'produtos' },
    { label: 'Editar Produto', value: 'produtos_edit', modulo: 'produtos' },
    { label: 'Excluir Produto', value: 'produtos_delete', modulo: 'produtos' },
    { label: 'Listar Vendas', value: 'vendas_list', modulo: 'vendas' },
    { label: 'Criar Venda', value: 'vendas_create', modulo: 'vendas' },
    { label: 'Editar Venda', value: 'vendas_edit', modulo: 'vendas' },
    { label: 'Excluir Venda', value: 'vendas_delete', modulo: 'vendas' },
    {
        label: 'Relatórios Financeiros',
        value: 'financeiro_reports',
        modulo: 'financeiro'
    },
    {
        label: 'Contas a Pagar',
        value: 'financeiro_payables',
        modulo: 'financeiro'
    },
    {
        label: 'Contas a Receber',
        value: 'financeiro_receivables',
        modulo: 'financeiro'
    },
    {
        label: 'Relatórios de Vendas',
        value: 'relatorios_vendas',
        modulo: 'relatorios'
    },
    {
        label: 'Relatórios de Estoque',
        value: 'relatorios_estoque',
        modulo: 'relatorios'
    },
    {
        label: 'Configurações do Sistema',
        value: 'configuracoes_sistema',
        modulo: 'configuracoes'
    },
    {
        label: 'Configurações de Usuário',
        value: 'configuracoes_usuario',
        modulo: 'configuracoes'
    }
])

// Ações possíveis
const acoes = ref([
    { label: 'Visualizar', value: 'view' },
    { label: 'Criar', value: 'create' },
    { label: 'Editar', value: 'edit' },
    { label: 'Excluir', value: 'delete' }
])

// Objeto para armazenar as permissões customizadas
const customPermissions = ref(
    {} as Record<string, Record<string, Record<string, boolean>>>
)

// Inicializa o objeto de permissões customizadas
const initCustomPermissions = () => {
    try {
        // Cria um novo objeto para armazenar as permissões
        const permissoesObj: Record<
            string,
            Record<string, Record<string, boolean>>
        > = {}

        // Inicializa a estrutura de permissões para cada módulo
        for (const modulo of modulos.value) {
            const moduloKey = modulo.value
            permissoesObj[moduloKey] = {}

            // Filtra as rotas para o módulo atual
            const rotasDoModulo = rotas.value.filter(
                rota => rota.modulo === moduloKey
            )

            // Para cada rota, inicializa as ações
            for (const rota of rotasDoModulo) {
                const rotaKey = rota.value
                permissoesObj[moduloKey][rotaKey] = {}

                // Para cada ação, inicializa como false
                for (const acao of acoes.value) {
                    const acaoKey = acao.value
                    permissoesObj[moduloKey][rotaKey][acaoKey] = false
                }
            }
        }

        // Atribui o objeto completo à referência
        customPermissions.value = permissoesObj
    } catch (error) {
        // Silencia o erro em produção
        if (process.env.NODE_ENV !== 'production') {
            // eslint-disable-next-line no-console
            console.error('Erro ao inicializar permissões customizadas:', error)
        }
    }
}

// Filtro para grupos de permissões
const filterPermissionGroups = (
    val: string,
    update: (callback: () => void) => void
) => {
    if (val === '') {
        update(() => {
            permissionGroupsOptions.value = [
                { label: 'Administradores', value: 'admin' },
                { label: 'Gerentes', value: 'gerentes' },
                { label: 'Vendedores', value: 'vendedores' },
                { label: 'Financeiro', value: 'financeiro' },
                { label: 'Estoque', value: 'estoque' },
                { label: 'Suporte', value: 'suporte' }
            ]
        })
    } else {
        update(() => {
            const needle = val.toLowerCase()
            permissionGroupsOptions.value =
                permissionGroupsOptions.value.filter(
                    v => v.label.toLowerCase().indexOf(needle) > -1
                )
        })
    }
}

onMounted(() => {
    initCustomPermissions()
})

// Validação manual para campos condicionais
const validateForm = async () => {
    // Validação padrão do Yup
    const result = await validate()

    // Validações condicionais
    let isValid = result.valid

    // Validar restrições de visualização
    if (viewRestrictionsRef.value) {
        const viewRestrictionsResult =
            await viewRestrictionsRef.value.validate()
        isValid = isValid && viewRestrictionsResult.valid

        // Adicionar erros das restrições de visualização aos erros do formulário
        if (!viewRestrictionsResult.valid) {
            Object.assign(errors.value, viewRestrictionsResult.errors)
        }
    }

    return isValid
}

// Expose form methods and data
defineExpose({
    validate: validateForm,
    getData: () => {
        // Obter dados das restrições de visualização
        const viewRestrictions = viewRestrictionsRef.value
            ? viewRestrictionsRef.value.getData()
            : {
                  viewOnlySeller: viewOnlySeller.value,
                  viewOnlyBuyer: viewOnlyBuyer.value,
                  viewOnlyCashier: viewOnlyCashier.value,
                  viewOnlyCompany: viewOnlyCompany.value,
                  viewOnlyRepresentative: viewOnlyRepresentative.value,
                  selectedSeller: selectedSeller.value,
                  selectedBuyer: selectedBuyer.value,
                  selectedCashier: selectedCashier.value,
                  selectedCompany: selectedCompany.value,
                  selectedRepresentative: selectedRepresentative.value
              }

        return {
            ...values,
            status: status.value,
            isAdmin: isAdmin.value,
            forceOperatorClosure: forceOperatorClosure.value,
            ...viewRestrictions,
            customPermissions: customPermissions.value,
            userCredentials: userCredentials.value
        }
    },
    resetForm
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
