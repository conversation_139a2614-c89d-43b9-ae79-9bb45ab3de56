<template>
    <div id="form-colecao-contatos">
        <div class="text-caption q-mb-xs text-center">
            {{ title || $t('contactCollection.titles.main') }}
        </div>

        <q-card flat bordered class="q-mb-md">
            <q-card-section class="q-pa-xs">
                <!-- Cabeçalho com botão adicionar -->
                <div class="row items-center q-mb-xs">
                    <div class="col">
                        <!-- Título removido para evitar duplicação -->
                    </div>
                    <div class="col-auto">
                        <q-btn
                            @click="abrirModalAdicionar"
                            color="primary"
                            icon="add"
                            :label="$t('contactCollection.labels.addContact')"
                            unelevated
                            size="sm"
                            :disable="
                                readonly ||
                                (!!maxContatos &&
                                    contatos.length >= maxContatos)
                            "
                        />
                    </div>
                </div>

                <!-- Espaçamento -->
                <div class="q-mb-md"></div>

                <!-- Lista de contatos -->
                <div v-if="contatos.length === 0" class="text-center q-pa-xs">
                    <q-icon
                        name="contacts"
                        size="48px"
                        color="grey-5"
                        class="q-mb-xs"
                    />
                    <div class="text-grey-6">
                        {{ $t('contactCollection.messages.noContacts') }}
                    </div>
                </div>

                <div v-else class="q-gutter-xs">
                    <q-card
                        v-for="(contato, index) in contatos"
                        :key="contato.id || index"
                        flat
                        bordered
                        class="contact-card q-mb-xs"
                    >
                        <q-card-section class="q-pa-xs">
                            <div class="row q-col-gutter-md">
                                <!-- Avatar e Nome -->
                                <div class="col-12 col-sm-auto">
                                    <div class="row items-center q-gutter-md">
                                        <div class="col-auto">
                                            <q-avatar
                                                size="48px"
                                                color="primary"
                                                text-color="white"
                                                class="contact-avatar"
                                            >
                                                {{ getInitials(contato.nome) }}
                                            </q-avatar>
                                        </div>
                                        <div class="col">
                                            <div
                                                class="text-h6 contact-name"
                                                :class="
                                                    isDark
                                                        ? 'text-white'
                                                        : 'text-primary'
                                                "
                                            >
                                                {{ contato.nome }}
                                            </div>
                                            <div
                                                class="text-body2 text-grey-7 contact-role"
                                            >
                                                <q-icon
                                                    name="work"
                                                    size="xs"
                                                    class="q-mr-xs"
                                                />
                                                {{
                                                    getOcupacaoLabel(
                                                        contato.ocupacao
                                                    )
                                                }}
                                                <span class="q-mx-xs">•</span>
                                                {{
                                                    getDepartamentoLabel(
                                                        contato.departamento
                                                    )
                                                }}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Informações de Contato -->
                                <div class="col-12 col-sm">
                                    <div class="row q-col-gutter-xs">
                                        <!-- Localização -->
                                        <div class="col-12 col-md-6">
                                            <div class="contact-info-item">
                                                <q-icon
                                                    name="location_on"
                                                    size="sm"
                                                    color="grey-6"
                                                    class="q-mr-sm"
                                                />
                                                <div>
                                                    <div class="text-body2">
                                                        {{
                                                            getCidadeEstadoLabel(
                                                                contato
                                                            )
                                                        }}
                                                    </div>
                                                    <div
                                                        class="text-caption text-grey-6"
                                                    >
                                                        Localização
                                                    </div>
                                                </div>
                                            </div>

                                            <div
                                                v-if="contato.dataNascimento"
                                                class="contact-info-item q-mt-sm"
                                            >
                                                <q-icon
                                                    name="cake"
                                                    size="sm"
                                                    color="grey-6"
                                                    class="q-mr-sm"
                                                />
                                                <div>
                                                    <div class="text-body2">
                                                        {{
                                                            formatarDataNascimento(
                                                                contato.dataNascimento
                                                            )
                                                        }}
                                                    </div>
                                                    <div
                                                        class="text-caption text-grey-6"
                                                    >
                                                        Nascimento
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Contatos -->
                                        <div class="col-12 col-md-6">
                                            <div
                                                v-if="contato.telefones?.length"
                                                class="contact-info-item"
                                            >
                                                <q-icon
                                                    name="phone"
                                                    size="sm"
                                                    color="grey-6"
                                                    class="q-mr-sm"
                                                />
                                                <div>
                                                    <div class="text-body2">
                                                        {{
                                                            contato.telefones
                                                                .length
                                                        }}
                                                        telefone(s)
                                                    </div>
                                                    <div
                                                        class="text-caption text-grey-6"
                                                    >
                                                        Telefones
                                                    </div>
                                                </div>
                                            </div>

                                            <div
                                                v-if="contato.emails?.length"
                                                class="contact-info-item q-mt-sm"
                                            >
                                                <q-icon
                                                    name="email"
                                                    size="sm"
                                                    color="grey-6"
                                                    class="q-mr-sm"
                                                />
                                                <div>
                                                    <div class="text-body2">
                                                        {{
                                                            contato.emails
                                                                .length
                                                        }}
                                                        email(s)
                                                    </div>
                                                    <div
                                                        class="text-caption text-grey-6"
                                                    >
                                                        E-mails
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Botões de Ação -->
                                <div class="col-12 col-sm-auto">
                                    <div
                                        class="row q-gutter-xs justify-center justify-sm-end"
                                    >
                                        <q-btn
                                            @click="abrirModalEditar(index)"
                                            color="primary"
                                            icon="edit"
                                            flat
                                            round
                                            size="sm"
                                            :disable="readonly"
                                            class="action-btn"
                                        >
                                            <q-tooltip>
                                                {{
                                                    $t(
                                                        'contactCollection.labels.editContact'
                                                    )
                                                }}
                                            </q-tooltip>
                                        </q-btn>
                                        <q-btn
                                            @click="removerContato(index)"
                                            color="negative"
                                            icon="delete"
                                            flat
                                            round
                                            size="sm"
                                            :disable="readonly"
                                            class="action-btn"
                                        >
                                            <q-tooltip>
                                                {{
                                                    $t(
                                                        'contactCollection.labels.removeContact'
                                                    )
                                                }}
                                            </q-tooltip>
                                        </q-btn>
                                    </div>
                                </div>
                            </div>
                        </q-card-section>
                    </q-card>
                </div>
            </q-card-section>
        </q-card>

        <!-- Modal do formulário de contato -->
        <modal-template
            :model-value="modalAberto"
            :title="
                modoEdicao
                    ? $t('contactCollection.titles.editContact')
                    : $t('contactCollection.titles.addContact')
            "
            card-style="min-width: 320px; max-width: 1200px; width: 95vw"
            @update:model-value="(value: boolean) => (modalAberto = value)"
            @close="fecharModal"
        >
            <!-- Conteúdo -->
            <form-contato
                ref="formContatoRef"
                :initial-values="contatoEdicao"
            />

            <!-- Ações -->
            <template #actions>
                <q-btn
                    flat
                    color="negative"
                    :label="$t('buttons.cancel')"
                    @click="fecharModal"
                />
                <q-btn
                    unelevated
                    color="positive"
                    :label="$t('buttons.confirm')"
                    @click="salvarContato"
                    :loading="salvando"
                />
            </template>
        </modal-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { Notify, Dialog } from 'quasar'
import { useTheme } from 'src/composables/useTheme'

const { t: $t } = useI18n()
const { isDark } = useTheme()

// Components
import ModalTemplate from '@/components/ModalTemplate.vue'
import FormContato from '@/components/forms/FormContato.vue'

// Types
import type {
    FormContatoData,
    FormColecaoContatosProps,
    ValidationResult
} from '@/interfaces/FormContato'

// Services
import {
    getStatesByCountry,
    getCitiesByState
} from '@/services/locationService'

// Props
const props = withDefaults(defineProps<FormColecaoContatosProps>(), {
    title: '',
    readonly: false,
    initialValues: () => []
})

// Estados
const contatos = ref<FormContatoData[]>(props.initialValues || [])
const modalAberto = ref(false)
const modoEdicao = ref(false)
const indiceEdicao = ref(-1)
const contatoEdicao = ref<Partial<FormContatoData>>({})
const salvando = ref(false)

// Referência do formulário
const formContatoRef = ref<InstanceType<typeof FormContato> | null>(null)

// Dados mockados para ocupações e departamentos
const ocupacoesMock = [
    { label: 'Gerente', value: 'gerente' },
    { label: 'Analista', value: 'analista' },
    { label: 'Coordenador', value: 'coordenador' },
    { label: 'Diretor', value: 'diretor' },
    { label: 'Assistente', value: 'assistente' }
]

const departamentosMock = [
    { label: 'Vendas', value: 'vendas' },
    { label: 'Marketing', value: 'marketing' },
    { label: 'Financeiro', value: 'financeiro' },
    { label: 'Recursos Humanos', value: 'rh' },
    { label: 'TI', value: 'ti' }
]

// Métodos para abrir/fechar modal
const abrirModalAdicionar = (): void => {
    modoEdicao.value = false
    indiceEdicao.value = -1
    contatoEdicao.value = {}
    modalAberto.value = true
}

const abrirModalEditar = (index: number): void => {
    modoEdicao.value = true
    indiceEdicao.value = index
    contatoEdicao.value = { ...contatos.value[index] }
    modalAberto.value = true
}

const fecharModal = (): void => {
    modalAberto.value = false
    modoEdicao.value = false
    indiceEdicao.value = -1
    contatoEdicao.value = {}
    salvando.value = false
}

// Método para salvar contato
const salvarContato = async (): Promise<void> => {
    if (!formContatoRef.value) return

    salvando.value = true

    try {
        const validationResult = await formContatoRef.value.validate()

        if (!validationResult.valid) {
            Notify.create({
                message: $t('notifications.pleaseFixErrors'),
                color: 'negative',
                position: 'bottom',
                timeout: 3000,
                icon: 'error'
            })
            return
        }

        const dadosContato = formContatoRef.value.getData()

        if (modoEdicao.value) {
            // Editar contato existente
            const contatoExistente = contatos.value[indiceEdicao.value]
            contatos.value[indiceEdicao.value] = {
                ...dadosContato,
                id: contatoExistente?.id || Date.now().toString()
            }

            Notify.create({
                message: $t('contactCollection.messages.contactUpdated'),
                color: 'positive',
                position: 'bottom',
                timeout: 2000,
                icon: 'check_circle'
            })
        } else {
            // Adicionar novo contato
            const novoContato: FormContatoData = {
                ...dadosContato,
                id: Date.now().toString()
            }

            contatos.value.push(novoContato)

            Notify.create({
                message: $t('contactCollection.messages.contactAdded'),
                color: 'positive',
                position: 'bottom',
                timeout: 2000,
                icon: 'check_circle'
            })
        }

        fecharModal()
    } catch {
        Notify.create({
            message: $t('contactCollection.messages.saveError'),
            color: 'negative',
            position: 'bottom',
            timeout: 3000,
            icon: 'error'
        })
    } finally {
        salvando.value = false
    }
}

// Método para remover contato
const removerContato = (index: number): void => {
    if (index < 0 || index >= contatos.value.length) {
        return
    }

    Dialog.create({
        title: $t('contactCollection.labels.removeContact'),
        message: $t('contactCollection.messages.confirmRemoveContact'),
        cancel: true,
        persistent: true
    }).onOk(() => {
        contatos.value.splice(index, 1)

        Notify.create({
            message: $t('contactCollection.messages.contactRemoved'),
            color: 'positive',
            position: 'bottom',
            timeout: 2000,
            icon: 'check_circle'
        })
    })
}

// Métodos utilitários para formatação
const getOcupacaoLabel = (ocupacao: string): string => {
    const ocupacaoOption = ocupacoesMock.find(
        option => option.value === ocupacao
    )
    return ocupacaoOption?.label || ocupacao
}

const getDepartamentoLabel = (departamento: string): string => {
    const departamentoOption = departamentosMock.find(
        option => option.value === departamento
    )
    return departamentoOption?.label || departamento
}

const getCidadeEstadoLabel = (contato: FormContatoData): string => {
    const paisContato = contato.pais || 'BR'
    const estadosOptions = getStatesByCountry(paisContato)
    const cidadesOptions = getCitiesByState(paisContato, contato.estado)

    const estadoLabel =
        estadosOptions.find(option => option.value === contato.estado)?.label ||
        contato.estado
    const cidadeLabel =
        cidadesOptions.find(option => option.value === contato.cidade)?.label ||
        contato.cidade

    return `${cidadeLabel}, ${estadoLabel}`
}

const formatarDataNascimento = (data: string): string => {
    if (!data) return ''

    try {
        const date = new Date(data)
        return date.toLocaleDateString('pt-BR')
    } catch {
        return data
    }
}

const getInitials = (nome: string): string => {
    if (!nome) return '?'

    const words = nome
        .trim()
        .split(' ')
        .filter(word => word.length > 0)
    if (words.length === 0) return '?'
    if (words.length === 1) {
        return words[0]?.charAt(0).toUpperCase() || '?'
    }

    const firstInitial = words[0]?.charAt(0) || ''
    const lastInitial = words[words.length - 1]?.charAt(0) || ''

    return (firstInitial + lastInitial).toUpperCase()
}

// Validação completa da coleção
const validateCollection = (): ValidationResult => {
    const errors: string[] = []

    if (contatos.value.length === 0) {
        errors.push($t('contactCollection.validation.minContacts'))
    }

    return {
        valid: errors.length === 0,
        errors
    }
}

// Expor métodos e dados
defineExpose({
    validate: validateCollection,
    getData: () => contatos.value,
    resetForm: () => {
        contatos.value = []
    }
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}

/* Estilos para os cards de contato */
.contact-card {
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
}

.contact-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* Avatar do contato */
.contact-avatar {
    font-weight: 600;
    font-size: 18px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Nome do contato */
.contact-name {
    font-weight: 600;
    margin-bottom: 4px;
    line-height: 1.2;
}

/* Cargo/função */
.contact-role {
    display: flex;
    align-items: center;
    line-height: 1.3;
}

/* Itens de informação */
.contact-info-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.contact-info-item .q-icon {
    margin-top: 2px;
    flex-shrink: 0;
}

/* Botões de ação */
.action-btn {
    transition: all 0.2s ease;
}

.action-btn:hover {
    transform: scale(1.1);
}

/* Responsividade para mobile */
@media (max-width: 599px) {
    .contact-card {
        border-radius: 8px;
    }

    .contact-card .q-card-section {
        padding: 16px;
    }

    .contact-name {
        font-size: 1.1rem;
    }

    .contact-role {
        font-size: 0.875rem;
    }

    .contact-info-item {
        margin-bottom: 8px;
    }

    .action-btn {
        margin: 4px;
    }
}

/* Modo escuro */
.body--dark .contact-card {
    background-color: #2d2d2d;
    border-color: #404040;
}

.body--dark .contact-card:hover {
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
    background-color: #353535;
}

.body--dark .contact-name {
    color: white;
}

.body--dark .contact-role {
    color: #b0b0b0;
}

.body--dark .contact-info-item .text-body2 {
    color: #e0e0e0;
}

.body--dark .contact-info-item .text-caption {
    color: #9e9e9e;
}

.body--dark .contact-avatar {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Animações suaves */
.contact-card,
.contact-avatar,
.action-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Melhorias para telas pequenas */
@media (max-width: 480px) {
    .contact-card .row.q-col-gutter-md {
        gap: 12px;
    }

    .contact-info-item {
        flex-direction: row;
        align-items: center;
    }

    .contact-info-item .q-icon {
        margin-top: 0;
    }
}

/* Estados de foco para acessibilidade */
.action-btn:focus {
    outline: 2px solid var(--q-primary);
    outline-offset: 2px;
}

.body--dark .action-btn:focus {
    outline-color: #90caf9;
}
</style>
