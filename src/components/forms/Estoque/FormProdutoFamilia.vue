<template>
    <div id="form-pais" class="q-pa-xs">
        <form-template :title="$t('pages.family.title')">
            <template #header>
                <div class="row q-col-gutter-xs">
                    <div class="col-auto">
                        <q-toggle
                            v-model="ativo"
                            :label="$t('forms.labels.active')"
                            color="secondary"
                            class="no-bottom-spacing text-white"
                            dense
                        />
                    </div>
                </div>
            </template>

            <template #body>
                <div class="row q-col-gutter-xs">
                    <!-- Informações Básicas -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.basicInfo') }}
                        </div>
                        <q-card flat bordered class="q-mb-md">
                            <q-card-section class="q-pa-xs">
                                <div class="row q-col-gutter-xs">
                                    <!-- Descrição -->
                                    <div class="col-12">
                                        <q-input
                                            v-model="descricao"
                                            :label="
                                                $t('forms.labels.description')
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            :error="!!errors.descricao"
                                            :error-message="errors.descricao"
                                            maxlength="100"
                                            v-required-field
                                            @update:model-value="
                                                onDescricaoInput
                                            "
                                        />
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Vue
import { ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

// Validation
import * as yup from 'yup'
import { useForm } from 'vee-validate'

// Components
import FormTemplate from '@/components/FormTemplate.vue'

// Internationalization
const { t: $t } = useI18n()

// Props
const props = defineProps({
    initialValues: {
        type: Object,
        default: () => ({})
    }
})

// Campo adicional não incluído na validação
const ativo = ref(
    props.initialValues.ativo !== undefined ? props.initialValues.ativo : true
)

// Schema de validação
const paisSchema = yup.object({
    descricao: yup
        .string()
        .required($t('validate.requiredDescription'))
        .max(100, $t('validate.maxLength', { max: 100 }))
})

// Inicializa o formulário
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: paisSchema,
    initialValues: {
        descricao: props.initialValues.descricao || ''
    },
    validateOnMount: false
})

// Define fields
const [descricao] = defineField('descricao')

// Watch para mudanças nos valores iniciais
watch(
    () => props.initialValues,
    newValues => {
        if (newValues && Object.keys(newValues).length > 0) {
            resetForm({
                values: {
                    descricao: newValues.descricao || ''
                }
            })
        }
    },
    { deep: true, immediate: true }
)

const onDescricaoInput = (value: string | number | null): void => {
    descricao.value = value ? value.toString().toUpperCase() : ''
}

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: () => {
        return {
            ativo: ativo.value,
            ...values
        }
    },
    resetForm: () => {
        resetForm()
    }
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
