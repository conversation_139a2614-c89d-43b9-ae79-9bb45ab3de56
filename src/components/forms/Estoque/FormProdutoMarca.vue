<template>
    <div id="form-produto-marca" class="q-pa-xs">
        <form-template :title="$t('forms.labels.brand')">
            <template #header>
                <div class="row q-col-gutter-xs">
                    <div class="col-auto">
                        <q-toggle
                            v-model="ativo"
                            :label="$t('forms.labels.active')"
                            color="secondary"
                            class="no-bottom-spacing text-white"
                            dense
                        />
                    </div>
                </div>
            </template>

            <template #body>
                <div class="row q-col-gutter-xs">
                    <!-- Informações Básicas -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.basicInfo') }}
                        </div>
                        <q-card flat bordered class="q-mb-md">
                            <q-card-section class="q-pa-xs">
                                <div class="row q-col-gutter-xs">
                                    <!-- Descrição -->
                                    <div class="col-12 col-md-6">
                                        <q-input
                                            v-model="descricao"
                                            :label="
                                                $t('forms.labels.description')
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            maxlength="100"
                                            counter
                                            v-required-field
                                            :error="!!errors.descricao"
                                            :error-message="errors.descricao"
                                            @update:model-value="
                                                value =>
                                                    (descricao =
                                                        typeof value ===
                                                        'string'
                                                            ? value.toUpperCase()
                                                            : String(
                                                                  value || ''
                                                              ).toUpperCase())
                                            "
                                        >
                                            <template #append>
                                                <q-icon name="description" />
                                            </template>
                                        </q-input>
                                    </div>

                                    <!-- Abreviação -->
                                    <div class="col-12 col-md-6">
                                        <q-input
                                            v-model="abreviacao"
                                            :label="
                                                $t('forms.labels.abbreviation')
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            maxlength="10"
                                            counter
                                            v-required-field
                                            :error="!!errors.abreviacao"
                                            :error-message="errors.abreviacao"
                                            @update:model-value="
                                                value =>
                                                    (abreviacao =
                                                        typeof value ===
                                                        'string'
                                                            ? value.toUpperCase()
                                                            : String(
                                                                  value || ''
                                                              ).toUpperCase())
                                            "
                                        >
                                            <template #append>
                                                <q-icon name="short_text" />
                                            </template>
                                        </q-input>
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'
import FormTemplate from '@/components/FormTemplate.vue'

const { t: $t } = useI18n()

// Props
const props = defineProps({
    initialValues: {
        type: Object,
        default: () => ({})
    }
})

// Form Schema
const marcaSchema = yup.object({
    descricao: yup
        .string()
        .trim()
        .required($t('validate.requiredDescription'))
        .min(2, $t('validate.minLength', { min: 2 }))
        .max(100, $t('validate.maxLength', { max: 100 })),
    abreviacao: yup
        .string()
        .trim()
        .required($t('validate.requiredAbbreviation'))
        .min(1, $t('validate.minLength', { min: 1 }))
        .max(10, $t('validate.maxLength', { max: 10 }))
})

// Inicializa o formulário com valores padrão ou iniciais
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: marcaSchema,
    initialValues: {
        descricao: props.initialValues.descricao || '',
        abreviacao: props.initialValues.abreviacao || ''
    },
    validateOnMount: false
})

// Define fields
const [descricao] = defineField('descricao')
const [abreviacao] = defineField('abreviacao')

// Campo adicional não incluído na validação
const ativo = ref(
    props.initialValues.ativo !== undefined ? props.initialValues.ativo : true
)

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: () => {
        return {
            ...values,
            ativo: ativo.value
        }
    },
    resetForm: () => {
        resetForm()
        ativo.value = true
    }
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
