<template>
    <div id="form-documento">
        <form-template :title="$t('forms.titles.documentForm')">
            <template #body>
                <div class="row q-col-gutter-xs">
                    <!-- Tipo de Documento -->
                    <div class="col-12 col-sm-6">
                        <q-select
                            v-model="tipoDocumento"
                            :label="$t('forms.labels.documentType')"
                            :options="tiposDocumentoOptions"
                            outlined
                            dense
                            stack-label
                            emit-value
                            map-options
                            :error="!!errors.tipoDocumento"
                            :error-message="errors.tipoDocumento"
                            @update:model-value="atualizarMascara"
                        />
                    </div>

                    <!-- Número do Documento -->
                    <div class="col-12 col-sm-6">
                        <q-input
                            v-model="numeroDocumento"
                            :label="$t('forms.labels.documentNumber')"
                            :mask="mascaraDocumento"
                            outlined
                            dense
                            stack-label
                            :error="!!errors.numeroDocumento"
                            :error-message="errors.numeroDocumento"
                        >
                            <template #append>
                                <q-icon name="description" />
                            </template>
                        </q-input>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { computed } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'

const { t: $t } = useI18n()
// Components
import FormTemplate from '@/components/FormTemplate.vue'

// Props
const props = defineProps({
    initialValues: {
        type: Object,
        default: () => ({})
    }
})

// Definição de tipos
interface OptionItem {
    label: string
    value: string
}

// Opções para os selects
const tiposDocumentoOptions: OptionItem[] = [
    { label: $t('forms.documentTypes.cpf'), value: 'cpf' },
    { label: $t('forms.documentTypes.cnpj'), value: 'cnpj' },
    { label: $t('forms.documentTypes.rg'), value: 'rg' },
    { label: $t('forms.documentTypes.passport'), value: 'passport' },
    { label: $t('forms.documentTypes.other'), value: 'other' }
]

// Máscaras para cada tipo de documento
const mascarasDocumento = {
    cpf: '###.###.###-##',
    cnpj: '##.###.###/####-##',
    rg: '##.###.###-#',
    passport: 'AA######',
    other: ''
}

// Form Documento Schema
const documentoSchema = yup.object({
    tipoDocumento: yup.string().required($t('validate.requiredDocumentType')),
    numeroDocumento: yup
        .string()
        .required($t('validate.requiredDocumentNumber'))
})

// Inicializa o formulário com valores padrão ou iniciais
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: documentoSchema,
    initialValues: {
        tipoDocumento: props.initialValues.tipoDocumento || '',
        numeroDocumento: props.initialValues.numeroDocumento || ''
    },
    validateOnMount: false
})

// Define fields
const [tipoDocumento] = defineField('tipoDocumento')
const [numeroDocumento] = defineField('numeroDocumento')

// Máscara do documento baseada no tipo selecionado
const mascaraDocumento = computed((): string => {
    return (
        mascarasDocumento[
            tipoDocumento.value as keyof typeof mascarasDocumento
        ] || ''
    )
})

// Atualizar máscara quando o tipo de documento muda
const atualizarMascara = (): void => {
    // Limpar o número do documento ao mudar o tipo
    numeroDocumento.value = ''
}

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: () => {
        return {
            ...values
        }
    },
    resetForm
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
