<template>
    <div id="form-colecao-enderecos">
        <div class="text-caption q-mb-xs text-center">
            {{ title || $t('addressCollection.titles.main') }}
        </div>

        <q-card flat bordered class="q-mb-md">
            <q-card-section class="q-pa-xs">
                <!-- Cabeçalho com botão adicionar -->
                <div class="row items-center q-mb-xs">
                    <div class="col">
                        <!-- Título removido para evitar duplicação -->
                    </div>
                    <div class="col-auto">
                        <q-btn
                            @click="abrirModalAdicionar"
                            color="primary"
                            icon="add"
                            :label="$t('addressCollection.labels.addAddress')"
                            unelevated
                            size="sm"
                            :disable="
                                readonly ||
                                (!!maxEnderecos &&
                                    enderecos.length >= maxEnderecos)
                            "
                        />
                    </div>
                </div>

                <div class="q-mb-md"></div>

                <!-- Estado vazio -->
                <div
                    v-if="enderecos.length === 0"
                    class="text-center q-pa-lg text-grey-5"
                >
                    <div class="flex justify-center q-mb-xs">
                        <q-icon name="location_off" size="32px" />
                    </div>
                    <div class="text-body2 q-mb-xs">
                        {{ $t('addressCollection.messages.noAddresses') }}
                    </div>
                    <div class="text-caption text-grey-6">
                        {{ $t('addressCollection.messages.noAddressHint') }}
                    </div>
                </div>

                <!-- Lista de endereços -->
                <div v-else class="q-gutter-xs">
                    <q-card
                        v-for="(endereco, index) in enderecos"
                        :key="endereco.id || index"
                        flat
                        bordered
                        class="q-pa-xs"
                    >
                        <div class="row q-col-gutter-xs">
                            <!-- Conteúdo do endereço -->
                            <div class="col-12 col-md-11">
                                <div class="row q-col-gutter-xs">
                                    <!-- Tipo e País -->
                                    <div class="col-12 col-sm-6">
                                        <div class="text-caption text-grey-6">
                                            {{ $t('forms.labels.addressType') }}
                                        </div>
                                        <div class="text-body2">
                                            {{ getTipoLabel(endereco.tipo) }}
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6">
                                        <div class="text-caption text-grey-6">
                                            {{ $t('forms.labels.country') }}
                                        </div>
                                        <div class="text-body2">
                                            {{ getPaisLabel(endereco.pais) }}
                                        </div>
                                    </div>

                                    <!-- Endereço completo -->
                                    <div class="col-12">
                                        <div class="text-caption text-grey-6">
                                            {{
                                                $t(
                                                    'addressCollection.labels.fullAddress'
                                                )
                                            }}
                                        </div>
                                        <div class="text-body2">
                                            {{
                                                formatarEnderecoCompleto(
                                                    endereco
                                                )
                                            }}
                                        </div>
                                    </div>

                                    <!-- CEP e Cidade/Estado -->
                                    <div
                                        class="col-12 col-sm-6"
                                        v-if="endereco.cep"
                                    >
                                        <div class="text-caption text-grey-6">
                                            {{ $t('forms.labels.zipCode') }}
                                        </div>
                                        <div class="text-body2">
                                            {{ endereco.cep }}
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6">
                                        <div class="text-caption text-grey-6">
                                            {{
                                                $t(
                                                    'addressCollection.labels.cityState'
                                                )
                                            }}
                                        </div>
                                        <div class="text-body2">
                                            {{ getCidadeEstadoLabel(endereco) }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Botões de ação -->
                            <div
                                class="col-12 col-md-1 flex justify-center items-center"
                            >
                                <div class="row column-md q-gutter-xs">
                                    <q-btn
                                        @click="abrirModalEditar(index)"
                                        color="primary"
                                        icon="edit"
                                        flat
                                        round
                                        size="sm"
                                        :disable="readonly"
                                    >
                                        <q-tooltip>
                                            {{
                                                $t(
                                                    'addressCollection.labels.editAddress'
                                                )
                                            }}
                                        </q-tooltip>
                                    </q-btn>
                                    <q-btn
                                        @click="removerEndereco(index)"
                                        color="negative"
                                        icon="delete"
                                        flat
                                        round
                                        size="sm"
                                        :disable="readonly"
                                    >
                                        <q-tooltip>
                                            {{
                                                $t(
                                                    'addressCollection.labels.removeAddress'
                                                )
                                            }}
                                        </q-tooltip>
                                    </q-btn>
                                </div>
                            </div>
                        </div>
                    </q-card>
                </div>
            </q-card-section>
        </q-card>

        <!-- Modal do formulário de endereço -->
        <modal-template
            :model-value="modalAberto"
            :title="
                modoEdicao
                    ? $t('addressCollection.titles.editAddress')
                    : $t('addressCollection.titles.addAddress')
            "
            card-style="min-width: 320px; max-width: 900px; width: 95vw"
            @update:model-value="(value: boolean) => (modalAberto = value)"
            @close="fecharModal"
        >
            <!-- Conteúdo -->
            <form-endereco
                ref="formEnderecoRef"
                :initial-values="enderecoEdicao"
            />

            <!-- Ações -->
            <template #actions>
                <q-btn
                    flat
                    color="negative"
                    :label="$t('buttons.cancel')"
                    @click="fecharModal"
                />
                <q-btn
                    unelevated
                    color="positive"
                    :label="$t('buttons.confirm')"
                    @click="salvarEndereco"
                    :loading="salvando"
                />
            </template>
        </modal-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { Notify, Dialog } from 'quasar'

const { t: $t } = useI18n()

// Components
import ModalTemplate from '@/components/ModalTemplate.vue'
import FormEndereco from 'src/components/forms/FormEndereco.vue'

// Types
import type {
    EnderecoData,
    FormColecaoEnderecosProps,
    ValidationResult
} from '@/interfaces/FormEndereco'

// Services
import {
    getStatesByCountry,
    getCitiesByState,
    getCountryNameTranslated
} from '@/services/locationService'

// Props
const props = withDefaults(defineProps<FormColecaoEnderecosProps>(), {
    title: '',
    readonly: false,
    initialValues: () => []
})

// Estados
const enderecos = ref<EnderecoData[]>(props.initialValues || [])
const modalAberto = ref(false)
const modoEdicao = ref(false)
const indiceEdicao = ref(-1)
const enderecoEdicao = ref<Partial<EnderecoData>>({})
const salvando = ref(false)

// Referência do formulário
const formEnderecoRef = ref<InstanceType<typeof FormEndereco> | null>(null)

// Opções para os selects
const tiposEnderecoOptions = [
    { label: $t('forms.addressTypes.residential'), value: 'residential' },
    { label: $t('forms.addressTypes.commercial'), value: 'commercial' },
    { label: $t('forms.addressTypes.delivery'), value: 'delivery' },
    { label: $t('forms.addressTypes.billing'), value: 'billing' },
    { label: $t('forms.addressTypes.other'), value: 'other' }
]

// Obter locale atual para tradução
const { locale } = useI18n()

// Métodos para abrir/fechar modal
const abrirModalAdicionar = (): void => {
    modoEdicao.value = false
    indiceEdicao.value = -1
    enderecoEdicao.value = {}
    modalAberto.value = true
}

const abrirModalEditar = (index: number): void => {
    modoEdicao.value = true
    indiceEdicao.value = index
    enderecoEdicao.value = { ...enderecos.value[index] }
    modalAberto.value = true
}

const fecharModal = (): void => {
    modalAberto.value = false
    modoEdicao.value = false
    indiceEdicao.value = -1
    enderecoEdicao.value = {}
    salvando.value = false
}

// Método para salvar endereço
const salvarEndereco = async (): Promise<void> => {
    if (!formEnderecoRef.value) return

    salvando.value = true

    try {
        const validationResult = await formEnderecoRef.value.validate()

        if (!validationResult.valid) {
            Notify.create({
                message: $t('notifications.pleaseFixErrors'),
                color: 'negative',
                position: 'bottom',
                timeout: 3000,
                icon: 'error'
            })
            return
        }

        const dadosEndereco = formEnderecoRef.value.getData()

        if (modoEdicao.value) {
            // Editar endereço existente
            const enderecoExistente = enderecos.value[indiceEdicao.value]
            enderecos.value[indiceEdicao.value] = {
                ...dadosEndereco,
                id: enderecoExistente?.id || Date.now().toString()
            }

            Notify.create({
                message: $t('addressCollection.messages.addressUpdated'),
                color: 'positive',
                position: 'bottom',
                timeout: 2000,
                icon: 'check_circle'
            })
        } else {
            // Adicionar novo endereço
            const novoEndereco: EnderecoData = {
                ...dadosEndereco,
                id: Date.now().toString()
            }

            enderecos.value.push(novoEndereco)

            Notify.create({
                message: $t('addressCollection.messages.addressAdded'),
                color: 'positive',
                position: 'bottom',
                timeout: 2000,
                icon: 'check_circle'
            })
        }

        fecharModal()
    } catch {
        Notify.create({
            message: $t('addressCollection.messages.saveError'),
            color: 'negative',
            position: 'bottom',
            timeout: 3000,
            icon: 'error'
        })
    } finally {
        salvando.value = false
    }
}

// Método para remover endereço
const removerEndereco = (index: number): void => {
    if (index < 0 || index >= enderecos.value.length) {
        return
    }

    Dialog.create({
        title: $t('addressCollection.labels.removeAddress'),
        message: $t('addressCollection.messages.confirmRemoveAddress'),
        cancel: true,
        persistent: true
    }).onOk(() => {
        enderecos.value.splice(index, 1)

        Notify.create({
            message: $t('addressCollection.messages.addressRemoved'),
            color: 'positive',
            position: 'bottom',
            timeout: 2000,
            icon: 'check_circle'
        })
    })
}

// Métodos utilitários para formatação
const getTipoLabel = (tipo: string): string => {
    const tipoOption = tiposEnderecoOptions.find(
        option => option.value === tipo
    )
    return tipoOption?.label || tipo
}

const getPaisLabel = (pais: string): string => {
    // Usar tradução direta do locationService com locale atual
    const translatedName = getCountryNameTranslated(pais, locale.value)
    return translatedName || pais
}

const getCidadeEstadoLabel = (endereco: EnderecoData): string => {
    const estadosOptions = getStatesByCountry(endereco.pais)
    const cidadesOptions = getCitiesByState(endereco.pais, endereco.estado)

    const estadoLabel =
        estadosOptions.find(option => option.value === endereco.estado)
            ?.label || endereco.estado
    const cidadeLabel =
        cidadesOptions.find(option => option.value === endereco.cidade)
            ?.label || endereco.cidade

    return `${cidadeLabel}, ${estadoLabel}`
}

const formatarEnderecoCompleto = (endereco: EnderecoData): string => {
    let enderecoCompleto = `${endereco.logradouro}, ${endereco.numero}`

    if (endereco.complemento) {
        enderecoCompleto += `, ${endereco.complemento}`
    }

    enderecoCompleto += `, ${endereco.bairro}`

    return enderecoCompleto
}

// Validação completa da coleção
const validateCollection = (): ValidationResult => {
    const errors: string[] = []

    if (enderecos.value.length === 0) {
        errors.push($t('addressCollection.validation.minAddresses'))
    }

    return {
        valid: errors.length === 0,
        errors
    }
}

// Expor métodos e dados
defineExpose({
    validate: validateCollection,
    getData: () => enderecos.value,
    resetForm: () => {
        enderecos.value = []
    }
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
