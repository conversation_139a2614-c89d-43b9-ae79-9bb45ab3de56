<template>
    <div id="form-ncm" class="q-pa-xs">
        <form-template :title="$t('forms.titles.ncmForm')">
            <template #header>
                <div class="row q-col-gutter-xs">
                    <div class="col-auto">
                        <q-toggle
                            v-model="ativo"
                            :label="$t('forms.labels.active')"
                            color="secondary"
                            class="no-bottom-spacing text-white"
                            dense
                        />
                    </div>
                </div>
            </template>

            <template #body>
                <div class="row q-col-gutter-xs">
                    <!-- Informações Básicas -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.basicInfo') }}
                        </div>
                        <q-card flat bordered class="q-mb-md">
                            <q-card-section class="q-pa-xs">
                                <div class="row q-col-gutter-xs">
                                    <!-- Código -->
                                    <div class="col-12 col-md-6">
                                        <q-input
                                            v-model="codigo"
                                            :label="$t('forms.labels.code')"
                                            outlined
                                            dense
                                            stack-label
                                            :error="!!errors.codigo"
                                            :error-message="errors.codigo"
                                            mask="####.##.##"
                                            fill-mask
                                            placeholder="0000.00.00"
                                            v-required-field
                                        />
                                    </div>

                                    <!-- Descrição -->
                                    <div class="col-12 col-md-6">
                                        <q-input
                                            v-model="descricao"
                                            :label="
                                                $t('forms.labels.description')
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            :error="!!errors.descricao"
                                            :error-message="errors.descricao"
                                            @update:model-value="
                                                onDescricaoInput
                                            "
                                            v-required-field
                                        />
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Vue
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

// Validation
import * as yup from 'yup'
import { useForm } from 'vee-validate'

// Components
import FormTemplate from '@/components/FormTemplate.vue'

// Internationalization
const { t: $t } = useI18n()

// Props
const props = defineProps({
    initialValues: {
        type: Object,
        default: () => ({})
    }
})

// Helper function para formatar código NCM
const formatNcmCode = (code: string): string => {
    if (!code) return ''
    // Remove qualquer formatação existente
    const cleanCode = code.replace(/\D/g, '')
    // Aplica a máscara se tiver 8 dígitos
    if (cleanCode.length === 8) {
        return `${cleanCode.slice(0, 4)}.${cleanCode.slice(4, 6)}.${cleanCode.slice(6, 8)}`
    }
    return cleanCode
}

// Schema de validação
const ncmSchema = yup.object({
    codigo: yup
        .string()
        .required($t('validate.requiredCode'))
        .test('ncm-format', $t('validate.ncmCodeFormat'), value => {
            if (!value) return false
            // Remove pontos e verifica se tem exatamente 8 dígitos
            const cleanCode = value.replace(/\./g, '')
            return /^\d{8}$/.test(cleanCode)
        }),
    descricao: yup
        .string()
        .required($t('validate.requiredDescription'))
        .max(500, $t('validate.maxLength', { max: 500 }))
})

// Inicializa o formulário
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: ncmSchema,
    initialValues: {
        codigo: formatNcmCode(props.initialValues.codigo || ''),
        descricao: props.initialValues.descricao || ''
    },
    validateOnMount: false
})

// Define fields
const [codigo] = defineField('codigo')
const [descricao] = defineField('descricao')

// Campo adicional não incluído na validação
const ativo = ref(
    props.initialValues.ativo !== undefined ? props.initialValues.ativo : true
)

// Função para formatar o código NCM à medida que o usuário digita
const onDescricaoInput = (value: string | number | null): void => {
    descricao.value = value ? value.toString().toUpperCase() : ''
}

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: () => {
        return {
            ...values,
            // Remove os pontos da máscara do código NCM antes de enviar
            codigo: values.codigo ? values.codigo.replace(/\./g, '') : '',
            ativo: ativo.value
        }
    },
    resetForm: () => {
        resetForm()
        ativo.value = true
    }
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
