<template>
    <div id="form-estado" class="q-pa-xs">
        <form-template :title="$t('pages.states.form.title')">
            <template #body>
                <div class="row q-col-gutter-xs">
                    <!-- Informações Básicas -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.basicInfo') }}
                        </div>
                        <q-card flat bordered class="q-mb-md">
                            <q-card-section class="q-pa-xs">
                                <div class="row q-col-gutter-xs">
                                    <!-- Descrição -->
                                    <div class="col-12 col-md-6">
                                        <q-input
                                            v-model="descricao"
                                            :label="
                                                $t('forms.labels.description')
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            :error="!!errors.descricao"
                                            :error-message="errors.descricao"
                                            maxlength="100"
                                            v-required-field
                                            @update:model-value="
                                                onDescricaoInput
                                            "
                                        />
                                    </div>

                                    <!-- Sigla -->
                                    <div class="col-12 col-md-3">
                                        <q-input
                                            v-model="sigla"
                                            :label="
                                                $t(
                                                    'forms.labels.stateAbbreviation'
                                                )
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            :error="!!errors.sigla"
                                            :error-message="errors.sigla"
                                            maxlength="2"
                                            @update:model-value="onSiglaInput"
                                        />
                                    </div>

                                    <!-- IBGE -->
                                    <div class="col-12 col-md-3">
                                        <q-input
                                            v-model="ibge"
                                            :label="$t('forms.labels.ibgeCode')"
                                            outlined
                                            dense
                                            stack-label
                                            :error="!!errors.ibge"
                                            :error-message="errors.ibge"
                                            maxlength="10"
                                            v-required-field
                                        />
                                    </div>

                                    <!-- País -->
                                    <div class="col-12 col-md-6">
                                        <q-select
                                            v-model="id_pais"
                                            :label="$t('forms.labels.country')"
                                            use-input
                                            input-debounce="500"
                                            :options="countryOptions"
                                            option-label="descricao"
                                            option-value="id"
                                            emit-value
                                            map-options
                                            outlined
                                            dense
                                            clearable
                                            @filter="onCountryFilter"
                                            :loading="loadingCountries"
                                            use-chips
                                            behavior="menu"
                                            class="form-field"
                                            :error="!!errors.id_pais"
                                            :error-message="errors.id_pais"
                                            v-required-field
                                        >
                                            <template #prepend>
                                                <q-icon
                                                    name="public"
                                                    color="primary"
                                                />
                                            </template>

                                            <template #option="scope">
                                                <q-item
                                                    v-bind="scope.itemProps"
                                                >
                                                    <q-item-section>
                                                        <q-item-label>{{
                                                            scope.opt.descricao
                                                        }}</q-item-label>
                                                    </q-item-section>
                                                </q-item>
                                            </template>

                                            <template #after-options>
                                                <div
                                                    v-if="
                                                        hasMoreCountries &&
                                                        !loadingCountries
                                                    "
                                                    class="q-pa-sm"
                                                >
                                                    <q-btn
                                                        flat
                                                        dense
                                                        color="primary"
                                                        :loading="
                                                            isFetchingCountries
                                                        "
                                                        @click="
                                                            loadMoreCountries
                                                        "
                                                        class="full-width"
                                                        icon="expand_more"
                                                        :label="
                                                            $t(
                                                                'buttons.loadMore'
                                                            )
                                                        "
                                                    />
                                                </div>
                                                <div
                                                    v-if="
                                                        loadingCountries &&
                                                        countryOptions.length ===
                                                            0
                                                    "
                                                    class="q-pa-sm text-center"
                                                >
                                                    <q-spinner
                                                        color="primary"
                                                        size="24px"
                                                    />
                                                    <div
                                                        class="text-caption q-mt-xs"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.labels.loading'
                                                            )
                                                        }}
                                                    </div>
                                                </div>
                                            </template>

                                            <template #no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            loadingCountries
                                                                ? $t(
                                                                      'forms.labels.loading'
                                                                  )
                                                                : currentCountryFilter
                                                                  ? $t(
                                                                        'forms.labels.noResults'
                                                                    )
                                                                  : $t(
                                                                        'forms.labels.typeToSearch'
                                                                    )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Vue
import { ref, watch, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'

// Validation
import * as yup from 'yup'
import { useForm } from 'vee-validate'

// Components
import FormTemplate from '@/components/FormTemplate.vue'

// Store
import { useAuthStore } from '@/stores/auth'

// Internationalization
const { t: $t } = useI18n()
const authStore = useAuthStore()

// Types
interface Country {
    id: number
    descricao: string
}

// Props
const props = defineProps({
    initialValues: {
        type: Object,
        default: () => ({})
    }
})

// Schema de validação
const estadoSchema = yup.object({
    descricao: yup
        .string()
        .required($t('validate.requiredDescription'))
        .max(100, $t('validate.maxLength', { max: 100 })),
    sigla: yup.string().max(2, $t('validate.maxLength', { max: 2 })),
    ibge: yup
        .string()
        .required($t('validate.requiredField'))
        .max(10, $t('validate.maxLength', { max: 10 })),
    id_pais: yup.number().required($t('validate.requiredCountry'))
})

// Inicializa o formulário
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: estadoSchema,
    initialValues: {
        descricao: props.initialValues.descricao || '',
        sigla: props.initialValues.sigla || '',
        ibge: props.initialValues.ibge || '',
        id_pais: props.initialValues.id_pais || undefined
    },
    validateOnMount: false
})

// Define fields
const [descricao] = defineField('descricao')
const [sigla] = defineField('sigla')
const [ibge] = defineField('ibge')
const [id_pais] = defineField('id_pais')

// Country select states
const countryOptions = ref<Country[]>([])
const loadingCountries = ref(false)
const isFetchingCountries = ref(false)
const currentCountryFilter = ref('')

// Paginação para países
const countryPagination = ref({
    page: 1,
    pageSize: 15,
    total: 0,
    hasMore: true
})

// Computed para verificar se há mais países
const hasMoreCountries = computed(() => {
    return (
        countryPagination.value.hasMore &&
        countryOptions.value.length < countryPagination.value.total
    )
})

// Load countries with pagination
const loadCountries = async (reset = true) => {
    if (isFetchingCountries.value) return

    isFetchingCountries.value = true
    loadingCountries.value = true

    try {
        const currentPage = reset ? 1 : countryPagination.value.page + 1
        const params = {
            page: currentPage,
            page_size: countryPagination.value.pageSize,
            ...(currentCountryFilter.value && {
                descricao: currentCountryFilter.value
            })
        }

        const response = await authStore.asyncRequest({
            endpoint: '/api/corpsystem/produto-service/pais',
            method: 'get',
            params,
            customErrorMessages: {
                400: $t('forms.errors.loadCountriesError'),
                404: $t('forms.errors.loadCountriesError'),
                500: $t('forms.errors.loadCountriesError')
            }
        })

        const data = response.data

        if (reset) {
            countryOptions.value = data.results
            countryPagination.value.page = 1
        } else {
            countryOptions.value = [...countryOptions.value, ...data.results]
            countryPagination.value.page = currentPage
        }

        // Atualizar informações de paginação
        countryPagination.value.total = data.count
        countryPagination.value.hasMore = !!data.next
    } catch {
        if (reset) {
            countryOptions.value = []
        }
        // Error is already handled by the authStore.asyncRequest
    } finally {
        isFetchingCountries.value = false
        loadingCountries.value = false
    }
}

// Country filter function
const onCountryFilter = (val: string, update: (fn: () => void) => void) => {
    currentCountryFilter.value = val
    update(() => {
        countryPagination.value.page = 1
        countryPagination.value.hasMore = true
        loadCountries(true)
    })
}

// Function to load more countries (load more button)
const loadMoreCountries = async () => {
    if (!isFetchingCountries.value && hasMoreCountries.value) {
        await loadCountries(false) // false = não resetar, carregar próxima página
    }
}

// Watch para mudanças nos valores iniciais
watch(
    () => props.initialValues,
    newValues => {
        if (newValues && Object.keys(newValues).length > 0) {
            resetForm({
                values: {
                    descricao: newValues.descricao || '',
                    sigla: newValues.sigla || '',
                    ibge: newValues.ibge || '',
                    id_pais: newValues.id_pais || undefined
                }
            })
        }
    },
    { deep: true, immediate: true }
)

const onDescricaoInput = (value: string | number | null): void => {
    descricao.value = value ? value.toString().toUpperCase() : ''
}

const onSiglaInput = (value: string | number | null): void => {
    sigla.value = value ? value.toString().toUpperCase() : ''
}

// Load countries on mount
onMounted(() => {
    loadCountries()
})

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: () => {
        return {
            ...values
        }
    },
    resetForm: () => {
        resetForm()
    }
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
