<template>
    <div id="form-categoria-cnh" class="q-pa-xs">
        <form-template :title="$t('forms.titles.categoriaCNHForm')">
            <template #header>
                <div class="row q-col-gutter-xs">
                    <div class="col-auto">
                        <q-toggle
                            v-model="ativo"
                            :label="$t('forms.labels.active')"
                            color="secondary"
                            class="no-bottom-spacing text-white"
                            dense
                        />
                    </div>
                </div>
            </template>

            <template #body>
                <div class="form-container">
                    <!-- Se<PERSON> de Informações Básicas -->
                    <div class="form-section">
                        <div class="section-header">
                            <q-icon name="info" class="section-icon" />
                            <span class="section-title">{{
                                $t('forms.titles.basicInfo')
                            }}</span>
                        </div>

                        <q-card flat bordered class="section-card">
                            <q-card-section class="q-pa-md">
                                <div class="row q-col-gutter-md">
                                    <!-- Código -->
                                    <div class="col-12 col-md-6">
                                        <q-input
                                            v-model="codigo"
                                            :label="$t('forms.labels.code')"
                                            outlined
                                            dense
                                            stack-label
                                            v-required-field
                                            :error="!!errors.codigo"
                                            :error-message="errors.codigo"
                                            @update:model-value="onCodigoInput"
                                            maxlength="10"
                                            class="form-field"
                                        >
                                            <template #prepend>
                                                <q-icon
                                                    name="tag"
                                                    color="primary"
                                                />
                                            </template>
                                        </q-input>
                                    </div>

                                    <!-- Descrição -->
                                    <div class="col-12 col-md-6">
                                        <q-input
                                            v-model="descricao"
                                            :label="
                                                $t('forms.labels.description')
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            v-required-field
                                            :error="!!errors.descricao"
                                            :error-message="errors.descricao"
                                            @update:model-value="
                                                onDescricaoInput
                                            "
                                            maxlength="100"
                                            class="form-field"
                                        >
                                            <template #prepend>
                                                <q-icon
                                                    name="description"
                                                    color="primary"
                                                />
                                            </template>
                                        </q-input>
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>

                    <!-- Seção de Anexo -->
                    <div class="form-section">
                        <div class="section-header">
                            <q-icon name="attach_file" class="section-icon" />
                            <span class="section-title">{{
                                $t('forms.labels.attachment')
                            }}</span>
                            <q-chip
                                size="sm"
                                color="grey-4"
                                text-color="grey-8"
                                class="section-chip"
                            >
                                {{ $t('forms.hints.attachment') }}
                            </q-chip>
                        </div>

                        <q-card
                            flat
                            bordered
                            class="section-card attachment-card"
                        >
                            <q-card-section class="q-pa-md">
                                <div class="attachment-container">
                                    <!-- Upload Area -->
                                    <div class="upload-area">
                                        <q-file
                                            v-model="anexo"
                                            :label="
                                                $t('forms.labels.selectFile')
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            accept=".jpg,.jpeg,.png,.pdf,.pfx,.txt,.crt,.ret,.doc,.docx,.xls,.xlsx,.msg"
                                            max-file-size="2097152"
                                            @rejected="onFileRejected"
                                            class="upload-field"
                                        >
                                            <template #prepend>
                                                <q-icon
                                                    name="cloud_upload"
                                                    color="primary"
                                                />
                                            </template>
                                            <template #append>
                                                <q-icon name="attach_file" />
                                            </template>
                                        </q-file>
                                    </div>

                                    <!-- Preview Area -->
                                    <div class="preview-area">
                                        <image-preview
                                            :file="anexo"
                                            :existing-url="
                                                existingAttachmentUrl
                                            "
                                            :existing-base64="
                                                existingAttachmentBase64
                                            "
                                            :file-name="existingAttachmentName"
                                            @view="viewAttachment"
                                            @remove="removeAttachment"
                                        />
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>
                </div>
            </template>
        </form-template>

        <!-- Image Viewer -->
        <image-viewer
            v-model="showImageViewer"
            :image-url="currentImageUrl"
            :title="currentImageTitle"
        />
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'
import { Notify } from 'quasar'

const { t: $t } = useI18n()

// Components
import FormTemplate from '@/components/FormTemplate.vue'
import ImagePreview from '@/components/ImagePreview.vue'
import ImageViewer from '@/components/ImageViewer.vue'

// Types
interface FileRejection {
    failedPropValidation: string
}

// Props
const props = defineProps({
    initialValues: {
        type: Object,
        default: () => ({})
    }
})

// Schema de validação
const categoriaCNHSchema = yup.object({
    codigo: yup.string().required($t('validate.requiredCode')),
    descricao: yup.string().required($t('validate.requiredDescription'))
})

// Inicializa o formulário
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: categoriaCNHSchema,
    initialValues: {
        codigo: props.initialValues.codigo || '',
        descricao: props.initialValues.descricao || ''
    },
    validateOnMount: false
})

// Define fields
const [codigo] = defineField('codigo')
const [descricao] = defineField('descricao')

// Função para extrair nome do arquivo da URL
const extractFileNameFromUrl = (url: string): string => {
    try {
        const urlParts = url.split('/')
        const fileName = urlParts[urlParts.length - 1]
        // Remover query parameters se houver
        return fileName?.split('?')[0] || 'arquivo'
    } catch {
        return 'arquivo'
    }
}

// Campos adicionais não incluídos na validação
const ativo = ref(
    props.initialValues.ativo !== undefined ? props.initialValues.ativo : true
)

// Separar arquivo novo de URL existente
const anexo = ref<File | null>(
    props.initialValues.anexo instanceof File ? props.initialValues.anexo : null
)

// Variáveis para preview de anexo existente
const existingAttachmentUrl = ref<string | null>(null)
const existingAttachmentBase64 = ref<string | null>(null)
const existingAttachmentName = ref<string | null>(null)

// Inicializar dados do anexo existente
if (props.initialValues.anexo_base64) {
    // Usar base64 se disponível
    existingAttachmentBase64.value = props.initialValues.anexo_base64
    existingAttachmentName.value = props.initialValues.anexo
        ? extractFileNameFromUrl(props.initialValues.anexo)
        : 'anexo'
} else if (typeof props.initialValues.anexo === 'string') {
    // Fallback para URL se base64 não estiver disponível
    existingAttachmentUrl.value = props.initialValues.anexo
    existingAttachmentName.value = extractFileNameFromUrl(
        props.initialValues.anexo
    )
}

// Image Viewer
const showImageViewer = ref(false)
const currentImageUrl = ref('')
const currentImageTitle = ref('')

// Métodos
// Função para converter código para uppercase
const onCodigoInput = (value: string | number | null): void => {
    codigo.value = value ? value.toString().toUpperCase() : ''
}

// Função para converter descrição para uppercase
const onDescricaoInput = (value: string | number | null): void => {
    descricao.value = value ? value.toString().toUpperCase() : ''
}

// Função para tratar rejeição de arquivo
const onFileRejected = (rejectedEntries: FileRejection[]) => {
    const rejection = rejectedEntries[0]

    if (!rejection) return

    let errorMessage = ''

    if (rejection.failedPropValidation === 'max-file-size') {
        errorMessage = $t('forms.errors.maxFileSize')
    } else if (rejection.failedPropValidation === 'accept') {
        errorMessage = $t('forms.errors.invalidFileType')
    }

    Notify.create({
        message: errorMessage,
        color: 'negative',
        position: 'top',
        timeout: 3000,
        icon: 'error'
    })
}

// Métodos para preview de anexo
const viewAttachment = () => {
    if (anexo.value) {
        currentImageUrl.value = URL.createObjectURL(anexo.value)
        currentImageTitle.value = anexo.value.name
    } else if (existingAttachmentBase64.value) {
        currentImageUrl.value = existingAttachmentBase64.value
        currentImageTitle.value = existingAttachmentName.value || 'Anexo'
    } else if (existingAttachmentUrl.value) {
        currentImageUrl.value = existingAttachmentUrl.value
        currentImageTitle.value = existingAttachmentName.value || 'Anexo'
    }
    showImageViewer.value = true
}

const removeAttachment = () => {
    anexo.value = null
    existingAttachmentUrl.value = null
    existingAttachmentBase64.value = null
    existingAttachmentName.value = null
}

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: () => {
        return {
            ...values,
            ativo: ativo.value,
            anexo: anexo.value
        }
    },
    resetForm: () => {
        resetForm()
        ativo.value = true
        anexo.value = null
        existingAttachmentUrl.value = null
        existingAttachmentBase64.value = null
        existingAttachmentName.value = null
    }
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}

/* Layout do formulário */
.form-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-section {
    width: 100%;
}

/* Cabeçalho das seções */
.section-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    padding: 8px 0;
}

.section-icon {
    font-size: 20px;
    color: var(--q-primary);
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--q-dark);
    flex: 1;
}

.section-chip {
    margin-left: auto;
    font-size: 11px;
}

/* Cards das seções */
.section-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.section-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Campos do formulário */
.form-field {
    transition: all 0.3s ease;
}

.form-field:hover {
    transform: translateY(-1px);
}

/* Seção de anexo */
.attachment-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.attachment-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.upload-area {
    width: 100%;
}

.upload-field {
    border: 2px dashed var(--q-primary);
    border-radius: 8px;
    padding: 16px;
    background: rgba(var(--q-primary-rgb), 0.05);
    transition: all 0.3s ease;
}

.upload-field:hover {
    background: rgba(var(--q-primary-rgb), 0.1);
    border-color: var(--q-secondary);
}

.preview-area {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    min-height: 60px;
}

/* Responsividade */
@media (max-width: 768px) {
    .form-container {
        gap: 16px;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .section-chip {
        margin-left: 0;
        align-self: flex-start;
    }

    .attachment-container {
        gap: 16px;
    }
}

/* Animações */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-section {
    animation: fadeInUp 0.5s ease-out;
}

.form-section:nth-child(2) {
    animation-delay: 0.1s;
}

/* Estados dos campos */
.form-field .q-field--error {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%,
    100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

/* Melhorias visuais */
.section-card .q-card__section {
    position: relative;
}

.section-card .q-card__section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--q-primary), var(--q-secondary));
    border-radius: 12px 12px 0 0;
}
</style>
