<template>
    <div id="form-pais" class="q-pa-xs">
        <form-template :title="$t('pages.countries.form.title')">
            <template #body>
                <div class="row q-col-gutter-xs">
                    <!-- Informações Básicas -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.basicInfo') }}
                        </div>
                        <q-card flat bordered class="q-mb-md">
                            <q-card-section class="q-pa-xs">
                                <div class="row q-col-gutter-xs">
                                    <!-- Descrição -->
                                    <div class="col-12 col-md-6">
                                        <q-input
                                            v-model="descricao"
                                            :label="
                                                $t('forms.labels.description')
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            :error="!!errors.descricao"
                                            :error-message="errors.descricao"
                                            maxlength="100"
                                            v-required-field
                                            @update:model-value="
                                                onDescricaoInput
                                            "
                                        />
                                    </div>

                                    <!-- Sigla -->
                                    <div class="col-12 col-md-3">
                                        <q-input
                                            v-model="sigla"
                                            :label="
                                                $t(
                                                    'forms.labels.countryAbbreviation'
                                                )
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            :error="!!errors.sigla"
                                            :error-message="errors.sigla"
                                            maxlength="3"
                                            @update:model-value="onSiglaInput"
                                        />
                                    </div>

                                    <!-- Código -->
                                    <div class="col-12 col-md-3">
                                        <q-input
                                            v-model="cod_pais"
                                            :label="
                                                $t('forms.labels.countryCode')
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            :error="!!errors.cod_pais"
                                            :error-message="errors.cod_pais"
                                            maxlength="10"
                                            v-required-field
                                        />
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Vue
import { watch } from 'vue'
import { useI18n } from 'vue-i18n'

// Validation
import * as yup from 'yup'
import { useForm } from 'vee-validate'

// Components
import FormTemplate from '@/components/FormTemplate.vue'

// Internationalization
const { t: $t } = useI18n()

// Props
const props = defineProps({
    initialValues: {
        type: Object,
        default: () => ({})
    }
})

// Schema de validação
const paisSchema = yup.object({
    descricao: yup
        .string()
        .required($t('validate.requiredDescription'))
        .max(100, $t('validate.maxLength', { max: 100 })),
    sigla: yup.string().max(3, $t('validate.maxLength', { max: 3 })),
    cod_pais: yup
        .string()
        .required($t('validate.requiredField'))
        .max(10, $t('validate.maxLength', { max: 10 }))
})

// Inicializa o formulário
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: paisSchema,
    initialValues: {
        descricao: props.initialValues.descricao || '',
        sigla: props.initialValues.sigla || '',
        cod_pais: props.initialValues.cod_pais || ''
    },
    validateOnMount: false
})

// Define fields
const [descricao] = defineField('descricao')
const [sigla] = defineField('sigla')
const [cod_pais] = defineField('cod_pais')

// Watch para mudanças nos valores iniciais
watch(
    () => props.initialValues,
    newValues => {
        if (newValues && Object.keys(newValues).length > 0) {
            resetForm({
                values: {
                    descricao: newValues.descricao || '',
                    sigla: newValues.sigla || '',
                    cod_pais: newValues.cod_pais || ''
                }
            })
        }
    },
    { deep: true, immediate: true }
)

const onDescricaoInput = (value: string | number | null): void => {
    descricao.value = value ? value.toString().toUpperCase() : ''
}

const onSiglaInput = (value: string | number | null): void => {
    sigla.value = value ? value.toString().toUpperCase() : ''
}

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: () => {
        return {
            ...values
        }
    },
    resetForm: () => {
        resetForm()
    }
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
