<template>
    <div id="form-cidade" class="q-pa-xs">
        <form-template :title="$t('pages.cities.form.title')">
            <template #body>
                <div class="row q-col-gutter-xs">
                    <!-- Informações Básicas -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.basicInfo') }}
                        </div>
                        <q-card flat bordered class="q-mb-md">
                            <q-card-section class="q-pa-xs">
                                <div class="row q-col-gutter-xs">
                                    <!-- Descrição -->
                                    <div class="col-12 col-md-6">
                                        <q-input
                                            v-model="descricao"
                                            :label="
                                                $t('forms.labels.description')
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            :error="!!errors.descricao"
                                            :error-message="errors.descricao"
                                            maxlength="100"
                                            v-required-field
                                            @update:model-value="
                                                onDescricaoInput
                                            "
                                        />
                                    </div>

                                    <!-- ISS -->
                                    <div class="col-12 col-md-3">
                                        <q-input
                                            v-model="aliq_iss"
                                            :label="$t('forms.labels.iss')"
                                            outlined
                                            dense
                                            stack-label
                                            mask="#.##"
                                            fill-mask="0"
                                            reverse-fill-mask
                                            :error="!!errors.aliq_iss"
                                            :error-message="errors.aliq_iss"
                                            maxlength="5"
                                        />
                                    </div>

                                    <!-- IBGE -->
                                    <div class="col-12 col-md-3">
                                        <q-input
                                            v-model="ibge"
                                            :label="$t('forms.labels.ibgeCode')"
                                            outlined
                                            dense
                                            stack-label
                                            :error="!!errors.ibge"
                                            :error-message="errors.ibge"
                                            maxlength="10"
                                            v-required-field
                                        />
                                    </div>

                                    <!-- Estado -->
                                    <div class="col-12 col-md-6">
                                        <q-select
                                            v-model="id_estado"
                                            :label="$t('forms.labels.state')"
                                            use-input
                                            input-debounce="500"
                                            :options="stateOptions"
                                            option-label="descricao"
                                            option-value="id"
                                            emit-value
                                            map-options
                                            outlined
                                            dense
                                            clearable
                                            @filter="onStateFilter"
                                            :loading="loadingStates"
                                            use-chips
                                            behavior="menu"
                                            class="form-field"
                                            :error="!!errors.id_estado"
                                            :error-message="errors.id_estado"
                                            v-required-field
                                        >
                                            <template #prepend>
                                                <q-icon
                                                    name="map"
                                                    color="primary"
                                                />
                                            </template>

                                            <template #option="scope">
                                                <q-item
                                                    v-bind="scope.itemProps"
                                                >
                                                    <q-item-section>
                                                        <q-item-label>{{
                                                            scope.opt.descricao
                                                        }}</q-item-label>
                                                    </q-item-section>
                                                </q-item>
                                            </template>

                                            <template #after-options>
                                                <div
                                                    v-if="
                                                        hasMoreStates &&
                                                        !loadingStates
                                                    "
                                                    class="q-pa-sm"
                                                >
                                                    <q-btn
                                                        flat
                                                        dense
                                                        color="primary"
                                                        :loading="
                                                            isFetchingStates
                                                        "
                                                        @click="loadMoreStates"
                                                        class="full-width"
                                                        icon="expand_more"
                                                        :label="
                                                            $t(
                                                                'buttons.loadMore'
                                                            )
                                                        "
                                                    />
                                                </div>
                                                <div
                                                    v-if="
                                                        loadingStates &&
                                                        stateOptions.length ===
                                                            0
                                                    "
                                                    class="q-pa-sm text-center"
                                                >
                                                    <q-spinner
                                                        color="primary"
                                                        size="24px"
                                                    />
                                                    <div
                                                        class="text-caption q-mt-xs"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.labels.loading'
                                                            )
                                                        }}
                                                    </div>
                                                </div>
                                            </template>

                                            <template #no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            loadingStates
                                                                ? $t(
                                                                      'forms.labels.loading'
                                                                  )
                                                                : currentStateFilter
                                                                  ? $t(
                                                                        'forms.labels.noResults'
                                                                    )
                                                                  : $t(
                                                                        'forms.labels.typeToSearch'
                                                                    )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Vue
import { ref, watch, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'

// Validation
import * as yup from 'yup'
import { useForm } from 'vee-validate'

// Components
import FormTemplate from '@/components/FormTemplate.vue'

// Stores
import { useAuthStore } from '@/stores/auth'

// Internationalization
const { t: $t } = useI18n()
const authStore = useAuthStore()

// Types
interface Estado {
    id: number
    descricao: string
}

// Props
const props = defineProps({
    initialValues: {
        type: Object,
        default: () => ({})
    }
})

// Form Schema
const cidadeSchema = yup.object({
    descricao: yup
        .string()
        .required($t('validate.requiredDescription'))
        .max(100, $t('validate.maxLength', { max: 100 })),
    aliq_iss: yup.string().max(5, $t('validate.maxLength', { max: 5 })),
    ibge: yup
        .string()
        .required($t('validate.fieldRequired', { field: 'IBGE' }))
        .max(10, $t('validate.maxLength', { max: 10 })),
    id_estado: yup.number().required($t('validate.requiredState'))
})

// Inicializa o formulário
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: cidadeSchema,
    initialValues: {
        descricao: props.initialValues.descricao || '',
        aliq_iss: props.initialValues.aliq_iss || '',
        ibge: props.initialValues.ibge || '',
        id_estado: props.initialValues.id_estado || undefined
    },
    validateOnMount: false
})

// Define fields
const [descricao] = defineField('descricao')
const [aliq_iss] = defineField('aliq_iss')
const [ibge] = defineField('ibge')
const [id_estado] = defineField('id_estado')

// State select states
const stateOptions = ref<Estado[]>([])
const loadingStates = ref(false)
const isFetchingStates = ref(false)
const currentStateFilter = ref('')

// Paginação para estados
const statePagination = ref({
    page: 1,
    pageSize: 15,
    total: 0,
    hasMore: true
})

// Computed para verificar se há mais estados
const hasMoreStates = computed(() => {
    return (
        statePagination.value.hasMore &&
        stateOptions.value.length < statePagination.value.total
    )
})

// Load states with pagination
const loadStates = async (reset = true) => {
    if (isFetchingStates.value) return

    isFetchingStates.value = true
    loadingStates.value = true

    try {
        const currentPage = reset ? 1 : statePagination.value.page + 1
        const params = {
            page: currentPage,
            page_size: statePagination.value.pageSize,
            ...(currentStateFilter.value && {
                descricao: currentStateFilter.value
            })
        }

        const response = await authStore.asyncRequest({
            endpoint: '/api/corpsystem/produto-service/estado',
            method: 'get',
            params,
            customErrorMessages: {
                400: $t('forms.errors.loadStatesError'),
                404: $t('forms.errors.loadStatesError'),
                500: $t('forms.errors.loadStatesError')
            }
        })

        const data = response.data

        if (reset) {
            stateOptions.value = data.results
            statePagination.value.page = 1
        } else {
            stateOptions.value = [...stateOptions.value, ...data.results]
            statePagination.value.page = currentPage
        }

        // Atualizar informações de paginação
        statePagination.value.total = data.count
        statePagination.value.hasMore = !!data.next
    } catch {
        if (reset) {
            stateOptions.value = []
        }
        // Error is already handled by the authStore.asyncRequest
    } finally {
        isFetchingStates.value = false
        loadingStates.value = false
    }
}

// State filter function
const onStateFilter = (val: string, update: (fn: () => void) => void) => {
    currentStateFilter.value = val
    update(() => {
        statePagination.value.page = 1
        statePagination.value.hasMore = true
        loadStates(true)
    })
}

// Function to load more states (load more button)
const loadMoreStates = async () => {
    if (!isFetchingStates.value && hasMoreStates.value) {
        await loadStates(false) // false = não resetar, carregar próxima página
    }
}

// Watch para mudanças nos valores iniciais
watch(
    () => props.initialValues,
    newValues => {
        if (newValues && Object.keys(newValues).length > 0) {
            resetForm({
                values: {
                    descricao: newValues.descricao || '',
                    aliq_iss: newValues.aliq_iss || '',
                    ibge: newValues.ibge || '',
                    id_estado: newValues.id_estado || undefined
                }
            })
        }
    },
    { deep: true, immediate: true }
)

const onDescricaoInput = (value: string | number | null): void => {
    descricao.value = value ? value.toString().toUpperCase() : ''
}

// Load states on mount
onMounted(() => {
    loadStates()
})

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: () => {
        return {
            ...values
        }
    },
    resetForm: () => {
        resetForm()
    }
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
