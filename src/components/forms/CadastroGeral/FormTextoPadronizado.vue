<template>
    <div id="form-texto-padronizado" class="q-pa-xs">
        <form-template :title="$t('forms.titles.standardizedTextForm')">
            <template #header>
                <div class="row q-col-gutter-xs">
                    <div class="col-auto">
                        <q-toggle
                            v-model="ativo"
                            :label="$t('forms.labels.active')"
                            color="secondary"
                            class="no-bottom-spacing text-white"
                            dense
                        />
                    </div>
                </div>
            </template>

            <template #body>
                <div class="row q-col-gutter-xs">
                    <!-- Informações Básicas -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.basicInfo') }}
                        </div>
                        <q-card flat bordered class="q-mb-md">
                            <q-card-section class="q-pa-xs">
                                <div class="row q-col-gutter-xs">
                                    <!-- Descrição -->
                                    <div class="col-12 col-md-6">
                                        <q-input
                                            v-model="descricao"
                                            :label="
                                                $t('forms.labels.description')
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            v-required-field
                                            :error="!!errors.descricao"
                                            :error-message="errors.descricao"
                                            @update:model-value="
                                                onDescricaoInput
                                            "
                                        />
                                    </div>

                                    <!-- Tipo -->
                                    <div class="col-12 col-md-6">
                                        <q-select
                                            v-model="tipo"
                                            :label="$t('forms.labels.type')"
                                            :options="tiposOptions"
                                            outlined
                                            dense
                                            stack-label
                                            v-required-field
                                            emit-value
                                            map-options
                                            options-dense
                                            use-input
                                            input-debounce="300"
                                            @filter="filterTipos"
                                            :loading="loadingTipos"
                                            :error="!!errors.tipo"
                                            :error-message="errors.tipo"
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.labels.noResults'
                                                            ) ||
                                                            'Nenhum resultado encontrado'
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>

                    <!-- Conteúdo -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.content') }}
                        </div>
                        <q-card flat bordered class="q-mb-md">
                            <q-card-section class="q-pa-xs">
                                <div class="row q-col-gutter-xs">
                                    <div class="col-12">
                                        <div class="text-body2 q-mb-xs">
                                            {{ $t('forms.labels.content') }}
                                            <span
                                                class="required-asterisk"
                                                style="color: red"
                                            >
                                                *</span
                                            >
                                        </div>
                                        <quill-rich-text-editor
                                            v-model="conteudo"
                                            :placeholder="
                                                $t(
                                                    'forms.placeholders.standardizedTextContent'
                                                )
                                            "
                                            :max-length="5000"
                                            :show-char-count="true"
                                            min-height="200px"
                                            max-height="400px"
                                        />
                                        <div
                                            v-if="!!errors.conteudo"
                                            class="text-negative text-caption q-mt-xs"
                                        >
                                            {{ errors.conteudo }}
                                        </div>
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref, onMounted } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'

const { t: $t } = useI18n()

// Components
import FormTemplate from '@/components/FormTemplate.vue'

// Composables
import { useTextoPadronizado } from '@/composables/useTextoPadronizado'

// Components
import QuillRichTextEditor from '@/components/QuillRichTextEditor.vue'

// Props
const props = defineProps({
    initialValues: {
        type: Object,
        default: () => ({})
    }
})

// Usar o composable para tipos de texto padronizado
const {
    loading: loadingTipos,
    tiposOptions,
    filterTipos: filterTiposComposable,
    loadTipos: loadTiposComposable
} = useTextoPadronizado()

// Função para extrair conteúdo de dentro da div (para edição)
const extractContentFromDiv = (content: string): string => {
    if (!content || content.trim() === '') {
        return ''
    }

    const trimmedContent = content.trim()

    // Se está envolvido em uma div simples, extrair o conteúdo interno
    if (
        trimmedContent.startsWith('<div>') &&
        trimmedContent.endsWith('</div>')
    ) {
        return trimmedContent.slice(5, -6) // Remove <div> e </div>
    }

    // Se está envolvido em uma div com atributos, extrair o conteúdo interno
    const divMatch = trimmedContent.match(/^<div[^>]*>(.*)<\/div>$/s)
    if (divMatch && divMatch.length > 1 && divMatch[1] !== undefined) {
        return divMatch[1]
    }

    // Se não está em uma div, retornar como está
    return content
}

// Função para garantir que o conteúdo esteja dentro de uma div
const ensureContentInDiv = (content: string): string => {
    if (!content || content.trim() === '') {
        return '<div></div>'
    }

    // Verificar se o conteúdo já está envolvido em uma div
    const trimmedContent = content.trim()

    // Se já começa e termina com div, retornar como está
    if (
        trimmedContent.startsWith('<div') &&
        trimmedContent.endsWith('</div>')
    ) {
        return content
    }

    // Se não está em uma div, envolver o conteúdo
    return `<div>${content}</div>`
}

// Schema de validação
const textoPadronizadoSchema = yup.object({
    descricao: yup.string().required($t('validate.requiredDescription')),
    tipo: yup.number().required($t('validate.requiredType')),
    conteudo: yup.string().required($t('validate.requiredContent'))
})

// Inicializa o formulário
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: textoPadronizadoSchema,
    initialValues: {
        descricao: props.initialValues.descricao || '',
        tipo: props.initialValues.tipo || null,
        conteudo: extractContentFromDiv(props.initialValues.conteudo || '')
    },
    validateOnMount: false
})

// Define fields
const [descricao] = defineField('descricao')
const [tipo] = defineField('tipo')
const [conteudo] = defineField('conteudo')

// Campo adicional não incluído na validação
const ativo = ref(
    props.initialValues.ativo !== undefined ? props.initialValues.ativo : true
)

// Métodos
const filterTipos = (val: string, update: (fn: () => void) => void): void => {
    update(() => {
        filterTiposComposable(val)
    })
}

// Função para converter descrição para uppercase
const onDescricaoInput = (value: string | number | null): void => {
    descricao.value = value ? value.toString().toUpperCase() : ''
}

// Lifecycle
onMounted(() => {
    loadTiposComposable()
})

// Função para obter a label do tipo pelo ID
const getTipoLabelById = (tipoId: number): string => {
    const tipoOption = tiposOptions.value.find(
        option => option.value === tipoId
    )
    return tipoOption ? tipoOption.label : ''
}

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: () => {
        return {
            ...values,
            tipo: getTipoLabelById(values.tipo), // Enviar a label do tipo, não o ID
            conteudo: ensureContentInDiv(values.conteudo || ''),
            ativo: ativo.value
        }
    },
    resetForm: () => {
        resetForm()
        ativo.value = true
    }
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
