<template>
    <div id="form-basico" class="q-pa-xs">
        <form-template :title="title || $t('forms.titles.basicForm')">
            <template #header>
                <div v-if="enabledFields.includes('ativo')" class="col-auto">
                    <q-toggle
                        v-model="ativo"
                        :label="$t('forms.labels.active')"
                        color="secondary"
                        class="no-bottom-spacing text-white"
                        dense
                    />
                </div>
            </template>

            <template #body>
                <div class="row q-col-gutter-xs">
                    <!-- <PERSON><PERSON> padrões -->
                    <div
                        v-if="enabledFields.includes('name')"
                        :class="getSpecificFieldClass('name')"
                    >
                        <q-input
                            v-model="nome"
                            :label="$t('forms.labels.name')"
                            outlined
                            dense
                            stack-label
                            v-required-field
                            :error="!!errors.nome"
                            :error-message="errors.nome"
                            @update:model-value="
                                value =>
                                    (nome =
                                        typeof value === 'string'
                                            ? value.toUpperCase()
                                            : String(value || '').toUpperCase())
                            "
                        />
                    </div>

                    <div
                        v-if="enabledFields.includes('descricao')"
                        :class="getSpecificFieldClass('descricao')"
                    >
                        <q-input
                            v-model="descricao"
                            :label="$t('forms.labels.description')"
                            outlined
                            dense
                            stack-label
                            counter
                            maxlength="250"
                            :error="!!errors.descricao"
                            :error-message="errors.descricao"
                            @update:model-value="
                                value =>
                                    (descricao =
                                        typeof value === 'string'
                                            ? value.toUpperCase()
                                            : String(value || '').toUpperCase())
                            "
                        />
                    </div>

                    <div
                        v-if="enabledFields.includes('email')"
                        :class="getSpecificFieldClass('email')"
                    >
                        <q-input
                            v-model="email"
                            :label="$t('forms.labels.email')"
                            outlined
                            dense
                            stack-label
                            v-required-field
                            :error="!!errors.email"
                            :error-message="errors.email"
                        />
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref, computed } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'
const { t: $t } = useI18n()
// Components
import FormTemplate from '@/components/FormTemplate.vue'

type TEnabledFields = 'email' | 'ativo' | 'name' | 'descricao'

// Props
const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    enabledFields: {
        type: Array as () => TEnabledFields[],
        default: () => []
    },
    initialValues: {
        type: Object,
        default: () => ({})
    }
})

// Computed para largura dinâmica dos campos
const getFieldClass = computed(() => {
    const visibleFields = props.enabledFields.filter(field => field !== 'ativo') // ativo não conta pois fica no header
    const fieldCount = visibleFields.length

    // Lógica para distribuição responsiva
    if (fieldCount === 1) {
        return 'col-12'
    } else if (fieldCount === 2) {
        return 'col-12 col-sm-6'
    } else if (fieldCount === 3) {
        return 'col-12 col-sm-4'
    } else {
        return 'col-12 col-sm-3'
    }
})

// Computed específico para campos especiais
const getSpecificFieldClass = (fieldName: string) => {
    const visibleFields = props.enabledFields.filter(field => field !== 'ativo')
    const fieldCount = visibleFields.length

    // Regras específicas para alguns campos
    if (
        fieldName === 'descricao' &&
        fieldCount === 2 &&
        props.enabledFields.includes('name')
    ) {
        return 'col-12 col-sm-8' // Descrição maior quando tem nome
    }

    return getFieldClass.value
}

// Schema dinâmico baseado nos campos habilitados
const validationSchema = computed(() => {
    const schema: Record<string, yup.StringSchema> = {}

    // Adicionar validações apenas para campos habilitados
    if (props.enabledFields.includes('name')) {
        schema.nome = yup
            .string()
            .trim()
            .required($t('validate.requiredName'))
            .min(3, $t('validate.invalidName'))
    }

    if (props.enabledFields.includes('descricao')) {
        schema.descricao = yup
            .string()
            .trim()
            .required($t('validate.requiredDescription'))
            .max(250, $t('validate.maxDescription'))
    }

    if (props.enabledFields.includes('email')) {
        schema.email = yup
            .string()
            .trim()
            .email($t('validate.invalidEmail'))
            .required($t('validate.requiredEmail'))
    }

    return yup.object(schema)
})

// Configurar o formulário com validação dinâmica
const { errors, validate, defineField, values, resetForm } = useForm({
    validationSchema: validationSchema.value,
    initialValues: {
        nome: props.initialValues.nome || '',
        descricao: props.initialValues.descricao || '',
        email: props.initialValues.email || ''
    }
})

// Definir campos padrões
const [nome] = defineField('nome')
const [descricao] = defineField('descricao')
const [email] = defineField('email')

// Campo adicional não incluído na validação
const ativo = ref(
    props.initialValues.ativo !== undefined ? props.initialValues.ativo : true
)

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: () => {
        const data: Record<string, string | boolean> = {}

        // Mapear campos habilitados para os valores correspondentes
        props.enabledFields.forEach(field => {
            if (field === 'name' && 'nome' in values) {
                data.nome = values.nome
            } else if (field === 'descricao' && 'descricao' in values) {
                data.descricao = values.descricao
            } else if (field === 'email' && 'email' in values) {
                data.email = values.email
            }
        })

        // Adicionar campo ativo se habilitado
        if (props.enabledFields.includes('ativo')) {
            data.ativo = ativo.value
        }

        return data
    },
    resetForm
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
