<template>
    <div id="form-cliente" class="q-pa-xs">
        <form-template :title="$t('forms.titles.customerForm')">
            <template #header>
                <div class="row q-col-gutter-xs">
                    <div class="col-auto">
                        <q-toggle
                            v-model="status"
                            :label="$t('forms.labels.active')"
                            color="secondary"
                            class="no-bottom-spacing text-white"
                            dense
                        />
                    </div>
                    <div class="col-auto">
                        <q-toggle
                            v-model="bloqueioVenda"
                            :label="$t('forms.labels.salesBlock')"
                            color="negative"
                            class="no-bottom-spacing text-white"
                            dense
                        />
                    </div>
                </div>
            </template>

            <template #body>
                <div class="row q-col-gutter-xs">
                    <!-- Informações Básicas -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.basicInfo') }}
                        </div>
                        <q-card flat bordered class="q-mb-md">
                            <q-card-section class="q-pa-xs">
                                <div class="row q-col-gutter-xs">
                                    <!-- Nome (readonly) -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-input
                                            v-model="nome"
                                            :label="$t('forms.labels.name')"
                                            outlined
                                            dense
                                            stack-label
                                            readonly
                                            disable
                                            :error="!!errors.nome"
                                            :error-message="errors.nome"
                                        />
                                    </div>

                                    <!-- Categoria -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-select
                                            v-model="categoria"
                                            :label="$t('forms.labels.category')"
                                            :options="categoriasFilteredOptions"
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            options-dense
                                            use-input
                                            input-debounce="300"
                                            @filter="filterCategoria"
                                            :error="!!errors.categoria"
                                            :error-message="errors.categoria"
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.noResults'
                                                            )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>

                                    <!-- Condição de pagamento -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-select
                                            v-model="condicaoPagamento"
                                            :label="
                                                $t(
                                                    'forms.labels.paymentCondition'
                                                )
                                            "
                                            :options="
                                                condicoesPagamentoFilteredOptions
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            options-dense
                                            use-input
                                            input-debounce="300"
                                            @filter="filterCondicaoPagamento"
                                            :error="!!errors.condicaoPagamento"
                                            :error-message="
                                                errors.condicaoPagamento
                                            "
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.noResults'
                                                            )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>

                                    <!-- Modalidade de pagamento -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-select
                                            v-model="modalidadePagamento"
                                            :label="
                                                $t(
                                                    'forms.labels.paymentModality'
                                                )
                                            "
                                            :options="
                                                modalidadesPagamentoFilteredOptions
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            options-dense
                                            use-input
                                            input-debounce="300"
                                            @filter="filterModalidadePagamento"
                                            :error="
                                                !!errors.modalidadePagamento
                                            "
                                            :error-message="
                                                errors.modalidadePagamento
                                            "
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.noResults'
                                                            )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>

                                    <!-- Transação -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-select
                                            v-model="transacao"
                                            :label="
                                                $t('forms.labels.transaction')
                                            "
                                            :options="transacoesFilteredOptions"
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            options-dense
                                            use-input
                                            input-debounce="300"
                                            @filter="filterTransacao"
                                            :error="!!errors.transacao"
                                            :error-message="errors.transacao"
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.noResults'
                                                            )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>

                                    <!-- Tabela de Preço -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-select
                                            v-model="tabelaPreco"
                                            :label="
                                                $t('forms.labels.priceTable')
                                            "
                                            :options="
                                                tabelasPrecosFilteredOptions
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            options-dense
                                            use-input
                                            input-debounce="300"
                                            @filter="filterTabelaPreco"
                                            :error="!!errors.tabelaPreco"
                                            :error-message="errors.tabelaPreco"
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.noResults'
                                                            )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>

                                    <!-- Vendedor -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-select
                                            v-model="vendedor"
                                            :label="$t('forms.labels.seller')"
                                            :options="vendedoresFilteredOptions"
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            options-dense
                                            use-input
                                            input-debounce="300"
                                            @filter="filterVendedor"
                                            :error="!!errors.vendedor"
                                            :error-message="errors.vendedor"
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.noResults'
                                                            )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>

                                    <!-- Conta Financeira -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-select
                                            v-model="contaFinanceira"
                                            :label="
                                                $t(
                                                    'forms.labels.financialAccount'
                                                )
                                            "
                                            :options="
                                                contasFinanceirasFilteredOptions
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            options-dense
                                            use-input
                                            input-debounce="300"
                                            @filter="filterContaFinanceira"
                                            :error="!!errors.contaFinanceira"
                                            :error-message="
                                                errors.contaFinanceira
                                            "
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.noResults'
                                                            )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>

                                    <!-- Representante -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-select
                                            v-model="representante"
                                            :label="
                                                $t(
                                                    'forms.labels.representative'
                                                )
                                            "
                                            :options="
                                                representantesFilteredOptions
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            options-dense
                                            use-input
                                            input-debounce="300"
                                            @filter="filterRepresentante"
                                            :error="!!errors.representante"
                                            :error-message="
                                                errors.representante
                                            "
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.noResults'
                                                            )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>

                                    <!-- Tipo de Entrega -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-select
                                            v-model="tipoEntrega"
                                            :label="
                                                $t('forms.labels.deliveryType')
                                            "
                                            :options="
                                                tiposEntregaFilteredOptions
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            options-dense
                                            use-input
                                            input-debounce="300"
                                            @filter="filterTipoEntrega"
                                            :error="!!errors.tipoEntrega"
                                            :error-message="errors.tipoEntrega"
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.noResults'
                                                            )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>

                                    <!-- Indicador de Frete -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-select
                                            v-model="indicadorFrete"
                                            :label="
                                                $t(
                                                    'forms.labels.freightIndicator'
                                                )
                                            "
                                            :options="
                                                indicadoresFreteFilteredOptions
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            options-dense
                                            use-input
                                            input-debounce="300"
                                            @filter="filterIndicadorFrete"
                                            :error="!!errors.indicadorFrete"
                                            :error-message="
                                                errors.indicadorFrete
                                            "
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.noResults'
                                                            )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>

                                    <!-- Transportadora -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-select
                                            v-model="transportadora"
                                            :label="$t('forms.labels.carrier')"
                                            :options="
                                                transportadorasFilteredOptions
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            options-dense
                                            use-input
                                            input-debounce="300"
                                            @filter="filterTransportadora"
                                            :error="!!errors.transportadora"
                                            :error-message="
                                                errors.transportadora
                                            "
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.noResults'
                                                            )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>

                                    <!-- Centro de Custo -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-select
                                            v-model="centroCusto"
                                            :label="
                                                $t('forms.labels.costCenter')
                                            "
                                            :options="
                                                centrosCustoFilteredOptions
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            options-dense
                                            use-input
                                            input-debounce="300"
                                            @filter="filterCentroCusto"
                                            :error="!!errors.centroCusto"
                                            :error-message="errors.centroCusto"
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.noResults'
                                                            )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>

                                    <!-- Grupo Forma de Pagamento -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-select
                                            v-model="grupoFormaPagamento"
                                            :label="
                                                $t(
                                                    'forms.labels.paymentMethodGroup'
                                                )
                                            "
                                            :options="
                                                gruposFormaPagamentoFilteredOptions
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            options-dense
                                            use-input
                                            input-debounce="300"
                                            @filter="filterGrupoFormaPagamento"
                                            :error="
                                                !!errors.grupoFormaPagamento
                                            "
                                            :error-message="
                                                errors.grupoFormaPagamento
                                            "
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.noResults'
                                                            )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>

                    <!-- Limites e Configurações -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.limitsAndSettings') }}
                        </div>
                        <q-card flat bordered class="q-mb-md">
                            <q-card-section class="q-pa-xs">
                                <div class="row q-col-gutter-xs">
                                    <!-- Dias para informar o Aviso de Vencimento -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-input
                                            v-model.number="diasAvisoVencimento"
                                            :label="
                                                $t(
                                                    'forms.labels.daysForDueNotice'
                                                )
                                            "
                                            type="number"
                                            outlined
                                            dense
                                            stack-label
                                            :error="
                                                !!errors.diasAvisoVencimento
                                            "
                                            :error-message="
                                                errors.diasAvisoVencimento
                                            "
                                        >
                                            <template #append>
                                                <q-icon name="event" />
                                            </template>
                                        </q-input>
                                    </div>

                                    <!-- Dias para informar o aviso de cobrança -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-input
                                            v-model.number="diasAvisoCobranca"
                                            :label="
                                                $t(
                                                    'forms.labels.daysForCollectionNotice'
                                                )
                                            "
                                            type="number"
                                            outlined
                                            dense
                                            stack-label
                                            :error="!!errors.diasAvisoCobranca"
                                            :error-message="
                                                errors.diasAvisoCobranca
                                            "
                                        >
                                            <template #append>
                                                <q-icon name="event" />
                                            </template>
                                        </q-input>
                                    </div>

                                    <!-- Dia Limite para o faturamento -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-input
                                            v-model.number="
                                                diaLimiteFaturamento
                                            "
                                            :label="
                                                $t(
                                                    'forms.labels.billingDayLimit'
                                                )
                                            "
                                            type="number"
                                            outlined
                                            dense
                                            stack-label
                                            :error="
                                                !!errors.diaLimiteFaturamento
                                            "
                                            :error-message="
                                                errors.diaLimiteFaturamento
                                            "
                                        >
                                            <template #append>
                                                <q-icon name="event" />
                                            </template>
                                        </q-input>
                                    </div>

                                    <!-- Percentual de Desconto Máximo -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-input
                                            v-model.number="
                                                percentualDescontoMaximo
                                            "
                                            :label="
                                                $t(
                                                    'forms.labels.maxDiscountPercentage'
                                                )
                                            "
                                            type="number"
                                            outlined
                                            dense
                                            stack-label
                                            :error="
                                                !!errors.percentualDescontoMaximo
                                            "
                                            :error-message="
                                                errors.percentualDescontoMaximo
                                            "
                                        >
                                            <template #append>
                                                <q-icon name="percent" />
                                            </template>
                                        </q-input>
                                    </div>

                                    <!-- Valor do Faturamento Minimo -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-input
                                            v-model.number="
                                                valorFaturamentoMinimo
                                            "
                                            :label="
                                                $t(
                                                    'forms.labels.minimumBillingValue'
                                                )
                                            "
                                            type="number"
                                            prefix="R$"
                                            outlined
                                            dense
                                            stack-label
                                            :error="
                                                !!errors.valorFaturamentoMinimo
                                            "
                                            :error-message="
                                                errors.valorFaturamentoMinimo
                                            "
                                        >
                                            <template #prepend>
                                                <q-icon name="attach_money" />
                                            </template>
                                        </q-input>
                                    </div>

                                    <!-- Limite de Crédito -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-input
                                            v-model.number="limiteCredito"
                                            :label="
                                                $t('forms.labels.creditLimit')
                                            "
                                            type="number"
                                            prefix="R$"
                                            outlined
                                            dense
                                            stack-label
                                            :error="!!errors.limiteCredito"
                                            :error-message="
                                                errors.limiteCredito
                                            "
                                        >
                                            <template #prepend>
                                                <q-icon name="attach_money" />
                                            </template>
                                        </q-input>
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>

                    <!-- Horários de Funcionamento -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.businessHours') }}
                        </div>
                        <q-card flat bordered class="q-mb-md">
                            <q-card-section class="q-pa-xs">
                                <div class="row q-col-gutter-xs">
                                    <!-- Manhã -->
                                    <div class="col-12 col-md-4">
                                        <div class="text-subtitle2 q-mb-xs">
                                            {{ $t('forms.labels.morning') }}
                                        </div>
                                        <div class="row q-col-gutter-xs">
                                            <div class="col-6">
                                                <q-input
                                                    v-model="horarioManhaInicio"
                                                    :label="
                                                        $t('forms.labels.start')
                                                    "
                                                    type="time"
                                                    outlined
                                                    dense
                                                    stack-label
                                                    :error="
                                                        !!errors.horarioManhaInicio
                                                    "
                                                    :error-message="
                                                        errors.horarioManhaInicio
                                                    "
                                                />
                                            </div>
                                            <div class="col-6">
                                                <q-input
                                                    v-model="horarioManhaFim"
                                                    :label="
                                                        $t('forms.labels.end')
                                                    "
                                                    type="time"
                                                    outlined
                                                    dense
                                                    stack-label
                                                    :error="
                                                        !!errors.horarioManhaFim
                                                    "
                                                    :error-message="
                                                        errors.horarioManhaFim
                                                    "
                                                />
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Tarde -->
                                    <div class="col-12 col-md-4">
                                        <div class="text-subtitle2 q-mb-xs">
                                            {{ $t('forms.labels.afternoon') }}
                                        </div>
                                        <div class="row q-col-gutter-xs">
                                            <div class="col-6">
                                                <q-input
                                                    v-model="horarioTardeInicio"
                                                    :label="
                                                        $t('forms.labels.start')
                                                    "
                                                    type="time"
                                                    outlined
                                                    dense
                                                    stack-label
                                                    :error="
                                                        !!errors.horarioTardeInicio
                                                    "
                                                    :error-message="
                                                        errors.horarioTardeInicio
                                                    "
                                                />
                                            </div>
                                            <div class="col-6">
                                                <q-input
                                                    v-model="horarioTardeFim"
                                                    :label="
                                                        $t('forms.labels.end')
                                                    "
                                                    type="time"
                                                    outlined
                                                    dense
                                                    stack-label
                                                    :error="
                                                        !!errors.horarioTardeFim
                                                    "
                                                    :error-message="
                                                        errors.horarioTardeFim
                                                    "
                                                />
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Noite -->
                                    <div class="col-12 col-md-4">
                                        <div class="text-subtitle2 q-mb-xs">
                                            {{ $t('forms.labels.evening') }}
                                        </div>
                                        <div class="row q-col-gutter-xs">
                                            <div class="col-6">
                                                <q-input
                                                    v-model="horarioNoiteInicio"
                                                    :label="
                                                        $t('forms.labels.start')
                                                    "
                                                    type="time"
                                                    outlined
                                                    dense
                                                    stack-label
                                                    :error="
                                                        !!errors.horarioNoiteInicio
                                                    "
                                                    :error-message="
                                                        errors.horarioNoiteInicio
                                                    "
                                                />
                                            </div>
                                            <div class="col-6">
                                                <q-input
                                                    v-model="horarioNoiteFim"
                                                    :label="
                                                        $t('forms.labels.end')
                                                    "
                                                    type="time"
                                                    outlined
                                                    dense
                                                    stack-label
                                                    :error="
                                                        !!errors.horarioNoiteFim
                                                    "
                                                    :error-message="
                                                        errors.horarioNoiteFim
                                                    "
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'

const { t: $t } = useI18n()
// Components
import FormTemplate from '@/components/FormTemplate.vue'

// Props
const props = defineProps({
    initialValues: {
        type: Object,
        default: () => ({})
    }
})

// Opções para os selects (mock - deve vir do banco)
const categoriasOptions = [
    { label: 'Categoria 1', value: 'cat1' },
    { label: 'Categoria 2', value: 'cat2' }
]

const condicoesPagamentoOptions = [
    { label: 'À Vista', value: 'avista' },
    { label: 'Parcelado', value: 'parcelado' }
]

const modalidadesPagamentoOptions = [
    { label: 'Dinheiro', value: 'dinheiro' },
    { label: 'Cartão', value: 'cartao' }
]

const transacoesOptions = [
    { label: 'Venda', value: 'venda' },
    { label: 'Devolução', value: 'devolucao' }
]

const tabelasPrecosOptions = [
    { label: 'Tabela Padrão', value: 'padrao' },
    { label: 'Tabela Promocional', value: 'promocional' }
]

const vendedoresOptions = [
    { label: 'Vendedor 1', value: 'vend1' },
    { label: 'Vendedor 2', value: 'vend2' }
]

const contasFinanceirasOptions = [
    { label: 'Conta 1', value: 'conta1' },
    { label: 'Conta 2', value: 'conta2' }
]

const representantesOptions = [
    { label: 'Representante 1', value: 'rep1' },
    { label: 'Representante 2', value: 'rep2' }
]

const tiposEntregaOptions = [
    { label: 'Entrega Padrão', value: 'padrao' },
    { label: 'Entrega Expressa', value: 'expressa' }
]

const indicadoresFreteOptions = [
    { label: 'CIF', value: 'cif' },
    { label: 'FOB', value: 'fob' }
]

const transportadorasOptions = [
    { label: 'Transportadora 1', value: 'trans1' },
    { label: 'Transportadora 2', value: 'trans2' }
]

const centrosCustoOptions = [
    { label: 'Centro 1', value: 'centro1' },
    { label: 'Centro 2', value: 'centro2' }
]

const gruposFormaPagamentoOptions = [
    { label: 'Grupo 1', value: 'grupo1' },
    { label: 'Grupo 2', value: 'grupo2' }
]

// Form Cliente Schema
const clienteSchema = yup.object({
    nome: yup.string().required($t('validate.requiredName')),
    categoria: yup.string().required($t('validate.requiredCategory')),
    condicaoPagamento: yup
        .string()
        .required($t('validate.requiredPaymentCondition')),
    modalidadePagamento: yup
        .string()
        .required($t('validate.requiredPaymentModality')),
    transacao: yup.string().required($t('validate.requiredTransaction')),
    tabelaPreco: yup.string().required($t('validate.requiredPriceTable')),
    vendedor: yup.string().required($t('validate.requiredSeller')),
    contaFinanceira: yup
        .string()
        .required($t('validate.requiredFinancialAccount')),
    representante: yup.string().required($t('validate.requiredRepresentative')),
    tipoEntrega: yup.string().required($t('validate.requiredDeliveryType')),
    indicadorFrete: yup
        .string()
        .required($t('validate.requiredFreightIndicator')),
    transportadora: yup.string().required($t('validate.requiredCarrier')),
    centroCusto: yup.string().required($t('validate.requiredCostCenter')),
    grupoFormaPagamento: yup
        .string()
        .required($t('validate.requiredPaymentMethodGroup')),

    // Campos numéricos
    diasAvisoVencimento: yup
        .number()
        .typeError($t('validate.mustBeNumber'))
        .min(0, $t('validate.minZero'))
        .required($t('validate.requiredDaysForDueNotice')),
    diasAvisoCobranca: yup
        .number()
        .typeError($t('validate.mustBeNumber'))
        .min(0, $t('validate.minZero'))
        .required($t('validate.requiredDaysForCollectionNotice')),
    diaLimiteFaturamento: yup
        .number()
        .typeError($t('validate.mustBeNumber'))
        .min(1, $t('validate.minOne'))
        .max(31, $t('validate.maxThirtyOne'))
        .required($t('validate.requiredBillingDayLimit')),
    percentualDescontoMaximo: yup
        .number()
        .typeError($t('validate.mustBeNumber'))
        .min(0, $t('validate.minZero'))
        .max(100, $t('validate.maxOneHundred'))
        .required($t('validate.requiredMaxDiscountPercentage')),
    valorFaturamentoMinimo: yup
        .number()
        .typeError($t('validate.mustBeNumber'))
        .min(0, $t('validate.minZero'))
        .required($t('validate.requiredMinimumBillingValue')),
    limiteCredito: yup
        .number()
        .typeError($t('validate.mustBeNumber'))
        .min(0, $t('validate.minZero'))
        .required($t('validate.requiredCreditLimit')),

    // Horários
    horarioManhaInicio: yup.string(),
    horarioManhaFim: yup.string(),
    horarioTardeInicio: yup.string(),
    horarioTardeFim: yup.string(),
    horarioNoiteInicio: yup.string(),
    horarioNoiteFim: yup.string()
})

// Inicializa o formulário com valores padrão ou iniciais
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: clienteSchema,
    initialValues: {
        nome: props.initialValues.nome || '',
        categoria: props.initialValues.categoria || '',
        condicaoPagamento: props.initialValues.condicaoPagamento || '',
        modalidadePagamento: props.initialValues.modalidadePagamento || '',
        transacao: props.initialValues.transacao || '',
        tabelaPreco: props.initialValues.tabelaPreco || '',
        vendedor: props.initialValues.vendedor || '',
        contaFinanceira: props.initialValues.contaFinanceira || '',
        representante: props.initialValues.representante || '',
        tipoEntrega: props.initialValues.tipoEntrega || '',
        indicadorFrete: props.initialValues.indicadorFrete || '',
        transportadora: props.initialValues.transportadora || '',
        centroCusto: props.initialValues.centroCusto || '',
        grupoFormaPagamento: props.initialValues.grupoFormaPagamento || '',

        // Campos numéricos
        diasAvisoVencimento: props.initialValues.diasAvisoVencimento || 0,
        diasAvisoCobranca: props.initialValues.diasAvisoCobranca || 0,
        diaLimiteFaturamento: props.initialValues.diaLimiteFaturamento || 1,
        percentualDescontoMaximo:
            props.initialValues.percentualDescontoMaximo || 0,
        valorFaturamentoMinimo: props.initialValues.valorFaturamentoMinimo || 0,
        limiteCredito: props.initialValues.limiteCredito || 0,

        // Horários
        horarioManhaInicio: props.initialValues.horarioManhaInicio || '08:00',
        horarioManhaFim: props.initialValues.horarioManhaFim || '12:00',
        horarioTardeInicio: props.initialValues.horarioTardeInicio || '13:00',
        horarioTardeFim: props.initialValues.horarioTardeFim || '18:00',
        horarioNoiteInicio: props.initialValues.horarioNoiteInicio || '19:00',
        horarioNoiteFim: props.initialValues.horarioNoiteFim || '22:00'
    },
    validateOnMount: false
})

// Define fields
const [nome] = defineField('nome')
const [categoria] = defineField('categoria')
const [condicaoPagamento] = defineField('condicaoPagamento')
const [modalidadePagamento] = defineField('modalidadePagamento')
const [transacao] = defineField('transacao')
const [tabelaPreco] = defineField('tabelaPreco')
const [vendedor] = defineField('vendedor')
const [contaFinanceira] = defineField('contaFinanceira')
const [representante] = defineField('representante')
const [tipoEntrega] = defineField('tipoEntrega')
const [indicadorFrete] = defineField('indicadorFrete')
const [transportadora] = defineField('transportadora')
const [centroCusto] = defineField('centroCusto')
const [grupoFormaPagamento] = defineField('grupoFormaPagamento')

// Campos numéricos
const [diasAvisoVencimento] = defineField('diasAvisoVencimento')
const [diasAvisoCobranca] = defineField('diasAvisoCobranca')
const [diaLimiteFaturamento] = defineField('diaLimiteFaturamento')
const [percentualDescontoMaximo] = defineField('percentualDescontoMaximo')
const [valorFaturamentoMinimo] = defineField('valorFaturamentoMinimo')
const [limiteCredito] = defineField('limiteCredito')

// Horários
const [horarioManhaInicio] = defineField('horarioManhaInicio')
const [horarioManhaFim] = defineField('horarioManhaFim')
const [horarioTardeInicio] = defineField('horarioTardeInicio')
const [horarioTardeFim] = defineField('horarioTardeFim')
const [horarioNoiteInicio] = defineField('horarioNoiteInicio')
const [horarioNoiteFim] = defineField('horarioNoiteFim')

// Variáveis para armazenar as opções filtradas
const categoriasFilteredOptions = ref([...categoriasOptions])
const condicoesPagamentoFilteredOptions = ref([...condicoesPagamentoOptions])
const modalidadesPagamentoFilteredOptions = ref([
    ...modalidadesPagamentoOptions
])
const transacoesFilteredOptions = ref([...transacoesOptions])
const tabelasPrecosFilteredOptions = ref([...tabelasPrecosOptions])
const vendedoresFilteredOptions = ref([...vendedoresOptions])
const contasFinanceirasFilteredOptions = ref([...contasFinanceirasOptions])
const representantesFilteredOptions = ref([...representantesOptions])
const tiposEntregaFilteredOptions = ref([...tiposEntregaOptions])
const indicadoresFreteFilteredOptions = ref([...indicadoresFreteOptions])
const transportadorasFilteredOptions = ref([...transportadorasOptions])
const centrosCustoFilteredOptions = ref([...centrosCustoOptions])
const gruposFormaPagamentoFilteredOptions = ref([
    ...gruposFormaPagamentoOptions
])

// Métodos de filtro para os selects
const filterCategoria = (val: string, update: (fn: () => void) => void) => {
    if (val === '') {
        update(() => {
            categoriasFilteredOptions.value = categoriasOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        categoriasFilteredOptions.value = categoriasOptions.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

const filterCondicaoPagamento = (
    val: string,
    update: (fn: () => void) => void
) => {
    if (val === '') {
        update(() => {
            condicoesPagamentoFilteredOptions.value = condicoesPagamentoOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        condicoesPagamentoFilteredOptions.value =
            condicoesPagamentoOptions.filter(
                v => v.label.toLowerCase().indexOf(needle) > -1
            )
    })
}

const filterModalidadePagamento = (
    val: string,
    update: (fn: () => void) => void
) => {
    if (val === '') {
        update(() => {
            modalidadesPagamentoFilteredOptions.value =
                modalidadesPagamentoOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        modalidadesPagamentoFilteredOptions.value =
            modalidadesPagamentoOptions.filter(
                v => v.label.toLowerCase().indexOf(needle) > -1
            )
    })
}

const filterTransacao = (val: string, update: (fn: () => void) => void) => {
    if (val === '') {
        update(() => {
            transacoesFilteredOptions.value = transacoesOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        transacoesFilteredOptions.value = transacoesOptions.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

const filterTabelaPreco = (val: string, update: (fn: () => void) => void) => {
    if (val === '') {
        update(() => {
            tabelasPrecosFilteredOptions.value = tabelasPrecosOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        tabelasPrecosFilteredOptions.value = tabelasPrecosOptions.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

const filterVendedor = (val: string, update: (fn: () => void) => void) => {
    if (val === '') {
        update(() => {
            vendedoresFilteredOptions.value = vendedoresOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        vendedoresFilteredOptions.value = vendedoresOptions.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

const filterContaFinanceira = (
    val: string,
    update: (fn: () => void) => void
) => {
    if (val === '') {
        update(() => {
            contasFinanceirasFilteredOptions.value = contasFinanceirasOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        contasFinanceirasFilteredOptions.value =
            contasFinanceirasOptions.filter(
                v => v.label.toLowerCase().indexOf(needle) > -1
            )
    })
}

const filterRepresentante = (val: string, update: (fn: () => void) => void) => {
    if (val === '') {
        update(() => {
            representantesFilteredOptions.value = representantesOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        representantesFilteredOptions.value = representantesOptions.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

const filterTipoEntrega = (val: string, update: (fn: () => void) => void) => {
    if (val === '') {
        update(() => {
            tiposEntregaFilteredOptions.value = tiposEntregaOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        tiposEntregaFilteredOptions.value = tiposEntregaOptions.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

const filterIndicadorFrete = (
    val: string,
    update: (fn: () => void) => void
) => {
    if (val === '') {
        update(() => {
            indicadoresFreteFilteredOptions.value = indicadoresFreteOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        indicadoresFreteFilteredOptions.value = indicadoresFreteOptions.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

const filterTransportadora = (
    val: string,
    update: (fn: () => void) => void
) => {
    if (val === '') {
        update(() => {
            transportadorasFilteredOptions.value = transportadorasOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        transportadorasFilteredOptions.value = transportadorasOptions.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

const filterCentroCusto = (val: string, update: (fn: () => void) => void) => {
    if (val === '') {
        update(() => {
            centrosCustoFilteredOptions.value = centrosCustoOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        centrosCustoFilteredOptions.value = centrosCustoOptions.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

const filterGrupoFormaPagamento = (
    val: string,
    update: (fn: () => void) => void
) => {
    if (val === '') {
        update(() => {
            gruposFormaPagamentoFilteredOptions.value =
                gruposFormaPagamentoOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        gruposFormaPagamentoFilteredOptions.value =
            gruposFormaPagamentoOptions.filter(
                v => v.label.toLowerCase().indexOf(needle) > -1
            )
    })
}

// Campos adicionais não incluídos na validação
const status = ref(
    props.initialValues.status !== undefined ? props.initialValues.status : true
)
const bloqueioVenda = ref(props.initialValues.bloqueioVenda || false)

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: () => {
        return {
            ...values,
            status: status.value,
            bloqueioVenda: bloqueioVenda.value
        }
    },
    resetForm
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
