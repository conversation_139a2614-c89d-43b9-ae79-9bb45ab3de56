<template>
    <div id="form-comprador" class="q-pa-xs">
        <form-template :title="$t('forms.titles.buyerForm')">
            <template #header>
                <q-toggle
                    v-model="status"
                    :label="$t('forms.labels.active')"
                    color="secondary"
                    class="no-bottom-spacing text-white"
                    dense
                />
            </template>

            <template #body>
                <div class="row q-col-gutter-xs">
                    <!-- Limite de compra do pedido -->
                    <div class="col-12 col-sm-6">
                        <q-input
                            v-model.number="limiteCompraPedido"
                            :label="$t('forms.labels.orderPurchaseLimit')"
                            type="number"
                            prefix="R$"
                            outlined
                            dense
                            stack-label
                            :error="!!errors.limiteCompraPedido"
                            :error-message="errors.limiteCompraPedido"
                        >
                            <template #prepend>
                                <q-icon name="attach_money" />
                            </template>
                        </q-input>
                    </div>

                    <!-- Limite de compra do mês -->
                    <div class="col-12 col-sm-6">
                        <q-input
                            v-model.number="limiteCompraMes"
                            :label="$t('forms.labels.monthlyPurchaseLimit')"
                            type="number"
                            prefix="R$"
                            outlined
                            dense
                            stack-label
                            :error="!!errors.limiteCompraMes"
                            :error-message="errors.limiteCompraMes"
                        >
                            <template #prepend>
                                <q-icon name="attach_money" />
                            </template>
                        </q-input>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'

const { t: $t } = useI18n()
// Components
import FormTemplate from '@/components/FormTemplate.vue'

// Props
const props = defineProps({
    initialValues: {
        type: Object,
        default: () => ({})
    }
})

// Form Comprador Schema
const compradorSchema = yup.object({
    limiteCompraPedido: yup
        .number()
        .typeError($t('validate.invalidOrderPurchaseLimit'))
        .min(0, $t('validate.minOrderPurchaseLimit'))
        .required($t('validate.requiredOrderPurchaseLimit')),
    limiteCompraMes: yup
        .number()
        .typeError($t('validate.invalidMonthlyPurchaseLimit'))
        .min(0, $t('validate.minMonthlyPurchaseLimit'))
        .required($t('validate.requiredMonthlyPurchaseLimit'))
})

// Inicializa o formulário com valores padrão ou iniciais
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: compradorSchema,
    initialValues: {
        limiteCompraPedido: props.initialValues.limiteCompraPedido || 0,
        limiteCompraMes: props.initialValues.limiteCompraMes || 0
    },
    validateOnMount: false
})

// Define fields
const [limiteCompraPedido] = defineField('limiteCompraPedido')
const [limiteCompraMes] = defineField('limiteCompraMes')

// Campo adicional não incluído na validação
const status = ref(
    props.initialValues.status !== undefined ? props.initialValues.status : true
)

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: () => {
        return {
            ...values,
            status: status.value
        }
    },
    resetForm
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
