<template>
    <div id="form-crm-prospect" class="q-pa-xs">
        <form-template :title="$t('forms.titles.crmProspectForm')">
            <template #header>
                <div class="row q-col-gutter-xs">
                    <div class="col-auto">
                        <q-toggle
                            v-model="status"
                            :label="$t('forms.labels.active')"
                            color="secondary"
                            class="no-bottom-spacing text-white"
                            dense
                        />
                    </div>
                </div>
            </template>

            <template #body>
                <div class="row q-col-gutter-xs">
                    <div class="col-12">
                        <div class="row q-col-gutter-xs">
                            <!-- Representante -->
                            <div class="col-12 col-md-3 col-lg-3">
                                <q-select
                                    v-model="representante"
                                    :label="$t('forms.labels.representative')"
                                    :options="representantesFilteredOptions"
                                    outlined
                                    dense
                                    stack-label
                                    emit-value
                                    map-options
                                    options-dense
                                    use-input
                                    input-debounce="300"
                                    @filter="filterRepresentante"
                                    :error="!!errors.representante"
                                    :error-message="errors.representante"
                                >
                                    <template v-slot:no-option>
                                        <q-item>
                                            <q-item-section class="text-grey">
                                                {{ $t('forms.noResults') }}
                                            </q-item-section>
                                        </q-item>
                                    </template>
                                </q-select>
                            </div>

                            <!-- Contato Principal -->
                            <div class="col-12 col-md-3 col-lg-3">
                                <q-select
                                    v-model="contatoPrincipal"
                                    :label="$t('forms.labels.mainContact')"
                                    :options="contatosPrincipaisFilteredOptions"
                                    outlined
                                    dense
                                    stack-label
                                    emit-value
                                    map-options
                                    options-dense
                                    use-input
                                    input-debounce="300"
                                    @filter="filterContatoPrincipal"
                                    :error="!!errors.contatoPrincipal"
                                    :error-message="errors.contatoPrincipal"
                                >
                                    <template v-slot:no-option>
                                        <q-item>
                                            <q-item-section class="text-grey">
                                                {{ $t('forms.noResults') }}
                                            </q-item-section>
                                        </q-item>
                                    </template>
                                </q-select>
                            </div>

                            <!-- Parceiro de Negócio que Indicou -->
                            <div class="col-12 col-md-3 col-lg-3">
                                <q-select
                                    v-model="parceiroNegocio"
                                    :label="$t('forms.labels.businessPartner')"
                                    :options="parceirosNegocioFilteredOptions"
                                    outlined
                                    dense
                                    stack-label
                                    emit-value
                                    map-options
                                    options-dense
                                    use-input
                                    input-debounce="300"
                                    @filter="filterParceiroNegocio"
                                    :error="!!errors.parceiroNegocio"
                                    :error-message="errors.parceiroNegocio"
                                >
                                    <template v-slot:no-option>
                                        <q-item>
                                            <q-item-section class="text-grey">
                                                {{ $t('forms.noResults') }}
                                            </q-item-section>
                                        </q-item>
                                    </template>
                                </q-select>
                            </div>

                            <!-- Número de Funcionários -->
                            <div class="col-12 col-md-3 col-lg-3">
                                <q-input
                                    v-model.number="numeroFuncionarios"
                                    :label="$t('forms.labels.employeeCount')"
                                    type="number"
                                    outlined
                                    dense
                                    stack-label
                                    :error="!!errors.numeroFuncionarios"
                                    :error-message="errors.numeroFuncionarios"
                                >
                                    <template #prepend>
                                        <q-icon name="people" />
                                    </template>
                                </q-input>
                            </div>

                            <!-- Faturamento Anual -->
                            <div class="col-12 col-md-3 col-lg-3">
                                <q-input
                                    v-model.number="faturamentoAnual"
                                    :label="$t('forms.labels.annualRevenue')"
                                    type="number"
                                    :prefix="$t('forms.labels.currencySymbol')"
                                    outlined
                                    dense
                                    stack-label
                                    :error="!!errors.faturamentoAnual"
                                    :error-message="errors.faturamentoAnual"
                                >
                                    <template #prepend>
                                        <q-icon name="attach_money" />
                                    </template>
                                </q-input>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'

const { t: $t } = useI18n()
// Components
import FormTemplate from '@/components/FormTemplate.vue'

// Props
const props = defineProps({
    initialValues: {
        type: Object,
        default: () => ({})
    }
})

// Opções para os selects (mock - deve vir do banco)
const representantesOptions = [
    { label: 'João Silva', value: 'joao' },
    { label: 'Maria Santos', value: 'maria' },
    { label: 'Pedro Oliveira', value: 'pedro' },
    { label: 'Ana Souza', value: 'ana' },
    { label: 'Carlos Ferreira', value: 'carlos' }
]

const parceirosNegocioOptions = [
    { label: 'Empresa ABC', value: 'abc' },
    { label: 'Consultoria XYZ', value: 'xyz' },
    { label: 'Distribuidora 123', value: '123' },
    { label: 'Parceiro Tech', value: 'tech' },
    { label: 'Global Solutions', value: 'global' }
]

const contatosPrincipaisOptions = [
    { label: 'Roberto Almeida - Diretor', value: 'roberto' },
    { label: 'Carla Mendes - Gerente', value: 'carla' },
    { label: 'Marcelo Santos - CEO', value: 'marcelo' },
    { label: 'Fernanda Lima - Diretora Financeira', value: 'fernanda' },
    { label: 'Paulo Oliveira - Gerente de TI', value: 'paulo' }
]

// Form CRM/Prospect Schema
const crmProspectSchema = yup.object({
    representante: yup.string().required($t('validate.requiredRepresentative')),
    contatoPrincipal: yup.string().required($t('validate.requiredMainContact')),
    parceiroNegocio: yup.string(),
    numeroFuncionarios: yup
        .number()
        .typeError($t('validate.mustBeNumber'))
        .min(0, $t('validate.minZero'))
        .required($t('validate.requiredEmployeeCount')),
    faturamentoAnual: yup
        .number()
        .typeError($t('validate.mustBeNumber'))
        .min(0, $t('validate.minZero'))
        .required($t('validate.requiredAnnualRevenue'))
})

// Inicializa o formulário com valores padrão ou iniciais
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: crmProspectSchema,
    initialValues: {
        representante: props.initialValues.representante || '',
        contatoPrincipal: props.initialValues.contatoPrincipal || '',
        parceiroNegocio: props.initialValues.parceiroNegocio || '',
        numeroFuncionarios: props.initialValues.numeroFuncionarios || 0,
        faturamentoAnual: props.initialValues.faturamentoAnual || 0
    },
    validateOnMount: false
})

// Define fields
const [representante] = defineField('representante')
const [contatoPrincipal] = defineField('contatoPrincipal')
const [parceiroNegocio] = defineField('parceiroNegocio')
const [numeroFuncionarios] = defineField('numeroFuncionarios')
const [faturamentoAnual] = defineField('faturamentoAnual')

// Variáveis para armazenar as opções filtradas
const representantesFilteredOptions = ref([...representantesOptions])
const contatosPrincipaisFilteredOptions = ref([...contatosPrincipaisOptions])
const parceirosNegocioFilteredOptions = ref([...parceirosNegocioOptions])

// Métodos de filtro para os selects
const filterRepresentante = (val: string, update: (fn: () => void) => void) => {
    if (val === '') {
        update(() => {
            representantesFilteredOptions.value = representantesOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        representantesFilteredOptions.value = representantesOptions.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

const filterContatoPrincipal = (
    val: string,
    update: (fn: () => void) => void
) => {
    if (val === '') {
        update(() => {
            contatosPrincipaisFilteredOptions.value = contatosPrincipaisOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        contatosPrincipaisFilteredOptions.value =
            contatosPrincipaisOptions.filter(
                v => v.label.toLowerCase().indexOf(needle) > -1
            )
    })
}

const filterParceiroNegocio = (
    val: string,
    update: (fn: () => void) => void
) => {
    if (val === '') {
        update(() => {
            parceirosNegocioFilteredOptions.value = parceirosNegocioOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        parceirosNegocioFilteredOptions.value = parceirosNegocioOptions.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

// Campo adicional não incluído na validação
const status = ref(
    props.initialValues.status !== undefined ? props.initialValues.status : true
)

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: () => {
        return {
            ...values,
            status: status.value
        }
    },
    resetForm: () => {
        resetForm()
        status.value = true
    }
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
