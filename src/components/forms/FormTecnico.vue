<template>
    <div id="form-tecnico" class="q-pa-xs">
        <form-template :title="$t('forms.titles.technicianForm')">
            <template #header>
                <div class="row q-col-gutter-xs">
                    <div class="col-auto">
                        <q-toggle
                            v-model="status"
                            :label="$t('forms.labels.active')"
                            color="secondary"
                            class="no-bottom-spacing text-white"
                            dense
                        />
                    </div>
                </div>
            </template>

            <template #body>
                <div class="row q-col-gutter-xs">
                    <div class="col-12">
                        <div class="row q-col-gutter-xs">
                            <!-- Valor Custo Hora -->
                            <div class="col-12">
                                <q-input
                                    v-model.number="valorCustoHora"
                                    :label="$t('forms.labels.hourCost')"
                                    type="number"
                                    :prefix="$t('forms.labels.currencySymbol')"
                                    outlined
                                    dense
                                    stack-label
                                    :error="!!errors.valorCustoHora"
                                    :error-message="errors.valorCustoHora"
                                >
                                    <template #prepend>
                                        <q-icon name="attach_money" />
                                    </template>
                                </q-input>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'

const { t: $t } = useI18n()
// Components
import FormTemplate from '@/components/FormTemplate.vue'

// Props
const props = defineProps({
    initialValues: {
        type: Object,
        default: () => ({})
    }
})

// Form Tecnico Schema
const tecnicoSchema = yup.object({
    valorCustoHora: yup
        .number()
        .typeError($t('validate.mustBeNumber'))
        .min(0, $t('validate.minZero'))
        .required($t('validate.requiredHourCost'))
})

// Inicializa o formulário com valores padrão ou iniciais
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: tecnicoSchema,
    initialValues: {
        valorCustoHora: props.initialValues.valorCustoHora || 0
    },
    validateOnMount: false
})

// Define fields
const [valorCustoHora] = defineField('valorCustoHora')

// Campo adicional não incluído na validação
const status = ref(
    props.initialValues.status !== undefined ? props.initialValues.status : true
)

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: () => {
        return {
            ...values,
            status: status.value
        }
    },
    resetForm: () => {
        resetForm()
        status.value = true
    }
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
