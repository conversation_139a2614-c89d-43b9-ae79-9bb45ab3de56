<template>
    <div id="form-permissoes" class="q-pa-xs">
        <form-template>
            <template #header>
                <div class="col-auto">
                    <div class="text-h6 q-my-none text-white">
                        {{ $t('forms.titles.permissionsManagement') }}
                    </div>
                </div>
                <div class="col-auto">
                    <q-toggle
                        v-model="status"
                        :label="$t('forms.labels.active')"
                        color="secondary"
                        class="no-bottom-spacing text-white"
                        dense
                    />
                </div>
            </template>

            <template #body>
                <div class="row q-col-gutter-xs justify-center">
                    <!-- Nome do Perfil de Permissão -->
                    <div class="col-12 col-sm-6 col-md-4">
                        <q-input
                            v-model="nome"
                            :label="$t('forms.labels.permissionProfileName')"
                            outlined
                            dense
                            stack-label
                            v-required-field
                            :error="!!errors.nome"
                            :error-message="errors.nome"
                        />
                    </div>

                    <!-- Descrição -->
                    <div class="col-12 col-sm-6 col-md-4">
                        <q-input
                            v-model="descricao"
                            :label="$t('forms.labels.description')"
                            outlined
                            dense
                            stack-label
                            :error="!!errors.descricao"
                            :error-message="errors.descricao"
                        />
                    </div>

                    <!-- Grupos de Permissões -->
                    <div class="col-12 col-sm-6 col-md-4">
                        <q-select
                            v-model="gruposPermissoes"
                            :label="$t('forms.labels.permissionGroups')"
                            :options="gruposPermissoesOptions"
                            outlined
                            dense
                            stack-label
                            multiple
                            use-chips
                            clearable
                            use-input
                            @filter="filterGruposPermissoes"
                            emit-value
                            map-options
                            options-dense
                            :error="!!errors.gruposPermissoes"
                            :error-message="errors.gruposPermissoes"
                        />
                    </div>

                    <!-- Permissões por Módulos -->
                    <div class="col-12 q-mt-md">
                        <module-permissions
                            :modulos="modulos"
                            :rotas="rotas"
                            :acoes="acoes"
                            v-model:permissoes="permissoes"
                        />
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref, reactive, onMounted } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'

const { t: $t } = useI18n()
//Composables
// Components
import FormTemplate from '@/components/FormTemplate.vue'
import ModulePermissions from '@/components/permissions/ModulePermissions.vue'

// Form Permissões
const permissoesSchema = yup.object({
    nome: yup
        .string()
        .trim()
        .required($t('validate.requiredPermissionProfileName'))
        .min(3, $t('validate.invalidPermissionProfileName')),
    descricao: yup.string().trim(),
    gruposPermissoes: yup.array().of(yup.string()).nullable()
})

const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: permissoesSchema,
    initialValues: {
        nome: '',
        descricao: '',
        gruposPermissoes: null
    },
    validateOnMount: false
})

// Define fields
const [nome] = defineField('nome')
const [descricao] = defineField('descricao')
const [gruposPermissoes] = defineField('gruposPermissoes')

// Additional fields not in validation
const status = ref(true)

// Grupos de Permissões (mock - deve vir do banco)
const gruposPermissoesOptions = ref([
    { label: 'Administradores', value: 'admin' },
    { label: 'Gerentes', value: 'gerentes' },
    { label: 'Vendedores', value: 'vendedores' },
    { label: 'Financeiro', value: 'financeiro' },
    { label: 'Estoque', value: 'estoque' },
    { label: 'Suporte', value: 'suporte' }
])

// Módulos (mock - deve vir do banco ou das rotas)
const modulos = ref([
    { label: 'Dashboard', value: 'dashboard' },
    { label: 'Usuários', value: 'usuarios' },
    { label: 'Clientes', value: 'clientes' },
    { label: 'Produtos', value: 'produtos' },
    { label: 'Vendas', value: 'vendas' },
    { label: 'Financeiro', value: 'financeiro' },
    { label: 'Relatórios', value: 'relatorios' },
    { label: 'Configurações', value: 'configuracoes' }
])

// Rotas por módulo (mock - deve vir do banco ou das rotas)
const rotas = ref([
    {
        label: 'Visualizar Dashboard',
        value: 'dashboard_view',
        modulo: 'dashboard'
    },
    { label: 'Listar Usuários', value: 'usuarios_list', modulo: 'usuarios' },
    { label: 'Criar Usuário', value: 'usuarios_create', modulo: 'usuarios' },
    { label: 'Editar Usuário', value: 'usuarios_edit', modulo: 'usuarios' },
    { label: 'Excluir Usuário', value: 'usuarios_delete', modulo: 'usuarios' },
    { label: 'Listar Clientes', value: 'clientes_list', modulo: 'clientes' },
    { label: 'Criar Cliente', value: 'clientes_create', modulo: 'clientes' },
    { label: 'Editar Cliente', value: 'clientes_edit', modulo: 'clientes' },
    { label: 'Excluir Cliente', value: 'clientes_delete', modulo: 'clientes' },
    { label: 'Listar Produtos', value: 'produtos_list', modulo: 'produtos' },
    { label: 'Criar Produto', value: 'produtos_create', modulo: 'produtos' },
    { label: 'Editar Produto', value: 'produtos_edit', modulo: 'produtos' },
    { label: 'Excluir Produto', value: 'produtos_delete', modulo: 'produtos' },
    { label: 'Listar Vendas', value: 'vendas_list', modulo: 'vendas' },
    { label: 'Criar Venda', value: 'vendas_create', modulo: 'vendas' },
    { label: 'Editar Venda', value: 'vendas_edit', modulo: 'vendas' },
    { label: 'Excluir Venda', value: 'vendas_delete', modulo: 'vendas' },
    {
        label: 'Relatórios Financeiros',
        value: 'financeiro_reports',
        modulo: 'financeiro'
    },
    {
        label: 'Contas a Pagar',
        value: 'financeiro_payables',
        modulo: 'financeiro'
    },
    {
        label: 'Contas a Receber',
        value: 'financeiro_receivables',
        modulo: 'financeiro'
    },
    {
        label: 'Relatórios de Vendas',
        value: 'relatorios_vendas',
        modulo: 'relatorios'
    },
    {
        label: 'Relatórios de Estoque',
        value: 'relatorios_estoque',
        modulo: 'relatorios'
    },
    {
        label: 'Configurações do Sistema',
        value: 'configuracoes_sistema',
        modulo: 'configuracoes'
    },
    {
        label: 'Configurações de Usuário',
        value: 'configuracoes_usuario',
        modulo: 'configuracoes'
    }
])

// Ações possíveis
const acoes = ref([
    { label: 'Visualizar', value: 'view' },
    { label: 'Criar', value: 'create' },
    { label: 'Editar', value: 'edit' },
    { label: 'Excluir', value: 'delete' }
])

// Objeto para armazenar as permissões
const permissoes = reactive(
    {} as Record<string, Record<string, Record<string, boolean>>>
)

// Inicializa o objeto de permissões
const initPermissoes = () => {
    try {
        // Inicializa a estrutura de permissões para cada módulo
        for (const modulo of modulos.value) {
            const moduloKey = modulo.value

            // Garante que o módulo existe no objeto de permissões
            if (!permissoes[moduloKey]) {
                permissoes[moduloKey] = {}
            }

            // Filtra as rotas para o módulo atual
            const rotasDoModulo = rotas.value.filter(
                rota => rota.modulo === moduloKey
            )

            // Para cada rota, inicializa as ações
            for (const rota of rotasDoModulo) {
                const rotaKey = rota.value

                // Garante que a rota existe no módulo
                if (!permissoes[moduloKey][rotaKey]) {
                    permissoes[moduloKey][rotaKey] = {}
                }

                // Para cada ação, inicializa como false
                for (const acao of acoes.value) {
                    const acaoKey = acao.value
                    permissoes[moduloKey][rotaKey][acaoKey] = false
                }
            }
        }
    } catch (error) {
        // Silencia o erro em produção
        if (process.env.NODE_ENV !== 'production') {
            // eslint-disable-next-line no-console
            console.error('Erro ao inicializar permissões:', error)
        }
    }
}

// Filtro para grupos de permissões
const filterGruposPermissoes = (
    val: string,
    update: (callback: () => void) => void
) => {
    if (val === '') {
        update(() => {
            gruposPermissoesOptions.value = [
                { label: 'Administradores', value: 'admin' },
                { label: 'Gerentes', value: 'gerentes' },
                { label: 'Vendedores', value: 'vendedores' },
                { label: 'Financeiro', value: 'financeiro' },
                { label: 'Estoque', value: 'estoque' },
                { label: 'Suporte', value: 'suporte' }
            ]
        })
    } else {
        update(() => {
            const needle = val.toLowerCase()
            gruposPermissoesOptions.value =
                gruposPermissoesOptions.value.filter(
                    v => v.label.toLowerCase().indexOf(needle) > -1
                )
        })
    }
}

onMounted(() => {
    initPermissoes()
})

// Expose form methods and data
defineExpose({
    validate,
    getData: () => ({
        ...values,
        status: status.value,
        permissoes: permissoes
    }),
    resetForm
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q-field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
