<template>
    <div id="form-motorista" class="q-pa-xs">
        <form-template :title="$t('forms.titles.driverForm')">
            <template #header>
                <div class="row q-col-gutter-xs">
                    <div class="col-auto">
                        <q-toggle
                            v-model="status"
                            :label="$t('forms.labels.active')"
                            color="secondary"
                            class="no-bottom-spacing text-white"
                            dense
                        />
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'

const { t: $t } = useI18n()
// Components
import FormTemplate from '@/components/FormTemplate.vue'

// Props
const props = defineProps({
    initialValues: {
        type: Object,
        default: () => ({})
    }
})

// Form Motorista Schema (vazio, pois só temos o status que não é validado)
const motoristaSchema = yup.object({})

// Inicializa o formulário com valores padrão ou iniciais
const { values, validate } = useForm({
    validationSchema: motoristaSchema,
    initialValues: {},
    validateOnMount: false
})

// Campo adicional não incluído na validação
const status = ref(
    props.initialValues.status !== undefined ? props.initialValues.status : true
)

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: () => {
        return {
            ...values,
            status: status.value
        }
    },
    resetForm: () => {
        status.value = true
        // Não há outros campos para resetar
    }
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
