<template>
    <div id="form-usuario" class="q-pa-xs">
        <form-template :title="$t('forms.titles.userInfo')">
            <template #body>
                <div
                    class="row justify-center items-center q-col-gutter-xs q-pt-sm"
                >
                    <!-- Foto de perfil -->
                    <div class="col-12 col-md-2 q-mt-md">
                        <profile-picture-upload
                            v-model="photo"
                            class="q-pa-xs"
                            :readonly="props.readonly"
                        />
                        <!-- <PERSON>leção de Idioma e Tema -->
                        <div class="row justify-center q-mt-sm">
                            <div class="col-auto q-mr-sm">
                                <lang-changer />
                            </div>
                            <div class="col-auto">
                                <theme-changer />
                            </div>
                        </div>
                    </div>

                    <!-- Informações básicas -->
                    <div class="col-12 col-md-10 q-mt-md">
                        <div class="row col-12 q-col-gutter-xs justify-center">
                            <!-- Nome e Sobrenome -->
                            <div class="col-12 col-sm-6 col-md-4">
                                <q-input
                                    v-model="name"
                                    :label="$t('forms.labels.name')"
                                    outlined
                                    dense
                                    stack-label
                                    v-required-field
                                    :readonly="props.readonly"
                                    :disable="props.readonly"
                                    :error="!!errors.name"
                                    :error-message="errors.name"
                                />
                            </div>
                            <div class="col-12 col-sm-6 col-md-4">
                                <q-input
                                    v-model="surname"
                                    :label="$t('forms.labels.surname')"
                                    outlined
                                    dense
                                    stack-label
                                    v-required-field
                                    :readonly="props.readonly"
                                    :disable="props.readonly"
                                    :error="!!errors.surname"
                                    :error-message="errors.surname"
                                />
                            </div>
                            <div class="col-12 col-sm-6 col-md-4">
                                <q-input
                                    v-model="email"
                                    :label="$t('forms.labels.email')"
                                    outlined
                                    dense
                                    stack-label
                                    v-required-field
                                    :readonly="props.readonly"
                                    :disable="props.readonly"
                                    :error="!!errors.email"
                                    :error-message="errors.email"
                                />
                            </div>

                            <!-- Username e Perfil -->
                            <div class="col-12 col-sm-6 col-md-4">
                                <q-input
                                    v-model="username"
                                    :label="$t('forms.labels.username')"
                                    outlined
                                    dense
                                    stack-label
                                    v-required-field
                                    :readonly="props.readonly"
                                    :disable="props.readonly"
                                    :error="!!errors.username"
                                    :error-message="errors.username"
                                />
                            </div>
                            <div class="col-12 col-sm-6 col-md-4">
                                <q-input
                                    v-model="password"
                                    :label="$t('forms.labels.password')"
                                    outlined
                                    dense
                                    stack-label
                                    v-required-field
                                    type="password"
                                    :readonly="props.readonly"
                                    :disable="props.readonly"
                                    :error="!!errors.password"
                                    :error-message="errors.password"
                                />
                            </div>
                            <div class="col-12 col-sm-6 col-md-4">
                                <q-select
                                    v-model="role"
                                    :label="$t('forms.labels.role')"
                                    :options="roles"
                                    outlined
                                    dense
                                    stack-label
                                    clearable
                                    use-input
                                    @filter="filterRoles"
                                    emit-value
                                    map-options
                                    options-dense
                                    :readonly="props.readonly"
                                    :disable="props.readonly"
                                    :error="!!errors.role"
                                    :error-message="errors.role"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { onMounted, ref } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'
const { t: $t } = useI18n()
// Composables
import { useUser } from 'src/composables/user'
const user = useUser()
// Components
import ProfilePictureUpload from '@/components/ProfilePictureUpload.vue'
import FormTemplate from '@/components/FormTemplate.vue'
import LangChanger from '@/components/LangChanger.vue'
import ThemeChanger from '@/components/ThemeChanger.vue'

// Props
const props = defineProps({
    readonly: {
        type: Boolean,
        default: false
    }
})

// Form Usuario
const userSchema = yup.object({
    name: yup
        .string()
        .trim()
        .required($t('validate.requiredName'))
        .min(3, $t('validate.invalidName')),
    surname: yup
        .string()
        .trim()
        .required($t('validate.requiredSurname'))
        .min(3, $t('validate.invalidSurname')),
    username: yup
        .string()
        .trim()
        .required($t('validate.requiredUsername'))
        .min(3, $t('validate.invalidUsername')),
    role: yup.string().nullable(),
    email: yup
        .string()
        .trim()
        .email($t('validate.invalidEmail'))
        .required($t('validate.requiredEmail')),
    password: yup.string().trim().min(6, $t('validate.invalidPassword'))
})

const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: userSchema,
    initialValues: {
        name: '',
        surname: '',
        username: '',
        role: null,
        email: '',
        password: ''
    },
    validateOnMount: false
})

// Define fields
const [name] = defineField('name')
const [surname] = defineField('surname')
const [username] = defineField('username')
const [role] = defineField('role')
const [email] = defineField('email')
const [password] = defineField('password')

// Additional fields not in validation
const photo = ref('')

// Role options
const roles = ref([
    {
        label: 'Administrador',
        value: 'admin'
    },
    {
        label: 'Usuario',
        value: 'user'
    }
])

const filterRoles = (val: string, update: (callback: () => void) => void) => {
    if (val === '') {
        update(() => {
            roles.value = [
                {
                    label: 'Administrador',
                    value: 'admin'
                },
                {
                    label: 'Usuario',
                    value: 'user'
                }
            ]
        })
    } else {
        update(() => {
            const needle = val.toLowerCase()
            roles.value = roles.value.filter(
                v => v.label.toLowerCase().indexOf(needle) > -1
            )
        })
    }
}

const getBasicInfo = () => {
    photo.value = user.getUserPicture()
    name.value = user.getUserName()
    surname.value = user.getUserSurname()
    username.value = user.getUserUsername()
    email.value = user.getUserEmail?.() || ''
}

onMounted(() => {
    getBasicInfo()
})

defineExpose({
    validate,
    getData: () => ({
        ...values,
        photo: photo.value
    }),
    resetForm
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q-field__marginal {
    height: 36px;
}

/* Estilos para tornar o formulário mais compacto */
.q-pa-xs {
    padding: 4px;
}

.q-col-gutter-xs > * {
    padding: 0 4px;
}

.q-mt-xs {
    margin-top: 4px;
}
</style>
