<template>
    <div id="form-grupo-permissoes" class="q-pa-xs">
        <form-template :title="$t('forms.titles.permissionGroupManagement')">
            <template #header>
                <q-toggle
                    v-model="status"
                    :label="$t('forms.labels.active')"
                    color="secondary"
                    class="no-bottom-spacing text-white"
                    dense
                />
            </template>

            <template #body>
                <div class="row q-col-gutter-xs justify-center">
                    <!-- Nome do Grupo de Permissão -->
                    <div class="col-12 col-sm-3">
                        <q-input
                            v-model="nome"
                            :label="$t('forms.labels.permissionGroupName')"
                            outlined
                            dense
                            stack-label
                            v-required-field
                            :error="!!errors.nome"
                            :error-message="errors.nome"
                        />
                    </div>

                    <!-- Descrição -->
                    <div class="col-12 col-sm-9">
                        <q-input
                            v-model="descricao"
                            :label="$t('forms.labels.description')"
                            outlined
                            dense
                            stack-label
                            counter
                            maxlength="250"
                            :error="!!errors.descricao"
                            :error-message="errors.descricao"
                        />
                    </div>

                    <!-- Permissões por Módulos -->
                    <div class="col-12 q-mt-md">
                        <module-permissions
                            v-model:permissoes="permissoes"
                            :modulos="modulos"
                            :rotas="rotas"
                            :acoes="acoes"
                        />
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref, onMounted } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'

const { t: $t } = useI18n()
// Components
import FormTemplate from '@/components/FormTemplate.vue'
import ModulePermissions from '@/components/permissions/ModulePermissions.vue'

// Form Grupo Permissões
const grupoPermissoesSchema = yup.object({
    nome: yup
        .string()
        .trim()
        .required($t('validate.requiredPermissionGroupName'))
        .min(3, $t('validate.invalidPermissionGroupName')),
    descricao: yup.string().trim().max(250, $t('validate.maxDescription'))
})

const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: grupoPermissoesSchema,
    initialValues: {
        nome: '',
        descricao: ''
    },
    validateOnMount: false
})

// Define fields
const [nome] = defineField('nome')
const [descricao] = defineField('descricao')

// Additional fields not in validation
const status = ref(true)

// Módulos (mock - deve vir do banco ou das rotas)
const modulos = ref([
    { label: 'Dashboard', value: 'dashboard' },
    { label: 'Usuários', value: 'usuarios' },
    { label: 'Clientes', value: 'clientes' },
    { label: 'Produtos', value: 'produtos' },
    { label: 'Vendas', value: 'vendas' },
    { label: 'Financeiro', value: 'financeiro' },
    { label: 'Relatórios', value: 'relatorios' },
    { label: 'Configurações', value: 'configuracoes' }
])

// Rotas por módulo (mock - deve vir do banco ou das rotas)
const rotas = ref([
    {
        label: 'Visualizar Dashboard',
        value: 'dashboard_view',
        modulo: 'dashboard'
    },
    { label: 'Listar Usuários', value: 'usuarios_list', modulo: 'usuarios' },
    { label: 'Criar Usuário', value: 'usuarios_create', modulo: 'usuarios' },
    { label: 'Editar Usuário', value: 'usuarios_edit', modulo: 'usuarios' },
    { label: 'Excluir Usuário', value: 'usuarios_delete', modulo: 'usuarios' },
    { label: 'Listar Clientes', value: 'clientes_list', modulo: 'clientes' },
    { label: 'Criar Cliente', value: 'clientes_create', modulo: 'clientes' },
    { label: 'Editar Cliente', value: 'clientes_edit', modulo: 'clientes' },
    { label: 'Excluir Cliente', value: 'clientes_delete', modulo: 'clientes' },
    { label: 'Listar Produtos', value: 'produtos_list', modulo: 'produtos' },
    { label: 'Criar Produto', value: 'produtos_create', modulo: 'produtos' },
    { label: 'Editar Produto', value: 'produtos_edit', modulo: 'produtos' },
    { label: 'Excluir Produto', value: 'produtos_delete', modulo: 'produtos' },
    { label: 'Listar Vendas', value: 'vendas_list', modulo: 'vendas' },
    { label: 'Criar Venda', value: 'vendas_create', modulo: 'vendas' },
    { label: 'Editar Venda', value: 'vendas_edit', modulo: 'vendas' },
    { label: 'Excluir Venda', value: 'vendas_delete', modulo: 'vendas' },
    {
        label: 'Relatórios Financeiros',
        value: 'financeiro_reports',
        modulo: 'financeiro'
    },
    {
        label: 'Contas a Pagar',
        value: 'financeiro_payables',
        modulo: 'financeiro'
    },
    {
        label: 'Contas a Receber',
        value: 'financeiro_receivables',
        modulo: 'financeiro'
    },
    {
        label: 'Relatórios de Vendas',
        value: 'relatorios_vendas',
        modulo: 'relatorios'
    },
    {
        label: 'Relatórios de Estoque',
        value: 'relatorios_estoque',
        modulo: 'relatorios'
    },
    {
        label: 'Configurações do Sistema',
        value: 'configuracoes_sistema',
        modulo: 'configuracoes'
    },
    {
        label: 'Configurações de Usuário',
        value: 'configuracoes_usuario',
        modulo: 'configuracoes'
    }
])

// Ações possíveis
const acoes = ref([
    { label: 'Visualizar', value: 'view' },
    { label: 'Criar', value: 'create' },
    { label: 'Editar', value: 'edit' },
    { label: 'Excluir', value: 'delete' }
])

// Objeto para armazenar as permissões
const permissoes = ref(
    {} as Record<string, Record<string, Record<string, boolean>>>
)

// Inicializa o objeto de permissões
const initPermissoes = () => {
    try {
        // Cria um novo objeto para armazenar as permissões
        const permissoesObj: Record<
            string,
            Record<string, Record<string, boolean>>
        > = {}

        // Inicializa a estrutura de permissões para cada módulo
        for (const modulo of modulos.value) {
            const moduloKey = modulo.value
            permissoesObj[moduloKey] = {}

            // Filtra as rotas para o módulo atual
            const rotasDoModulo = rotas.value.filter(
                rota => rota.modulo === moduloKey
            )

            // Para cada rota, inicializa as ações
            for (const rota of rotasDoModulo) {
                const rotaKey = rota.value
                permissoesObj[moduloKey][rotaKey] = {}

                // Para cada ação, inicializa como false
                for (const acao of acoes.value) {
                    const acaoKey = acao.value
                    permissoesObj[moduloKey][rotaKey][acaoKey] = false
                }
            }
        }

        // Atribui o objeto completo à referência
        permissoes.value = permissoesObj
    } catch (error) {
        // Silencia o erro em produção
        if (process.env.NODE_ENV !== 'production') {
            // eslint-disable-next-line no-console
            console.error('Erro ao inicializar permissões:', error)
        }
    }
}

onMounted(() => {
    initPermissoes()
})

// Expose form methods and data
defineExpose({
    validate,
    getData: () => ({
        ...values,
        status: status.value,
        permissoes: permissoes.value
    }),
    resetForm
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
