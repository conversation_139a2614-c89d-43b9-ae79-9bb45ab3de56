<template>
    <div id="form-email-config" class="q-pa-xs">
        <form-template :title="$t('forms.titles.emailConfig')">
            <template #body>
                <div class="row q-col-gutter-xs justify-center">
                    <!-- Servidor de E-mail -->
                    <div class="col-12 col-sm-6 col-md-4">
                        <q-input
                            v-model="smtpServer"
                            :label="$t('forms.labels.smtpServer')"
                            outlined
                            dense
                            stack-label
                            v-required-field
                            :error="!!errors.smtpServer"
                            :error-message="errors.smtpServer"
                        />
                    </div>

                    <!-- Porta SMTP -->
                    <div class="col-12 col-sm-6 col-md-4">
                        <q-input
                            v-model.number="smtpPort"
                            :label="$t('forms.labels.smtpPort')"
                            type="number"
                            step="any"
                            outlined
                            dense
                            stack-label
                            v-required-field
                            :error="!!errors.smtpPort"
                            :error-message="errors.smtpPort"
                        />
                    </div>

                    <!-- E-mail -->
                    <div class="col-12 col-sm-6 col-md-4">
                        <q-input
                            v-model="email"
                            :label="$t('forms.labels.email')"
                            outlined
                            dense
                            stack-label
                            v-required-field
                            readonly
                            disable
                            :error="!!errors.email"
                            :error-message="errors.email"
                        />
                    </div>

                    <!-- Senha do Email -->
                    <div class="col-12 col-sm-6 col-md-4">
                        <q-input
                            v-model="password"
                            :label="$t('forms.labels.emailPassword')"
                            outlined
                            dense
                            stack-label
                            v-required-field
                            type="password"
                            :error="!!errors.password"
                            :error-message="errors.password"
                        />
                    </div>

                    <!-- TLS -->
                    <div class="col-12 col-sm-6 col-md-4">
                        <q-toggle
                            v-model="useTLS"
                            :label="$t('forms.labels.useTLS')"
                            color="primary"
                        />
                    </div>

                    <!-- SSL -->
                    <div class="col-12 col-sm-6 col-md-4">
                        <q-toggle
                            v-model="useSSL"
                            :label="$t('forms.labels.useSSL')"
                            color="primary"
                        />
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'

const { t: $t } = useI18n()

// Components
import FormTemplate from '@/components/FormTemplate.vue'

// Form Email Config
const emailConfigSchema = yup.object({
    smtpServer: yup.string().trim().required($t('validate.requiredSmtpServer')),
    smtpPort: yup
        .number()
        .required($t('validate.requiredSmtpPort'))
        .typeError($t('validate.invalidSmtpPort'))
        .min(1, $t('validate.invalidSmtpPort'))
        .max(65535, $t('validate.invalidSmtpPort')),
    email: yup
        .string()
        .trim()
        .email($t('validate.invalidEmail'))
        .required($t('validate.requiredEmail')),
    password: yup.string().required($t('validate.requiredEmailPassword'))
})

const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: emailConfigSchema,
    initialValues: {
        smtpServer: '',
        smtpPort: 587,
        email: '<EMAIL>',
        password: ''
    },
    validateOnMount: false
})

// Define fields
const [smtpServer] = defineField('smtpServer')
const [smtpPort] = defineField('smtpPort')
const [email] = defineField('email')
const [password] = defineField('password')

// Additional fields not in validation
const useTLS = ref(true)
const useSSL = ref(false)

// Expose form methods and data
defineExpose({
    validate,
    getData: () => ({
        ...values,
        useTLS: useTLS.value,
        useSSL: useSSL.value
    }),
    resetForm
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}
</style>
