<template>
    <div id="form-marca-modelo">
        <form-template :title="$t('forms.titles.brandModel')">
            <template #body>
                <div class="form-container">
                    <!-- Seção de Informações do Modelo -->
                    <div class="form-section">
                        <div class="section-header">
                            <q-icon
                                name="directions_car"
                                class="section-icon"
                            />
                            <span class="section-title">{{
                                $t('forms.titles.modelInfo')
                            }}</span>
                        </div>

                        <q-card flat bordered class="section-card">
                            <q-card-section class="q-pa-md">
                                <div class="row q-col-gutter-md">
                                    <!-- <PERSON><PERSON> (Disabled) -->
                                    <div class="col-12 col-md-6">
                                        <q-input
                                            :model-value="
                                                selectedMarca?.descricao || ''
                                            "
                                            :label="$t('forms.labels.brand')"
                                            outlined
                                            dense
                                            stack-label
                                            disable
                                            :error="!!errors.marca"
                                            :error-message="errors.marca"
                                            class="form-field"
                                        >
                                            <template #prepend>
                                                <q-icon
                                                    name="business"
                                                    color="primary"
                                                />
                                            </template>
                                        </q-input>
                                    </div>

                                    <!-- Modelo (Select com filtro) -->
                                    <div class="col-12 col-md-6">
                                        <q-select
                                            v-model="modelo"
                                            :label="$t('forms.labels.model')"
                                            use-input
                                            input-debounce="500"
                                            :options="options"
                                            option-label="descricao"
                                            option-value="id"
                                            emit-value
                                            map-options
                                            outlined
                                            dense
                                            clearable
                                            @filter="onFilter"
                                            :loading="loading"
                                            use-chips
                                            behavior="menu"
                                            class="form-field"
                                        >
                                            <template #prepend>
                                                <q-icon
                                                    name="model_training"
                                                    color="primary"
                                                />
                                            </template>

                                            <template #option="scope">
                                                <q-item
                                                    v-bind="scope.itemProps"
                                                >
                                                    <q-item-section>
                                                        <q-item-label>{{
                                                            scope.opt.descricao
                                                        }}</q-item-label>
                                                    </q-item-section>
                                                </q-item>
                                            </template>

                                            <template #after-options>
                                                <div
                                                    v-if="
                                                        hasMoreItems && !loading
                                                    "
                                                    class="q-pa-sm"
                                                >
                                                    <q-btn
                                                        flat
                                                        dense
                                                        color="primary"
                                                        :loading="isFetching"
                                                        @click="loadMoreModels"
                                                        class="full-width"
                                                        icon="expand_more"
                                                        :label="
                                                            $t(
                                                                'buttons.loadMore'
                                                            )
                                                        "
                                                    />
                                                </div>
                                                <div
                                                    v-if="
                                                        loading &&
                                                        options.length === 0
                                                    "
                                                    class="q-pa-sm text-center"
                                                >
                                                    <q-spinner
                                                        color="primary"
                                                        size="24px"
                                                    />
                                                    <div
                                                        class="text-caption q-mt-xs"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.labels.loading'
                                                            )
                                                        }}
                                                    </div>
                                                </div>
                                            </template>

                                            <template #no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            loading
                                                                ? $t(
                                                                      'forms.labels.loading'
                                                                  )
                                                                : currentFilter
                                                                  ? $t(
                                                                        'forms.labels.noResults'
                                                                    )
                                                                  : $t(
                                                                        'forms.labels.typeToSearch'
                                                                    )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>

                                    <!-- Nível de Dificuldade -->
                                    <div class="col-12 col-md-6">
                                        <q-input
                                            v-model.number="nivelDificuldade"
                                            :label="
                                                $t(
                                                    'forms.labels.difficultyLevel'
                                                )
                                            "
                                            type="number"
                                            outlined
                                            dense
                                            stack-label
                                            :error="!!errors.nivelDificuldade"
                                            :error-message="
                                                errors.nivelDificuldade
                                            "
                                            class="form-field"
                                        >
                                            <template #prepend>
                                                <q-icon
                                                    name="trending_up"
                                                    color="primary"
                                                />
                                            </template>
                                        </q-input>
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>

                    <!-- Seção de Anexo -->
                    <div class="form-section">
                        <div class="section-header">
                            <q-icon name="attach_file" class="section-icon" />
                            <span class="section-title">{{
                                $t('forms.labels.attachment')
                            }}</span>
                            <q-chip
                                size="sm"
                                color="grey-4"
                                text-color="grey-8"
                                class="section-chip"
                            >
                                {{ $t('forms.hints.attachment') }}
                            </q-chip>
                        </div>

                        <q-card
                            flat
                            bordered
                            class="section-card attachment-card"
                        >
                            <q-card-section class="q-pa-md">
                                <div class="attachment-container">
                                    <!-- Upload Area -->
                                    <div class="upload-area">
                                        <q-file
                                            v-model="anexo"
                                            :label="
                                                $t('forms.labels.selectFile')
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            accept=".jpg,.jpeg,.png,.pdf,.pfx,.txt,.crt,.ret,.doc,.docx,.xls,.xlsx,.msg"
                                            max-file-size="2097152"
                                            @rejected="onFileRejected"
                                            class="upload-field"
                                        >
                                            <template #prepend>
                                                <q-icon
                                                    name="cloud_upload"
                                                    color="primary"
                                                />
                                            </template>
                                            <template #append>
                                                <q-icon name="attach_file" />
                                            </template>
                                        </q-file>
                                    </div>

                                    <!-- Preview Area -->
                                    <div class="preview-area">
                                        <image-preview
                                            :file="anexo"
                                            :existing-url="
                                                existingAttachmentUrl
                                            "
                                            :existing-base64="
                                                existingAttachmentBase64
                                            "
                                            :file-name="existingAttachmentName"
                                            @view="viewAttachment"
                                            @remove="removeAttachment"
                                        />
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>
                </div>
            </template>
        </form-template>

        <!-- Image Viewer -->
        <image-viewer
            v-model="showImageViewer"
            :image-url="currentImageUrl"
            :title="currentImageTitle"
        />
    </div>
</template>

<script setup lang="ts">
// Utils
import { computed, ref, watch } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'
import { Notify } from 'quasar'
import { useAuthStore } from '@/stores/auth'

const { t: $t } = useI18n()
const authStore = useAuthStore()

// Components
import FormTemplate from '@/components/FormTemplate.vue'
import ImagePreview from '@/components/ImagePreview.vue'
import ImageViewer from '@/components/ImageViewer.vue'

// Types
interface Modelo {
    id: number
    descricao: string
}

interface FileRejection {
    failedPropValidation: string
}

// Props
const props = defineProps<{
    selectedMarca: {
        id: number
        descricao: string
    }
    initialValues?: {
        modelo?: number | undefined
        nivelDificuldade?: number | undefined
        anexo?: File | null | string | undefined
        anexo_base64?: string | undefined
    }
}>()

// Form Schema
const marcaModeloSchema = yup.object({
    marca: yup.string().required($t('validate.requiredBrand')),
    modelo: yup.number().required($t('validate.requiredModel')),
    nivelDificuldade: yup
        .number()
        .required($t('validate.requiredDifficultyLevel'))
})

// Inicializa o formulário com valores padrão ou iniciais
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: marcaModeloSchema,
    initialValues: {
        marca: props.selectedMarca?.descricao || '',
        modelo: props.initialValues?.modelo || undefined,
        nivelDificuldade: props.initialValues?.nivelDificuldade || 1
    },
    validateOnMount: false
})

// Define fields
const [marca] = defineField('marca')
const [modelo] = defineField('modelo')
const [nivelDificuldade] = defineField('nivelDificuldade')

// Função para extrair nome do arquivo da URL
const extractFileNameFromUrl = (url: string): string => {
    try {
        const urlParts = url.split('/')
        const fileName = urlParts[urlParts.length - 1]
        // Remover query parameters se houver
        return fileName?.split('?')[0] || 'arquivo'
    } catch {
        return 'arquivo'
    }
}

// Campo adicional não incluído na validação
const anexo = ref<File | null>(
    props.initialValues?.anexo instanceof File
        ? props.initialValues.anexo
        : null
)

// Variáveis para preview de anexo existente
const existingAttachmentUrl = ref<string | null>(null)
const existingAttachmentBase64 = ref<string | null>(null)
const existingAttachmentName = ref<string | null>(null)

// Inicializar dados do anexo existente
if (props.initialValues?.anexo_base64) {
    // Usar base64 se disponível
    existingAttachmentBase64.value = props.initialValues.anexo_base64
    existingAttachmentName.value =
        typeof props.initialValues.anexo === 'string'
            ? extractFileNameFromUrl(props.initialValues.anexo)
            : 'anexo'
} else if (typeof props.initialValues?.anexo === 'string') {
    // Fallback para URL se base64 não estiver disponível
    existingAttachmentUrl.value = props.initialValues.anexo
    existingAttachmentName.value = extractFileNameFromUrl(
        props.initialValues.anexo
    )
}

// Image Viewer
const showImageViewer = ref(false)
const currentImageUrl = ref('')
const currentImageTitle = ref('')

// Estados para o virtual scroll e paginação
const options = ref<Modelo[]>([])
const loading = ref(false)
const isFetching = ref(false)
const currentFilter = ref('')

// Paginação
const pagination = ref({
    page: 1,
    pageSize: 15,
    total: 0,
    hasMore: true
})

// Computed para verificar se há mais itens
const hasMoreItems = computed(() => {
    return (
        pagination.value.hasMore &&
        options.value.length < pagination.value.total
    )
})

// Função para carregar modelos com paginação
const loadModels = async (reset = true) => {
    if (isFetching.value) return

    isFetching.value = true
    loading.value = true

    try {
        const currentPage = reset ? 1 : pagination.value.page + 1
        const params = {
            page: currentPage,
            page_size: pagination.value.pageSize,
            ...(currentFilter.value && { descricao: currentFilter.value })
        }

        const response = await authStore.asyncRequest({
            endpoint: '/api/corpsystem/produto-service/modelo',
            method: 'get',
            params,
            customErrorMessages: {
                400: $t('forms.errors.loadModelsError'),
                404: $t('forms.errors.loadModelsError'),
                500: $t('forms.errors.loadModelsError')
            }
        })

        const data = response.data

        if (reset) {
            options.value = data.results
            pagination.value.page = 1
        } else {
            options.value = [...options.value, ...data.results]
            pagination.value.page = currentPage
        }

        // Atualizar informações de paginação
        pagination.value.total = data.count
        pagination.value.hasMore = !!data.next
    } catch {
        if (reset) {
            options.value = []
        }
        // Error is already handled by the authStore.asyncRequest
    } finally {
        isFetching.value = false
        loading.value = false
    }
}

// Função de filtro
const onFilter = (val: string, update: (fn: () => void) => void) => {
    currentFilter.value = val
    update(() => {
        pagination.value.page = 1
        pagination.value.hasMore = true
        loadModels(true)
    })
}

// Função para carregar mais modelos (botão "Carregar mais")
const loadMoreModels = async () => {
    if (!isFetching.value && hasMoreItems.value) {
        await loadModels(false) // false = não resetar, carregar próxima página
    }
}

const onFileRejected = (rejectedEntries: FileRejection[]) => {
    const rejection = rejectedEntries[0]

    if (!rejection) return

    let errorMessage = ''

    if (rejection.failedPropValidation === 'max-file-size') {
        errorMessage = $t('forms.errors.maxFileSize')
    } else if (rejection.failedPropValidation === 'accept') {
        errorMessage = $t('forms.errors.invalidFileType')
    }

    Notify.create({
        message: errorMessage,
        color: 'negative',
        position: 'top',
        timeout: 3000,
        icon: 'error'
    })
}

// Métodos para preview de anexo
const viewAttachment = () => {
    if (anexo.value) {
        currentImageUrl.value = URL.createObjectURL(anexo.value)
        currentImageTitle.value = anexo.value.name
    } else if (existingAttachmentBase64.value) {
        currentImageUrl.value = existingAttachmentBase64.value
        currentImageTitle.value = existingAttachmentName.value || 'Anexo'
    } else if (existingAttachmentUrl.value) {
        currentImageUrl.value = existingAttachmentUrl.value
        currentImageTitle.value = existingAttachmentName.value || 'Anexo'
    }
    showImageViewer.value = true
}

const removeAttachment = () => {
    anexo.value = null
    existingAttachmentUrl.value = null
    existingAttachmentBase64.value = null
    existingAttachmentName.value = null
}

// Watch para carregar modelos quando o componente for montado
watch(
    () => props.selectedMarca?.id,
    newId => {
        if (newId && newId > 0) {
            loadModels(true)
        }
    },
    { immediate: true }
)

// Watch para atualizar campo marca quando selectedMarca mudar
watch(
    () => props.selectedMarca?.descricao,
    newDescricao => {
        marca.value = newDescricao || ''
    },
    { immediate: true }
)

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: () => {
        // Criar FormData conforme o curl fornecido
        const formData = new FormData()

        // Adicionar campos conforme o formato da API
        formData.append('id_marca', props.selectedMarca?.id?.toString() || '')
        formData.append('id_modelo', values.modelo?.toString() || '')
        formData.append(
            'nivel_dificuldade',
            values.nivelDificuldade?.toString() || '1'
        )

        // Adicionar anexo se existir
        if (anexo.value) {
            formData.append('anexo', anexo.value)
        }

        return formData
    },
    resetForm: () => {
        resetForm()
        anexo.value = null
        existingAttachmentUrl.value = null
        existingAttachmentBase64.value = null
        existingAttachmentName.value = null
        options.value = []
        pagination.value = {
            page: 1,
            pageSize: 15,
            total: 0,
            hasMore: true
        }
        currentFilter.value = ''
        loadModels(true)
    }
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

/* Layout do formulário */
.form-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-section {
    width: 100%;
}

/* Cabeçalho das seções */
.section-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    padding: 8px 0;
}

.section-icon {
    font-size: 20px;
    color: var(--q-primary);
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--q-dark);
    flex: 1;
}

.section-chip {
    margin-left: auto;
    font-size: 11px;
}

/* Cards das seções */
.section-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.section-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Campos do formulário */
.form-field {
    transition: all 0.3s ease;
}

.form-field:hover {
    transform: translateY(-1px);
}

/* Seção de anexo */
.attachment-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.attachment-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.upload-area {
    width: 100%;
}

.upload-field {
    border: 2px dashed var(--q-primary);
    border-radius: 8px;
    padding: 16px;
    background: rgba(var(--q-primary-rgb), 0.05);
    transition: all 0.3s ease;
}

.upload-field:hover {
    background: rgba(var(--q-primary-rgb), 0.1);
    border-color: var(--q-secondary);
}

.preview-area {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    min-height: 60px;
}

/* Responsividade */
@media (max-width: 768px) {
    .form-container {
        gap: 16px;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .section-chip {
        margin-left: 0;
        align-self: flex-start;
    }

    .attachment-container {
        gap: 16px;
    }
}

/* Animações */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-section {
    animation: fadeInUp 0.5s ease-out;
}

.form-section:nth-child(2) {
    animation-delay: 0.1s;
}

/* Estados dos campos */
.form-field .q-field--error {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%,
    100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

/* Melhorias visuais */
.section-card .q-card__section {
    position: relative;
}

.section-card .q-card__section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--q-primary), var(--q-secondary));
    border-radius: 12px 12px 0 0;
}
</style>
