<template>
    <div id="form-pessoa" class="q-pa-xs">
        <form-template>
            <template #header>
                <q-option-group
                    v-model="tipoPessoa"
                    :options="tiposPessoa"
                    :inline="$q.screen.gt.xs"
                    :dense="$q.screen.gt.xs"
                    dark
                    color="secondary"
                    class="text-white"
                />

                <q-toggle
                    v-model="status"
                    :label="$t('forms.labels.active')"
                    color="secondary"
                    class="no-bottom-spacing text-white"
                    dense
                    :error="!!errors.status"
                />
            </template>

            <template #body>
                <div class="row q-col-gutter-xs">
                    <!-- Campos comuns -->
                    <div class="col-12 col-sm-6 col-md-3 col-lg-2">
                        <q-input
                            v-model="nome"
                            :label="
                                tipoPessoa === 'fisica'
                                    ? $t('forms.labels.name')
                                    : $t('forms.labels.companyName')
                            "
                            outlined
                            v-required-field
                            dense
                            stack-label
                            :error="!!errors.nome"
                            :error-message="errors.nome"
                        />
                    </div>

                    <div class="col-12 col-sm-6 col-md-3 col-lg-2">
                        <q-input
                            v-model="apelido"
                            :label="
                                tipoPessoa === 'fisica'
                                    ? $t('forms.labels.nickname')
                                    : $t('forms.labels.fantasyName')
                            "
                            outlined
                            dense
                            stack-label
                            v-required-field="
                                tipoPessoa === 'juridica' ||
                                tipoPessoa === 'estrangeiro'
                            "
                            :error="!!errors.apelido"
                            :error-message="errors.apelido"
                        />
                    </div>

                    <div
                        v-if="tipoPessoa === 'fisica'"
                        class="col-12 col-sm-6 col-md-3 col-lg-2"
                    >
                        <q-select
                            v-model="estadoCivil"
                            :label="$t('forms.labels.maritalStatus')"
                            :options="estadosCivis"
                            outlined
                            dense
                            stack-label
                            clearable
                            use-input
                            @filter="filterEstadosCivis"
                            emit-value
                            map-options
                            options-dense
                            :error="!!errors.estadoCivil"
                            :error-message="errors.estadoCivil"
                        />
                    </div>

                    <div class="col-12 col-sm-6 col-md-3 col-lg-2">
                        <q-input
                            v-model="documento"
                            :label="
                                tipoPessoa === 'fisica'
                                    ? $t('forms.labels.docNaturalPerson')
                                    : $t('forms.labels.docLegalPerson')
                            "
                            :mask="
                                tipoPessoa === 'fisica'
                                    ? $t('forms.masks.docNaturalPerson')
                                    : $t('forms.masks.docLegalPerson')
                            "
                            outlined
                            dense
                            stack-label
                            v-required-field="tipoPessoa !== 'estrangeiro'"
                            :error="!!errors.documento"
                            :error-message="errors.documento"
                        />
                    </div>

                    <div class="col-12 col-sm-6 col-md-3 col-lg-2">
                        <q-select
                            v-model="indicadorIE"
                            :label="
                                $t(
                                    'forms.labels.indicatorStateEnrollmentRecipient'
                                )
                            "
                            :options="indicadoresIE"
                            outlined
                            dense
                            stack-label
                            emit-value
                            map-options
                            options-dense
                            v-required-field
                            :error="!!errors.indicadorIE"
                            :error-message="errors.indicadorIE"
                        />
                    </div>

                    <div class="col-12 col-sm-6 col-md-3 col-lg-2">
                        <q-input
                            v-model="site"
                            :label="$t('forms.labels.site')"
                            outlined
                            dense
                            stack-label
                            :error="!!errors.site"
                            :error-message="errors.site"
                        />
                    </div>

                    <div class="col-12 col-sm-6 col-md-3 col-lg-2">
                        <q-select
                            v-model="regiao"
                            :label="$t('forms.labels.region')"
                            :options="regioes"
                            outlined
                            dense
                            stack-label
                            clearable
                            use-input
                            @filter="filterRegioes"
                            emit-value
                            map-options
                            options-dense
                            :error="!!errors.regiao"
                            :error-message="errors.regiao"
                        />
                    </div>

                    <!-- Campos específicos para Pessoa Jurídica -->
                    <template
                        v-if="
                            tipoPessoa === 'juridica' ||
                            tipoPessoa === 'estrangeiro'
                        "
                    >
                        <div class="col-12 col-sm-6 col-md-3 col-lg-2">
                            <q-select
                                v-model="estadoIE"
                                :label="
                                    $t('forms.labels.stateRegistrationStatus')
                                "
                                :options="estadosIE"
                                outlined
                                dense
                                stack-label
                                clearable
                                use-input
                                @filter="filterEstadosIE"
                                emit-value
                                map-options
                                options-dense
                                v-required-field="tipoPessoa === 'estrangeiro'"
                                :error="!!errors.estadoIE"
                                :error-message="errors.estadoIE"
                            />
                        </div>

                        <div class="col-12 col-sm-6 col-md-3 col-lg-2">
                            <q-input
                                v-model="inscricaoEstadual"
                                :label="$t('forms.labels.stateRegistration')"
                                outlined
                                dense
                                stack-label
                                :error="!!errors.inscricaoEstadual"
                                :error-message="errors.inscricaoEstadual"
                            />
                        </div>

                        <div class="col-12 col-sm-6 col-md-3 col-lg-2">
                            <q-input
                                v-model="inscricaoMunicipal"
                                :label="
                                    $t('forms.labels.municipalRegistration')
                                "
                                outlined
                                dense
                                stack-label
                                :error="!!errors.inscricaoMunicipal"
                                :error-message="errors.inscricaoMunicipal"
                            />
                        </div>
                    </template>

                    <div class="col-12 col-sm-6 col-md-3 col-lg-2">
                        <q-select
                            v-model="empresa"
                            :label="$t('forms.labels.company')"
                            :options="empresas"
                            outlined
                            dense
                            stack-label
                            clearable
                            use-input
                            @filter="filterEmpresas"
                            emit-value
                            map-options
                            options-dense
                            :error="!!errors.empresa"
                            :error-message="errors.empresa"
                        />
                    </div>

                    <div class="col-12 col-sm-6 col-md-3 col-lg-2">
                        <q-select
                            v-model="ramoAtividade"
                            :label="$t('forms.labels.lineOfActivity')"
                            :options="ramosAtividade"
                            outlined
                            dense
                            stack-label
                            clearable
                            use-input
                            @filter="filterRamosAtividade"
                            emit-value
                            map-options
                            options-dense
                            :error="!!errors.ramoAtividade"
                            :error-message="errors.ramoAtividade"
                        />
                    </div>

                    <div class="col-12 col-sm-6 col-md-3 col-lg-2">
                        <q-input
                            v-model="dataOrigem"
                            :label="
                                tipoPessoa === 'fisica'
                                    ? $t('forms.labels.dateOfBith')
                                    : $t('forms.labels.dateOfFounding')
                            "
                            outlined
                            dense
                            stack-label
                            type="date"
                            :error="!!errors.dataOrigem"
                            :error-message="errors.dataOrigem"
                        />
                    </div>

                    <div class="col-12 col-sm-6 col-md-3 col-lg-2">
                        <q-select
                            v-model="tipoPessoaRelacao"
                            :label="$t('forms.labels.typeOfRelationship')"
                            :options="tiposPessoaRelacao"
                            option-label="descricao"
                            outlined
                            multiple
                            dense
                            stack-label
                            clearable
                            use-input
                            @filter="filterTiposPessoaRelacao"
                            emit-value
                            map-options
                            options-dense
                            :error="!!errors.tipoPessoaRelacao"
                            :error-message="errors.tipoPessoaRelacao"
                        />
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { computed, ref, watch } from 'vue'
import yup from 'src/plugins/schemas/validators'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'
const { t: $t } = useI18n()
// Components
import FormTemplate from '@/components/FormTemplate.vue'

// Props
const props = defineProps({
    initialValues: {
        type: Object,
        default: () => ({})
    }
})

const tiposPessoa = computed(() => [
    { label: $t('forms.labels.naturalPerson'), value: 'fisica' },
    { label: $t('forms.labels.legalPerson'), value: 'juridica' },
    { label: 'Estrangeiro', value: 'estrangeiro' }
])
const pessoaSchema = yup.object({
    tipoPessoa: yup // Campo novo no schema
        .string()
        .oneOf(tiposPessoa.value.map(tipo => tipo.value)),
    status: yup.boolean(),
    nome: yup
        .string()
        .trim()
        .when('tipoPessoa', {
            is: 'fisica',
            then: schema =>
                schema
                    .required($t('validate.requiredName'))
                    .min(3, $t('validate.invalidName')),
            otherwise: schema =>
                schema
                    .required($t('validate.requiredCompanyName'))
                    .min(3, $t('validate.invalidCompanyName'))
        }),
    apelido: yup
        .string()
        .trim()
        .when('tipoPessoa', {
            is: 'juridica',
            then: schema => schema.required($t('validate.requiredFansyName'))
        }),
    documento: yup
        .string()
        .trim()
        .when('tipoPessoa', {
            is: 'fisica',
            then: schema =>
                schema
                    .required($t('validate.docNaturalRequired'))
                    .naturalPerson($t('validate.docNaturalInvalid'))
        })
        .when('tipoPessoa', {
            is: 'juridica',
            then: schema =>
                schema
                    .required($t('validate.docLegalRequired'))
                    .legalPerson($t('validate.docLegalInvalid'))
        })
        .when('tipoPessoa', {
            is: 'estrangeiro',
            then: schema => schema.notRequired()
        }),
    indicadorIE: yup
        .string()
        .required($t('validate.requiredIndicatorStateEnrollmentRecipient')),
    site: yup.string().trim(),
    regiao: yup.string().trim().nullable(),
    empresa: yup.string().trim().nullable(),
    ramoAtividade: yup.string().trim().nullable(),
    tipoPessoaRelacao: yup
        .array()
        .of(
            yup.object().shape({
                id: yup.number().required(),
                descricao: yup.string().trim().nullable()
            })
        )
        .nullable(),
    dataOrigem: yup.string().trim(),
    estadoCivil: yup.string().trim().nullable(),
    estadoIE: yup.string().trim().nullable(),
    inscricaoEstadual: yup.string().trim(),
    inscricaoMunicipal: yup.string().trim()
})

// Inicializa o formulário
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: pessoaSchema,
    initialValues: {
        tipoPessoa: props.initialValues.tipoPessoa || 'fisica',
        nome: props.initialValues.nome || '',
        apelido: props.initialValues.apelido || '',
        estadoCivil: props.initialValues.estadoCivil || null,
        documento: props.initialValues.documento || '',
        indicadorIE: props.initialValues.indicadorIE || null,
        site: props.initialValues.site || '',
        regiao: props.initialValues.regiao || null,
        empresa: props.initialValues.empresa || null,
        ramoAtividade: props.initialValues.ramoAtividade || null,
        status:
            props.initialValues.status !== undefined
                ? props.initialValues.status
                : true,
        tipoPessoaRelacao: props.initialValues.tipoPessoaRelacao || null,
        estadoIE: props.initialValues.estadoIE || null,
        inscricaoEstadual: props.initialValues.inscricaoEstadual || '',
        inscricaoMunicipal: props.initialValues.inscricaoMunicipal || '',
        dataOrigem: props.initialValues.dataOrigem || ''
    },
    validateOnMount: false
})

// Define campos
const [tipoPessoa] = defineField('tipoPessoa')
const [nome] = defineField('nome')
const [apelido] = defineField('apelido')
const [estadoCivil] = defineField('estadoCivil')
const [documento] = defineField('documento')
const [indicadorIE] = defineField('indicadorIE')
const [site] = defineField('site')
const [regiao] = defineField('regiao')
const [empresa] = defineField('empresa')
const [ramoAtividade] = defineField('ramoAtividade')
const [status] = defineField('status')
const [tipoPessoaRelacao] = defineField('tipoPessoaRelacao')
const [estadoIE] = defineField('estadoIE')
const [inscricaoEstadual] = defineField('inscricaoEstadual')
const [inscricaoMunicipal] = defineField('inscricaoMunicipal')
const [dataOrigem] = defineField('dataOrigem')

// Atualiza o schema quando o tipo de pessoa muda
watch(tipoPessoa, () => {
    const tipoPessoaValue = tipoPessoa.value
    resetForm()
    tipoPessoa.value = tipoPessoaValue
})

// Estado Civil options
const estadosCivis = ref([
    { label: 'Solteiro(a)', value: 'solteiro' },
    { label: 'Casado(a)', value: 'casado' },
    { label: 'Divorciado(a)', value: 'divorciado' },
    { label: 'Viúvo(a)', value: 'viuvo' },
    { label: 'Separado(a)', value: 'separado' }
])

// Indicador IE options
const indicadoresIE = [
    { label: 'Contribuinte ICMS', value: 'contribuinte' },
    { label: 'Contribuinte Isento', value: 'isento' },
    { label: 'Não Contribuinte', value: 'nao_contribuinte' }
]

// Regiões (mock - deve vir do banco)
const regioes = ref([
    { label: 'Norte', value: 'norte' },
    { label: 'Sul', value: 'sul' },
    { label: 'Leste', value: 'leste' },
    { label: 'Oeste', value: 'oeste' },
    { label: 'Centro', value: 'centro' }
])

// Estados para Inscrição Estadual (mock - deve vir do banco)
const estadosIE = ref([
    { label: 'Acre', value: 'AC' },
    { label: 'Alagoas', value: 'AL' },
    { label: 'Amapá', value: 'AP' },
    { label: 'Amazonas', value: 'AM' },
    { label: 'Bahia', value: 'BA' },
    { label: 'Ceará', value: 'CE' },
    { label: 'Distrito Federal', value: 'DF' },
    { label: 'Espírito Santo', value: 'ES' },
    { label: 'Goiás', value: 'GO' },
    { label: 'Maranhão', value: 'MA' },
    { label: 'Mato Grosso', value: 'MT' },
    { label: 'Mato Grosso do Sul', value: 'MS' },
    { label: 'Minas Gerais', value: 'MG' },
    { label: 'Pará', value: 'PA' },
    { label: 'Paraíba', value: 'PB' },
    { label: 'Paraná', value: 'PR' },
    { label: 'Pernambuco', value: 'PE' },
    { label: 'Piauí', value: 'PI' },
    { label: 'Rio de Janeiro', value: 'RJ' },
    { label: 'Rio Grande do Norte', value: 'RN' },
    { label: 'Rio Grande do Sul', value: 'RS' },
    { label: 'Rondônia', value: 'RO' },
    { label: 'Roraima', value: 'RR' },
    { label: 'Santa Catarina', value: 'SC' },
    { label: 'São Paulo', value: 'SP' },
    { label: 'Sergipe', value: 'SE' },
    { label: 'Tocantins', value: 'TO' }
])

// Empresas (mock - deve vir do banco)
const empresas = ref([
    { label: 'Empresa A', value: 'empresa_a' },
    { label: 'Empresa B', value: 'empresa_b' },
    { label: 'Empresa C', value: 'empresa_c' }
])

// Ramos de Atividade (mock - deve vir do banco)
const ramosAtividade = ref([
    { label: 'Comércio', value: 'comercio' },
    { label: 'Indústria', value: 'industria' },
    { label: 'Serviços', value: 'servicos' },
    { label: 'Agronegócio', value: 'agronegocio' }
])

// Tipos de Pessoa Relação
const tiposPessoaRelacao = ref([
    {
        id: 1,
        descricao: 'CLIENTE'
    },
    {
        id: 2,
        descricao: 'VENDEDOR'
    },
    {
        id: 3,
        descricao: 'REPRESENTANTE'
    },
    {
        id: 4,
        descricao: 'FORNECEDOR'
    },
    {
        id: 5,
        descricao: 'COMPRADOR'
    },
    {
        id: 6,
        descricao: 'MOTORISTA'
    },
    {
        id: 7,
        descricao: 'TÉCNICO'
    },
    {
        id: 8,
        descricao: 'USUÁRIO'
    },
    {
        id: 9,
        descricao: 'CONTABILISTA'
    },
    {
        id: 10,
        descricao: 'RESPONSÁVEL TÉCNICO'
    },
    {
        id: 11,
        descricao: 'FUNCIONÁRIO'
    },
    {
        id: 12,
        descricao: 'CONTA CRM/PROSPECT'
    }
])

// Funções de filtro para autocomplete
const filterEstadosCivis = (
    val: string,
    update: (callback: () => void) => void
) => {
    if (val === '') {
        update(() => {
            estadosCivis.value = [
                { label: 'Solteiro(a)', value: 'solteiro' },
                { label: 'Casado(a)', value: 'casado' },
                { label: 'Divorciado(a)', value: 'divorciado' },
                { label: 'Viúvo(a)', value: 'viuvo' },
                { label: 'Separado(a)', value: 'separado' }
            ]
        })
    } else {
        update(() => {
            const needle = val.toLowerCase()
            estadosCivis.value = estadosCivis.value.filter(
                v => v.label.toLowerCase().indexOf(needle) > -1
            )
        })
    }
}

const filterRegioes = (val: string, update: (callback: () => void) => void) => {
    if (val === '') {
        update(() => {
            regioes.value = [
                { label: 'Norte', value: 'norte' },
                { label: 'Sul', value: 'sul' },
                { label: 'Leste', value: 'leste' },
                { label: 'Oeste', value: 'oeste' },
                { label: 'Centro', value: 'centro' }
            ]
        })
    } else {
        update(() => {
            const needle = val.toLowerCase()
            regioes.value = regioes.value.filter(
                v => v.label.toLowerCase().indexOf(needle) > -1
            )
        })
    }
}

const filterEmpresas = (
    val: string,
    update: (callback: () => void) => void
) => {
    if (val === '') {
        update(() => {
            empresas.value = [
                { label: 'Empresa A', value: 'empresa_a' },
                { label: 'Empresa B', value: 'empresa_b' },
                { label: 'Empresa C', value: 'empresa_c' }
            ]
        })
    } else {
        update(() => {
            const needle = val.toLowerCase()
            empresas.value = empresas.value.filter(
                v => v.label.toLowerCase().indexOf(needle) > -1
            )
        })
    }
}

const filterRamosAtividade = (
    val: string,
    update: (callback: () => void) => void
) => {
    if (val === '') {
        update(() => {
            ramosAtividade.value = [
                { label: 'Comércio', value: 'comercio' },
                { label: 'Indústria', value: 'industria' },
                { label: 'Serviços', value: 'servicos' },
                { label: 'Agronegócio', value: 'agronegocio' }
            ]
        })
    } else {
        update(() => {
            const needle = val.toLowerCase()
            ramosAtividade.value = ramosAtividade.value.filter(
                v => v.label.toLowerCase().indexOf(needle) > -1
            )
        })
    }
}

const filterTiposPessoaRelacao = (
    val: string,
    update: (callback: () => void) => void
) => {
    if (val === '') {
        update(() => {
            tiposPessoaRelacao.value = [
                {
                    id: 1,
                    descricao: 'CLIENTE'
                },
                {
                    id: 2,
                    descricao: 'VENDEDOR'
                },
                {
                    id: 3,
                    descricao: 'REPRESENTANTE'
                },
                {
                    id: 4,
                    descricao: 'FORNECEDOR'
                },
                {
                    id: 5,
                    descricao: 'COMPRADOR'
                },
                {
                    id: 6,
                    descricao: 'MOTORISTA'
                },
                {
                    id: 7,
                    descricao: 'TÉCNICO'
                },
                {
                    id: 8,
                    descricao: 'USUÁRIO'
                },
                {
                    id: 9,
                    descricao: 'CONTABILISTA'
                },
                {
                    id: 10,
                    descricao: 'RESPONSÁVEL TÉCNICO'
                },
                {
                    id: 11,
                    descricao: 'FUNCIONÁRIO'
                },
                {
                    id: 12,
                    descricao: 'CONTA CRM/PROSPECT'
                }
            ]
        })
    } else {
        update(() => {
            const needle = val.toLowerCase()
            tiposPessoaRelacao.value = tiposPessoaRelacao.value.filter(
                v => v.descricao.toLowerCase().indexOf(needle) > -1
            )
        })
    }
}

const filterEstadosIE = (
    val: string,
    update: (callback: () => void) => void
) => {
    if (val === '') {
        update(() => {
            estadosIE.value = [
                { label: 'Acre', value: 'AC' },
                { label: 'Alagoas', value: 'AL' },
                { label: 'Amapá', value: 'AP' },
                { label: 'Amazonas', value: 'AM' },
                { label: 'Bahia', value: 'BA' },
                { label: 'Ceará', value: 'CE' },
                { label: 'Distrito Federal', value: 'DF' },
                { label: 'Espírito Santo', value: 'ES' },
                { label: 'Goiás', value: 'GO' },
                { label: 'Maranhão', value: 'MA' },
                { label: 'Mato Grosso', value: 'MT' },
                { label: 'Mato Grosso do Sul', value: 'MS' },
                { label: 'Minas Gerais', value: 'MG' },
                { label: 'Pará', value: 'PA' },
                { label: 'Paraíba', value: 'PB' },
                { label: 'Paraná', value: 'PR' },
                { label: 'Pernambuco', value: 'PE' },
                { label: 'Piauí', value: 'PI' },
                { label: 'Rio de Janeiro', value: 'RJ' },
                { label: 'Rio Grande do Norte', value: 'RN' },
                { label: 'Rio Grande do Sul', value: 'RS' },
                { label: 'Rondônia', value: 'RO' },
                { label: 'Roraima', value: 'RR' },
                { label: 'Santa Catarina', value: 'SC' },
                { label: 'São Paulo', value: 'SP' },
                { label: 'Sergipe', value: 'SE' },
                { label: 'Tocantins', value: 'TO' }
            ]
        })
    } else {
        update(() => {
            const needle = val.toLowerCase()
            estadosIE.value = estadosIE.value.filter(
                v => v.label.toLowerCase().indexOf(needle) > -1
            )
        })
    }
}

// Expose form methods and data
defineExpose({
    validate,
    getData: () => ({ ...values }),
    resetForm
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q-field__marginal {
    height: 40px;
}

.q-field--dense .q-field__label {
    top: 10px;
    font-size: 12px;
}

.q-field--dense .q-field__native {
    padding-top: 4px;
    padding-bottom: 0;
    min-height: 24px;
}

.q-field--stack-label .q-field__label {
    font-size: 12px;
    top: 6px;
}

.no-bottom-spacing {
    margin-bottom: 0;
    padding-bottom: 0;
}

.no-bottom-spacing .q-field__control {
    margin-bottom: 0;
}

.no-bottom-spacing .q-field__bottom {
    padding-top: 2px;
    min-height: 12px;
}

.row.q-col-gutter-y-xs > div {
    padding-bottom: 0;
}
</style>
