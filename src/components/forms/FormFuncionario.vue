<template>
    <div id="form-funcionario" class="q-pa-xs">
        <form-template :title="$t('forms.titles.employeeForm')">
            <template #header>
                <div class="row q-col-gutter-xs">
                    <div class="col-auto">
                        <q-toggle
                            v-model="status"
                            :label="$t('forms.labels.active')"
                            color="secondary"
                            class="no-bottom-spacing text-white"
                            dense
                        />
                    </div>
                </div>
            </template>

            <template #body>
                <div class="row q-col-gutter-xs">
                    <!-- Informações Básicas -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.basicInfo') }}
                        </div>
                        <q-card flat bordered class="q-mb-md">
                            <q-card-section class="q-pa-xs">
                                <div class="row q-col-gutter-xs">
                                    <!-- No<PERSON> M<PERSON> -->
                                    <div class="col-12 col-md-3 col-lg-4">
                                        <q-input
                                            v-model="nomeMae"
                                            :label="
                                                $t('forms.labels.motherName')
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            :error="!!errors.nomeMae"
                                            :error-message="errors.nomeMae"
                                        />
                                    </div>

                                    <!-- Data de Admissão -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-input
                                            v-model="dataAdmissao"
                                            :label="
                                                $t('forms.labels.admissionDate')
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            type="date"
                                            :error="!!errors.dataAdmissao"
                                            :error-message="errors.dataAdmissao"
                                        />
                                    </div>

                                    <!-- PIS -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-input
                                            v-model="pis"
                                            :label="$t('forms.labels.pis')"
                                            outlined
                                            dense
                                            stack-label
                                            :error="!!errors.pis"
                                            :error-message="errors.pis"
                                        />
                                    </div>

                                    <!-- Título de Eleitor -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-input
                                            v-model="tituloEleitor"
                                            :label="
                                                $t('forms.labels.voterCard')
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            :error="!!errors.tituloEleitor"
                                            :error-message="
                                                errors.tituloEleitor
                                            "
                                        />
                                    </div>

                                    <!-- Carteira de Trabalho e Social -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-input
                                            v-model="carteiraTrabalho"
                                            :label="$t('forms.labels.workCard')"
                                            outlined
                                            dense
                                            stack-label
                                            :error="!!errors.carteiraTrabalho"
                                            :error-message="
                                                errors.carteiraTrabalho
                                            "
                                        />
                                    </div>

                                    <!-- Gênero (Radio Group) -->
                                    <div class="col-12 col-md-6">
                                        <div class="q-mb-xs">
                                            <label class="text-caption">{{
                                                $t('forms.labels.gender')
                                            }}</label>
                                            <q-option-group
                                                v-model="genero"
                                                :options="[
                                                    {
                                                        label: $t(
                                                            'forms.labels.male'
                                                        ),
                                                        value: 'M'
                                                    },
                                                    {
                                                        label: $t(
                                                            'forms.labels.female'
                                                        ),
                                                        value: 'F'
                                                    }
                                                ]"
                                                color="primary"
                                                inline
                                                dense
                                            />
                                        </div>
                                    </div>

                                    <!-- Responsável de Qualidade (Checkbox) -->
                                    <div class="col-12 col-md-3 col-lg-2">
                                        <q-checkbox
                                            v-model="responsavelQualidade"
                                            :label="
                                                $t(
                                                    'forms.labels.qualityResponsible'
                                                )
                                            "
                                            class="q-mt-sm"
                                        />
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>

                    <!-- Informações Profissionais -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.professionalInfo') }}
                        </div>
                        <q-card flat bordered class="q-mb-md">
                            <q-card-section class="q-pa-xs">
                                <div class="row q-col-gutter-xs">
                                    <!-- Departamento -->
                                    <div class="col-12 col-md-4">
                                        <q-select
                                            v-model="departamento"
                                            :label="
                                                $t('forms.labels.department')
                                            "
                                            :options="
                                                departamentosFilteredOptions
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            options-dense
                                            use-input
                                            input-debounce="300"
                                            @filter="filterDepartamento"
                                            :error="!!errors.departamento"
                                            :error-message="errors.departamento"
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.noResults'
                                                            )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>

                                    <!-- Setor -->
                                    <div class="col-12 col-md-4">
                                        <q-select
                                            v-model="setor"
                                            :label="$t('forms.labels.sector')"
                                            :options="setoresFilteredOptions"
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            options-dense
                                            use-input
                                            input-debounce="300"
                                            @filter="filterSetor"
                                            :error="!!errors.setor"
                                            :error-message="errors.setor"
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.noResults'
                                                            )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>

                                    <!-- Cargo -->
                                    <div class="col-12 col-md-4">
                                        <q-select
                                            v-model="cargo"
                                            :label="$t('forms.labels.position')"
                                            :options="cargosFilteredOptions"
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            options-dense
                                            use-input
                                            input-debounce="300"
                                            @filter="filterCargo"
                                            :error="!!errors.cargo"
                                            :error-message="errors.cargo"
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.noResults'
                                                            )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'

const { t: $t } = useI18n()
// Components
import FormTemplate from '@/components/FormTemplate.vue'

// Props
const props = defineProps({
    initialValues: {
        type: Object,
        default: () => ({})
    }
})

// Opções para os selects (mock - deve vir do banco)
const departamentosOptions = [
    { label: 'Administrativo', value: 'adm' },
    { label: 'Financeiro', value: 'fin' },
    { label: 'Recursos Humanos', value: 'rh' },
    { label: 'Comercial', value: 'com' },
    { label: 'Produção', value: 'prod' },
    { label: 'Tecnologia da Informação', value: 'ti' },
    { label: 'Logística', value: 'log' }
]

const setoresOptions = [
    { label: 'Contabilidade', value: 'cont' },
    { label: 'Compras', value: 'comp' },
    { label: 'Vendas', value: 'vend' },
    { label: 'Desenvolvimento', value: 'dev' },
    { label: 'Suporte', value: 'sup' },
    { label: 'Almoxarifado', value: 'alm' },
    { label: 'Expedição', value: 'exp' }
]

const cargosOptions = [
    { label: 'Analista', value: 'ana' },
    { label: 'Assistente', value: 'ass' },
    { label: 'Coordenador', value: 'coord' },
    { label: 'Gerente', value: 'ger' },
    { label: 'Diretor', value: 'dir' },
    { label: 'Desenvolvedor', value: 'dev' },
    { label: 'Técnico', value: 'tec' }
]

// Form Funcionario Schema
const funcionarioSchema = yup.object({
    nomeMae: yup.string().required($t('validate.requiredMotherName')),
    dataAdmissao: yup.string().required($t('validate.requiredAdmissionDate')),
    pis: yup.string().required($t('validate.requiredPIS')),
    tituloEleitor: yup.string().required($t('validate.requiredVoterCard')),
    carteiraTrabalho: yup.string().required($t('validate.requiredWorkCard')),
    departamento: yup.string().required($t('validate.requiredDepartment')),
    setor: yup.string().required($t('validate.requiredSector')),
    cargo: yup.string().required($t('validate.requiredPosition'))
})

// Inicializa o formulário com valores padrão ou iniciais
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: funcionarioSchema,
    initialValues: {
        nomeMae: props.initialValues.nomeMae || '',
        dataAdmissao: props.initialValues.dataAdmissao || '',
        pis: props.initialValues.pis || '',
        tituloEleitor: props.initialValues.tituloEleitor || '',
        carteiraTrabalho: props.initialValues.carteiraTrabalho || '',
        departamento: props.initialValues.departamento || '',
        setor: props.initialValues.setor || '',
        cargo: props.initialValues.cargo || ''
    },
    validateOnMount: false
})

// Define fields
const [nomeMae] = defineField('nomeMae')
const [dataAdmissao] = defineField('dataAdmissao')
const [pis] = defineField('pis')
const [tituloEleitor] = defineField('tituloEleitor')
const [carteiraTrabalho] = defineField('carteiraTrabalho')
const [departamento] = defineField('departamento')
const [setor] = defineField('setor')
const [cargo] = defineField('cargo')

// Variáveis para armazenar as opções filtradas
const departamentosFilteredOptions = ref([...departamentosOptions])
const setoresFilteredOptions = ref([...setoresOptions])
const cargosFilteredOptions = ref([...cargosOptions])

// Métodos de filtro para os selects
const filterDepartamento = (val: string, update: (fn: () => void) => void) => {
    if (val === '') {
        update(() => {
            departamentosFilteredOptions.value = departamentosOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        departamentosFilteredOptions.value = departamentosOptions.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

const filterSetor = (val: string, update: (fn: () => void) => void) => {
    if (val === '') {
        update(() => {
            setoresFilteredOptions.value = setoresOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        setoresFilteredOptions.value = setoresOptions.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

const filterCargo = (val: string, update: (fn: () => void) => void) => {
    if (val === '') {
        update(() => {
            cargosFilteredOptions.value = cargosOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        cargosFilteredOptions.value = cargosOptions.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

// Campos adicionais não incluídos na validação
const status = ref(
    props.initialValues.status !== undefined ? props.initialValues.status : true
)
const genero = ref(props.initialValues.genero || 'M') // 'M' = masculino, 'F' = feminino
const responsavelQualidade = ref(
    props.initialValues.responsavelQualidade || false
)

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: () => {
        return {
            ...values,
            status: status.value,
            genero: genero.value,
            responsavelQualidade: responsavelQualidade.value
        }
    },
    resetForm
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
