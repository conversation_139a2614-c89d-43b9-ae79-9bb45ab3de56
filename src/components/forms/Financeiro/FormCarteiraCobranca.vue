<template>
    <div id="form-pais" class="q-pa-xs">
        <form-template :title="$t('pages.billingPortfolio.title')">
            <template #header>
                <div class="row q-col-gutter-xs">
                    <div class="col-auto">
                        <q-toggle
                            v-model="ativo"
                            :label="$t('forms.labels.active')"
                            color="secondary"
                            class="no-bottom-spacing text-white"
                            dense
                        />
                    </div>
                </div>
            </template>

            <template #body>
                <div class="row q-col-gutter-xs">
                    <!-- Informações Básicas -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.basicInfo') }}
                        </div>
                        <q-card flat bordered class="q-mb-md">
                            <q-card-section class="q-pa-xs">
                                <div class="row q-col-gutter-md">
                                    <!-- Código -->
                                    <div class="col-12 col-md-6">
                                        <q-input
                                            v-model="codigo"
                                            :label="$t('forms.labels.code')"
                                            outlined
                                            dense
                                            stack-label
                                            v-required-field
                                            :error="!!errors.codigo"
                                            :error-message="errors.codigo"
                                            @update:model-value="onCodigoInput"
                                            maxlength="10"
                                            class="form-field"
                                        >
                                            <template #prepend>
                                                <q-icon
                                                    name="tag"
                                                    color="primary"
                                                />
                                            </template>
                                        </q-input>
                                    </div>

                                    <!-- Descrição -->
                                    <div class="col-12 col-md-6">
                                        <q-input
                                            v-model="descricao"
                                            :label="
                                                $t('forms.labels.description')
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            v-required-field
                                            :error="!!errors.descricao"
                                            :error-message="errors.descricao"
                                            @update:model-value="
                                                onDescricaoInput
                                            "
                                            maxlength="100"
                                            class="form-field"
                                        >
                                            <template #prepend>
                                                <q-icon
                                                    name="description"
                                                    color="primary"
                                                />
                                            </template>
                                        </q-input>
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Vue
import { ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

// Validation
import * as yup from 'yup'
import { useForm } from 'vee-validate'

// Components
import FormTemplate from '@/components/FormTemplate.vue'

// Internationalization
const { t: $t } = useI18n()

// Props
const props = defineProps({
    initialValues: {
        type: Object,
        default: () => ({})
    }
})

// Campo adicional não incluído na validação
const ativo = ref(
    props.initialValues.ativo !== undefined ? props.initialValues.ativo : true
)

// Schema de validação
const paisSchema = yup.object({
    codigo: yup.string().required($t('validate.requiredCode')),
    descricao: yup
        .string()
        .required($t('validate.requiredDescription'))
        .max(100, $t('validate.maxLength', { max: 100 }))
})

// Inicializa o formulário
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: paisSchema,
    initialValues: {
        codigo: props.initialValues.codigo || '',
        descricao: props.initialValues.descricao || ''
    },
    validateOnMount: false
})

// Define fields
const [codigo] = defineField('codigo')
const [descricao] = defineField('descricao')

// Watch para mudanças nos valores iniciais
watch(
    () => props.initialValues,
    newValues => {
        if (newValues && Object.keys(newValues).length > 0) {
            resetForm({
                values: {
                    descricao: newValues.descricao || ''
                }
            })
        }
    },
    { deep: true, immediate: true }
)

const onCodigoInput = (value: string | number | null): void => {
    codigo.value = value ? value.toString().toUpperCase() : ''
}

const onDescricaoInput = (value: string | number | null): void => {
    descricao.value = value ? value.toString().toUpperCase() : ''
}

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: () => {
        return {
            ativo: ativo.value,
            ...values
        }
    },
    resetForm: () => {
        resetForm()
    }
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
