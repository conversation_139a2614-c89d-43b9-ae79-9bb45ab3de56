<template>
    <div id="form-complexo">
        <form-template :title="title || $t('contactForm.titles.main')">
            <template #body>
                <div class="row q-col-gutter-xs">
                    <!-- Informações Básicas -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('contactForm.titles.generalInfo') }}
                        </div>
                        <q-card flat bordered class="q-mb-md">
                            <q-card-section class="q-pa-xs">
                                <div class="row q-col-gutter-xs">
                                    <!-- Nome -->
                                    <div class="col-12 col-md-6 col-lg-3">
                                        <q-input
                                            v-model="nome"
                                            :label="
                                                $t('contactForm.labels.name')
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            :error="!!errors.nome"
                                            :error-message="errors.nome"
                                        />
                                    </div>

                                    <!-- Ocupação -->
                                    <div class="col-12 col-md-6 col-lg-3">
                                        <q-select
                                            v-model="ocupacao"
                                            :label="
                                                $t(
                                                    'contactForm.labels.occupation'
                                                )
                                            "
                                            :options="ocupacoesFilteredOptions"
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            use-input
                                            input-debounce="300"
                                            @filter="filterOcupacao"
                                            :error="!!errors.ocupacao"
                                            :error-message="errors.ocupacao"
                                            clearable
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.labels.noResults'
                                                            )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>

                                    <!-- Departamento -->
                                    <div class="col-12 col-md-6 col-lg-3">
                                        <q-select
                                            v-model="departamento"
                                            :label="
                                                $t(
                                                    'contactForm.labels.department'
                                                )
                                            "
                                            :options="
                                                departamentosFilteredOptions
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            use-input
                                            input-debounce="300"
                                            @filter="filterDepartamento"
                                            :error="!!errors.departamento"
                                            :error-message="errors.departamento"
                                            clearable
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.labels.noResults'
                                                            )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>

                                    <!-- Data de Nascimento -->
                                    <div class="col-12 col-md-6 col-lg-3">
                                        <q-input
                                            v-model="dataNascimento"
                                            :label="
                                                $t(
                                                    'contactForm.labels.birthDate'
                                                )
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            type="date"
                                            :error="!!errors.dataNascimento"
                                            :error-message="
                                                errors.dataNascimento
                                            "
                                        />
                                    </div>

                                    <!-- País -->
                                    <div class="col-12 col-md-4">
                                        <q-select
                                            v-model="pais"
                                            :label="$t('forms.labels.country')"
                                            :options="paisesFilteredOptions"
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            use-input
                                            input-debounce="300"
                                            @filter="filterPaises"
                                            @update:model-value="
                                                atualizarEstados
                                            "
                                            :error="!!errors.pais"
                                            :error-message="errors.pais"
                                            clearable
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.labels.noResults'
                                                            )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>

                                    <!-- Estado -->
                                    <div class="col-12 col-md-4">
                                        <q-select
                                            v-model="estado"
                                            :label="$t('forms.labels.state')"
                                            :options="estadosFilteredOptions"
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            use-input
                                            input-debounce="300"
                                            @filter="filterEstado"
                                            @update:model-value="
                                                atualizarCidades
                                            "
                                            :error="!!errors.estado"
                                            :error-message="errors.estado"
                                            :disable="!pais"
                                            clearable
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.labels.noResults'
                                                            )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>

                                    <!-- Cidade -->
                                    <div class="col-12 col-md-4">
                                        <q-select
                                            v-model="cidade"
                                            :label="$t('forms.labels.city')"
                                            :options="cidadesOptions"
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            use-input
                                            input-debounce="300"
                                            @filter="filterCidade"
                                            :error="!!errors.cidade"
                                            :error-message="errors.cidade"
                                            :disable="!estado"
                                            clearable
                                        >
                                            <template v-slot:no-option>
                                                <q-item>
                                                    <q-item-section
                                                        class="text-grey"
                                                    >
                                                        {{
                                                            $t(
                                                                'forms.labels.noResults'
                                                            )
                                                        }}
                                                    </q-item-section>
                                                </q-item>
                                            </template>
                                        </q-select>
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>

                    <!-- Telefones -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('contactForm.titles.phones') }}
                        </div>
                        <q-card flat bordered class="q-mb-md">
                            <q-card-section class="q-pa-xs">
                                <div class="row items-center q-mb-xs">
                                    <div class="col">
                                        <!-- Título removido para evitar duplicação -->
                                    </div>
                                    <div class="col-auto">
                                        <q-btn
                                            @click="adicionarTelefone"
                                            color="primary"
                                            icon="add"
                                            :label="
                                                $t(
                                                    'contactForm.labels.addPhone'
                                                )
                                            "
                                            unelevated
                                            size="sm"
                                        />
                                    </div>
                                </div>

                                <div class="q-mb-md"></div>

                                <div
                                    v-if="telefones.length === 0"
                                    class="text-center q-pa-lg text-grey-5"
                                >
                                    <div class="flex justify-center q-mb-xs">
                                        <q-icon
                                            name="phone_disabled"
                                            size="32px"
                                        />
                                    </div>
                                    <div class="text-body2 q-mb-xs">
                                        {{
                                            $t('contactForm.messages.noPhones')
                                        }}
                                    </div>
                                    <div class="text-caption text-grey-6">
                                        {{
                                            $t(
                                                'contactForm.messages.noPhoneHint'
                                            )
                                        }}
                                    </div>
                                </div>

                                <div v-else class="q-gutter-xs">
                                    <q-card
                                        v-for="(telefone, index) in telefones"
                                        :key="telefone.id || index"
                                        flat
                                        bordered
                                        class="q-pa-xs"
                                    >
                                        <div class="row q-col-gutter-xs">
                                            <!-- Formulário -->
                                            <div class="col-12 col-md-11">
                                                <!-- Primeira linha: Tipo e WhatsApp -->
                                                <div
                                                    class="row q-col-gutter-xs items-start q-mb-xs"
                                                >
                                                    <!-- Tipo -->
                                                    <div
                                                        class="col-12 col-lg-8 col-md-6 col-sm-6"
                                                    >
                                                        <q-select
                                                            v-model="
                                                                telefone.tipo
                                                            "
                                                            :label="
                                                                $t(
                                                                    'contactForm.labels.phoneType'
                                                                )
                                                            "
                                                            :options="
                                                                tiposTelefoneOptions
                                                            "
                                                            outlined
                                                            dense
                                                            stack-label
                                                            emit-value
                                                            map-options
                                                            :error="
                                                                !!getPhoneError(
                                                                    index,
                                                                    'tipo'
                                                                )
                                                            "
                                                            :error-message="
                                                                getPhoneError(
                                                                    index,
                                                                    'tipo'
                                                                )
                                                            "
                                                            clearable
                                                        />
                                                    </div>

                                                    <!-- WhatsApp Checkbox -->
                                                    <div
                                                        class="col-12 col-lg-4 col-md-6 col-sm-6 flex justify-center"
                                                    >
                                                        <q-checkbox
                                                            v-model="
                                                                telefone.whatsapp
                                                            "
                                                            :label="
                                                                $t(
                                                                    'contactForm.labels.whatsappMessages'
                                                                )
                                                            "
                                                            color="green"
                                                            dense
                                                            class="q-mt-sm"
                                                        />
                                                    </div>
                                                </div>

                                                <!-- Segunda linha: Resto dos campos -->
                                                <div
                                                    class="row q-col-gutter-xs items-end"
                                                >
                                                    <!-- País -->
                                                    <div
                                                        class="col-12 col-sm-6 col-md-3"
                                                    >
                                                        <q-select
                                                            v-model="
                                                                telefone.codigoPais
                                                            "
                                                            :label="
                                                                $t(
                                                                    'contactForm.labels.countryCode'
                                                                )
                                                            "
                                                            :options="
                                                                paisesFilteredOptions
                                                            "
                                                            outlined
                                                            dense
                                                            stack-label
                                                            emit-value
                                                            map-options
                                                            use-input
                                                            input-debounce="300"
                                                            @filter="
                                                                filterPaises
                                                            "
                                                            :error="
                                                                !!getPhoneError(
                                                                    index,
                                                                    'codigoPais'
                                                                )
                                                            "
                                                            :error-message="
                                                                getPhoneError(
                                                                    index,
                                                                    'codigoPais'
                                                                )
                                                            "
                                                            clearable
                                                        />
                                                    </div>

                                                    <!-- DDD -->
                                                    <div
                                                        class="col-12 col-sm-6 col-md-2"
                                                    >
                                                        <q-input
                                                            v-model="
                                                                telefone.ddd
                                                            "
                                                            :label="
                                                                $t(
                                                                    'contactForm.labels.areaCode'
                                                                )
                                                            "
                                                            outlined
                                                            dense
                                                            stack-label
                                                            mask="##"
                                                            :error="
                                                                !!getPhoneError(
                                                                    index,
                                                                    'ddd'
                                                                )
                                                            "
                                                            :error-message="
                                                                getPhoneError(
                                                                    index,
                                                                    'ddd'
                                                                )
                                                            "
                                                        />
                                                    </div>

                                                    <!-- Número -->
                                                    <div
                                                        class="col-12 col-sm-6 col-md-4"
                                                    >
                                                        <q-input
                                                            v-model="
                                                                telefone.numero
                                                            "
                                                            :label="
                                                                $t(
                                                                    'contactForm.labels.phoneNumber'
                                                                )
                                                            "
                                                            outlined
                                                            dense
                                                            stack-label
                                                            :mask="
                                                                getPhoneMask(
                                                                    telefone.codigoPais
                                                                )
                                                            "
                                                            :error="
                                                                !!getPhoneError(
                                                                    index,
                                                                    'numero'
                                                                )
                                                            "
                                                            :error-message="
                                                                getPhoneError(
                                                                    index,
                                                                    'numero'
                                                                )
                                                            "
                                                        />
                                                    </div>

                                                    <!-- Ramal -->
                                                    <div
                                                        class="col-12 col-sm-6 col-md-3"
                                                    >
                                                        <q-input
                                                            v-model="
                                                                telefone.ramal
                                                            "
                                                            :label="
                                                                $t(
                                                                    'contactForm.labels.extension'
                                                                )
                                                            "
                                                            outlined
                                                            dense
                                                            stack-label
                                                            mask="####"
                                                            :error="
                                                                !!getPhoneError(
                                                                    index,
                                                                    'ramal'
                                                                )
                                                            "
                                                            :error-message="
                                                                getPhoneError(
                                                                    index,
                                                                    'ramal'
                                                                )
                                                            "
                                                        />
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Botão Remover -->
                                            <div
                                                class="col-12 col-md-1 flex justify-center items-center"
                                            >
                                                <q-btn
                                                    @click="
                                                        removerTelefone(index)
                                                    "
                                                    color="negative"
                                                    icon="delete"
                                                    flat
                                                    round
                                                    size="sm"
                                                />
                                            </div>
                                        </div>
                                    </q-card>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>

                    <!-- E-mails -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('contactForm.titles.emails') }}
                        </div>
                        <q-card flat bordered class="q-mb-md">
                            <q-card-section class="q-pa-xs">
                                <div class="row items-center q-mb-xs">
                                    <div class="col">
                                        <!-- Título removido para evitar duplicação -->
                                    </div>
                                    <div class="col-auto">
                                        <q-btn
                                            @click="adicionarEmail"
                                            color="primary"
                                            icon="add"
                                            :label="
                                                $t(
                                                    'contactForm.labels.addEmail'
                                                )
                                            "
                                            unelevated
                                            size="sm"
                                        />
                                    </div>
                                </div>

                                <div class="q-mb-md"></div>

                                <div
                                    v-if="emails.length === 0"
                                    class="text-center q-pa-lg text-grey-5"
                                >
                                    <div class="flex justify-center q-mb-xs">
                                        <q-icon
                                            name="unsubscribe"
                                            size="32px"
                                        />
                                    </div>
                                    <div class="text-body2 q-mb-xs">
                                        {{
                                            $t('contactForm.messages.noEmails')
                                        }}
                                    </div>
                                    <div class="text-caption text-grey-6">
                                        {{
                                            $t(
                                                'contactForm.messages.noEmailHint'
                                            )
                                        }}
                                    </div>
                                </div>

                                <div v-else class="q-gutter-xs">
                                    <q-card
                                        v-for="(email, index) in emails"
                                        :key="email.id || index"
                                        flat
                                        bordered
                                        class="q-pa-xs"
                                    >
                                        <div class="row q-col-gutter-xs">
                                            <!-- E-mail -->
                                            <div class="col-12 col-md-11">
                                                <q-input
                                                    v-model="email.email"
                                                    :label="
                                                        $t(
                                                            'contactForm.labels.email'
                                                        )
                                                    "
                                                    outlined
                                                    dense
                                                    stack-label
                                                    type="email"
                                                    :error="
                                                        !!getEmailError(
                                                            index,
                                                            'email'
                                                        )
                                                    "
                                                    :error-message="
                                                        getEmailError(
                                                            index,
                                                            'email'
                                                        )
                                                    "
                                                />
                                            </div>

                                            <!-- Botão Remover -->
                                            <div
                                                class="col-12 col-md-1 flex justify-center items-center"
                                            >
                                                <q-btn
                                                    @click="removerEmail(index)"
                                                    color="negative"
                                                    icon="delete"
                                                    flat
                                                    round
                                                    size="sm"
                                                />
                                            </div>
                                        </div>
                                    </q-card>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref, onMounted } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'
import { Notify, Dialog } from 'quasar'

const { t: $t, locale } = useI18n()

// Components
import FormTemplate from '@/components/FormTemplate.vue'

// Types
import type {
    FormContatoData,
    FormContatoProps,
    TelefoneData,
    EmailData,
    OptionItem
} from '@/interfaces/FormContato'

// Mock data (dados virão do backend)
const ocupacoesMock = [
    {
        label: $t('contactForm.occupations.dev_frontend'),
        value: 'dev_frontend'
    },
    { label: $t('contactForm.occupations.dev_backend'), value: 'dev_backend' },
    {
        label: $t('contactForm.occupations.dev_fullstack'),
        value: 'dev_fullstack'
    },
    {
        label: $t('contactForm.occupations.analista_sistemas'),
        value: 'analista_sistemas'
    },
    {
        label: $t('contactForm.occupations.gerente_projetos'),
        value: 'gerente_projetos'
    },
    {
        label: $t('contactForm.occupations.designer_ux_ui'),
        value: 'designer_ux_ui'
    },
    {
        label: $t('contactForm.occupations.analista_dados'),
        value: 'analista_dados'
    },
    { label: $t('contactForm.occupations.devops'), value: 'devops' },
    {
        label: $t('contactForm.occupations.product_manager'),
        value: 'product_manager'
    },
    { label: $t('contactForm.occupations.scrum_master'), value: 'scrum_master' }
]

const departamentosMock = [
    { label: $t('contactForm.departments.ti'), value: 'ti' },
    { label: $t('contactForm.departments.rh'), value: 'rh' },
    { label: $t('contactForm.departments.financeiro'), value: 'financeiro' },
    { label: $t('contactForm.departments.comercial'), value: 'comercial' },
    { label: $t('contactForm.departments.marketing'), value: 'marketing' },
    { label: $t('contactForm.departments.operacoes'), value: 'operacoes' },
    { label: $t('contactForm.departments.juridico'), value: 'juridico' },
    { label: $t('contactForm.departments.compras'), value: 'compras' }
]

const tiposTelefoneMock = [
    {
        label: $t('contactForm.phoneTypes.celular'),
        value: 'celular',
        icone: 'smartphone'
    },
    {
        label: $t('contactForm.phoneTypes.residencial'),
        value: 'residencial',
        icone: 'home'
    },
    {
        label: $t('contactForm.phoneTypes.comercial'),
        value: 'comercial',
        icone: 'business'
    },
    {
        label: $t('contactForm.phoneTypes.whatsapp'),
        value: 'whatsapp',
        icone: 'chat'
    },
    {
        label: $t('contactForm.phoneTypes.outro'),
        value: 'outro',
        icone: 'phone'
    }
]

// Services
import {
    getCountries,
    getStatesByCountry,
    getCitiesByState,
    getLatinAmericaCountries,
    getPhoneMaskByCountryWithoutDDD
} from '@/services/locationService'

// Props
const props = withDefaults(defineProps<FormContatoProps>(), {
    title: '',
    readonly: false,
    initialValues: () => ({
        nome: '',
        ocupacao: '',
        departamento: '',
        pais: '',
        estado: '',
        cidade: '',
        dataNascimento: '',
        telefones: [],
        emails: []
    })
})

// Estado da aba ativa removido - layout compacto sem abas

// Dados dinâmicos da API
const paisesOptions = ref<OptionItem[]>([])
const estadosOptions = ref<OptionItem[]>([])
const cidadesOptions = ref<OptionItem[]>([])

// País selecionado para controlar estados
const paisSelecionado = ref('BR') // Brasil como padrão

// Opções para os selects (usando dados mockados)
const ocupacoesOptions = ocupacoesMock
const departamentosOptions = departamentosMock
const tiposTelefoneOptions = tiposTelefoneMock.map(tipo => ({
    label: tipo.label,
    value: tipo.value
}))

// Estados filtrados para os selects
const ocupacoesFilteredOptions = ref<OptionItem[]>([...ocupacoesOptions])
const departamentosFilteredOptions = ref<OptionItem[]>([
    ...departamentosOptions
])
const estadosFilteredOptions = ref<OptionItem[]>([])
const paisesFilteredOptions = ref<OptionItem[]>([])
const paisesLatinAmericaOptions = ref<OptionItem[]>([])

// Arrays para telefones e emails
const telefones = ref<TelefoneData[]>(props.initialValues.telefones || [])
const emails = ref<EmailData[]>(props.initialValues.emails || [])

// Carregar dados da API
const carregarPaises = (): void => {
    paisesOptions.value = getCountries(locale.value)
    paisesFilteredOptions.value = [...paisesOptions.value]
    paisesLatinAmericaOptions.value = getLatinAmericaCountries(locale.value)
}

const carregarEstados = (paisCodigo: string): void => {
    estadosOptions.value = getStatesByCountry(paisCodigo)
    estadosFilteredOptions.value = [...estadosOptions.value]
}

const carregarCidades = (paisCodigo: string, estadoCodigo: string): void => {
    cidadesOptions.value = getCitiesByState(paisCodigo, estadoCodigo)
}

// Inicializar dados
carregarPaises()

// Carregar estados baseado no país inicial
const paisInicial = props.initialValues.pais || 'BR'
carregarEstados(paisInicial)
paisSelecionado.value = paisInicial

// Carregar cidades imediatamente se há estado inicial
if (props.initialValues.estado) {
    carregarCidades(paisInicial, props.initialValues.estado)
}

// Carregar cidades se há estado inicial (backup no onMounted)
onMounted(() => {
    if (props.initialValues.estado && cidadesOptions.value.length === 0) {
        const paisAtual = pais.value || paisInicial
        carregarCidades(paisAtual, props.initialValues.estado)
    }
})

// Schema de validação
const formSchema = yup.object({
    nome: yup.string().required($t('contactForm.validation.requiredName')),
    ocupacao: yup
        .string()
        .required($t('contactForm.validation.requiredOccupation')),
    departamento: yup
        .string()
        .required($t('contactForm.validation.requiredDepartment')),
    pais: yup.string().required($t('validate.requiredCountry')),
    estado: yup.string().required($t('validate.requiredState')),
    cidade: yup.string().required($t('validate.requiredCity')),
    dataNascimento: yup
        .string()
        .required($t('contactForm.validation.requiredBirthDate'))
})

// Inicializar formulário
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: formSchema,
    initialValues: {
        nome: props.initialValues.nome || '',
        ocupacao: props.initialValues.ocupacao || '',
        departamento: props.initialValues.departamento || '',
        pais: props.initialValues.pais || 'BR',
        estado: props.initialValues.estado || '',
        cidade: props.initialValues.cidade || '',
        dataNascimento: props.initialValues.dataNascimento || ''
    },
    validateOnMount: false
})

// Define fields
const [nome] = defineField('nome')
const [ocupacao] = defineField('ocupacao')
const [departamento] = defineField('departamento')
const [pais] = defineField('pais')
const [estado] = defineField('estado')
const [cidade] = defineField('cidade')
const [dataNascimento] = defineField('dataNascimento')

// Métodos de filtro para os selects
const filterOcupacao = (
    val: string,
    update: (fn: () => void) => void
): void => {
    if (val === '') {
        update(() => {
            ocupacoesFilteredOptions.value = ocupacoesOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        ocupacoesFilteredOptions.value = ocupacoesOptions.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

const filterDepartamento = (
    val: string,
    update: (fn: () => void) => void
): void => {
    if (val === '') {
        update(() => {
            departamentosFilteredOptions.value = departamentosOptions
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        departamentosFilteredOptions.value = departamentosOptions.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

const filterEstado = (val: string, update: (fn: () => void) => void): void => {
    if (val === '') {
        update(() => {
            estadosFilteredOptions.value = estadosOptions.value
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        estadosFilteredOptions.value = estadosOptions.value.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

const filterCidade = (val: string, update: (fn: () => void) => void): void => {
    if (val === '') {
        update(() => {
            if (estado.value && pais.value) {
                carregarCidades(pais.value, estado.value)
            }
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        const todasCidades = getCitiesByState(pais.value, estado.value)
        cidadesOptions.value = todasCidades.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

const filterPaises = (val: string, update: (fn: () => void) => void): void => {
    if (val === '') {
        update(() => {
            paisesFilteredOptions.value = paisesOptions.value
        })
        return
    }

    update(() => {
        const needle = val.toLowerCase()
        paisesFilteredOptions.value = paisesOptions.value.filter(
            v => v.label.toLowerCase().indexOf(needle) > -1
        )
    })
}

// Atualizar estados quando o país muda
const atualizarEstados = (): void => {
    estado.value = ''
    cidade.value = ''
    if (pais.value) {
        carregarEstados(pais.value)
        paisSelecionado.value = pais.value
    } else {
        estadosOptions.value = []
        cidadesOptions.value = []
    }
}

// Atualizar cidades quando o estado muda
const atualizarCidades = (): void => {
    cidade.value = ''
    if (estado.value && pais.value) {
        carregarCidades(pais.value, estado.value)
    } else {
        cidadesOptions.value = []
    }
}

// Métodos para gerenciar telefones
const adicionarTelefone = (): void => {
    const novoTelefone: TelefoneData = {
        id: Date.now().toString(),
        tipo: '',
        codigoPais: 'BR',
        ddd: '',
        numero: '',
        ramal: '',
        whatsapp: false
    }
    telefones.value.push(novoTelefone)

    Notify.create({
        message: $t('contactForm.messages.phoneAdded'),
        color: 'positive',
        position: 'bottom',
        timeout: 2000,
        icon: 'check_circle'
    })
}

const removerTelefone = (index: number): void => {
    if (index < 0 || index >= telefones.value.length) {
        return
    }

    Dialog.create({
        title: $t('contactForm.labels.removePhone'),
        message: $t('contactForm.messages.confirmRemovePhone'),
        cancel: true,
        persistent: true
    }).onOk(() => {
        telefones.value.splice(index, 1)

        Notify.create({
            message: $t('contactForm.messages.phoneRemoved'),
            color: 'positive',
            position: 'bottom',
            timeout: 2000,
            icon: 'check_circle'
        })
    })
}

// Métodos para gerenciar emails
const adicionarEmail = (): void => {
    const novoEmail: EmailData = {
        id: Date.now().toString(),
        email: ''
    }
    emails.value.push(novoEmail)

    Notify.create({
        message: $t('contactForm.messages.emailAdded'),
        color: 'positive',
        position: 'bottom',
        timeout: 2000,
        icon: 'check_circle'
    })
}

const removerEmail = (index: number): void => {
    if (index < 0 || index >= emails.value.length) {
        return
    }

    Dialog.create({
        title: $t('contactForm.labels.removeEmail'),
        message: $t('contactForm.messages.confirmRemoveEmail'),
        cancel: true,
        persistent: true
    }).onOk(() => {
        emails.value.splice(index, 1)

        Notify.create({
            message: $t('contactForm.messages.emailRemoved'),
            color: 'positive',
            position: 'bottom',
            timeout: 2000,
            icon: 'check_circle'
        })
    })
}

// Métodos utilitários

const getPhoneMask = (codigoPais: string): string => {
    return getPhoneMaskByCountryWithoutDDD(codigoPais)
}

// Métodos para validação de telefones e emails
const getPhoneError = (index: number, field: keyof TelefoneData): string => {
    const telefone = telefones.value[index]
    if (!telefone) return ''

    switch (field) {
        case 'tipo':
            return !telefone.tipo
                ? $t('contactForm.validation.requiredPhoneType')
                : ''
        case 'codigoPais':
            return !telefone.codigoPais
                ? $t('contactForm.validation.requiredCountryCode')
                : ''
        case 'ddd':
            return !telefone.ddd
                ? $t('contactForm.validation.requiredAreaCode')
                : ''
        case 'numero':
            return !telefone.numero
                ? $t('contactForm.validation.requiredPhoneNumber')
                : ''
        case 'ramal':
            // Ramal é opcional, mas se preenchido deve ter pelo menos 2 dígitos
            if (
                telefone.ramal &&
                telefone.ramal.length > 0 &&
                telefone.ramal.length < 2
            ) {
                return $t('contactForm.validation.invalidExtension')
            }
            return ''
        default:
            return ''
    }
}

const getEmailError = (index: number, field: keyof EmailData): string => {
    const email = emails.value[index]
    if (!email) return ''

    switch (field) {
        case 'email':
            if (!email.email) return $t('contactForm.validation.requiredEmail')
            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.email)) {
                return $t('contactForm.validation.invalidEmail')
            }
            return ''
        default:
            return ''
    }
}

// Validação customizada para arrays
const validateArrays = (): boolean => {
    let isValid = true

    // Validar se há pelo menos um telefone
    if (telefones.value.length === 0) {
        Notify.create({
            message: $t('contactForm.validation.minPhones'),
            color: 'negative',
            position: 'bottom',
            timeout: 3000,
            icon: 'error'
        })
        isValid = false
    }

    // Validar se há pelo menos um email
    if (emails.value.length === 0) {
        Notify.create({
            message: $t('contactForm.validation.minEmails'),
            color: 'negative',
            position: 'bottom',
            timeout: 3000,
            icon: 'error'
        })
        isValid = false
    }

    // Validar cada telefone
    telefones.value.forEach(telefone => {
        if (
            !telefone.tipo ||
            !telefone.codigoPais ||
            !telefone.ddd ||
            !telefone.numero
        ) {
            isValid = false
        }
    })

    // Validar cada email
    emails.value.forEach(email => {
        if (!email.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.email)) {
            isValid = false
        }
    })

    return isValid
}

// Validação completa do formulário
const validateCompleteForm = async () => {
    const formValid = await validate()
    const arraysValid = validateArrays()

    return {
        valid: formValid.valid && arraysValid,
        errors: formValid.errors
    }
}

// Expor métodos e dados do formulário
defineExpose({
    validate: validateCompleteForm,
    getData: () => {
        return {
            ...values,
            telefones: telefones.value,
            emails: emails.value
        } as FormContatoData
    },
    resetForm: () => {
        resetForm()
        telefones.value = []
        emails.value = []
    }
})
</script>
