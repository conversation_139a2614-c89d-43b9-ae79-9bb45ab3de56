<template>
    <div id="form-novidades" class="q-pa-xs">
        <form-template :title="$t('forms.titles.newsForm')">
            <template #header>
                <div class="row q-col-gutter-xs">
                    <div class="col-auto">
                        <q-toggle
                            v-model="ativo"
                            :label="$t('forms.labels.active')"
                            color="secondary"
                            class="no-bottom-spacing text-white"
                            dense
                        />
                    </div>
                </div>
            </template>

            <template #body>
                <div class="row q-col-gutter-xs">
                    <!-- Informações Básicas -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.basicInfo') }}
                        </div>
                        <q-card flat bordered class="q-mb-md">
                            <q-card-section class="q-pa-xs">
                                <div class="row q-col-gutter-xs">
                                    <!-- Título -->
                                    <div class="col-12 col-md-6">
                                        <q-input
                                            v-model="titulo"
                                            :label="$t('forms.labels.title')"
                                            outlined
                                            dense
                                            stack-label
                                            v-required-field
                                            :error="!!errors.titulo"
                                            :error-message="errors.titulo"
                                            @update:model-value="onTituloInput"
                                        />
                                    </div>

                                    <!-- Módulo -->
                                    <div class="col-12 col-md-3">
                                        <q-select
                                            v-model="modulo"
                                            :options="modulosOptions"
                                            :label="$t('forms.labels.module')"
                                            outlined
                                            dense
                                            stack-label
                                            v-required-field
                                            :error="!!errors.modulo"
                                            :error-message="errors.modulo"
                                            emit-value
                                            map-options
                                            @update:model-value="onModuloInput"
                                        />
                                    </div>

                                    <!-- Rota -->
                                    <div class="col-12 col-md-3">
                                        <q-select
                                            v-model="rota"
                                            :options="rotasOptions"
                                            :label="$t('forms.labels.route')"
                                            :placeholder="
                                                !modulo
                                                    ? $t(
                                                          'forms.placeholders.selectModuleFirst'
                                                      )
                                                    : $t(
                                                          'forms.placeholders.selectRoute'
                                                      )
                                            "
                                            outlined
                                            dense
                                            stack-label
                                            v-required-field
                                            :error="!!errors.rota"
                                            :error-message="errors.rota"
                                            :disable="!modulo"
                                            emit-value
                                            map-options
                                            @update:model-value="onRotaInput"
                                        />
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>

                    <!-- Descrição -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.description') }}
                        </div>
                        <q-card flat bordered>
                            <q-card-section class="q-pa-xs">
                                <quill-rich-text-editor
                                    v-model="descricao"
                                    :placeholder="
                                        $t('forms.placeholders.newsDescription')
                                    "
                                    :min-height="'300px'"
                                    :max-height="'500px'"
                                    :show-char-count="true"
                                    :max-length="5000"
                                />
                                <div
                                    v-if="errors.descricao"
                                    class="text-negative text-caption q-mt-xs"
                                >
                                    {{ errors.descricao }}
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref, computed, onMounted } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'

const { t: $t } = useI18n()

// Components
import FormTemplate from '@/components/FormTemplate.vue'
import QuillRichTextEditor from '@/components/QuillRichTextEditor.vue'

// Composables
import { usePermissions } from '@/composables/usePermissions'

// Types
import type {
    FormNovidadesProps,
    FormNovidadesData,
    ModuloOption
} from '@/interfaces/Novidades'

// Props
const props = defineProps<FormNovidadesProps>()

// Composable para acessar dados do sistema
const { systemModules, fetchSystemModules } = usePermissions()

// Opções de módulos baseadas nos dados do sistema
const modulosOptions = computed((): ModuloOption[] => {
    return systemModules.value
        .filter(module => module.ativo) // Apenas módulos ativos
        .map(module => ({
            label: module.descricao,
            value: module.descricao
        }))
        .sort((a, b) => a.label.localeCompare(b.label)) // Ordenar alfabeticamente
})

// Opções de rotas baseadas no módulo selecionado
const rotasOptions = computed((): ModuloOption[] => {
    if (!modulo.value) {
        return [] // Se nenhum módulo selecionado, não mostrar rotas
    }

    const selectedModule = systemModules.value.find(
        module => module.ativo && module.descricao === modulo.value
    )

    if (!selectedModule) {
        return []
    }

    return selectedModule.rota
        .filter(route => route.ativo) // Apenas rotas ativas
        .map(route => ({
            label: route.descricao,
            value: route.descricao
        }))
        .sort((a, b) => a.label.localeCompare(b.label)) // Ordenar alfabeticamente
})

// Schema de validação
const novidadesSchema = yup.object({
    titulo: yup.string().required($t('validate.requiredTitle')),
    descricao: yup.string().required($t('validate.requiredDescription')),
    modulo: yup.string().required($t('validate.requiredModule')),
    rota: yup.string().required($t('validate.requiredRoute'))
})

// Inicializa o formulário
const { values, errors, validate, defineField, resetForm } = useForm({
    validationSchema: novidadesSchema,
    initialValues: {
        titulo: props.initialValues?.titulo || '',
        descricao: props.initialValues?.descricao || '',
        modulo: props.initialValues?.modulo || '',
        rota: props.initialValues?.rota || ''
    },
    validateOnMount: false
})

// Define fields
const [titulo] = defineField('titulo')
const [descricao] = defineField('descricao')
const [modulo] = defineField('modulo')
const [rota] = defineField('rota')

// Campo adicional não incluído na validação
const ativo = ref(
    props.initialValues?.ativo !== undefined ? props.initialValues.ativo : true
)

// Métodos para converter texto para uppercase
const onTituloInput = (value: string | number | null): void => {
    titulo.value = value ? value.toString().toUpperCase() : ''
}

const onModuloInput = (value: string | null): void => {
    modulo.value = value || ''
    // Limpar a rota selecionada quando o módulo for alterado
    rota.value = ''
}

const onRotaInput = (value: string | null): void => {
    rota.value = value || ''
}

// Lifecycle - Carregar dados do sistema
onMounted(async () => {
    // Carregar módulos e rotas do sistema se ainda não estiverem carregados
    if (systemModules.value.length === 0) {
        await fetchSystemModules()
    }
})

// Expor métodos e dados do formulário
defineExpose({
    validate,
    getData: (): FormNovidadesData => {
        return {
            titulo: values.titulo,
            descricao: values.descricao,
            modulo: values.modulo,
            rota: values.rota,
            ativo: ativo.value
        }
    },
    resetForm: () => {
        resetForm()
        ativo.value = true
    }
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

.no-bottom-spacing {
    margin-bottom: 0;
}
</style>
