<template>
    <q-form ref="formRef" @submit.prevent="handleSubmit">
        <div class="row q-col-gutter-md">
            <!-- Título -->
            <div class="col-12">
                <q-input
                    v-model="formData.titulo"
                    :label="$t('forms.labels.title')"
                    outlined
                    dense
                    :rules="[
                        val => !!val || $t('forms.validation.required'),
                        val =>
                            val.length >= 3 ||
                            $t('forms.validation.minLength', { min: 3 }),
                        val =>
                            val.length <= 200 ||
                            $t('forms.validation.maxLength', { max: 200 })
                    ]"
                    @update:model-value="
                        val =>
                            (formData.titulo =
                                (typeof val === 'string'
                                    ? val.toUpperCase()
                                    : '') || '')
                    "
                    bg-color="white"
                />
            </div>

            <!-- Módulo e Rota -->
            <div class="col-md-6 col-12">
                <q-select
                    v-model="formData.modulo"
                    :options="modulosOptions"
                    :label="$t('forms.labels.module')"
                    outlined
                    dense
                    emit-value
                    map-options
                    :rules="[val => !!val || $t('forms.validation.required')]"
                    bg-color="white"
                    @update:model-value="onModuloChange"
                />
            </div>

            <div class="col-md-6 col-12">
                <q-select
                    v-model="formData.rota"
                    :options="rotasOptions"
                    :label="$t('forms.labels.route')"
                    outlined
                    dense
                    emit-value
                    map-options
                    :rules="[val => !!val || $t('forms.validation.required')]"
                    :placeholder="$t('forms.placeholders.selectRoute')"
                    bg-color="white"
                    :disable="!formData.modulo"
                />
            </div>

            <!-- Conteúdo -->
            <div class="col-12">
                <q-field
                    :label="$t('forms.labels.content')"
                    outlined
                    dense
                    bg-color="white"
                    :rules="[
                        () =>
                            !!formData.conteudo ||
                            $t('forms.validation.required'),
                        () =>
                            formData.conteudo.length >= 10 ||
                            $t('forms.validation.minLength', { min: 10 })
                    ]"
                >
                    <template v-slot:control>
                        <QuillRichTextEditor
                            v-model="formData.conteudo"
                            :placeholder="$t('forms.placeholders.helpContent')"
                            style="min-height: 200px"
                        />
                    </template>
                </q-field>
            </div>

            <!-- Status -->
            <div class="col-12">
                <q-toggle
                    v-model="formData.ativo"
                    :label="$t('forms.labels.active')"
                    color="positive"
                />
            </div>
        </div>
    </q-form>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { usePermissions } from '@/composables/usePermissions'
import QuillRichTextEditor from '@/components/QuillRichTextEditor.vue'
import type { FormHelpProps, FormHelpData } from '@/interfaces/Help'

const { t: $t } = useI18n()

// Props
const props = withDefaults(defineProps<FormHelpProps>(), {
    initialValues: () => ({})
})

// Emits
const emit = defineEmits<{
    submit: [data: FormHelpData]
}>()

// Refs
const formRef = ref()

// Composables
const { systemModules } = usePermissions()

// Form data
const formData = ref<FormHelpData>({
    titulo: props.initialValues.titulo || '',
    conteudo: props.initialValues.conteudo || '',
    modulo: props.initialValues.modulo || '',
    rota: props.initialValues.rota || '',
    ativo:
        props.initialValues.ativo !== undefined
            ? props.initialValues.ativo
            : true
})

// Computed
const modulosOptions = computed(() => {
    return systemModules.value
        .filter(module => module.ativo)
        .map(module => ({
            label: module.descricao,
            value: module.descricao
        }))
})

// Opções de rotas baseadas no módulo selecionado
const rotasOptions = computed(() => {
    if (!formData.value.modulo) {
        return [] // Se nenhum módulo selecionado, não mostrar rotas
    }

    const selectedModule = systemModules.value.find(
        module => module.ativo && module.descricao === formData.value.modulo
    )

    if (!selectedModule) {
        return []
    }

    return selectedModule.rota
        .filter(route => route.ativo) // Apenas rotas ativas
        .map(route => ({
            label: route.descricao,
            value: route.descricao
        }))
        .sort((a, b) => a.label.localeCompare(b.label)) // Ordenar alfabeticamente
})

// Métodos
const onModuloChange = () => {
    // Limpar rota quando módulo muda
    formData.value.rota = ''
}

const handleSubmit = async () => {
    const isValid = await formRef.value?.validate()
    if (isValid) {
        emit('submit', formData.value)
    }
}

const validate = async () => {
    return await formRef.value?.validate()
}

const resetForm = () => {
    formData.value = {
        titulo: '',
        conteudo: '',
        modulo: '',
        rota: '',
        ativo: true
    }
    formRef.value?.resetValidation()
}

const getData = () => {
    return formData.value
}

// Expor métodos
defineExpose({
    validate,
    resetForm,
    getData,
    handleSubmit
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

:deep(.q-field__control) {
    min-height: 40px;
}
</style>
