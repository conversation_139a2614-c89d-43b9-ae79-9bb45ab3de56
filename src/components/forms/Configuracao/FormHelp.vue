<template>
    <div id="form-help" class="q-pa-xs">
        <form-template :title="$t('forms.titles.helpForm')">
            <template #header>
                <div class="row q-col-gutter-xs">
                    <div class="col-auto">
                        <q-toggle
                            v-model="ativo"
                            :label="$t('forms.labels.active')"
                            color="secondary"
                            class="no-bottom-spacing text-white"
                            dense
                        />
                    </div>
                </div>
            </template>

            <template #body>
                <div class="row q-col-gutter-xs">
                    <!-- Informações Básicas -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.basicInfo') }}
                        </div>
                        <q-card flat bordered class="q-mb-md">
                            <q-card-section class="q-pa-xs">
                                <div class="row q-col-gutter-xs">
                                    <!-- Título -->
                                    <div class="col-12">
                                        <q-input
                                            v-model="titulo"
                                            :label="$t('forms.labels.title')"
                                            outlined
                                            dense
                                            stack-label
                                            v-required-field
                                            :error="!!errors.titulo"
                                            :error-message="errors.titulo"
                                            @update:model-value="onTituloInput"
                                        />
                                    </div>

                                    <!-- Módulo -->
                                    <div class="col-12 col-md-6">
                                        <q-select
                                            v-model="modulo"
                                            :options="modulosOptions"
                                            :label="$t('forms.labels.module')"
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            v-required-field
                                            :error="!!errors.modulo"
                                            :error-message="errors.modulo"
                                            @update:model-value="onModuloInput"
                                        />
                                    </div>

                                    <!-- Rota -->
                                    <div class="col-12 col-md-6">
                                        <q-select
                                            v-model="rota"
                                            :options="rotasOptions"
                                            :label="$t('forms.labels.route')"
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            v-required-field
                                            :error="!!errors.rota"
                                            :error-message="errors.rota"
                                            :disable="!modulo"
                                            :placeholder="
                                                $t(
                                                    'forms.placeholders.selectRoute'
                                                )
                                            "
                                            @update:model-value="onRotaInput"
                                        />
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>

                    <!-- Conteúdo -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.content') }}
                        </div>
                        <q-card flat bordered>
                            <q-card-section class="q-pa-xs">
                                <quill-rich-text-editor
                                    v-model="conteudo"
                                    :placeholder="
                                        $t('forms.placeholders.helpContent')
                                    "
                                    :min-height="'300px'"
                                    :max-height="'500px'"
                                    :show-char-count="true"
                                    :max-length="5000"
                                />
                                <div
                                    v-if="errors.conteudo"
                                    class="text-negative text-caption q-mt-xs"
                                >
                                    {{ errors.conteudo }}
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref, computed } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'

const { t: $t } = useI18n()

// Components
import FormTemplate from '@/components/FormTemplate.vue'
import QuillRichTextEditor from '@/components/QuillRichTextEditor.vue'

// Composables
import { usePermissions } from '@/composables/usePermissions'

// Types
import type { FormHelpProps, FormHelpData } from '@/interfaces/Help'

// Props
const props = withDefaults(defineProps<FormHelpProps>(), {
    initialValues: () => ({})
})

// Composables
const { systemModules } = usePermissions()

// Schema de validação
const helpSchema = yup.object({
    titulo: yup
        .string()
        .trim()
        .required($t('validate.requiredTitle'))
        .min(3, $t('validate.minLength', { min: 3 }))
        .max(200, $t('validate.maxLength', { max: 200 })),
    modulo: yup.string().required($t('validate.requiredModule')),
    rota: yup.string().required($t('validate.requiredRoute')),
    conteudo: yup
        .string()
        .trim()
        .required($t('validate.requiredContent'))
        .min(10, $t('validate.minLength', { min: 10 }))
})

// Inicializa o formulário
const { errors, validate, defineField, resetForm } = useForm({
    validationSchema: helpSchema,
    initialValues: {
        titulo: props.initialValues.titulo || '',
        modulo: props.initialValues.modulo || '',
        rota: props.initialValues.rota || '',
        conteudo: props.initialValues.conteudo || ''
    },
    validateOnMount: false
})

// Define fields
const [titulo] = defineField('titulo')
const [modulo] = defineField('modulo')
const [rota] = defineField('rota')
const [conteudo] = defineField('conteudo')

// Campo adicional não incluído na validação
const ativo = ref(
    props.initialValues.ativo !== undefined ? props.initialValues.ativo : true
)

// Computed
const modulosOptions = computed(() => {
    return systemModules.value
        .filter(module => module.ativo)
        .map(module => ({
            label: module.descricao,
            value: module.descricao
        }))
})

// Opções de rotas baseadas no módulo selecionado
const rotasOptions = computed(() => {
    if (!modulo.value) {
        return [] // Se nenhum módulo selecionado, não mostrar rotas
    }

    const selectedModule = systemModules.value.find(
        module => module.ativo && module.descricao === modulo.value
    )

    if (!selectedModule) {
        return []
    }

    return selectedModule.rota
        .filter(route => route.ativo) // Apenas rotas ativas
        .map(route => ({
            label: route.descricao,
            value: route.descricao
        }))
        .sort((a, b) => a.label.localeCompare(b.label)) // Ordenar alfabeticamente
})

// Métodos de input
const onTituloInput = (value: string | number | null) => {
    if (typeof value === 'string') {
        titulo.value = value.toUpperCase()
    }
}

const onModuloInput = () => {
    // Limpar rota quando módulo muda
    rota.value = ''
}

const onRotaInput = () => {
    // Método para rota se necessário
}

// Métodos expostos
const validateForm = async () => {
    const { valid } = await validate()
    return valid
}

const resetFormData = () => {
    resetForm()
    ativo.value = true
}

const getFormData = (): FormHelpData => {
    return {
        titulo: titulo.value || '',
        modulo: modulo.value || '',
        rota: rota.value || '',
        conteudo: conteudo.value || '',
        ativo: ativo.value
    }
}

// Expose
defineExpose({
    validate: validateForm,
    resetForm: resetFormData,
    getFormData
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

:deep(.q-field__control) {
    min-height: 40px;
}
</style>
