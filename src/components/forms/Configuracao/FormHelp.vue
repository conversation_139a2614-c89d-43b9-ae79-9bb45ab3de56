<template>
    <div id="form-help" class="q-pa-xs">
        <form-template :title="$t('forms.titles.helpForm')">
            <template #header>
                <div class="row q-col-gutter-xs">
                    <div class="col-auto">
                        <q-toggle
                            v-model="ativo"
                            :label="$t('forms.labels.active')"
                            color="secondary"
                            class="no-bottom-spacing text-white"
                            dense
                        />
                    </div>
                </div>
            </template>

            <template #body>
                <div class="row q-col-gutter-xs">
                    <!-- Informações Básicas -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.basicInfo') }}
                        </div>
                        <q-card flat bordered class="q-mb-md">
                            <q-card-section class="q-pa-xs">
                                <div class="row q-col-gutter-xs">
                                    <!-- Título -->
                                    <div class="col-12 col-md-8">
                                        <q-input
                                            v-model="titulo"
                                            :label="$t('forms.labels.title')"
                                            outlined
                                            dense
                                            stack-label
                                            v-required-field
                                            :error="!!errors.titulo"
                                            :error-message="errors.titulo"
                                            @update:model-value="onTituloInput"
                                        />
                                    </div>

                                    <!-- Rota -->
                                    <div class="col-12 col-md-4">
                                        <q-select
                                            v-model="rota"
                                            :options="rotasOptions"
                                            :label="$t('forms.labels.route')"
                                            outlined
                                            dense
                                            stack-label
                                            emit-value
                                            map-options
                                            v-required-field
                                            :error="!!errors.rota"
                                            :error-message="errors.rota"
                                            @update:model-value="onRotaInput"
                                        />
                                    </div>
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>

                    <!-- Conteúdo -->
                    <div class="col-12">
                        <div class="text-caption q-mb-xs text-center">
                            {{ $t('forms.titles.content') }}
                        </div>
                        <q-card flat bordered>
                            <q-card-section class="q-pa-xs">
                                <quill-rich-text-editor
                                    v-model="conteudo"
                                    :placeholder="
                                        $t('forms.placeholders.helpContent')
                                    "
                                    :min-height="'300px'"
                                    :max-height="'500px'"
                                    :show-char-count="true"
                                    :max-length="5000"
                                />
                                <div
                                    v-if="errors.conteudo"
                                    class="text-negative text-caption q-mt-xs"
                                >
                                    {{ errors.conteudo }}
                                </div>
                            </q-card-section>
                        </q-card>
                    </div>
                </div>
            </template>
        </form-template>
    </div>
</template>

<script setup lang="ts">
// Utils
import { ref, computed } from 'vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'

const { t: $t } = useI18n()

// Components
import FormTemplate from '@/components/FormTemplate.vue'
import QuillRichTextEditor from '@/components/QuillRichTextEditor.vue'

// Composables
import { usePermissions } from '@/composables/usePermissions'

// Types
import type { FormHelpProps, FormHelpData } from '@/interfaces/Help'

// Props
const props = withDefaults(defineProps<FormHelpProps>(), {
    initialValues: () => ({})
})

// Composables
const { systemModules } = usePermissions()

// Schema de validação
const helpSchema = yup.object({
    titulo: yup
        .string()
        .trim()
        .required($t('validate.requiredTitle'))
        .min(3, $t('validate.minLength', { min: 3 }))
        .max(200, $t('validate.maxLength', { max: 200 })),
    rota: yup.string().required($t('validate.requiredRoute')),
    conteudo: yup
        .string()
        .required($t('validate.requiredContent'))
        .test('not-empty-html', $t('validate.requiredContent'), value => {
            if (!value) return false
            // Remove tags HTML e espaços para verificar se há conteúdo real
            const textContent = value.replace(/<[^>]*>/g, '').trim()
            return textContent.length >= 10
        })
})

// Inicializa o formulário
const { errors, validate, defineField, resetForm } = useForm({
    validationSchema: helpSchema,
    initialValues: {
        titulo: props.initialValues.titulo || '',
        rota: props.initialValues.rota || '',
        conteudo: props.initialValues.conteudo || ''
    },
    validateOnMount: false
})

// Define fields
const [titulo] = defineField('titulo')
const [rota] = defineField('rota')
const [conteudo] = defineField('conteudo')

// Campo adicional não incluído na validação
const ativo = ref(
    props.initialValues.ativo !== undefined ? props.initialValues.ativo : true
)

// Computed - Todas as rotas disponíveis no sistema
const rotasOptions = computed(() => {
    const allRoutes: Array<{ label: string; value: string }> = []

    systemModules.value
        .filter(module => module.ativo)
        .forEach(module => {
            module.rota
                .filter(route => route.ativo)
                .forEach(route => {
                    allRoutes.push({
                        label: `${module.descricao} - ${route.descricao}`,
                        value: route.descricao
                    })
                })
        })

    return allRoutes.sort((a, b) => a.label.localeCompare(b.label))
})

// Métodos de input
const onTituloInput = (value: string | number | null) => {
    if (typeof value === 'string') {
        titulo.value = value.toUpperCase()
    }
}

const onRotaInput = () => {
    // Método para rota se necessário
}

// Métodos expostos
const validateForm = async () => {
    const result = await validate()
    return result
}

const resetFormData = () => {
    resetForm()
    ativo.value = true
}

const getFormData = (): FormHelpData => {
    return {
        titulo: titulo.value || '',
        rota: rota.value || '',
        conteudo: conteudo.value || '',
        ativo: ativo.value
    }
}

// Expose
defineExpose({
    validate: validateForm,
    resetForm: resetFormData,
    getFormData
})
</script>

<style scoped>
.q-field--dense .q-field__control,
.q-field--dense .q_field__marginal {
    height: 36px;
}

:deep(.q-field__control) {
    min-height: 40px;
}

/* Melhorar comportamento das imagens no editor */
:deep(.ql-editor img) {
    max-width: 100% !important;
    height: auto !important;
    display: block;
    margin: 8px auto;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Garantir que o conteúdo do editor não ultrapasse os limites */
:deep(.ql-editor) {
    overflow-x: auto;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Melhorar o container do editor */
:deep(.ql-container) {
    overflow: hidden;
}

/* Estilo para tabelas no editor */
:deep(.ql-editor table) {
    max-width: 100%;
    table-layout: auto;
    border-collapse: collapse;
}

:deep(.ql-editor table td, .ql-editor table th) {
    word-wrap: break-word;
    overflow-wrap: break-word;
    max-width: 200px;
}
</style>
