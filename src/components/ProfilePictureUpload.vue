<template>
    <div class="profile-picture-upload">
        <div class="current-photo" v-if="previewUrl || model">
            <q-avatar size="120px">
                <img
                    :src="previewUrl || model || ''"
                    alt="Foto de perfil"
                    @error="handleImageError"
                />

                <div class="upload-overlay" @click="triggerFileInput">
                    <q-icon name="photo_camera" size="24px" />
                    <span>Alterar foto</span>
                </div>
            </q-avatar>
        </div>

        <div v-else class="no-photo" @click="triggerFileInput">
            <q-avatar size="120px" color="grey-4" text-color="grey-8">
                <q-icon name="person" size="60px" />
                <div class="add-photo-hint">
                    <q-icon name="add_a_photo" size="20px" />
                </div>
            </q-avatar>
        </div>

        <input
            ref="fileInput"
            type="file"
            accept="image/*"
            style="display: none"
            @change="handleFileChange"
        />

        <q-btn
            v-if="model || previewUrl"
            flat
            dense
            color="negative"
            icon="delete"
            :label="$t('forms.labels.remove')"
            @click="removePhoto"
            class="q-mt-sm remove-btn"
            size="sm"
        />

        <div v-if="errorMessage" class="text-negative q-mt-xs">
            {{ errorMessage }}
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps({
    maxFileSize: {
        type: Number,
        default: 2 // 2MB
    },
    allowedTypes: {
        type: Array as () => string[],
        default: () => ['image/jpeg', 'image/png', 'image/gif']
    },
    baseUrl: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['upload'])

const model = defineModel<string | null>({
    default: null
})

const fileInput = ref<HTMLInputElement | null>(null)
const previewUrl = ref<string | null>(null)
const errorMessage = ref<string | null>(null)
const imageError = ref(false)

const triggerFileInput = () => {
    imageError.value = false
    fileInput.value?.click()
}

const handleFileChange = (event: Event) => {
    errorMessage.value = null
    const target = event.target as HTMLInputElement
    const file = target.files?.[0]

    if (!file) return

    if (!props.allowedTypes.includes(file.type)) {
        errorMessage.value = `Tipo de arquivo não suportado. Use: ${props.allowedTypes.join(', ')}`
        return
    }

    const fileSizeMB = file.size / (1024 * 1024)
    if (fileSizeMB > props.maxFileSize) {
        errorMessage.value = `Arquivo muito grande. Tamanho máximo: ${props.maxFileSize}MB`
        return
    }

    const reader = new FileReader()
    reader.onload = e => {
        previewUrl.value = e.target?.result as string
        model.value = previewUrl.value
        emit('upload', file)
    }
    reader.readAsDataURL(file)

    target.value = ''
}

const handleImageError = () => {
    imageError.value = true
}

const removePhoto = () => {
    previewUrl.value = null
    model.value = null
    emit('upload', null)
}

watch(model, newVal => {
    if (!newVal) {
        previewUrl.value = null
    }
    imageError.value = false
})
</script>

<style scoped>
.profile-picture-upload {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.current-photo {
    position: relative;
    cursor: pointer;
}

.upload-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s;
    border-radius: 50%;
}

.upload-overlay:hover {
    opacity: 1;
}

.upload-overlay span {
    font-size: 12px;
    margin-top: 5px;
}

.no-photo {
    cursor: pointer;
    position: relative;
}

.no-photo .q-avatar {
    border: 1px dashed var(--q-grey-5);
    transition: all 0.3s ease;
}

.no-photo:hover .q-avatar {
    background-color: var(--q-grey-3);
    border-color: var(--q-primary);
}

.add-photo-hint {
    position: absolute;
    bottom: 5px;
    right: 5px;
    background: var(--q-primary);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-btn {
    padding: 0.25em 0.5em;
    font-size: 0.75rem;
    min-height: 1.75em;
}

.remove-btn .q-icon {
    font-size: 1em;
    margin-right: 0.25em;
}
</style>
