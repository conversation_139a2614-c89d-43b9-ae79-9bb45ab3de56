<template>
    <q-dialog
        v-model="showModal"
        :maximized="maximized"
        :persistent="persistent"
        :transition-show="transitionShow"
        :transition-hide="transitionHide"
    >
        <q-card :class="cardClass" :style="cardStyle">
            <!-- Header padrão -->
            <q-card-section
                v-if="title"
                class="row items-center q-pa-md bg-primary text-white"
            >
                <div class="text-h5">
                    {{ title }}
                </div>
                <q-space />
                <q-btn
                    icon="close"
                    flat
                    round
                    dense
                    @click="closeModal"
                    class="text-white"
                />
            </q-card-section>

            <!-- Header secund<PERSON>rio (opcional) -->
            <q-card-section
                v-if="$slots.subheader"
                class="q-pa-md border-bottom"
            >
                <slot name="subheader"></slot>
            </q-card-section>

            <!-- Toolbar (opcional) -->
            <q-card-section v-if="$slots.toolbar" class="q-pa-xs q-pb-none">
                <q-card flat bordered class="toolbar-container q-mb-xs q-pa-xs">
                    <slot name="toolbar"></slot>
                </q-card>
            </q-card-section>

            <!-- Conteúdo principal -->
            <q-card-section v-if="$slots.default" :class="contentClass">
                <slot></slot>
            </q-card-section>

            <!-- Ações do modal (opcional) -->
            <q-card-actions v-if="$slots.actions" align="right" class="q-pa-md">
                <slot name="actions"></slot>
            </q-card-actions>
        </q-card>
    </q-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

// Props
interface Props {
    modelValue: boolean
    title?: string
    maximized?: boolean
    persistent?: boolean
    transitionShow?: string
    transitionHide?: string
    cardClass?: string
    cardStyle?: string | Record<string, string>
    contentClass?: string
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    title: '',
    maximized: false,
    persistent: true,
    transitionShow: 'scale',
    transitionHide: 'scale',
    cardClass: '',
    cardStyle: 'min-width: 400px; max-width: 800px; width: 90vw',
    contentClass: 'q-pa-xs'
})

// Emits
const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    close: []
}>()

// Refs
const showModal = ref(false)

// Watch
watch(
    () => props.modelValue,
    newVal => {
        showModal.value = newVal
    },
    { immediate: true }
)

watch(showModal, newVal => {
    emit('update:modelValue', newVal)
    if (!newVal) {
        emit('close')
    }
})

// Methods
const closeModal = () => {
    showModal.value = false
}

// Expose methods
defineExpose({
    closeModal
})
</script>

<style scoped>
.border-bottom {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.toolbar-container {
    border-radius: 4px;
}
</style>
