<template>
    <q-btn-dropdown
        :color="color"
        :icon="icon"
        :label="label"
        :size="size"
        :outline="outline"
        :flat="flat"
        :dense="dense"
        :disable="disable || !hasActiveActions"
        :loading="loading"
        :dropdown-icon="dropdownIcon"
        auto-close
        v-bind="$attrs"
    >
        <q-list>
            <template v-for="(action, index) in activeActions" :key="index">
                <q-item
                    clickable
                    v-close-popup
                    @click="executeAction(action)"
                    :disable="action.disable"
                >
                    <q-item-section v-if="action.icon" avatar>
                        <q-icon :name="action.icon" :color="action.iconColor" />
                    </q-item-section>

                    <q-item-section>
                        <q-item-label>{{ action.label }}</q-item-label>
                        <q-item-label v-if="action.description" caption>
                            {{ action.description }}
                        </q-item-label>
                    </q-item-section>

                    <q-item-section v-if="action.shortcut" side>
                        <q-item-label caption class="text-grey-6">
                            {{ action.shortcut }}
                        </q-item-label>
                    </q-item-section>
                </q-item>

                <!-- Separador se especificado -->
                <q-separator
                    v-if="action.separator && index < activeActions.length - 1"
                    :key="`separator-${index}`"
                />
            </template>
        </q-list>

        <!-- Slot para conteúdo customizado -->
        <template v-if="$slots.default">
            <q-separator />
            <slot />
        </template>
    </q-btn-dropdown>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Types
interface ActionItem {
    label: string
    active: boolean
    action: (...args: unknown[]) => void | Promise<void>
    icon?: string
    iconColor?: string
    description?: string
    shortcut?: string
    separator?: boolean
    disable?: boolean
    params?: unknown[]
}

// Props
interface Props {
    actions: ActionItem[]
    color?: string
    icon?: string
    label?: string
    size?: string
    outline?: boolean
    flat?: boolean
    dense?: boolean
    disable?: boolean
    loading?: boolean
    dropdownIcon?: string
    anchor?: string
    self?: string
}

const props = withDefaults(defineProps<Props>(), {
    color: 'primary',
    icon: 'more_vert',
    label: '',
    size: 'md',
    outline: false,
    flat: false,
    dense: false,
    disable: false,
    loading: false,
    dropdownIcon: 'arrow_drop_down',
    anchor: 'bottom left',
    self: 'top left'
})

// Emits
interface Emits {
    (e: 'action-executed', action: ActionItem, result?: unknown): void
    (e: 'action-error', action: ActionItem, error: Error): void
}

const emit = defineEmits<Emits>()

// Computed
const activeActions = computed(() => {
    return props.actions.filter(action => action.active)
})

const hasActiveActions = computed(() => {
    return activeActions.value.length > 0
})

// Methods
const executeAction = async (action: ActionItem) => {
    try {
        let result: unknown

        if (action.params && action.params.length > 0) {
            result = await action.action(...action.params)
        } else {
            result = await action.action()
        }

        emit('action-executed', action, result)
    } catch (error) {
        emit('action-error', action, error as Error)
    }
}
</script>

<style scoped>
/* Estilos customizados se necessário */
.q-btn-dropdown :deep(.q-btn-dropdown__arrow) {
    transition: transform 0.3s ease;
}

.q-btn-dropdown.q-btn--active :deep(.q-btn-dropdown__arrow) {
    transform: rotate(180deg);
}

/* Melhorar visual dos itens */
.q-item {
    min-height: 40px;
    padding: 8px 16px;
}

.q-item:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

.body--dark .q-item:hover {
    background-color: rgba(255, 255, 255, 0.04);
}

/* Ícones com espaçamento adequado */
.q-item-section--avatar {
    min-width: 32px;
    padding-right: 12px;
}

/* Shortcuts com estilo discreto */
.q-item-section--side .q-item-label {
    font-size: 11px;
    font-family: monospace;
}
</style>
