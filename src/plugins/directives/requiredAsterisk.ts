import type { DirectiveBinding } from 'vue'

const RequiredAsterisk = {
    mounted(el: HTMLElement, binding: DirectiveBinding) {
        if (binding.value !== false) {
            addAsteriskToLabel(el)
        }
    },
    updated(el: HTMLElement, binding: DirectiveBinding) {
        if (binding.value !== false) {
            addAsteriskToLabel(el)
        } else {
            removeAsteriskFromLabel(el)
        }
    }
}

function addAsteriskToLabel(el: HTMLElement) {
    // Encontra o elemento da label do Quasar
    const label = el.querySelector('.q-field__label') as HTMLElement

    if (label && !label.innerHTML.includes('*')) {
        label.innerHTML +=
            '<span class="required-asterisk" style="color: red"> *</span>'
    }
}

function removeAsteriskFromLabel(el: HTMLElement) {
    const label = el.querySelector('.q-field__label') as HTMLElement
    if (label) {
        label.innerHTML = label.innerHTML.replace(
            '<span class="required-asterisk" style="color: red"> *</span>',
            ''
        )
    }
}

export default RequiredAsterisk
