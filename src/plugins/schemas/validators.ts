import * as yup from 'yup'
import { i18n } from '@/boot/i18n'

const lang = i18n.global.locale.value

// Adiciona tipos para os métodos personalizados do Yup
declare module 'yup' {
    interface StringSchema {
        naturalPerson(message?: string): StringSchema
        legalPerson(message?: string): StringSchema
    }
}

/**
 * Valida CPF (formato: 123.456.789-00)
 * @param cpf
 * @returns
 */
function validateCPF(cpf: string) {
    cpf = cpf.replace(/[^\d]+/g, '')
    if (cpf.length !== 11 || !!cpf.match(/(\d)\1{10}/)) return false

    const digits = cpf.split('').map(x => parseInt(x))
    const rest = (count: number) =>
        ((digits
            .slice(0, count - 12)
            .reduce((sum, el, index) => sum + el * (count - index), 0) *
            10) %
            11) %
        10

    return rest(10) === digits[9] && rest(11) === digits[10]
}

/**
 * Valida CNPJ (formato: 12.345.678/0001-90)
 * @param cnpj
 * @returns
 */
function validateCNPJ(cnpj: string) {
    cnpj = cnpj.replace(/[^\d]+/g, '')
    if (cnpj.length !== 14 || !!cnpj.match(/(\d)\1{13}/)) return false

    const digits = cnpj.split('').map(x => parseInt(x))
    const calc = (x: number) => {
        const slice = digits.slice(0, x)
        let factor = x - 7
        let sum = 0

        for (let i = x; i >= 1; i--) {
            const n = slice[x - i] ?? 0
            sum += n * factor--
            if (factor < 2) factor = 9
        }

        const result = 11 - (sum % 11)
        return result > 9 ? 0 : result
    }
    return calc(12) === digits[12] && calc(13) === digits[13]
}

/**
 * Valida RUC do Paraguai (formato: 1234567-8)
 * @param {string} ruc
 * @returns {boolean}
 */
function validarRUCParaguai(ruc: string): boolean {
    const regexFormat: RegExp = /^(\d|\.|-)+$/
    const regexAdjust: RegExp = /\D/gi
    const baseMod = 11

    if (!ruc) return false

    if (!regexFormat.test(ruc)) return false

    const adjustedRuc = ruc.replace(regexAdjust, '')
    if (![8, 9].includes(adjustedRuc.length)) return false

    const identityDigitsLength: number = adjustedRuc.length - 2
    let valueSum = 0
    let increasingSequenceQtd = 0
    let decreasingSequenceQtd = 0
    let equalsDigitQtd = 0
    for (
        let index = identityDigitsLength, multiplier = 2;
        index >= 0;
        index -= 1, multiplier += 1
    ) {
        valueSum += Number(adjustedRuc[index]) * multiplier

        if (index > 0) {
            const previousDigit = Number(adjustedRuc[index - 1])
            const currentDigit = Number(adjustedRuc[index])
            if (previousDigit === currentDigit) equalsDigitQtd += 1

            if (currentDigit > previousDigit) increasingSequenceQtd += 1

            if (previousDigit > currentDigit) decreasingSequenceQtd += 1
        }
    }

    if (
        increasingSequenceQtd >= 6 ||
        decreasingSequenceQtd >= 6 ||
        equalsDigitQtd >= 6
    )
        return false

    const mod = valueSum % baseMod
    const verifyDigit = mod > 1 ? baseMod - mod : 0

    return adjustedRuc[identityDigitsLength + 1] === verifyDigit.toString()
}

/**
 * Valida CI do Paraguai (formato: 1234567 ou 1.234.567)
 * @param {string} ci
 * @returns {boolean}
 */
function validarCIParaguai(ci: string) {
    if (!ci) return false

    // Remove pontos se existirem
    const numero = ci.replace(/\./g, '')

    // Deve ter 6-8 dígitos (normalmente 7)
    if (!/^[0-9]{6,8}$/.test(numero)) return false

    // Não há dígito verificador na CI paraguaia, apenas validação básica
    return true
}

/**
 * Valida SSN americano (formato: *********** ou 123456789)
 * @param {string} ssn
 * @returns {boolean}
 */
function validarSSN(ssn: string) {
    if (!ssn) return false

    // Remove hífens se existirem
    const numero = ssn.replace(/-/g, '')

    // Verifica formato básico (9 dígitos)
    if (!/^[0-9]{9}$/.test(numero)) return false

    const parte1 = numero.substring(0, 3)
    const parte2 = numero.substring(3, 5)
    const parte3 = numero.substring(5, 9)

    // Verifica partes inválidas
    if (parte1 === '000' || parte1 === '666' || parseInt(parte1, 10) >= 900) {
        return false
    }

    if (parte2 === '00') return false
    if (parte3 === '0000') return false

    return true
}

/**
 * Valida EIN americano (formato: 12-3456789 ou 123456789)
 * @param {string} ein
 * @returns {boolean}
 */
function validarEIN(ein: string) {
    if (!ein) return false

    // Remove hífens se existirem
    const numero = ein.replace(/-/g, '')

    // Verifica formato básico (9 dígitos)
    if (!/^[0-9]{9}$/.test(numero)) return false

    // Estrutura básica: XX-XXXXXXX
    const parte1 = numero.substring(0, 2)

    // Algumas validações comuns:
    // - Não começa com 00
    // - O primeiro dígito não pode ser 7, 8 ou 9
    if (parte1 === '00') return false

    const primeiroDigito = numero.charAt(0)
    if (['7', '8', '9'].includes(primeiroDigito)) return false

    return true
}

/**
 * Valida documento de pessoa física de acordo com o idioma
 * @param {string} value
 * @returns {boolean}
 */
function validarNaturalPerson(value: string) {
    if (lang === 'pt-BR') {
        return validateCPF(value)
    } else if (lang === 'es-PY') {
        return validarCIParaguai(value)
    } else if (lang === 'en-US') {
        return validarSSN(value)
    } else {
        return validateCPF(value)
    }
}

function validarLegalPerson(value: string) {
    if (lang === 'pt-BR') {
        return validateCNPJ(value)
    } else if (lang === 'es-PY') {
        return validarRUCParaguai(value)
    } else if (lang === 'en-US') {
        return validarEIN(value)
    } else {
        return validateCNPJ(value)
    }
}

yup.addMethod(
    yup.string,
    'naturalPerson',
    function (message = 'Documento de pessoa física inválido') {
        return this.test('naturalPerson', message, function (value) {
            const { path, createError } = this

            // Se não houver valor, não valida (outras regras como required tratam isso)
            if (!value) return true

            // Valida o documento de pessoa física
            return validarNaturalPerson(value) || createError({ path, message })
        })
    }
)

yup.addMethod(
    yup.string,
    'legalPerson',
    function (message = 'Documento de pessoa jurídica inválido') {
        return this.test('legalPerson', message, function (value) {
            const { path, createError } = this

            // Se não houver valor, não valida (outras regras como required tratam isso)
            if (!value) return true

            // Valida o documento de pessoa jurídica
            return validarLegalPerson(value) || createError({ path, message })
        })
    }
)

export default yup
