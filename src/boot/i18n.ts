/* eslint-disable @typescript-eslint/no-explicit-any */
import { defineBoot } from '#q-app/wrappers'
import { createI18n } from 'vue-i18n'

import { messages, datetimeFormats, numberFormats } from 'src/i18n'

export type MessageLanguages = keyof typeof messages
// Type-define 'en-US' as the master schema for the resource
export type MessageSchema = (typeof messages)['en-US']

// See https://vue-i18n.intlify.dev/guide/advanced/typescript.html#global-resource-schema-type-definition
/* eslint-disable @typescript-eslint/no-empty-object-type */
declare module 'vue-i18n' {
    // define the locale messages schema
    export interface DefineLocaleMessage extends MessageSchema {}

    // define the datetime format schema
    export interface DefineDateTimeFormat {}

    // define the number format schema
    export interface DefineNumberFormat {}
}
/* eslint-enable @typescript-eslint/no-empty-object-type */
const config = {
    locale: 'pt-BR',
    legacy: false,
    fallbackLocale: 'en-US',
    messages,
    datetimeFormats,
    numberFormats
}

const i18n = createI18n(config as any)
export let i18nGlobal: typeof i18n.global

export default defineBoot(({ app }) => {
    const i18n = createI18n<{ message: MessageSchema }, MessageLanguages>(
        config as any
    )

    // Set i18n instance on app
    app.use(i18n)
    i18nGlobal = i18n.global
})

export { i18n }
