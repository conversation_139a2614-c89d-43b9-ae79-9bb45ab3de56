/**
 * Tipos compartilhados para o CrudTemplate
 */

// Interface para colunas da tabela
export interface TableColumn {
    name: string
    label: string
    field: string | ((row: Record<string, unknown>) => unknown)
    align?: 'left' | 'right' | 'center'
    sortable?: boolean
    visible?: boolean
    order?: number
    // Propriedades para edição inline
    inlineEdit?: boolean
    fieldType?: 'text' | 'number' | 'boolean' | 'select'
    selectOptions?: Array<{ label: string; value: unknown }>
    [key: string]: unknown
}

// Interface para ações da toolbar
export interface ActionItem {
    label: string
    active: boolean
    action: (...args: unknown[]) => void | Promise<void>
    icon?: string
    iconColor?: string
    description?: string
    shortcut?: string
    separator?: boolean
    disable?: boolean
    params?: unknown[]
}

// Interface para colunas de filtro
export interface FilterColumn {
    label: string
    value: string
    field: string
    type: 'number' | 'boolean' | 'text' | 'select'
}

// Interface para valores de filtro rápido
export interface QuickFilterValue {
    column: string
    content: string | number | boolean
}

// Interface para requisições da tabela
export interface TableRequestProps {
    pagination: {
        sortBy: string
        descending: boolean
        page: number
        rowsPerPage: number
        rowsNumber?: number
    }
    filter?: Record<string, unknown>
    getCellValue?: (col: unknown, row: unknown) => unknown
}

// Props do CrudTemplate
export interface CrudTemplateProps {
    // Dados da tabela
    tableName: string
    columns: TableColumn[]
    rows: Array<Record<string, unknown>>
    loading?: boolean
    filter?: Record<string, unknown>
    pagination?: Record<string, unknown>

    // Seleção
    selectedItems?: Array<Record<string, unknown>>

    // Configurações da tabela
    saveTableOptions?: boolean
    showTableToolbar?: boolean
    showQuickFilter?: boolean
    filterColumns?: FilterColumn[]
    quickFilterValue?: QuickFilterValue | null
    tableMaxHeight?: string
    tableSelection?: string
    tableDense?: boolean
    tableFlat?: boolean
    tableBordered?: boolean
    tableBinaryStateSort?: boolean
    rowsPerPageOptions?: number[]
    tableProps?: Record<string, unknown>
    actionsPerRow?: number

    // Toolbar
    showAddButton?: boolean
    addButtonLabel?: string
    refreshButtonLabel?: string
    toolbarActions?: ActionItem[]

    // Ações padrão
    showDefaultActions?: boolean
    showEditAction?: boolean
    showDeleteAction?: boolean
    showDefaultStatusColumn?: boolean

    // Modal
    showModal?: boolean
    modalTitle?: string
    modalCardStyle?: string
    modalSaving?: boolean
    showClearButton?: boolean
}

// Eventos do CrudTemplate
export interface CrudTemplateEmits {
    // Toolbar events
    'add-clicked': []
    'refresh-clicked': []

    // Table events
    'table-request': [event: Record<string, unknown>]
    'restore-pagination': [event: Record<string, unknown>]
    'quick-filter': [event: QuickFilterValue | null]
    'columns-changed': [columns: TableColumn[]]
    'edit-clicked': [row: Record<string, unknown>]
    'delete-clicked': [row: Record<string, unknown>]
    'inline-edit': [
        data: {
            row: Record<string, unknown>
            column: string
            value: unknown
            oldValue: unknown
        }
    ]

    // Modal events
    'update:show-modal': [value: boolean]
    'modal-close': []
    'modal-cancel': []
    'modal-clear': []
    'modal-save': []

    // Selection
    'update:selected-items': [items: Array<Record<string, unknown>>]

    // Quick filter
    'update:quick-filter-value': [value: QuickFilterValue | null]
}
