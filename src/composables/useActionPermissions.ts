import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'

/**
 * Composable para verificar permissões de ações específicas em rotas
 */
export function useActionPermissions() {
    const authStore = useAuthStore()

    /**
     * Computed para verificar se o usuário tem privilégios de admin ou staff
     * Útil para ações que independem de permissões específicas
     */
    const isOnlyAdmin = computed(() => authStore.hasSpecialPrivileges())

    /**
     * Computed para verificar se o usuário é exclusivamente staff
     * Útil para ações que são apenas para staff (não admin)
     */
    const isOnlyStaff = computed(() => authStore.isStaff())

    /**
     * Computed para verificar se o usuário é admin
     * Útil para ações específicas de admin
     */
    const isAdmin = computed(() => authStore.isAdmin())

    /**
     * Verifica se o usuário tem permissão para uma ação específica
     * @param actionId - ID da ação no sistema
     * @returns boolean
     */
    function hasActionPermission(actionId: number): boolean {
        // Admin e Staff têm acesso irrestrito
        if (authStore.hasSpecialPrivileges()) {
            return true
        }

        return authStore.hasActionPermission(actionId)
    }

    /**
     * Verifica se o usuário tem permissão para uma ação específica em uma rota específica
     * @param routePath - Caminho da rota (ex: '/estoque/produto-marca')
     * @param actionDescription - Descrição da ação (ex: 'CRIAR', 'EDITAR', 'EXCLUIR')
     * @returns boolean
     */
    function hasActionPermissionInRoute(
        routePath: string,
        actionDescription: string
    ): boolean {
        // Admin e Staff têm acesso irrestrito
        if (authStore.hasSpecialPrivileges()) {
            return true
        }

        const permissions = authStore.getUserPermissions()
        if (!permissions) return false

        // Verificar se existe uma ação com a descrição especificada para esta rota
        return permissions.actions.some(action => {
            // Verificar se a descrição da ação corresponde exatamente
            const actionMatches =
                action.id_acao_rota_id_acao_descricao?.toUpperCase() ===
                actionDescription.toUpperCase()

            // Para a rota de produto-marca, verificar se é "MARCA"
            let routeMatches = false
            if (routePath === '/estoque/produto-marca') {
                routeMatches =
                    action.id_acao_rota_id_rota_descricao?.toUpperCase() ===
                    'MARCA'
            } else if (routePath === '/estoque/modelo') {
                routeMatches =
                    action.id_acao_rota_id_rota_descricao?.toUpperCase() ===
                    'MODELO'
            } else {
                // Para outras rotas, usar o nome da rota
                const routeName =
                    routePath.split('/').pop()?.toLowerCase() || ''
                routeMatches = action.id_acao_rota_id_rota_descricao
                    ?.toLowerCase()
                    .includes(routeName)
            }

            return actionMatches && routeMatches
        })
    }
    /**
     * Funções específicas para ações comuns na rota de Cadastro geral região
     */

    const cadastroGeralRegiaoPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute(
            '/cadastro-geral/regiao',
            'CRIAR'
        ),
        canEdit: hasActionPermissionInRoute('/cadastro-geral/regiao', 'EDITAR'),
        canDelete: hasActionPermissionInRoute(
            '/cadastro-geral/regiao',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/cadastro-geral/regiao',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de Cadastro geral textos padronizados
     */

    const cadastroGeralTextosPadronizadosPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute(
            '/cadastro-geral/textos-padronizados',
            'CRIAR'
        ),
        canEdit: hasActionPermissionInRoute(
            '/cadastro-geral/textos-padronizados',
            'EDITAR'
        ),
        canDelete: hasActionPermissionInRoute(
            '/cadastro-geral/textos-padronizados',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/cadastro-geral/textos-padronizados',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de Cadastro geral ncm
     */

    const cadastroGeralNcmPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/cadastro-geral/ncm', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/cadastro-geral/ncm', 'EDITAR'),
        canDelete: hasActionPermissionInRoute('/cadastro-geral/ncm', 'EXCLUIR'),
        canView: hasActionPermissionInRoute('/cadastro-geral/ncm', 'VISUALIZAR')
    }))

    /**
     * Funções específicas para ações comuns na rota de Cadastro geral categoria CNH
     */

    const cadastroGeralTCategoriasCNHPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute(
            '/cadastro-geral/categoria-cnh',
            'CRIAR'
        ),
        canEdit: hasActionPermissionInRoute(
            '/cadastro-geral/categoria-cnh',
            'EDITAR'
        ),
        canDelete: hasActionPermissionInRoute(
            '/cadastro-geral/categoria-cnh',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/cadastro-geral/categoria-cnh',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de Cadastro geral tipo de endereço
     */

    const cadastroGeralTipoEnderecoPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute(
            '/cadastro-geral/tipo-endereco',
            'CRIAR'
        ),
        canEdit: hasActionPermissionInRoute(
            '/cadastro-geral/tipo-endereco',
            'EDITAR'
        ),
        canDelete: hasActionPermissionInRoute(
            '/cadastro-geral/tipo-endereco',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/cadastro-geral/tipo-endereco',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de Cadastro geral tipo de documentos
     */

    const cadastroGeralTipoDocumentoPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute(
            '/cadastro-geral/tipo-documento',
            'CRIAR'
        ),
        canEdit: hasActionPermissionInRoute(
            '/cadastro-geral/tipo-documento',
            'EDITAR'
        ),
        canDelete: hasActionPermissionInRoute(
            '/cadastro-geral/tipo-documento',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/cadastro-geral/tipo-documento',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de Cadastro geral tipo de formas de pagamento
     */

    const cadastroGeralGrupoFormaPagamentoPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute(
            '/cadastro-geral/grupo-forma-pagamento',
            'CRIAR'
        ),
        canEdit: hasActionPermissionInRoute(
            '/cadastro-geral/grupo-forma-pagamento',
            'EDITAR'
        ),
        canDelete: hasActionPermissionInRoute(
            '/cadastro-geral/grupo-forma-pagamento',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/cadastro-geral/grupo-forma-pagamento',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de Cadastro geral de ramo de atividade
     */

    const cadastroGeralRamoAtividadePermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute(
            '/cadastro-geral/ramo-atividade',
            'CRIAR'
        ),
        canEdit: hasActionPermissionInRoute(
            '/cadastro-geral/ramo-atividade',
            'EDITAR'
        ),
        canDelete: hasActionPermissionInRoute(
            '/cadastro-geral/ramo-atividade',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/cadastro-geral/ramo-atividade',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de Cadastro geral histórico de lançamento
     */

    const cadastroGeralHistoricoLancamentoPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute(
            '/cadastro-geral/historico-lancamento',
            'CRIAR'
        ),
        canEdit: hasActionPermissionInRoute(
            '/cadastro-geral/historico-lancamento',
            'EDITAR'
        ),
        canDelete: hasActionPermissionInRoute(
            '/cadastro-geral/historico-lancamento',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/cadastro-geral/historico-lancamento',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de Crm Origem
     */

    const crmOrigemPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/crm/origem', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/crm/origem', 'EDITAR'),
        canDelete: hasActionPermissionInRoute('/crm/origem', 'EXCLUIR'),
        canView: hasActionPermissionInRoute('/crm/origem', 'VISUALIZAR')
    }))

    /**
     * Funções específicas para ações comuns na rota de Produto Marca
     */
    const produtoMarcaPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute(
            '/estoque/produto-marca',
            'CRIAR'
        ),
        canEdit: hasActionPermissionInRoute('/estoque/produto-marca', 'EDITAR'),
        canDelete: hasActionPermissionInRoute(
            '/estoque/produto-marca',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/estoque/produto-marca',
            'VISUALIZAR'
        ),
        canLink: hasActionPermissionInRoute(
            '/estoque/produto-marca',
            'VINCULAR MODELO'
        ),
        canEditLink: hasActionPermissionInRoute(
            '/estoque/produto-marca',
            'EDITAR VINCULO'
        ),
        canDeleteLink: hasActionPermissionInRoute(
            '/estoque/produto-marca',
            'DELETAR VINCULO'
        ),
        canViewLinkImage: hasActionPermissionInRoute(
            '/estoque/produto-marca',
            'EXIBIR IMAGEM DE VINCULO'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de Produto Modelo
     */
    const produtoModeloPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/estoque/modelo', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/estoque/modelo', 'EDITAR'),
        canDelete: hasActionPermissionInRoute('/estoque/modelo', 'EXCLUIR'),
        canView: hasActionPermissionInRoute('/estoque/modelo', 'VISUALIZAR')
    }))

    /**
     * Funções específicas para ações comuns na rota de Produto Grupo
     */

    const produtoGrupoPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/estoque/grupo', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/estoque/grupo', 'EDITAR'),
        canDelete: hasActionPermissionInRoute('/estoque/grupo', 'EXCLUIR'),
        canView: hasActionPermissionInRoute('/estoque/grupo', 'VISUALIZAR')
    }))

    /**
     * Funções específicas para ações comuns na rota de Produto Familia
     */

    const produtoFamiliaPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/estoque/familia', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/estoque/familia', 'EDITAR'),
        canDelete: hasActionPermissionInRoute('/estoque/familia', 'EXCLUIR'),
        canView: hasActionPermissionInRoute('/estoque/familia', 'VISUALIZAR')
    }))

    /**
     * Funções específicas para ações comuns na rota de Produto Familia
     */

    const produtoSubGrupoPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/estoque/sub-grupo', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/estoque/sub-grupo', 'EDITAR'),
        canDelete: hasActionPermissionInRoute('/estoque/sub-grupo', 'EXCLUIR'),
        canView: hasActionPermissionInRoute('/estoque/sub-grupo', 'VISUALIZAR')
    }))

    /**
     * Funções específicas para ações comuns na rota de Venda Categoria Cliente
     */

    const vendaCategoriaClientePermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute(
            '/venda/categoria-cliente',
            'CRIAR'
        ),
        canEdit: hasActionPermissionInRoute(
            '/venda/categoria-cliente',
            'EDITAR'
        ),
        canDelete: hasActionPermissionInRoute(
            '/venda/categoria-cliente',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/venda/categoria-cliente',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de Venda Grupo de Desconto
     */

    const vendaGrupoDescontoPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/venda/grupo-desconto', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/venda/grupo-desconto', 'EDITAR'),
        canDelete: hasActionPermissionInRoute(
            '/venda/grupo-desconto',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/venda/grupo-desconto',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de Projeto Aplicação
     */

    const projetoAplicacaoPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/projeto/aplicacao', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/projeto/aplicacao', 'EDITAR'),
        canDelete: hasActionPermissionInRoute('/projeto/aplicacao', 'EXCLUIR'),
        canView: hasActionPermissionInRoute('/projeto/aplicacao', 'VISUALIZAR')
    }))

    /**
     * Funções específicas para ações comuns na rota de Projeto Tipo de Instalação
     */

    const projetoTipoInstalacaoPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute(
            '/projeto/tipo-instalacao',
            'CRIAR'
        ),
        canEdit: hasActionPermissionInRoute(
            '/projeto/tipo-instalacao',
            'EDITAR'
        ),
        canDelete: hasActionPermissionInRoute(
            '/projeto/tipo-instalacao',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/projeto/tipo-instalacao',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de Projeto Tipo de Perfil
     */

    const projetoTipoPerfilPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/projeto/tipo-perfil', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/projeto/tipo-perfil', 'EDITAR'),
        canDelete: hasActionPermissionInRoute(
            '/projeto/tipo-perfil',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/projeto/tipo-perfil',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de Projeto Tipo de Material
     */

    const projetoTipoMaterialPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute(
            '/projeto/tipo-material',
            'CRIAR'
        ),
        canEdit: hasActionPermissionInRoute('/projeto/tipo-material', 'EDITAR'),
        canDelete: hasActionPermissionInRoute(
            '/projeto/tipo-material',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/projeto/tipo-material',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de Projeto Grupo de Projeto
     */

    const projetoGrupoProjetoPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute(
            '/projeto/grupo-projeto',
            'CRIAR'
        ),
        canEdit: hasActionPermissionInRoute('/projeto/grupo-projeto', 'EDITAR'),
        canDelete: hasActionPermissionInRoute(
            '/projeto/grupo-projeto',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/projeto/grupo-projeto',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de Projeto Grupo de Material
     */

    const projetoGrupoMaterialPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute(
            '/projeto/grupo-material',
            'CRIAR'
        ),
        canEdit: hasActionPermissionInRoute(
            '/projeto/grupo-material',
            'EDITAR'
        ),
        canDelete: hasActionPermissionInRoute(
            '/projeto/grupo-material',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/projeto/grupo-material',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de OS Titulo
     */

    const osTituloPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/os/titulo', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/os/titulo', 'EDITAR'),
        canDelete: hasActionPermissionInRoute('/os/titulo', 'EXCLUIR'),
        canView: hasActionPermissionInRoute('/os/titulo', 'VISUALIZAR')
    }))

    /**
     * Funções específicas para ações comuns na rota de OS Parte do Equipamento
     */

    const osParteEquipamentoPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/os/parte-equipamento', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/os/parte-equipamento', 'EDITAR'),
        canDelete: hasActionPermissionInRoute(
            '/os/parte-equipamento',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/os/parte-equipamento',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de OS defeito
     */

    const osDefeitoPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/os/defeito', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/os/defeito', 'EDITAR'),
        canDelete: hasActionPermissionInRoute('/os/defeito', 'EXCLUIR'),
        canView: hasActionPermissionInRoute('/os/defeito', 'VISUALIZAR')
    }))

    /**
     * Funções específicas para ações comuns na rota de OS Causa
     */

    const osCausaPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/os/causa', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/os/causa', 'EDITAR'),
        canDelete: hasActionPermissionInRoute('/os/causa', 'EXCLUIR'),
        canView: hasActionPermissionInRoute('/os/causa', 'VISUALIZAR')
    }))

    /**
     * Funções específicas para ações comuns na rota de OS Familia do Equipamento
     */

    const osFamiliaEquipamentoPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute(
            '/os/familia-equipamento',
            'CRIAR'
        ),
        canEdit: hasActionPermissionInRoute(
            '/os/familia-equipamento',
            'EDITAR'
        ),
        canDelete: hasActionPermissionInRoute(
            '/os/familia-equipamento',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/os/familia-equipamento',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de Financeiro Carteira de Cobrança
     */

    const financeiroCarteiraCobrancaPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute(
            '/financeiro/carteira-cobranca',
            'CRIAR'
        ),
        canEdit: hasActionPermissionInRoute(
            '/financeiro/carteira-cobranca',
            'EDITAR'
        ),
        canDelete: hasActionPermissionInRoute(
            '/financeiro/carteira-cobranca',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/financeiro/carteira-cobranca',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de Produção Checklist
     */

    const producaoChecklistPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/producao/checklist', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/producao/checklist', 'EDITAR'),
        canDelete: hasActionPermissionInRoute('/producao/checklist', 'EXCLUIR'),
        canView: hasActionPermissionInRoute('/producao/checklist', 'VISUALIZAR')
    }))

    /**
     * Funções específicas para ações comuns na rota de Qualidade Operação
     */

    const qualidadeOperacaoPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/qualidade/operacao', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/qualidade/operacao', 'EDITAR'),
        canDelete: hasActionPermissionInRoute('/qualidade/operacao', 'EXCLUIR'),
        canView: hasActionPermissionInRoute('/qualidade/operacao', 'VISUALIZAR')
    }))

    /**
     * Funções específicas para ações comuns na rota de Qualidade Não Conformidade
     */

    const qualidadeNaoConformidadePermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute(
            '/qualidade/nao-conformidade',
            'CRIAR'
        ),
        canEdit: hasActionPermissionInRoute(
            '/qualidade/nao-conformidade',
            'EDITAR'
        ),
        canDelete: hasActionPermissionInRoute(
            '/qualidade/nao-conformidade',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/qualidade/nao-conformidade',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de Qualidade Não Conformidade
     */

    const qualidadeCausaPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/qualidade/causa', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/qualidade/causa', 'EDITAR'),
        canDelete: hasActionPermissionInRoute('/qualidade/causa', 'EXCLUIR'),
        canView: hasActionPermissionInRoute('/qualidade/causa', 'VISUALIZAR')
    }))

    /**
     * Funções específicas para ações comuns na rota de RH Cargo
     */

    const rhCargoPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/rh/cargo', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/rh/cargo', 'EDITAR'),
        canDelete: hasActionPermissionInRoute('/rh/cargo', 'EXCLUIR'),
        canView: hasActionPermissionInRoute('/rh/cargo', 'VISUALIZAR')
    }))

    /**
     * Funções específicas para ações comuns na rota de RH Escolaridade
     */

    const rhEscolaridadePermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/rh/escolaridade', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/rh/escolaridade', 'EDITAR'),
        canDelete: hasActionPermissionInRoute('/rh/escolaridade', 'EXCLUIR'),
        canView: hasActionPermissionInRoute('/rh/escolaridade', 'VISUALIZAR')
    }))

    /**
     * Funções específicas para ações comuns na rota de RH Especialidade
     */

    const rhEspecialidadePermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/rh/especidalidade', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/rh/especidalidade', 'EDITAR'),
        canDelete: hasActionPermissionInRoute('/rh/especidalidade', 'EXCLUIR'),
        canView: hasActionPermissionInRoute('/rh/especidalidade', 'VISUALIZAR')
    }))

    /**
     * Funções específicas para ações comuns na rota de RH Especialidade
     */

    const rhHabilidadePermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/rh/habilidade', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/rh/habilidade', 'EDITAR'),
        canDelete: hasActionPermissionInRoute('/rh/habilidade', 'EXCLUIR'),
        canView: hasActionPermissionInRoute('/rh/habilidade', 'VISUALIZAR')
    }))

    /**
     * Funções específicas para ações comuns na rota de RH Especialidade
     */

    const rhDepartamentoPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/rh/departamento', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/rh/departamento', 'EDITAR'),
        canDelete: hasActionPermissionInRoute('/rh/departamento', 'EXCLUIR'),
        canView: hasActionPermissionInRoute('/rh/departamento', 'VISUALIZAR')
    }))

    /**
     * Funções específicas para ações comuns na rota Configuração Perfil de Usuário
     */

    const configuracaoPerfilUsuarioPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute(
            '/configuracao/perfil-usuario',
            'CRIAR'
        ),
        canEdit: hasActionPermissionInRoute(
            '/configuracao/perfil-usuario',
            'EDITAR'
        ),
        canDelete: hasActionPermissionInRoute(
            '/configuracao/perfil-usuario',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/configuracao/perfil-usuario',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota Configuração Módulo
     */

    const configuracaoModuloPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/configuracao/modulo', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/configuracao/modulo', 'EDITAR'),
        canDelete: hasActionPermissionInRoute(
            '/configuracao/modulo',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/configuracao/modulo',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de Configuração Novidades
     */

    const configuracaoNovidadesPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute(
            '/configuracao/novidades',
            'CRIAR'
        ),
        canEdit: hasActionPermissionInRoute(
            '/configuracao/novidades',
            'EDITAR'
        ),
        canDelete: hasActionPermissionInRoute(
            '/configuracao/novidades',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/configuracao/novidades',
            'VISUALIZAR'
        )
    }))

    /**
     * Funções específicas para ações comuns na rota de Configuração Ajudas
     */
    const configuracaoHelpPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/configuracao/ajudas', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/configuracao/ajudas', 'EDITAR'),
        canDelete: hasActionPermissionInRoute(
            '/configuracao/ajudas',
            'EXCLUIR'
        ),
        canView: hasActionPermissionInRoute(
            '/configuracao/ajudas',
            'VISUALIZAR'
        )
    }))

    /**
     * Função genérica para verificar permissões de CRUD em qualquer rota
     * @param routePath - Caminho da rota
     * @returns Objeto com permissões de CRUD
     */
    function getCrudPermissions(routePath: string) {
        return computed(() => ({
            canCreate: hasActionPermissionInRoute(routePath, 'CRIAR'),
            canEdit: hasActionPermissionInRoute(routePath, 'EDITAR'),
            canDelete: hasActionPermissionInRoute(routePath, 'EXCLUIR'),
            canView: hasActionPermissionInRoute(routePath, 'VISUALIZAR')
        }))
    }

    return {
        // Verificações gerais
        hasActionPermission,
        hasActionPermissionInRoute,

        // Permissões específicas por rota
        cadastroGeralRegiaoPermissions,
        cadastroGeralTextosPadronizadosPermissions,
        cadastroGeralNcmPermissions,
        cadastroGeralTCategoriasCNHPermissions,
        cadastroGeralTipoEnderecoPermissions,
        cadastroGeralTipoDocumentoPermissions,
        cadastroGeralGrupoFormaPagamentoPermissions,
        cadastroGeralRamoAtividadePermissions,
        cadastroGeralHistoricoLancamentoPermissions,
        crmOrigemPermissions,
        produtoMarcaPermissions,
        produtoModeloPermissions,
        produtoGrupoPermissions,
        produtoFamiliaPermissions,
        produtoSubGrupoPermissions,
        vendaCategoriaClientePermissions,
        vendaGrupoDescontoPermissions,
        projetoAplicacaoPermissions,
        projetoTipoInstalacaoPermissions,
        projetoTipoPerfilPermissions,
        projetoTipoMaterialPermissions,
        projetoGrupoProjetoPermissions,
        projetoGrupoMaterialPermissions,
        osTituloPermissions,
        osParteEquipamentoPermissions,
        osDefeitoPermissions,
        osCausaPermissions,
        osFamiliaEquipamentoPermissions,
        financeiroCarteiraCobrancaPermissions,
        producaoChecklistPermissions,
        qualidadeOperacaoPermissions,
        qualidadeNaoConformidadePermissions,
        qualidadeCausaPermissions,
        rhCargoPermissions,
        rhEscolaridadePermissions,
        rhEspecialidadePermissions,
        rhHabilidadePermissions,
        rhDepartamentoPermissions,
        configuracaoPerfilUsuarioPermissions,
        configuracaoModuloPermissions,
        configuracaoNovidadesPermissions,
        configuracaoHelpPermissions,

        // Função genérica
        getCrudPermissions,

        // Privilégios especiais (computeds)
        isOnlyAdmin,
        isOnlyStaff,
        isAdmin
    }
}
