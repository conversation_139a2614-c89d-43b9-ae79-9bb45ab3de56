/**
 * Composable para obter variáveis de ambiente
 * Prioriza variáveis do Rancher (runtime) sobre build-time
 */
export function useEnv() {
    /**
     * Obtém uma variável de ambiente
     * @param key Nome da variável (ex: 'VITE_API_URL')
     * @returns Valor da variável ou string vazia
     */
    function getEnv(key: string): string {
        // Em desenvolvimento, use as variáveis do Vite
        if (import.meta.env.DEV) {
            return (import.meta.env[key] as string) || ''
        }

        // Em produção, primeiro tente as variáveis do window (runtime do Rancher)
        // Se não existir, tente as variáveis do Vite (build-time)
        /* eslint-disable @typescript-eslint/no-explicit-any */
        return (window as any)[key] || (import.meta.env[key] as string) || ''
    }

    /**
     * Obtém a URL da API
     */
    function getApiUrl(): string {
        return getEnv('VITE_API_URL') || 'http://localhost:3000/api'
    }

    /**
     * Obtém o logo personalizado da empresa
     */
    function getLogoSystem(): string {
        return (
            getEnv('VITE_LOGO_SYSTEM') ||
            '/src/assets/logos/logo-personalizado.svg'
        )
    }

    /**
     * Obtém o logo da CorpSystem (header)
     */
    function getImgLoginSystem(): string {
        return (
            getEnv('VITE_IMG_LOGIN_SYSTEM') ||
            '/src/assets/logos/logo-corpsystem.svg'
        )
    }

    return {
        getEnv,
        getApiUrl,
        getLogoSystem,
        getImgLoginSystem
    }
}
