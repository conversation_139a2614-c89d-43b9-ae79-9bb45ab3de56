import { onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

/**
 * Composable para detectar recarregamento da página (F5) e recarregar dados do usuário
 */
export function usePageReload() {
    const authStore = useAuthStore()

    // Detectar se a página foi recarregada
    function isPageReloaded(): boolean {
        // Verifica se há dados no sessionStorage que indicam que não é um reload
        const isNotReload = sessionStorage.getItem('app-not-reload')

        if (!isNotReload) {
            // É um reload, marcar para próximas navegações
            sessionStorage.setItem('app-not-reload', 'true')
            return true
        }

        return false
    }

    // Recarregar dados do usuário se necessário
    async function handlePageReload(): Promise<void> {
        // Só recarregar se:
        // 1. A página foi recarregada (F5)
        // 2. O usuário está autenticado
        // 3. Tem profile carregado
        // 4. Não está já carregando
        if (
            isPageReloaded() &&
            authStore.token &&
            authStore.user?.profile?.id &&
            !authStore.isLoading
        ) {
            try {
                await authStore.reloadUserData()
            } catch (error) {
                // eslint-disable-next-line no-console
                console.error('Erro ao recarregar dados após F5:', error)
            }
        }
    }

    // Limpar flag quando a página for fechada/navegada
    function handleBeforeUnload(): void {
        sessionStorage.removeItem('app-not-reload')
    }

    // Configurar listeners
    onMounted(() => {
        // Recarregar dados se necessário
        handlePageReload()

        // Limpar flag ao sair da página
        window.addEventListener('beforeunload', handleBeforeUnload)
    })

    onUnmounted(() => {
        window.removeEventListener('beforeunload', handleBeforeUnload)
    })

    return {
        isPageReloaded,
        handlePageReload
    }
}
