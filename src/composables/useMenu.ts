import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import type { IMenuItem } from '@/interfaces/Menu'
import { useAuthStore } from '@/stores/auth'
import { useRoutePermissions } from '@/composables/useRoutePermissions'
import { usePermissions } from '@/composables/usePermissions'

/**
 * Composable para gerenciar itens do menu da aplicação
 * Centraliza a definição e lógica dos itens de menu
 */
export function useMenu() {
    const { t } = useI18n()
    const authStore = useAuthStore()
    const { canAccessRoute } = useRoutePermissions()
    const { systemModules, isLoading } = usePermissions()

    /**
     * Definição dos itens do menu principal (sem filtro de permissões)
     */
    const allMenuItems = computed<IMenuItem[]>(() => {
        const items = [
            {
                icon: 'home',
                label: t('pages.titles.home'),
                route: '/'
            },
            // Cadastro Geral
            {
                icon: 'app_registration',
                label: t('pages.titles.generalRegistration'),
                subItens: [
                    {
                        label: t('pages.titles.country'),
                        icon: 'public',
                        route: '/cadastro-geral/pais'
                    },
                    {
                        label: t('pages.titles.state'),
                        icon: 'location_on',
                        route: '/cadastro-geral/estado'
                    },
                    {
                        label: t('pages.titles.city'),
                        icon: 'location_city',
                        route: '/cadastro-geral/cidade'
                    },
                    {
                        label: t('pages.titles.region'),
                        icon: 'south_america',
                        route: '/cadastro-geral/regiao'
                    },
                    {
                        label: t('pages.titles.standardizedTexts'),
                        icon: 'text_fields',
                        route: '/cadastro-geral/textos-padronizados'
                    },
                    {
                        label: t('pages.titles.ncm'),
                        icon: 'inventory_2',
                        route: '/cadastro-geral/ncm'
                    },
                    {
                        label: t('pages.titles.categoryCNH'),
                        icon: 'badge',
                        route: '/cadastro-geral/categoria-cnh'
                    },
                    {
                        label: t('pages.titles.addressType'),
                        icon: 'home',
                        route: '/cadastro-geral/tipo-endereco'
                    },
                    {
                        label: t('pages.titles.documentType'),
                        icon: 'description',
                        route: '/cadastro-geral/tipo-documento'
                    },
                    {
                        label: t('pages.titles.paymentGroup'),
                        icon: 'payments',
                        route: '/cadastro-geral/grupo-forma-pagamento'
                    },
                    {
                        label: t('pages.titles.activitySector'),
                        icon: 'business_center',
                        route: '/cadastro-geral/ramo-atividade'
                    },
                    {
                        label: t('pages.titles.historicLaunch'),
                        icon: 'history',
                        route: '/cadastro-geral/historico-lancamento'
                    }
                ]
            },
            {
                icon: 'inventory',
                label: t('pages.titles.crm'),
                subItens: [
                    {
                        label: t('pages.titles.common.register'),
                        icon: 'add',
                        subItens: [
                            {
                                label: t('pages.titles.origin'),
                                icon: 'family_restroom',
                                route: '/crm/origem'
                            },
                            {
                                label: t('pages.titles.opportunity'),
                                icon: 'groups',
                                route: '/crm/oportunidade'
                            }
                        ]
                    }
                ]
            },

            // Estoque
            {
                icon: 'inventory',
                label: t('pages.titles.productQuery'),
                subItens: [
                    {
                        label: t('pages.titles.common.register'),
                        icon: 'add',
                        subItens: [
                            {
                                label: t('pages.titles.family'),
                                icon: 'family_restroom',
                                route: '/estoque/produto-familia'
                            },
                            {
                                label: t('pages.titles.group'),
                                icon: 'groups',
                                route: '/estoque/produto-grupo'
                            },
                            {
                                label: t('pages.titles.subgroup'),
                                icon: 'groups',
                                route: '/estoque/sub-grupo'
                            },
                            {
                                label: t('pages.titles.productBrand'),
                                icon: 'branding_watermark',
                                route: '/estoque/produto-marca'
                            },
                            {
                                label: t('pages.titles.productModel'),
                                icon: 'model_training',
                                route: '/estoque/modelo'
                            }
                        ]
                    }
                ]
            },

            // Venda
            {
                icon: 'shopping_cart',
                label: t('pages.titles.sale'),
                subItens: [
                    {
                        label: t('pages.titles.common.register'),
                        icon: 'add',
                        subItens: [
                            {
                                label: t('pages.titles.customerCategory'),
                                icon: 'category',
                                route: '/venda/categoria-cliente'
                            },
                            {
                                label: t('pages.titles.discountGroup'),
                                icon: 'discount',
                                route: '/venda/grupo-desconto'
                            }
                        ]
                    }
                ]
            },

            // Projeto
            {
                icon: 'engineering',
                label: t('pages.titles.project'),
                subItens: [
                    {
                        label: t('pages.titles.common.register'),
                        icon: 'add',
                        subItens: [
                            {
                                label: t('pages.titles.application'),
                                icon: 'developer_board',
                                route: '/projeto/aplicacao'
                            },
                            {
                                label: t('pages.titles.installationType'),
                                icon: 'engineering',
                                route: '/projeto/tipo-instalacao'
                            },
                            {
                                label: t('pages.titles.profileType'),
                                icon: 'account_tree',
                                route: '/projeto/tipo-perfil'
                            },
                            {
                                label: t('pages.titles.materialType'),
                                icon: 'construction',
                                route: '/projeto/tipo-material'
                            },
                            {
                                label: t('pages.titles.projectGroup'),
                                icon: 'groups',
                                route: '/projeto/grupo-projeto'
                            },
                            {
                                label: t('pages.titles.materialGroup'),
                                icon: 'batch_prediction',
                                route: '/projeto/grupo-material'
                            }
                        ]
                    }
                ]
            },

            // OS
            {
                icon: 'build',
                label: t('pages.titles.os'),
                subItens: [
                    {
                        label: t('pages.titles.common.register'),
                        icon: 'add',
                        subItens: [
                            {
                                label: t('pages.titles.title'),
                                icon: 'title',
                                route: '/os/titulo'
                            },
                            {
                                label: t('pages.titles.equipmentPart'),
                                icon: 'hardware',
                                route: '/os/parte-equipamento'
                            },
                            {
                                label: t('pages.titles.defect'),
                                icon: 'bug_report',
                                route: '/os/defeito'
                            },
                            {
                                label: t('pages.titles.cause'),
                                icon: 'broken_image',
                                route: '/os/causa'
                            },
                            {
                                label: t('pages.titles.equipmentFamily'),
                                icon: 'family_restroom',
                                route: '/os/familia-equipamento'
                            }
                        ]
                    }
                ]
            },

            // Financeiro
            {
                icon: 'attach_money',
                label: t('pages.titles.finance'),
                subItens: [
                    {
                        label: t('pages.titles.common.register'),
                        icon: 'add',
                        subItens: [
                            {
                                label: t('pages.titles.billingPortfolio'),
                                icon: 'account_balance',
                                route: '/financeiro/carteira-cobranca'
                            }
                        ]
                    }
                ]
            },

            // Produção
            {
                icon: 'factory',
                label: t('pages.titles.production'),
                subItens: [
                    {
                        label: t('pages.titles.common.register'),
                        icon: 'add',
                        subItens: [
                            {
                                label: t('pages.titles.checklist'),
                                icon: 'checklist',
                                route: '/producao/checklist'
                            }
                        ]
                    }
                ]
            },

            // Qualidade
            {
                icon: 'diamond',
                label: t('pages.titles.quality'),
                subItens: [
                    {
                        label: t('pages.titles.common.register'),
                        icon: 'add',
                        subItens: [
                            {
                                label: t('pages.titles.operation'),
                                icon: 'build',
                                route: '/qualidade/operacao'
                            },
                            {
                                label: t('pages.titles.nonConformity'),
                                icon: 'mood_bad',
                                route: '/qualidade/nao-conformidade'
                            },
                            {
                                label: t('pages.titles.cause'),
                                icon: 'broken_image',
                                route: '/qualidade/causa'
                            }
                        ]
                    }
                ]
            },

            // RH
            {
                icon: 'people',
                label: t('pages.titles.rh'),
                subItens: [
                    {
                        label: t('pages.titles.common.register'),
                        icon: 'add',
                        subItens: [
                            {
                                label: t('pages.titles.position'),
                                icon: 'work',
                                route: '/rh/cargo'
                            },
                            {
                                label: t('pages.titles.education'),
                                icon: 'school',
                                route: '/rh/escolaridade'
                            },
                            {
                                label: t('pages.titles.specialty'),
                                icon: 'local_library',
                                route: '/rh/especialidade'
                            },
                            {
                                label: t('pages.titles.skill'),
                                icon: 'build',
                                route: '/rh/habilidade'
                            },
                            {
                                label: t('pages.titles.department'),
                                icon: 'business',
                                route: '/rh/departamento'
                            }
                        ]
                    }
                ]
            },

            // Configuração
            {
                icon: 'settings',
                label: t('pages.titles.config'),
                subItens: [
                    {
                        label: t('pages.titles.userProfile'),
                        icon: 'person',
                        route: '/configuracao/perfil-usuario'
                    },
                    {
                        label: t('pages.titles.module'),
                        icon: 'person',
                        route: '/configuracao/modulo'
                    },
                    {
                        label: t('pages.titles.news'),
                        icon: 'newspaper',
                        route: '/configuracao/novidades'
                    },
                    {
                        label: t('pages.titles.help'),
                        icon: 'help',
                        route: '/configuracao/ajudas'
                    }
                ]
            }
        ]

        if (import.meta.env.MODE === 'development') {
            items.push({
                icon: 'developer_board',
                label: t('pages.titles.dev'),
                route: '/dev'
            })
        }

        return items.filter(Boolean) as IMenuItem[]
    })

    /**
     * Filtra um item do menu baseado nas permissões do usuário
     */
    const filterMenuItemByPermissions = (item: IMenuItem): boolean => {
        // Se não tem rota, é um item pai - verificar se tem subitens acessíveis
        if (!item.route) {
            return true // Será filtrado recursivamente pelos subitens
        }

        // Verificar se o usuário tem acesso à rota
        return canAccessRoute(item.route)
    }

    /**
     * Filtra recursivamente os itens do menu baseado nas permissões
     */
    const filterMenuItemsRecursively = (items: IMenuItem[]): IMenuItem[] => {
        return items
            .map(item => {
                // Se tem subitens, filtrar recursivamente
                if (item.subItens) {
                    const filteredSubItems = filterMenuItemsRecursively(
                        item.subItens
                    )

                    // Se não sobrou nenhum subitem acessível, remover o item pai
                    if (filteredSubItems.length === 0) {
                        return null
                    }

                    // Retornar item com subitens filtrados
                    return {
                        ...item,
                        subItens: filteredSubItems
                    }
                }

                // Para itens sem subitens, verificar permissão direta
                return filterMenuItemByPermissions(item) ? item : null
            })
            .filter((item): item is IMenuItem => item !== null)
    }

    /**
     * Itens do menu filtrados por permissões do usuário
     */
    const menuItems = computed<IMenuItem[]>(() => {
        // Se não estiver logado, mostrar apenas itens públicos
        if (!authStore.token) {
            return allMenuItems.value.filter(
                item => !item.route || ['/', '/login'].includes(item.route)
            )
        }

        // Admin e Staff têm acesso a todos os itens ATIVOS (exceto rotas exclusivas para staff)
        if (authStore.hasSpecialPrivileges()) {
            // Mesmo Staff/Admin devem respeitar módulos inativos
            return filterMenuItemsRecursively(allMenuItems.value)
        }

        // Filtrar itens baseado nas permissões normais
        return filterMenuItemsRecursively(allMenuItems.value)
    })

    /**
     * Função recursiva para extrair todos os itens com rotas do menu
     * @param items - Array de itens do menu
     * @returns Array de objetos com label e route
     */
    const extractRoutesFromMenu = (
        items: IMenuItem[]
    ): { label: string; route: string }[] => {
        const routes: { label: string; route: string }[] = []

        const traverse = (menuItems: IMenuItem[]) => {
            for (const item of menuItems) {
                // Se o item tem rota, adiciona à lista
                if (item.route) {
                    routes.push({
                        label: item.label,
                        route: item.route
                    })
                }

                // Se o item tem subitens, percorre recursivamente
                if (item.subItens && item.subItens.length > 0) {
                    traverse(item.subItens)
                }
            }
        }

        traverse(items)
        return routes
    }

    /**
     * Computed que retorna todas as páginas/rotas disponíveis no menu
     */
    const systemPages = computed(() => extractRoutesFromMenu(menuItems.value))

    /**
     * Verifica se um item pai está ativo (tem algum subitem ativo)
     * @param item - Item do menu para verificar
     * @param currentPath - Caminho atual da rota
     * @returns true se o item pai está ativo
     */
    const isActiveParent = (item: IMenuItem, currentPath: string): boolean => {
        if (!item.subItens) return false

        const checkSubItems = (items: IMenuItem[]): boolean => {
            return items.some(subItem => {
                if (subItem.route === currentPath) return true
                if (subItem.subItens) return checkSubItems(subItem.subItens)
                return false
            })
        }

        return checkSubItems(item.subItens)
    }

    /**
     * Busca um item do menu por rota
     * @param route - Rota para buscar
     * @returns Item do menu encontrado ou undefined
     */
    const findMenuItemByRoute = (route: string): IMenuItem | undefined => {
        const findInItems = (items: IMenuItem[]): IMenuItem | undefined => {
            for (const item of items) {
                if (item.route === route) {
                    return item
                }
                if (item.subItens) {
                    const found = findInItems(item.subItens)
                    if (found) return found
                }
            }
            return undefined
        }

        return findInItems(menuItems.value)
    }

    /**
     * Obtém o breadcrumb para uma rota específica
     * @param route - Rota para obter o breadcrumb
     * @returns Array com o caminho do breadcrumb
     */
    const getBreadcrumb = (route: string): IMenuItem[] => {
        const breadcrumb: IMenuItem[] = []

        const findPath = (
            items: IMenuItem[],
            path: IMenuItem[] = []
        ): boolean => {
            for (const item of items) {
                const currentPath = [...path, item]

                if (item.route === route) {
                    breadcrumb.push(...currentPath)
                    return true
                }

                if (item.subItens && findPath(item.subItens, currentPath)) {
                    return true
                }
            }
            return false
        }

        findPath(menuItems.value)
        return breadcrumb
    }

    /**
     * Filtra itens do menu baseado em permissões ou outros critérios
     * @param filterFn - Função de filtro
     * @returns Itens do menu filtrados
     */
    const filterMenuItems = (
        filterFn: (item: IMenuItem) => boolean
    ): IMenuItem[] => {
        const filterRecursive = (items: IMenuItem[]): IMenuItem[] => {
            return items.filter(filterFn).map(item => {
                if (item.subItens) {
                    return {
                        ...item,
                        subItens: filterRecursive(item.subItens)
                    } as IMenuItem
                }
                return item
            })
        }

        return filterRecursive(menuItems.value)
    }

    /**
     * Verifica se o usuário tem acesso a um módulo específico
     */
    const hasModuleAccess = (modulePath: string): boolean => {
        const { canAccessModule } = useRoutePermissions()
        return canAccessModule(modulePath)
    }

    /**
     * Obtém apenas os itens do menu que o usuário tem acesso
     */
    const getAccessibleMenuItems = (): IMenuItem[] => {
        return menuItems.value
    }

    /**
     * Verifica se o menu deve ser mostrado (módulos carregados ou usuário não logado)
     *
     * Esta função controla quando o menu lateral deve ser renderizado para evitar
     * que itens de módulos inativos apareçam brevemente durante o carregamento.
     *
     * @returns true se o menu deve ser mostrado, false para manter vazio durante loading
     */
    const shouldShowMenu = computed(() => {
        // Se não estiver logado, sempre mostrar menu (itens públicos)
        if (!authStore.token) {
            return true
        }

        // Se estiver logado, só mostrar quando módulos estiverem carregados
        // Isso previne o "flash" de conteúdo não autorizado
        return !isLoading.value && systemModules.value.length > 0
    })

    return {
        menuItems,
        allMenuItems, // Menu completo sem filtros
        systemPages,
        extractRoutesFromMenu,
        isActiveParent,
        findMenuItemByRoute,
        getBreadcrumb,
        filterMenuItems,
        // Novas funções de permissões
        filterMenuItemsRecursively,
        hasModuleAccess,
        getAccessibleMenuItems,
        // Estado de loading para controle do menu
        shouldShowMenu
    }
}
