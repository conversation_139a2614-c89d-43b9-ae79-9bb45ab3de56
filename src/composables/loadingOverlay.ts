import { Loading, QSpinnerOval } from 'quasar'

export function useLoadingOverlay() {
    const setLoading = async (actions: () => Promise<void>): Promise<void> => {
        try {
            Loading.show({
                spinner: QSpinnerOval,
                spinnerColor: 'primary'
            })
            await actions()
            Loading.hide()
        } catch {
            Loading.hide()
        }
    }

    return {
        setLoading
    }
}
