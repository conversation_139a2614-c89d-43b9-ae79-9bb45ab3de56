import { useAuthStore } from '@/stores/auth'
import { usePermissions } from '@/composables/usePermissions'
import type { IRouteModuleMapping } from '@/interfaces/Permissions'

export function useRoutePermissions() {
    const authStore = useAuthStore()
    // Usar a mesma instância de usePermissions para garantir estado compartilhado
    const { isModuleActiveByDescription, systemModules } = usePermissions()

    /**
     * Mapeamento de rotas para módulos/rotas do sistema
     * Este mapeamento conecta as rotas do Vue Router com os IDs do sistema de permissões
     */
    const routeModuleMapping: IRouteModuleMapping = {
        // Home - sempre permitida
        '/': {},
        '/profile': {},
        '/login': {},

        // Cadastro Geral
        '/cadastro-geral': {
            moduleDescription: 'CADASTRO GERAL',
            modulePath: 'cadastro-geral'
        },
        '/cadastro-geral/pais': {
            moduleDescription: 'CADASTRO GERAL',
            routeDescription: 'PAÍS',
            modulePath: 'cadastro-geral'
        },
        '/cadastro-geral/estado': {
            moduleDescription: 'CADASTRO GERAL',
            routeDescription: 'ESTADO',
            modulePath: 'cadastro-geral'
        },
        '/cadastro-geral/cidade': {
            moduleDescription: 'CADASTRO GERAL',
            routeDescription: 'CIDADE',
            modulePath: 'cadastro-geral'
        },
        '/cadastro-geral/regiao': {
            moduleDescription: 'CADASTRO GERAL',
            routeDescription: 'REGIÃO',
            modulePath: 'cadastro-geral'
        },
        '/cadastro-geral/textos-padronizados': {
            moduleDescription: 'CADASTRO GERAL',
            routeDescription: 'TEXTOS PADRONIZADOS',
            modulePath: 'cadastro-geral',
            onlyAdmin: true
        },
        '/cadastro-geral/ncm': {
            moduleDescription: 'CADASTRO GERAL',
            routeDescription: 'NCM',
            modulePath: 'cadastro-geral',
            onlyAdmin: true
        },
        '/cadastro-geral/categoria-cnh': {
            moduleDescription: 'CADASTRO GERAL',
            routeDescription: 'CATEGORIA CNH',
            modulePath: 'cadastro-geral',
            onlyAdmin: true
        },
        '/cadastro-geral/tipo-documento': {
            moduleDescription: 'CADASTRO GERAL',
            routeDescription: 'TIPO DE DOCUMENTO',
            modulePath: 'cadastro-geral'
        },
        '/cadastro-geral/tipo-endereco': {
            moduleDescription: 'CADASTRO GERAL',
            routeDescription: 'TIPO DE ENDEREÇO',
            modulePath: 'cadastro-geral'
        },
        '/cadastro-geral/grupo-forma-pagamento': {
            moduleDescription: 'CADASTRO GERAL',
            routeDescription: 'GRUPO FORMA DE PAGAMENTO',
            modulePath: 'cadastro-geral'
        },
        '/cadastro-geral/ramo-atividade': {
            moduleDescription: 'CADASTRO GERAL',
            routeDescription: 'RAMO DE ATIVIDADE',
            modulePath: 'cadastro-geral'
        },
        '/cadastro-geral/historico-lancamento': {
            moduleDescription: 'CADASTRO GERAL',
            routeDescription: 'HISTÓRICO',
            modulePath: 'cadastro-geral'
        },
        // crm
        '/crm': {
            moduleDescription: 'CRM',
            modulePath: 'crm'
        },
        '/crm/origem': {
            moduleDescription: 'CRM',
            routeDescription: 'ORIGEM',
            modulePath: 'crm'
        },
        '/crm/oportunidade': {
            moduleDescription: 'CRM',
            routeDescription: 'OPORTUNIDADE',
            modulePath: 'crm'
        },
        // Estoque
        '/estoque': {
            moduleDescription: 'ESTOQUE',
            modulePath: 'estoque'
        },
        '/estoque/produto-familia': {
            moduleDescription: 'ESTOQUE',
            routeDescription: 'FAMÍLIA',
            modulePath: 'estoque'
        },
        '/estoque/produto-grupo': {
            moduleDescription: 'ESTOQUE',
            routeDescription: 'GRUPO',
            modulePath: 'estoque'
        },
        '/estoque/sub-grupo': {
            moduleDescription: 'ESTOQUE',
            routeDescription: 'SUBGRUPO',
            modulePath: 'estoque'
        },
        '/estoque/produto-marca': {
            moduleDescription: 'ESTOQUE',
            routeDescription: 'MARCA',
            modulePath: 'estoque'
        },
        '/estoque/modelo': {
            moduleDescription: 'ESTOQUE',
            routeDescription: 'MODELO',
            modulePath: 'estoque'
        },
        //Venda
        '/venda': {
            moduleDescription: 'VENDA',
            modulePath: 'venda'
        },
        '/venda/categoria-cliente': {
            moduleDescription: 'VENDA',
            routeDescription: 'CATEGORIA DE CLIENTE',
            modulePath: 'venda'
        },
        '/venda/grupo-desconto': {
            moduleDescription: 'VENDA',
            routeDescription: 'GRUPO DE DESCONTO',
            modulePath: 'venda'
        },
        // Projeto
        '/projeto': {
            moduleDescription: 'PROJETO',
            modulePath: 'projeto'
        },
        '/projeto/aplicacao': {
            moduleDescription: 'PROJETO',
            routeDescription: 'APLICAÇÃO',
            modulePath: 'projeto'
        },
        '/projeto/tipo-instalacao': {
            moduleDescription: 'PROJETO',
            routeDescription: 'TIPO DE INSTALAÇÃO',
            modulePath: 'projeto'
        },
        '/projeto/tipo-perfil': {
            moduleDescription: 'PROJETO',
            routeDescription: 'TIPO DE PERFIL',
            modulePath: 'projeto'
        },
        '/projeto/tipo-material': {
            moduleDescription: 'PROJETO',
            routeDescription: 'TIPO DE MATERIAL',
            modulePath: 'projeto'
        },
        '/projeto/grupo-projeto': {
            moduleDescription: 'PROJETO',
            routeDescription: 'GRUPO DE PROJETO',
            modulePath: 'projeto'
        },
        '/projeto/grupo-material': {
            moduleDescription: 'PROJETO',
            routeDescription: 'GRUPO DE MATERIAL',
            modulePath: 'projeto'
        },
        // OS
        '/os/titulo': {
            moduleDescription: 'OS',
            routeDescription: 'TÍTULO',
            modulePath: 'ordem-servico'
        },
        '/os/parte-equipamento': {
            moduleDescription: 'OS',
            routeDescription: 'PARTE DO EQUIPAMENTO',
            modulePath: 'ordem-servico'
        },
        '/os/defeito': {
            moduleDescription: 'OS',
            routeDescription: 'DEFEITO',
            modulePath: 'ordem-servico'
        },
        '/os/causa': {
            moduleDescription: 'OS',
            routeDescription: 'CAUSA',
            modulePath: 'ordem-servico'
        },
        //
        '/os/familia-equipamento': {
            moduleDescription: 'OS',
            routeDescription: 'FAMÍLIA DE EQUIPAMENTO',
            modulePath: 'ordem-servico'
        },

        // Financeiro
        '/financeiro/carteira-cobranca': {
            moduleDescription: 'OS',
            routeDescription: 'CARTEIRA DE COBRANÇA',
            modulePath: 'ordem-servico'
        },

        //Produção
        '/producao': {
            moduleDescription: 'PRODUÇÃO',
            modulePath: 'producao'
        },
        '/producao/checklist': {
            moduleDescription: 'PRODUÇÃO',
            routeDescription: 'CHECKLIST',
            modulePath: 'producao'
        },

        // Qualidade
        '/qualidade': {
            moduleDescription: 'QUALIDADE',
            modulePath: 'qualidade'
        },
        '/qualidade/operacao': {
            moduleDescription: 'QUALIDADE',
            routeDescription: 'OPERAÇÃO',
            modulePath: 'qualidade'
        },
        '/qualidade/nao-conformidade': {
            moduleDescription: 'QUALIDADE',
            routeDescription: 'NÃO CONFORMIDADE',
            modulePath: 'qualidade'
        },
        '/qualidade/causa': {
            moduleDescription: 'QUALIDADE',
            routeDescription: 'CAUSA',
            modulePath: 'qualidade'
        },

        // RH
        '/rh': {
            moduleDescription: 'RH',
            modulePath: 'rh'
        },
        '/rh/cargo': {
            moduleDescription: 'RH',
            routeDescription: 'CARGO',
            modulePath: 'rh'
        },
        '/rh/escolaridade': {
            moduleDescription: 'RH',
            routeDescription: 'ESCOLARIDADE',
            modulePath: 'rh'
        },
        '/rh/especialidade': {
            moduleDescription: 'RH',
            routeDescription: 'ESPECIALIDADE',
            modulePath: 'rh'
        },
        '/rh/habilidade': {
            moduleDescription: 'RH',
            routeDescription: 'HABILIDADE',
            modulePath: 'rh'
        },
        '/rh/departamento': {
            moduleDescription: 'RH',
            routeDescription: 'DEPARTAMENTO',
            modulePath: 'rh'
        },

        //Configuração
        '/configuracao': {
            moduleDescription: 'CONFIGURAÇÃO',
            modulePath: 'configuracao'
        },
        '/configuracao/perfil-usuario': {
            moduleDescription: 'CONFIGURAÇÃO',
            routeDescription: 'PERFIL DE USUÁRIO',
            modulePath: 'configuracao',
            staffOnly: true
        },
        '/configuracao/modulo': {
            moduleDescription: 'CONFIGURAÇÃO',
            routeDescription: 'MÓDULO',
            modulePath: 'configuracao',
            staffOnly: true
        },
        '/configuracao/novidades': {
            moduleDescription: 'CONFIGURAÇÃO',
            routeDescription: 'NOVIDADES',
            modulePath: 'configuracao',
            staffOnly: true
        },

        // Rotas exclusivas para STAFF (sysadmin)
        '/dev': { staffOnly: true } // Página de desenvolvimento

        // Rotas exclusivas para ADMIN e STAFF
        // '/admin': { onlyAdmin: true }, // Página de administração
        // '/admin/usuarios': {
        //     onlyAdmin: true,
        //     moduleDescription: 'ADMINISTRAÇÃO',
        //     routeDescription: 'USUÁRIOS'
        // },
        // '/admin/configuracoes': {
        //     onlyAdmin: true,
        //     moduleDescription: 'ADMINISTRAÇÃO',
        //     routeDescription: 'CONFIGURAÇÕES'
        // }
    }

    /**
     * Verifica se o usuário tem permissão para acessar uma rota específica
     */
    function canAccessRoute(routePath: string): boolean {
        // Rotas sempre permitidas (públicas)
        const publicRoutes = ['/', '/login', '/profile']
        if (publicRoutes.includes(routePath)) {
            return true
        }

        // Verificar se o usuário está logado
        if (!authStore.token) {
            return false
        }

        // Buscar mapeamento da rota
        const mapping = routeModuleMapping[routePath]
        if (!mapping) {
            // Se não há mapeamento, permitir acesso (para rotas não mapeadas)
            return true
        }

        // PRIMEIRA VERIFICAÇÃO: Se o módulo está inativo, negar acesso SEMPRE
        // Esta verificação tem prioridade ABSOLUTA sobre todas as outras permissões.
        // Nem Admin nem Staff podem acessar rotas de módulos inativos.
        // Isso garante que módulos desativados sejam completamente inacessíveis.
        if (mapping.moduleDescription) {
            // Só aplicar filtro se os módulos estão carregados
            if (systemModules.value.length > 0) {
                const isActive = isModuleActiveByDescription(
                    mapping.moduleDescription
                )

                if (!isActive) {
                    return false // Módulo inativo = acesso negado para TODOS
                }
            }
            // Se módulos não estão carregados, continuar com outras verificações
        }

        // Verificar se é rota exclusiva para staff
        if (mapping.staffOnly) {
            return authStore.isStaff()
        }

        // Verificar se é rota exclusiva para admin e staff
        if (mapping.onlyAdmin) {
            return authStore.hasSpecialPrivileges()
        }

        // Verificar permissão de módulo (priorizar descrição sobre ID)
        if (mapping.moduleDescription) {
            const hasModuleAccess = authStore.hasModulePermissionByDescription(
                mapping.moduleDescription
            )
            if (!hasModuleAccess) {
                return false
            }
        } else if (mapping.moduleId) {
            // Fallback para IDs (compatibilidade)
            const hasModuleAccess = authStore.hasModulePermission(
                mapping.moduleId
            )
            if (!hasModuleAccess) {
                return false
            }
        } else if (mapping.modulePath) {
            // Fallback para path do módulo
            const hasModuleAccess = canAccessModule(mapping.modulePath)
            if (!hasModuleAccess) {
                return false
            }
        }

        // Verificar permissão de rota específica (priorizar descrição sobre ID)
        if (mapping.routeDescription) {
            const hasRouteAccess = authStore.hasRoutePermissionByDescription(
                mapping.routeDescription
            )
            if (!hasRouteAccess) {
                return false
            }
        } else if (mapping.routeId) {
            // Fallback para IDs (compatibilidade)
            const hasRouteAccess = authStore.hasRoutePermission(mapping.routeId)
            if (!hasRouteAccess) {
                return false
            }
        } else if (mapping.requiresRoutePermission) {
            // Se requer permissão específica mas não tem descrição nem ID configurado
            return false
        }

        // Admin e Staff têm acesso a rotas normais (não exclusivas) SE o módulo estiver ativo
        if (authStore.hasSpecialPrivileges()) {
            return true
        }

        return true
    }

    /**
     * Verifica se o usuário tem permissão para acessar um módulo pelo path
     */
    function canAccessModule(modulePath: string): boolean {
        // Buscar o moduleId pelo path
        const moduleMapping = Object.values(routeModuleMapping).find(
            mapping => mapping.modulePath === modulePath
        )

        if (!moduleMapping || !moduleMapping.moduleId) {
            return true // Se não encontrar mapeamento, permitir
        }

        return authStore.hasModulePermission(moduleMapping.moduleId)
    }

    /**
     * Obtém o ID do módulo baseado no path da rota
     */
    function getModuleIdByPath(routePath: string): number | null {
        const mapping = routeModuleMapping[routePath]
        return mapping?.moduleId || null
    }

    /**
     * Obtém o ID da rota baseado no path
     */
    function getRouteIdByPath(routePath: string): number | null {
        const mapping = routeModuleMapping[routePath]
        return mapping?.routeId || null
    }

    /**
     * Adiciona ou atualiza um mapeamento de rota
     */
    function addRouteMapping(
        routePath: string,
        moduleId?: number,
        routeId?: number,
        modulePath?: string,
        staffOnly?: boolean,
        onlyAdmin?: boolean
    ) {
        const mapping: {
            moduleId?: number
            routeId?: number
            modulePath?: string
            staffOnly?: boolean
            onlyAdmin?: boolean
            requiresRoutePermission?: boolean
        } = {}

        if (moduleId !== undefined) mapping.moduleId = moduleId
        if (routeId !== undefined) mapping.routeId = routeId
        if (modulePath !== undefined) mapping.modulePath = modulePath
        if (staffOnly !== undefined) mapping.staffOnly = staffOnly
        if (onlyAdmin !== undefined) mapping.onlyAdmin = onlyAdmin

        routeModuleMapping[routePath] = mapping
    }

    /**
     * Lista todas as rotas que o usuário tem acesso
     */
    function getAccessibleRoutes(): string[] {
        return Object.keys(routeModuleMapping).filter(routePath =>
            canAccessRoute(routePath)
        )
    }

    /**
     * Lista todos os módulos que o usuário tem acesso
     */
    function getAccessibleModules(): string[] {
        const permissions = authStore.getUserPermissions()
        if (!permissions) return []

        const accessibleModulePaths = new Set<string>()

        permissions.modules.forEach(modulePermission => {
            // Buscar o path do módulo pelo ID
            const moduleMapping = Object.values(routeModuleMapping).find(
                mapping => mapping.moduleId === modulePermission.id_modulo
            )

            if (moduleMapping?.modulePath) {
                accessibleModulePaths.add(moduleMapping.modulePath)
            }
        })

        return Array.from(accessibleModulePaths)
    }

    /**
     * Verifica se uma rota requer autenticação
     */
    function requiresAuth(routePath: string): boolean {
        const publicRoutes = ['/', '/login']
        return !publicRoutes.includes(routePath)
    }

    /**
     * Função de debug para identificar IDs das permissões do usuário
     * Útil para configurar o mapeamento correto das rotas
     */
    function debugUserPermissions(): void {
        // Função removida para produção
        // Use os componentes de debug em src/examples/ para visualizar permissões
        return
    }

    /**
     * Configura IDs corretos para rotas específicas
     * Use esta função para atualizar o mapeamento com IDs reais
     */
    function configureRouteIds(
        routePath: string,
        moduleId?: number,
        routeId?: number
    ): void {
        if (routeModuleMapping[routePath]) {
            if (moduleId) routeModuleMapping[routePath].moduleId = moduleId
            if (routeId) {
                routeModuleMapping[routePath].routeId = routeId
                // Remove a flag requiresRoutePermission quando routeId é configurado
                delete routeModuleMapping[routePath].requiresRoutePermission
            }
        }
    }

    /**
     * Configuração rápida para IDs conhecidos do sistema
     * Chame esta função para aplicar IDs conhecidos
     */
    function applyKnownRouteIds(): void {
        // Estoque - IDs confirmados
        // configureRouteIds('/estoque', 5) // Módulo estoque
        // configureRouteIds('/estoque/produto-marca', 5, 61) // Rota marca
        // TODO: Adicione outros IDs conforme identificados
        // configureRouteIds('/estoque/modelo', 5, ID_DA_ROTA_MODELO)
    }

    return {
        // Verificações principais
        canAccessRoute,
        canAccessModule,
        requiresAuth,

        // Utilitários
        getModuleIdByPath,
        getRouteIdByPath,
        addRouteMapping,
        getAccessibleRoutes,
        getAccessibleModules,

        // Debug e configuração
        debugUserPermissions,
        configureRouteIds,
        applyKnownRouteIds,

        // Dados
        routeModuleMapping
    }
}
