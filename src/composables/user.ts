export function useUser() {
    /**
     * Get user picture
     * @return {*}  {string}
     * @memberof useUser
     */
    function getUserPicture(): string {
        const user = JSON.parse(localStorage.getItem('user') || '{}')
        return user?.profile?.selfie_base64 || ''
    }

    /**
     * Get user username
     * @returns {string}
     * @memberof useUser
     */
    function getUserUsername(): string {
        const user = JSON.parse(localStorage.getItem('user') || '{}')
        return user.username
    }

    /**
     * Get user name
     * @returns {string}
     * @memberof useUser
     */
    function getUserName(): string {
        const user = JSON.parse(localStorage.getItem('user') || '{}')
        return user.nome
    }

    /**
     * Get user surname
     * @returns {string}
     * @memberof useUser
     */
    function getUserSurname(): string {
        const user = JSON.parse(localStorage.getItem('user') || '{}')
        return user.sobrenome
    }

    /**
     * Get user full name
     * @return {*}  {string}
     * @memberof useUser
     */
    function getUserFullName(): string {
        const user = JSON.parse(localStorage.getItem('user') || '{}')
        return `${user.nome} ${user.sobrenome}`
    }

    /**
     * Get user email
     * @returns {string}
     * @memberof useUser
     */
    function getUserEmail(): string {
        const user = JSON.parse(localStorage.getItem('user') || '{}')
        return user.email
    }

    /**
     * Get user role
     * @returns {string}
     * @memberof useUser
     */
    function getUserRole(): string {
        const user = JSON.parse(localStorage.getItem('user') || '{}')
        return user?.profile?.cargo || user?.role || 'Usuário'
    }

    /**
     * Check if user is admin (contratante - acesso irrestrito a módulos/rotas/ações ativos)
     * @returns {boolean}
     * @memberof useUser
     */
    function isUserAdmin(): boolean {
        const user = JSON.parse(localStorage.getItem('user') || '{}')
        return user.admin === true
    }

    /**
     * Check if user is staff (sysadmin - acesso irrestrito + rotas exclusivas)
     * @returns {boolean}
     * @memberof useUser
     */
    function isUserStaff(): boolean {
        const user = JSON.parse(localStorage.getItem('user') || '{}')
        return user.staff === true
    }

    /**
     * Check if user has special privileges (admin or staff)
     * @returns {boolean}
     * @memberof useUser
     */
    function hasUserSpecialPrivileges(): boolean {
        return isUserAdmin() || isUserStaff()
    }

    /**
     * Get user privilege level
     * @returns {string}
     * @memberof useUser
     */
    function getUserPrivilegeLevel(): string {
        if (isUserStaff()) return 'Staff (SysAdmin)'
        if (isUserAdmin()) return 'Admin (Contratante)'
        return 'Usuário Normal'
    }

    return {
        getUserPicture,
        getUserFullName,
        getUserEmail,
        getUserName,
        getUserSurname,
        getUserUsername,
        getUserRole,
        // Funções de privilégios
        isUserAdmin,
        isUserStaff,
        hasUserSpecialPrivileges,
        getUserPrivilegeLevel
    }
}
