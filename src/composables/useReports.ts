import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import ExcelJS from 'exceljs'
import jsPDF from 'jspdf'
import autoTable from 'jspdf-autotable'
import { useUser } from '@/composables/user'
import type { TableColumn } from '@/types/CrudTemplate'

// Extend jsPDF type to include autoTable
declare module 'jspdf' {
    interface jsPDF {
        autoTable: typeof autoTable
    }
}

export interface ReportConfig {
    title: string
    endpoint: string
    tableName?: string
    columns: TableColumn[]
    visibleColumns: string[]
    filters?: Record<string, unknown>
    sortBy?: string | undefined
    descending?: boolean | undefined
}

export interface ReportOptions {
    type: 'simple' | 'grouped' | 'summary'
    format: 'excel' | 'pdf'
    groupColumn?: string
    summaryColumn?: string
}

export const useReports = () => {
    const { t } = useI18n()
    const route = useRoute()
    const authStore = useAuthStore()
    const loading = ref(false)
    const error = ref<string | null>(null)

    // Buscar todos os dados sem paginação
    const fetchAllData = async (
        config: ReportConfig
    ): Promise<Record<string, unknown>[]> => {
        try {
            const params: Record<string, unknown> = {
                page_size: 999999 // Buscar todos os registros
            }

            // Aplicar filtros se existirem
            if (config.filters && Object.keys(config.filters).length > 0) {
                Object.entries(config.filters).forEach(([key, value]) => {
                    if (value !== null && value !== undefined && value !== '') {
                        params[key] = value
                    }
                })
            }

            // Aplicar ordenação se existir
            if (config.sortBy) {
                const sortOrder = config.descending ? '-' : ''
                params.ordering = `${sortOrder}${config.sortBy}`
            }

            const response = await authStore.asyncRequest({
                endpoint: config.endpoint,
                method: 'get',
                params
            })

            return response.data.results || []
        } catch (err) {
            error.value = t('reports.errors.fetchData')
            throw err
        }
    }

    // Função para obter colunas visíveis do localStorage
    const getVisibleColumnsFromStorage = (tableName: string): string[] => {
        try {
            const TABLES_CONFIG_KEY = 'dataTables_configurations'
            const routeName =
                route.path.replace(/\//g, '-').replace(/^-/, '') || 'default'
            const tableKey = `table-${tableName}-${routeName}`

            const allConfigs = localStorage.getItem(TABLES_CONFIG_KEY)
            if (allConfigs) {
                const configurations = JSON.parse(allConfigs)
                const config = configurations[tableKey]

                if (config && config.columns) {
                    return config.columns
                        .filter(
                            (col: { visible?: boolean; name: string }) =>
                                col.visible !== false && col.name !== 'actions'
                        )
                        .sort(
                            (a: { order?: number }, b: { order?: number }) =>
                                (a.order || 0) - (b.order || 0)
                        )
                        .map((col: { name: string }) => col.name)
                }
            }
        } catch {
            // Ignorar erros de localStorage
        }
        return []
    }

    // Filtrar colunas visíveis e ordenar
    const getVisibleColumns = (config: ReportConfig): TableColumn[] => {
        // Tentar obter colunas do localStorage primeiro
        const savedVisibleColumns = getVisibleColumnsFromStorage(
            config.tableName || ''
        )

        // Se há colunas salvas, usar elas; senão usar as do config
        const columnsToUse =
            savedVisibleColumns.length > 0
                ? savedVisibleColumns
                : config.visibleColumns

        return config.columns
            .filter(col => columnsToUse.includes(col.name))
            .filter(col => col.name !== 'actions') // Remover coluna de ações
            .sort((a, b) => (a.order || 0) - (b.order || 0))
    }

    // Tipo para dados agrupados
    interface GroupedData {
        groupName: string
        items: Record<string, unknown>[]
    }

    // Tipo para dados de resumo
    interface SummaryData {
        item: string
        quantity: number
        percentage: number
    }

    // Agrupar dados por coluna
    const groupDataByColumn = (
        data: Record<string, unknown>[],
        groupColumn: string
    ): GroupedData[] => {
        const grouped: Record<string, Record<string, unknown>[]> = {}

        data.forEach(item => {
            const groupValue = String(item[groupColumn] || 'Sem valor')
            if (!grouped[groupValue]) {
                grouped[groupValue] = []
            }
            grouped[groupValue].push(item)
        })

        // Ordenar grupos por nome
        return Object.keys(grouped)
            .sort()
            .map(groupName => ({
                groupName,
                items: grouped[groupName] || []
            }))
    }

    // Formatar filtros aplicados para exibição
    const formatAppliedFilters = (
        filters: Record<string, unknown>,
        columns: TableColumn[]
    ): string => {
        if (!filters || Object.keys(filters).length === 0) {
            return 'Nenhum filtro aplicado'
        }

        const formattedFilters = Object.entries(filters)
            .filter(
                ([, value]) =>
                    value !== null && value !== undefined && value !== ''
            )
            .map(([key, value]) => {
                // Encontrar o label da coluna
                const column = columns.find(col => col.name === key)
                const label = column?.label || key

                // Formatar o valor
                let formattedValue = String(value)
                if (Array.isArray(value)) {
                    formattedValue = value.join(', ')
                }

                return `${label}: ${formattedValue}`
            })

        return formattedFilters.length > 0
            ? formattedFilters.join(' | ')
            : 'Nenhum filtro aplicado'
    }

    // Gerar dados de resumo por coluna
    const generateSummaryData = (
        data: Record<string, unknown>[],
        summaryColumn: string
    ): SummaryData[] => {
        // Contar ocorrências de cada valor
        const counts: Record<string, number> = {}

        data.forEach(item => {
            const value = String(item[summaryColumn] || 'Sem valor')
            counts[value] = (counts[value] || 0) + 1
        })

        const total = data.length

        // Converter para array de resumo com percentuais
        return Object.entries(counts)
            .map(([item, quantity]) => ({
                item,
                quantity,
                percentage: (quantity / total) * 100
            }))
            .sort((a, b) => a.item.localeCompare(b.item))
    }

    // Gerar relatório Excel
    const generateExcelReport = async (
        config: ReportConfig,
        options: ReportOptions
    ): Promise<void> => {
        try {
            loading.value = true
            error.value = null

            const data = await fetchAllData(config)
            let visibleColumns = getVisibleColumns(config)
            const { getUserFullName } = useUser()

            // Agrupar dados se necessário
            const isGrouped = options.type === 'grouped' && options.groupColumn
            const groupedData: GroupedData[] | null = isGrouped
                ? groupDataByColumn(data, options.groupColumn!)
                : null

            // Gerar dados de resumo se necessário
            const isSummary =
                options.type === 'summary' && options.summaryColumn
            const summaryData: SummaryData[] | null = isSummary
                ? generateSummaryData(data, options.summaryColumn!)
                : null

            // Remover coluna de agrupamento das colunas visíveis se for relatório agrupado
            if (isGrouped && options.groupColumn) {
                visibleColumns = visibleColumns.filter(
                    col => col.name !== options.groupColumn
                )
            }

            // Criar workbook com ExcelJS
            const workbook = new ExcelJS.Workbook()
            const reportTitle =
                options.type === 'grouped'
                    ? 'Relatório Agrupado'
                    : options.type === 'summary'
                      ? 'Relatório Resumido Simples'
                      : 'Relatório Simples'
            const worksheet = workbook.addWorksheet(config.title)

            // Informações do usuário e registros
            const userName = getUserFullName()
            const recordsCount = data.length
            const generatedDate = new Date().toLocaleString('pt-BR')
            const filtersText = formatAppliedFilters(
                config.filters || {},
                config.columns
            )

            // Formatar informações de ordenação para Excel
            const sortTextExcel = config.sortBy
                ? `${config.columns.find(col => col.name === config.sortBy)?.label || config.sortBy} (${config.descending ? 'Decrescente' : 'Crescente'})`
                : 'Nenhuma ordenação'

            // Header compacto estilo relatório corporativo
            const headerColumns = isSummary ? 3 : visibleColumns.length

            // Linha 1: Título do relatório centralizado
            worksheet.mergeCells(
                'A1',
                `${String.fromCharCode(65 + headerColumns - 1)}1`
            )
            const titleCell = worksheet.getCell('A1')
            titleCell.value = `${reportTitle.toUpperCase()} - ${config.title.toUpperCase()}`
            titleCell.font = {
                bold: true,
                size: 12,
                color: { argb: 'FF000000' }
            }
            titleCell.alignment = { horizontal: 'center', vertical: 'middle' }
            titleCell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFFFFFFF' }
            }
            titleCell.border = {
                top: { style: 'medium', color: { argb: 'FF000000' } },
                bottom: { style: 'thin', color: { argb: 'FF000000' } },
                left: { style: 'medium', color: { argb: 'FF000000' } },
                right: { style: 'medium', color: { argb: 'FF000000' } }
            }
            worksheet.getRow(1).height = 20

            // Linha 2: Data de cadastro (com padding interno para simular margens menores)
            worksheet.mergeCells(
                'A2',
                `${String.fromCharCode(65 + headerColumns - 1)}2`
            )
            const dateCell = worksheet.getCell('A2')
            dateCell.value = `  DATA DE CADASTRO: ${generatedDate}` // Padding à esquerda
            dateCell.font = { size: 8, color: { argb: 'FF000000' } }
            dateCell.alignment = { horizontal: 'left', vertical: 'middle' }
            dateCell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFFFFFFF' }
            }
            dateCell.border = {
                top: { style: 'thin', color: { argb: 'FF000000' } },
                bottom: { style: 'thin', color: { argb: 'FF000000' } },
                left: { style: 'medium', color: { argb: 'FF000000' } },
                right: { style: 'medium', color: { argb: 'FF000000' } }
            }
            worksheet.getRow(2).height = 12

            // Linha 3: Filtros (com padding interno)
            worksheet.mergeCells(
                'A3',
                `${String.fromCharCode(65 + headerColumns - 1)}3`
            )
            const filtersCell = worksheet.getCell('A3')
            filtersCell.value = `  FILTROS: ${filtersText}` // Padding à esquerda
            filtersCell.font = { size: 8, color: { argb: 'FF000000' } }
            filtersCell.alignment = { horizontal: 'left', vertical: 'middle' }
            filtersCell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFFFFFFF' }
            }
            filtersCell.border = {
                top: { style: 'thin', color: { argb: 'FF000000' } },
                bottom: { style: 'thin', color: { argb: 'FF000000' } },
                left: { style: 'medium', color: { argb: 'FF000000' } },
                right: { style: 'medium', color: { argb: 'FF000000' } }
            }
            worksheet.getRow(3).height = 12

            // Linha 4: Ordenação (com padding interno)
            worksheet.mergeCells(
                'A4',
                `${String.fromCharCode(65 + headerColumns - 1)}4`
            )
            const sortCell = worksheet.getCell('A4')
            sortCell.value = `  ORDENADO POR: ${sortTextExcel}` // Padding à esquerda
            sortCell.font = { size: 8, color: { argb: 'FF000000' } }
            sortCell.alignment = { horizontal: 'left', vertical: 'middle' }
            sortCell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFFFFFFF' }
            }
            sortCell.border = {
                top: { style: 'thin', color: { argb: 'FF000000' } },
                bottom: { style: 'medium', color: { argb: 'FF000000' } },
                left: { style: 'medium', color: { argb: 'FF000000' } },
                right: { style: 'medium', color: { argb: 'FF000000' } }
            }
            worksheet.getRow(4).height = 12

            // Linha 5: Espaço mínimo
            worksheet.getRow(5).height = 5

            // Linha 6: Headers das colunas
            const headerRow = worksheet.getRow(6)
            visibleColumns.forEach((col, index) => {
                const cell = headerRow.getCell(index + 1)
                cell.value = col.label
                cell.font = {
                    bold: true,
                    size: 11,
                    color: { argb: 'FFFFFFFF' }
                }
                cell.alignment = { horizontal: 'center', vertical: 'middle' }
                cell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'FF1F4E79' }
                }
                cell.border = {
                    top: { style: 'medium', color: { argb: 'FF0F2A44' } },
                    bottom: { style: 'medium', color: { argb: 'FF0F2A44' } },
                    left: { style: 'thin', color: { argb: 'FF0F2A44' } },
                    right: { style: 'thin', color: { argb: 'FF0F2A44' } }
                }
            })

            // Adicionar dados (resumido, agrupados ou simples)
            let currentRow = 7

            if (isSummary && summaryData) {
                // Relatório resumido - criar colunas específicas
                const summaryHeaders = ['Item', 'Quantidade', '% Part.']

                // Headers específicos para resumo
                summaryHeaders.forEach((header, colIndex) => {
                    const headerCell = worksheet.getCell(6, colIndex + 1)
                    headerCell.value = header
                    headerCell.font = {
                        bold: true,
                        size: 9,
                        color: { argb: 'FFFFFFFF' }
                    }
                    headerCell.alignment = {
                        horizontal: 'center',
                        vertical: 'middle'
                    }
                    headerCell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FF2980B9' }
                    }
                    headerCell.border = {
                        top: { style: 'thin', color: { argb: 'FFFFFFFF' } },
                        bottom: { style: 'thin', color: { argb: 'FFFFFFFF' } },
                        left: { style: 'thin', color: { argb: 'FFFFFFFF' } },
                        right: { style: 'thin', color: { argb: 'FFFFFFFF' } }
                    }
                })

                // Dados do resumo
                let totalQuantity = 0
                summaryData.forEach((item, rowIndex) => {
                    const dataRow = worksheet.getRow(currentRow)
                    const isOddRow = rowIndex % 2 === 1
                    const fillColor = isOddRow ? 'FFF0F8FF' : 'FFFFFFFF'

                    // Item
                    const itemCell = dataRow.getCell(1)
                    itemCell.value = item.item
                    itemCell.font = { size: 10 }
                    itemCell.alignment = { vertical: 'middle' }
                    itemCell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: fillColor }
                    }
                    itemCell.border = {
                        top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                        bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                        left: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                        right: { style: 'thin', color: { argb: 'FFD0D0D0' } }
                    }

                    // Quantidade
                    const quantityCell = dataRow.getCell(2)
                    quantityCell.value = item.quantity
                    quantityCell.font = { size: 10 }
                    quantityCell.alignment = {
                        horizontal: 'center',
                        vertical: 'middle'
                    }
                    quantityCell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: fillColor }
                    }
                    quantityCell.border = {
                        top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                        bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                        left: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                        right: { style: 'thin', color: { argb: 'FFD0D0D0' } }
                    }

                    // Percentual
                    const percentageCell = dataRow.getCell(3)
                    percentageCell.value = item.percentage.toFixed(6)
                    percentageCell.font = { size: 10 }
                    percentageCell.alignment = {
                        horizontal: 'center',
                        vertical: 'middle'
                    }
                    percentageCell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: fillColor }
                    }
                    percentageCell.border = {
                        top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                        bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                        left: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                        right: { style: 'thin', color: { argb: 'FFD0D0D0' } }
                    }

                    totalQuantity += item.quantity
                    currentRow++
                })

                // Linha de total
                const totalRow = worksheet.getRow(currentRow)

                // TOTAL
                const totalLabelCell = totalRow.getCell(1)
                totalLabelCell.value = 'TOTAL'
                totalLabelCell.font = {
                    bold: true,
                    size: 10,
                    color: { argb: 'FFFFFFFF' }
                }
                totalLabelCell.alignment = {
                    horizontal: 'center',
                    vertical: 'middle'
                }
                totalLabelCell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'FF2980B9' }
                }
                totalLabelCell.border = {
                    top: { style: 'medium', color: { argb: 'FF1F4E79' } },
                    bottom: { style: 'medium', color: { argb: 'FF1F4E79' } },
                    left: { style: 'medium', color: { argb: 'FF1F4E79' } },
                    right: { style: 'medium', color: { argb: 'FF1F4E79' } }
                }

                // Total quantidade
                const totalQuantityCell = totalRow.getCell(2)
                totalQuantityCell.value = totalQuantity
                totalQuantityCell.font = {
                    bold: true,
                    size: 10,
                    color: { argb: 'FFFFFFFF' }
                }
                totalQuantityCell.alignment = {
                    horizontal: 'center',
                    vertical: 'middle'
                }
                totalQuantityCell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'FF2980B9' }
                }
                totalQuantityCell.border = {
                    top: { style: 'medium', color: { argb: 'FF1F4E79' } },
                    bottom: { style: 'medium', color: { argb: 'FF1F4E79' } },
                    left: { style: 'medium', color: { argb: 'FF1F4E79' } },
                    right: { style: 'medium', color: { argb: 'FF1F4E79' } }
                }

                // Célula vazia para percentual
                const totalPercentageCell = totalRow.getCell(3)
                totalPercentageCell.value = ''
                totalPercentageCell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'FF2980B9' }
                }
                totalPercentageCell.border = {
                    top: { style: 'medium', color: { argb: 'FF1F4E79' } },
                    bottom: { style: 'medium', color: { argb: 'FF1F4E79' } },
                    left: { style: 'medium', color: { argb: 'FF1F4E79' } },
                    right: { style: 'medium', color: { argb: 'FF1F4E79' } }
                }

                worksheet.getRow(currentRow).height = 20
                currentRow++
            } else if (isGrouped && groupedData) {
                // Relatório agrupado
                groupedData.forEach((group, groupIndex) => {
                    // Linha do grupo
                    worksheet.mergeCells(
                        `A${currentRow}`,
                        `${String.fromCharCode(65 + visibleColumns.length - 1)}${currentRow}`
                    )
                    const groupCell = worksheet.getCell(`A${currentRow}`)
                    groupCell.value = `${group.groupName} (${group.items.length} registros)`
                    groupCell.font = {
                        bold: true,
                        size: 11,
                        color: { argb: 'FFFFFFFF' }
                    }
                    groupCell.alignment = {
                        horizontal: 'left',
                        vertical: 'middle'
                    }
                    groupCell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FF4A90E2' }
                    }
                    groupCell.border = {
                        top: { style: 'medium', color: { argb: 'FF2980B9' } },
                        bottom: {
                            style: 'medium',
                            color: { argb: 'FF2980B9' }
                        },
                        left: { style: 'medium', color: { argb: 'FF2980B9' } },
                        right: { style: 'medium', color: { argb: 'FF2980B9' } }
                    }
                    worksheet.getRow(currentRow).height = 22
                    currentRow++

                    // Dados do grupo
                    group.items.forEach((item, itemIndex) => {
                        const dataRow = worksheet.getRow(currentRow)
                        const isOddRow = itemIndex % 2 === 1
                        const fillColor = isOddRow ? 'FFF0F8FF' : 'FFFFFFFF'

                        visibleColumns.forEach((col, colIndex) => {
                            const cell = dataRow.getCell(colIndex + 1)
                            const value = item[col.field as string]
                            cell.value =
                                value !== null && value !== undefined
                                    ? String(value)
                                    : ''
                            cell.font = { size: 10 }
                            cell.alignment = { vertical: 'middle' }
                            cell.fill = {
                                type: 'pattern',
                                pattern: 'solid',
                                fgColor: { argb: fillColor }
                            }
                            cell.border = {
                                top: {
                                    style: 'thin',
                                    color: { argb: 'FFD0D0D0' }
                                },
                                bottom: {
                                    style: 'thin',
                                    color: { argb: 'FFD0D0D0' }
                                },
                                left: {
                                    style: 'thin',
                                    color: { argb: 'FFD0D0D0' }
                                },
                                right: {
                                    style: 'thin',
                                    color: { argb: 'FFD0D0D0' }
                                }
                            }
                        })
                        currentRow++
                    })

                    // Linha de espaço entre grupos (exceto no último)
                    if (groupIndex < groupedData.length - 1) {
                        worksheet.getRow(currentRow).height = 8
                        currentRow++
                    }
                })
            } else {
                // Relatório simples
                data.forEach((item, rowIndex) => {
                    const dataRow = worksheet.getRow(currentRow)
                    const isOddRow = rowIndex % 2 === 1
                    const fillColor = isOddRow ? 'FFF0F8FF' : 'FFFFFFFF'

                    visibleColumns.forEach((col, colIndex) => {
                        const cell = dataRow.getCell(colIndex + 1)
                        const value = item[col.field as string]
                        cell.value =
                            value !== null && value !== undefined
                                ? String(value)
                                : ''
                        cell.font = { size: 10 }
                        cell.alignment = { vertical: 'middle' }
                        cell.fill = {
                            type: 'pattern',
                            pattern: 'solid',
                            fgColor: { argb: fillColor }
                        }
                        cell.border = {
                            top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                            bottom: {
                                style: 'thin',
                                color: { argb: 'FFD0D0D0' }
                            },
                            left: {
                                style: 'thin',
                                color: { argb: 'FFD0D0D0' }
                            },
                            right: {
                                style: 'thin',
                                color: { argb: 'FFD0D0D0' }
                            }
                        }
                    })
                    currentRow++
                })
            }

            // Criar footer estilizado
            const lastDataRow = currentRow - 1
            const footerStartRow = lastDataRow + 2
            const footerColumns = isSummary ? 3 : visibleColumns.length

            // Linha de separação estilizada
            const separatorRow = worksheet.getRow(lastDataRow + 1)
            separatorRow.height = 8
            for (let col = 1; col <= footerColumns; col++) {
                const cell = separatorRow.getCell(col)
                cell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'FFE0E0E0' }
                }
            }

            // Footer - Linha 1: Informações gerais
            worksheet.mergeCells(
                `A${footerStartRow}`,
                `${String.fromCharCode(65 + footerColumns - 1)}${footerStartRow}`
            )
            const footerInfoCell = worksheet.getCell(`A${footerStartRow}`)
            footerInfoCell.value = `Gerado em: ${generatedDate} | Usuário: ${userName}`
            footerInfoCell.font = {
                bold: true,
                size: 10,
                color: { argb: 'FF1F4E79' }
            }
            footerInfoCell.alignment = {
                horizontal: 'center',
                vertical: 'middle'
            }
            footerInfoCell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFE8F4FD' }
            }
            footerInfoCell.border = {
                top: { style: 'thin', color: { argb: 'FF2980B9' } },
                bottom: { style: 'thin', color: { argb: 'FF2980B9' } },
                left: { style: 'thin', color: { argb: 'FF2980B9' } },
                right: { style: 'thin', color: { argb: 'FF2980B9' } }
            }

            // Footer - Linha 2: Total de registros (destaque)
            worksheet.mergeCells(
                `A${footerStartRow + 1}`,
                `${String.fromCharCode(65 + footerColumns - 1)}${footerStartRow + 1}`
            )
            const footerCountCell = worksheet.getCell(`A${footerStartRow + 1}`)
            footerCountCell.value = `Total de registros: ${recordsCount}`
            footerCountCell.font = {
                bold: true,
                size: 12,
                color: { argb: 'FFFFFFFF' }
            }
            footerCountCell.alignment = {
                horizontal: 'center',
                vertical: 'middle'
            }
            footerCountCell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FF2980B9' }
            }
            footerCountCell.border = {
                top: { style: 'medium', color: { argb: 'FF1F4E79' } },
                bottom: { style: 'medium', color: { argb: 'FF1F4E79' } },
                left: { style: 'medium', color: { argb: 'FF1F4E79' } },
                right: { style: 'medium', color: { argb: 'FF1F4E79' } }
            }

            // Definir altura das linhas do footer
            worksheet.getRow(footerStartRow).height = 20
            worksheet.getRow(footerStartRow + 1).height = 25

            // Footer - Linha 3: Copyright
            worksheet.mergeCells(
                `A${footerStartRow + 2}`,
                `${String.fromCharCode(65 + footerColumns - 1)}${footerStartRow + 2}`
            )
            const copyrightCell = worksheet.getCell(`A${footerStartRow + 2}`)
            copyrightCell.value = '@Software de Gestão - www.corpsystem.com.br'
            copyrightCell.font = {
                italic: true,
                size: 8,
                color: { argb: 'FF888888' }
            }
            copyrightCell.alignment = {
                horizontal: 'center',
                vertical: 'middle'
            }
            copyrightCell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFF5F5F5' }
            }
            copyrightCell.border = {
                top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
                bottom: { style: 'medium', color: { argb: 'FF1F4E79' } },
                left: { style: 'medium', color: { argb: 'FF1F4E79' } },
                right: { style: 'medium', color: { argb: 'FF1F4E79' } }
            }
            worksheet.getRow(footerStartRow + 2).height = 18

            // Definir largura automática das colunas (permite redimensionamento)
            if (isSummary) {
                // Para relatório resumido, definir larguras específicas mas redimensionáveis
                worksheet.getColumn(1).width = 30 // Item
                worksheet.getColumn(2).width = 15 // Quantidade
                worksheet.getColumn(3).width = 15 // Percentual
            } else {
                // Para outros relatórios, largura automática baseada no conteúdo
                visibleColumns.forEach((_, index) => {
                    worksheet.getColumn(index + 1).width = 20 // Largura inicial maior
                })
            }

            // Permitir redimensionamento automático das colunas
            worksheet.properties.defaultColWidth = 15

            // Configurar worksheet para permitir redimensionamento
            worksheet.views = [
                {
                    state: 'normal',
                    showGridLines: true,
                    showRowColHeaders: true
                }
            ]

            // Definir altura das linhas do header
            worksheet.getRow(1).height = 20
            worksheet.getRow(2).height = 12
            worksheet.getRow(3).height = 12
            worksheet.getRow(4).height = 12
            worksheet.getRow(5).height = 5
            worksheet.getRow(6).height = 18

            // Isolar a grid do relatório - definir área de impressão e ocultar resto
            const lastColumn = String.fromCharCode(65 + footerColumns - 1)
            const lastRow = footerStartRow + 2

            // Definir área de impressão (apenas o relatório)
            worksheet.pageSetup.printArea = `A1:${lastColumn}${lastRow}`

            // Ocultar colunas após a última coluna do relatório
            for (let col = footerColumns + 1; col <= 50; col++) {
                worksheet.getColumn(col).hidden = true
            }

            // Ocultar linhas após o footer
            for (let row = lastRow + 1; row <= 1000; row++) {
                worksheet.getRow(row).hidden = true
            }

            // Configurações adicionais da planilha
            worksheet.views = [
                {
                    state: 'normal',
                    showGridLines: false, // Ocultar linhas de grade
                    showRowColHeaders: false, // Ocultar cabeçalhos de linha/coluna
                    zoomScale: 100
                }
            ]

            // Proteger planilha para evitar edições acidentais (opcional)
            worksheet.protect('', {
                selectLockedCells: true,
                selectUnlockedCells: true,
                formatCells: false,
                formatColumns: false,
                formatRows: false,
                insertColumns: false,
                insertRows: false,
                deleteColumns: false,
                deleteRows: false
            })

            // Gerar arquivo Excel em formato .xls
            const buffer = await workbook.xlsx.writeBuffer()
            const blob = new Blob([buffer], {
                type: 'application/vnd.ms-excel'
            })
            const url = URL.createObjectURL(blob)

            const link = document.createElement('a')
            link.href = url
            link.download = `${config.title.replace(/\s+/g, '_')}.xls`
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            URL.revokeObjectURL(url)
        } catch (err) {
            error.value = t('reports.errors.generateExcel')
            throw err
        } finally {
            loading.value = false
        }
    }

    // Gerar relatório PDF
    const generatePdfReport = async (
        config: ReportConfig,
        options: ReportOptions
    ): Promise<void> => {
        try {
            loading.value = true
            error.value = null

            const data = await fetchAllData(config)
            let visibleColumns = getVisibleColumns(config)
            const { getUserFullName } = useUser()
            const filtersText = formatAppliedFilters(
                config.filters || {},
                config.columns
            )

            // Formatar informações de ordenação
            const sortText = config.sortBy
                ? `${config.columns.find(col => col.name === config.sortBy)?.label || config.sortBy} (${config.descending ? 'Decrescente' : 'Crescente'})`
                : 'Nenhuma ordenação'

            // Agrupar dados se necessário
            const isGrouped = options.type === 'grouped' && options.groupColumn
            const groupedData: GroupedData[] | null = isGrouped
                ? groupDataByColumn(data, options.groupColumn!)
                : null

            // Gerar dados de resumo se necessário
            const isSummary =
                options.type === 'summary' && options.summaryColumn
            const summaryData: SummaryData[] | null = isSummary
                ? generateSummaryData(data, options.summaryColumn!)
                : null

            // Remover coluna de agrupamento das colunas visíveis se for relatório agrupado
            if (isGrouped && options.groupColumn) {
                visibleColumns = visibleColumns.filter(
                    col => col.name !== options.groupColumn
                )
            }

            // Criar PDF
            const doc = new jsPDF('l', 'mm', 'a4') // Landscape para mais colunas
            const pageWidth = doc.internal.pageSize.width
            const pageHeight = doc.internal.pageSize.height
            const generatedDate = new Date().toLocaleString('pt-BR')

            // Informações do usuário e registros
            const userName = getUserFullName()
            const recordsCount = data.length

            // Função para adicionar header compacto estilo corporativo
            const addHeader = () => {
                // Título principal centralizado e em maiúsculas (margens menores)
                doc.setFontSize(12)
                doc.setFont('helvetica', 'bold')
                doc.setTextColor(0, 0, 0) // Preto
                const reportType =
                    options.type === 'grouped'
                        ? 'RELATÓRIO AGRUPADO'
                        : options.type === 'summary'
                          ? 'RELATÓRIO RESUMIDO SIMPLES'
                          : 'RELATÓRIO SIMPLES'
                const fullTitle = `${reportType} - ${config.title.toUpperCase()}`

                // Truncar título se muito longo para caber com margens menores
                const maxTitleWidth = pageWidth - 20
                let finalTitle = fullTitle
                if (doc.getTextWidth(fullTitle) > maxTitleWidth) {
                    finalTitle = fullTitle.substring(0, 80) + '...'
                }

                const titleWidth = doc.getTextWidth(finalTitle)
                const centerX = pageWidth / 2
                doc.text(finalTitle, centerX - titleWidth / 2, 15)

                // Linha de separação superior (margens menores que a tabela)
                doc.setDrawColor(0, 0, 0)
                doc.setLineWidth(0.8)
                doc.line(10, 17, pageWidth - 10, 17)

                // Informações de contexto - uma por linha (margens menores)
                doc.setFontSize(8)
                doc.setFont('helvetica', 'normal')
                doc.setTextColor(0, 0, 0)

                // Linha 1: Data de cadastro
                doc.text(`DATA DE CADASTRO: ${generatedDate}`, 10, 22)

                // Linha 2: Filtros (truncar se muito longo)
                const maxWidth = pageWidth - 20 // Margem menor que a tabela
                let filtersLine = `FILTROS: ${filtersText}`
                if (doc.getTextWidth(filtersLine) > maxWidth) {
                    filtersLine = filtersLine.substring(0, 110) + '...'
                }
                doc.text(filtersLine, 10, 26)

                // Linha 3: Ordenação
                let sortLine = `ORDENADO POR: ${sortText}`
                if (doc.getTextWidth(sortLine) > maxWidth) {
                    sortLine = sortLine.substring(0, 90) + '...'
                }
                doc.text(sortLine, 10, 30)

                // Linha de separação inferior (margens menores que a tabela)
                doc.setDrawColor(0, 0, 0)
                doc.setLineWidth(0.8)
                doc.line(10, 32, pageWidth - 10, 32)

                // Resetar cor do texto para preto
                doc.setTextColor(0, 0, 0)
            }

            // Função para adicionar footer compacto
            const addFooter = (pageNumber: number, totalPages: number) => {
                doc.setFontSize(7)
                doc.setFont('helvetica', 'normal')
                doc.setTextColor(80, 80, 80)

                // Linha 1: Data/hora, usuário e paginação (compacta)
                doc.text(`Gerado: ${generatedDate}`, 20, pageHeight - 15)
                doc.text(`Usuário: ${userName}`, 20, pageHeight - 11)
                doc.text(
                    `Página ${pageNumber}/${totalPages} | Registros: ${recordsCount}`,
                    pageWidth - 20,
                    pageHeight - 15,
                    { align: 'right' }
                )

                // Linha 2: Copyright (centralizado e compacto)
                doc.setFontSize(6)
                doc.setFont('helvetica', 'italic')
                doc.setTextColor(120, 120, 120)
                const copyrightText =
                    '@Software de Gestão - www.corpsystem.com.br'
                const copyrightWidth = doc.getTextWidth(copyrightText)
                doc.text(
                    copyrightText,
                    (pageWidth - copyrightWidth) / 2,
                    pageHeight - 7
                )

                // Resetar cor e fonte do texto
                doc.setTextColor(0, 0, 0)
                doc.setFont('helvetica', 'normal')
                doc.setFontSize(8)
            }

            // Preparar headers da tabela
            let headers: string[] = []
            let rows: string[][] = []

            if (isSummary && summaryData) {
                // Relatório resumido - headers específicos
                headers = ['Item', 'Quantidade', '% Part.']

                // Dados do resumo
                let totalQuantity = 0
                summaryData.forEach(item => {
                    rows.push([
                        item.item,
                        item.quantity.toString(),
                        item.percentage.toFixed(6)
                    ])
                    totalQuantity += item.quantity
                })

                // Linha de total
                rows.push(['TOTAL', totalQuantity.toString(), ''])
            } else if (isGrouped && groupedData) {
                // Relatório agrupado
                headers = visibleColumns.map(col => col.label)
                // Relatório agrupado - criar linhas com grupos
                groupedData.forEach((group, groupIndex) => {
                    // Linha do grupo (mesclada)
                    const groupRow = Array.from(
                        { length: visibleColumns.length },
                        () => ''
                    )
                    groupRow[0] = `${group.groupName} (${group.items.length} registros)`
                    rows.push(groupRow)

                    // Dados do grupo
                    group.items.forEach(item => {
                        const dataRow = visibleColumns.map(col => {
                            const value = item[col.field as string]
                            return value !== null && value !== undefined
                                ? String(value)
                                : ''
                        })
                        rows.push(dataRow)
                    })

                    // Linha de espaço entre grupos (exceto no último)
                    if (groupIndex < groupedData.length - 1) {
                        rows.push(
                            Array.from(
                                { length: visibleColumns.length },
                                () => ''
                            )
                        )
                    }
                })
            } else {
                // Relatório simples
                headers = visibleColumns.map(col => col.label)
                rows = data.map(item => {
                    return visibleColumns.map(col => {
                        const value = item[col.field as string]
                        return value !== null && value !== undefined
                            ? String(value)
                            : ''
                    })
                })
            }

            // Configurar tabela com callbacks para header/footer
            autoTable(doc, {
                head: [headers],
                body: rows,
                startY: 38, // Aumentado para dar mais espaço após o header
                styles: {
                    fontSize: 7, // Reduzido de 8 para 7
                    cellPadding: 1, // Reduzido de 2 para 1
                    lineColor: [200, 200, 200],
                    lineWidth: 0.1
                },
                headStyles: {
                    fillColor: [41, 128, 185],
                    textColor: 255,
                    fontStyle: 'bold',
                    fontSize: 7, // Mesmo tamanho do corpo
                    cellPadding: 1.5 // Pouco mais que o corpo para destaque
                },
                alternateRowStyles: {
                    fillColor: [248, 248, 248] // Linhas zebradas mais sutis
                },
                margin: { top: 38, left: 15, right: 15, bottom: 20 }, // Margem superior ajustada para o header
                tableWidth: 'auto',
                columnStyles: {},
                theme: 'grid', // Adiciona bordas para melhor legibilidade
                willDrawPage: () => {
                    // Adicionar header em cada página (incluindo a primeira)
                    addHeader()
                },
                didDrawPage: data => {
                    // Adicionar footer em cada página
                    const totalPages = Math.ceil(rows.length / 25) // Estimativa de páginas
                    addFooter(data.pageNumber, totalPages)
                },
                didParseCell: data => {
                    if (data.row.index < rows.length) {
                        const rowData = rows[data.row.index]
                        if (rowData) {
                            // Estilizar relatório resumido
                            if (isSummary) {
                                // Verificar se é linha de total
                                const isTotalRow = rowData[0] === 'TOTAL'
                                if (isTotalRow) {
                                    data.cell.styles.fillColor = [41, 128, 185] // Azul
                                    data.cell.styles.textColor = [255, 255, 255] // Branco
                                    data.cell.styles.fontStyle = 'bold'
                                    data.cell.styles.fontSize = 8
                                    data.cell.styles.cellPadding = 2
                                }
                            }

                            // Estilizar relatório agrupado
                            if (isGrouped) {
                                // Verificar se é uma linha de grupo (primeira célula preenchida, outras vazias)
                                const isGroupRow =
                                    rowData[0] &&
                                    rowData[0].includes('registros)') &&
                                    rowData.slice(1).every(cell => cell === '')

                                if (isGroupRow) {
                                    // Estilo para linha de grupo
                                    data.cell.styles.fillColor = [74, 144, 226] // Azul
                                    data.cell.styles.textColor = [255, 255, 255] // Branco
                                    data.cell.styles.fontStyle = 'bold'
                                    data.cell.styles.fontSize = 8
                                    data.cell.styles.cellPadding = 2
                                }

                                // Verificar se é linha de espaço (todas as células vazias)
                                const isSpaceRow = rowData.every(
                                    cell => cell === ''
                                )
                                if (isSpaceRow) {
                                    data.cell.styles.fillColor = [240, 240, 240] // Cinza claro
                                    data.cell.styles.minCellHeight = 3
                                }
                            }
                        }
                    }
                }
            })

            // Obter número total de páginas
            const totalPages = doc.getNumberOfPages()

            // Adicionar footer em todas as páginas
            for (let i = 1; i <= totalPages; i++) {
                doc.setPage(i)
                addFooter(i, totalPages)
            }

            // Fazer download do arquivo PDF
            const fileName = `${config.title.replace(/\s+/g, '_')}.pdf`
            doc.save(fileName)
        } catch (err) {
            error.value = t('reports.errors.generatePdf')
            throw err
        } finally {
            loading.value = false
        }
    }

    // Função principal para gerar relatório
    const generateReport = async (
        config: ReportConfig,
        options: ReportOptions
    ): Promise<void> => {
        try {
            if (
                options.type !== 'simple' &&
                options.type !== 'grouped' &&
                options.type !== 'summary'
            ) {
                throw new Error(t('reports.errors.unsupportedType'))
            }

            if (options.format === 'excel') {
                await generateExcelReport(config, options)
            } else if (options.format === 'pdf') {
                await generatePdfReport(config, options)
            } else {
                throw new Error(t('reports.errors.unsupportedFormat'))
            }
        } catch (err) {
            // eslint-disable-next-line no-console
            console.error('Erro ao gerar relatório:', err)
            throw err
        }
    }

    return {
        loading,
        error,
        generateReport,
        generateExcelReport,
        generatePdfReport
    }
}
