import { ref, computed } from 'vue'

// Interface para tipo de texto padronizado
export interface TipoTextoPadronizado {
    id: number
    value: string
    descricao: string
}

// Dados estáticos dos tipos de texto padronizado
const listTextosPadronizados: TipoTextoPadronizado[] = [
    {
        id: 1,
        value: 'TEXTO INICIAL',
        descricao: 'TEXTO INICIAL'
    },
    {
        id: 2,
        value: 'CONDIÇÃO COMERCIAL',
        descricao: 'CONDIÇÃO COMERCIAL'
    },
    {
        id: 3,
        value: 'INFORMAÇÃO ADICIONAL',
        descricao: 'INFORMAÇÃO ADICIONAL'
    },
    {
        id: 4,
        value: 'TEXTO FINAL',
        descricao: 'TEXTO FINAL'
    },
    {
        id: 5,
        value: 'TEXTO CONTRATO COMPRA-VENDA',
        descricao: 'TEXTO CONTRATO COMPRA-VENDA'
    },
    {
        id: 6,
        value: 'TEXTO CONTRATO',
        descricao: 'TEXTO CONTRATO'
    }
]

/**
 * Composable para gerenciar tipos de texto padronizado
 */
export function useTextoPadronizado() {
    const loading = ref(false)
    const searchTerm = ref('')

    // Lista completa de tipos
    const allTipos = ref<TipoTextoPadronizado[]>([...listTextosPadronizados])

    // Lista filtrada baseada na busca
    const filteredTipos = computed(() => {
        if (!searchTerm.value) {
            return allTipos.value
        }

        const needle = searchTerm.value.toLowerCase()
        return allTipos.value.filter(
            tipo =>
                tipo.descricao.toLowerCase().includes(needle) ||
                tipo.value.toLowerCase().includes(needle)
        )
    })

    // Opções formatadas para o q-select
    const tiposOptions = computed(() => {
        return filteredTipos.value.map(tipo => ({
            label: tipo.descricao,
            value: tipo.id
        }))
    })

    /**
     * Carrega os tipos (simula carregamento assíncrono)
     */
    const loadTipos = async (): Promise<void> => {
        loading.value = true

        try {
            // Simular delay de carregamento
            await new Promise(resolve => setTimeout(resolve, 300))

            // Os dados já estão carregados estaticamente
            // Aqui poderia ser uma chamada real para API se necessário
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error('Erro ao carregar tipos:', error)
            throw error
        } finally {
            loading.value = false
        }
    }

    /**
     * Filtra os tipos baseado no termo de busca
     */
    const filterTipos = (search: string): void => {
        searchTerm.value = search
    }

    /**
     * Busca um tipo por ID
     */
    const getTipoById = (id: number): TipoTextoPadronizado | undefined => {
        return allTipos.value.find(tipo => tipo.id === id)
    }

    /**
     * Busca um tipo por value
     */
    const getTipoByValue = (
        value: string
    ): TipoTextoPadronizado | undefined => {
        return allTipos.value.find(tipo => tipo.value === value)
    }

    /**
     * Limpa o filtro de busca
     */
    const clearFilter = (): void => {
        searchTerm.value = ''
    }

    return {
        // Estado
        loading,
        searchTerm,

        // Dados
        allTipos,
        filteredTipos,
        tiposOptions,

        // Métodos
        loadTipos,
        filterTipos,
        getTipoById,
        getTipoByValue,
        clearFilter
    }
}

// Export default para compatibilidade
export default useTextoPadronizado
