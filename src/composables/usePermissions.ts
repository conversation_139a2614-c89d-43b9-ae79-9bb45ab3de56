import { ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import type {
    IUserPermissions,
    ISystemModule,
    ISystemAction,
    IPermissionModule,
    IPermissionStructure,
    IModulesApiResponse,
    IActionsApiResponse
} from '@/interfaces/Permissions'

// Estado global compartilhado (singleton)
// Implementado para garantir que todas as instâncias de usePermissions()
// compartilhem o mesmo estado, evitando problemas de sincronização
const globalSystemModules = ref<ISystemModule[]>([])
const globalSystemActions = ref<ISystemAction[]>([])
const globalIsLoading = ref(false)
const globalError = ref<string | null>(null)

export function usePermissions() {
    const authStore = useAuthStore()

    // Usar estado global compartilhado
    const systemModules = globalSystemModules
    const systemActions = globalSystemActions
    const isLoading = globalIsLoading
    const error = globalError

    /**
     * Busca todos os módulos do sistema
     */
    async function fetchSystemModules(): Promise<ISystemModule[]> {
        try {
            const response = await authStore.asyncRequest({
                endpoint: '/api/corpsystem/produto-service/modulo',
                method: 'get',
                params: {
                    page: 1,
                    page_size: 10000
                }
            })

            const data = response.data as IModulesApiResponse
            systemModules.value = data.results
            return data.results
        } catch (err) {
            error.value = 'Erro ao buscar módulos do sistema'
            throw err
        }
    }

    /**
     * Busca todas as ações do sistema
     */
    async function fetchSystemActions(): Promise<ISystemAction[]> {
        try {
            const response = await authStore.asyncRequest({
                endpoint: '/api/corpsystem/produto-service/acao',
                method: 'get',
                params: {
                    page: 1,
                    page_size: 10000
                }
            })

            const data = response.data as IActionsApiResponse
            systemActions.value = data.results
            return data.results
        } catch (err) {
            error.value = 'Erro ao buscar ações do sistema'
            throw err
        }
    }

    /**
     * Organiza as permissões do usuário em estrutura hierárquica
     */
    function buildUserPermissionStructure(
        userPermissions: IUserPermissions,
        modules: ISystemModule[]
    ): IPermissionModule[] {
        // Verificar se o usuário tem privilégios especiais
        const isUserAdmin = authStore.isAdmin()
        const isUserStaff = authStore.isStaff()

        // Admin e Staff têm acesso a todos os módulos/rotas/ações ativos
        if (isUserAdmin || isUserStaff) {
            return modules
                .filter(module => module.ativo) // Apenas módulos ativos
                .map(module => ({
                    id: module.id,
                    descricao: module.descricao,
                    path_em_rota: module.path_em_rota,
                    hasPermission: true,
                    ativo: module.ativo,
                    rotas: module.rota
                        .filter(route => route.ativo) // Apenas rotas ativas
                        .map(route => ({
                            id: route.id,
                            descricao: route.descricao,
                            hasPermission: true,
                            ativo: route.ativo,
                            acoes: route.acao_rota
                                .filter(action => action.ativo) // Apenas ações ativas
                                .map(action => ({
                                    id: action.id,
                                    descricao: action.descricao,
                                    hasPermission: true,
                                    ativo: action.ativo
                                }))
                        }))
                }))
        }

        // Para usuários normais, usar as permissões específicas
        const userModuleIds = new Set(
            userPermissions.modules.map(m => m.id_modulo)
        )
        const userRouteIds = new Set(userPermissions.routes.map(r => r.id_rota))
        const userActionIds = new Set(
            userPermissions.actions.map(a => a.id_acao_rota)
        )

        return modules
            .filter(module => userModuleIds.has(module.id))
            .map(module => ({
                id: module.id,
                descricao: module.descricao,
                path_em_rota: module.path_em_rota,
                hasPermission: true,
                ativo: module.ativo,
                rotas: module.rota
                    .filter(route => userRouteIds.has(route.id))
                    .map(route => ({
                        id: route.id,
                        descricao: route.descricao,
                        hasPermission: true,
                        ativo: route.ativo,
                        acoes: route.acao_rota
                            .filter(action => userActionIds.has(action.id))
                            .map(action => ({
                                id: action.id,
                                descricao: action.descricao,
                                hasPermission: true,
                                ativo: action.ativo
                            }))
                    }))
            }))
    }

    /**
     * Organiza todos os módulos do sistema em estrutura hierárquica
     */
    function buildSystemPermissionStructure(
        modules: ISystemModule[]
    ): IPermissionModule[] {
        return modules.map(module => ({
            id: module.id,
            descricao: module.descricao,
            path_em_rota: module.path_em_rota,
            hasPermission: false, // Por padrão, sistema não tem permissão
            ativo: module.ativo,
            rotas: module.rota.map(route => ({
                id: route.id,
                descricao: route.descricao,
                hasPermission: false,
                ativo: route.ativo,
                acoes: route.acao_rota.map(action => ({
                    id: action.id,
                    descricao: action.descricao,
                    hasPermission: false,
                    ativo: action.ativo
                }))
            }))
        }))
    }

    /**
     * Método principal que retorna a estrutura completa de permissões
     */
    async function getPermissionStructure(
        userPermissions: IUserPermissions
    ): Promise<IPermissionStructure> {
        try {
            isLoading.value = true
            error.value = null

            // Buscar dados do sistema se não estiverem em cache
            let modules = systemModules.value
            let actions = systemActions.value

            if (modules.length === 0) {
                modules = await fetchSystemModules()
            }

            if (actions.length === 0) {
                actions = await fetchSystemActions()
            }

            // Construir estruturas
            const userPermissionStructure = buildUserPermissionStructure(
                userPermissions,
                modules
            )

            const systemPermissionStructure =
                buildSystemPermissionStructure(modules)

            return {
                userPermissions: userPermissionStructure,
                systemPermissions: systemPermissionStructure
            }
        } catch (err) {
            error.value = 'Erro ao processar estrutura de permissões'
            throw err
        } finally {
            isLoading.value = false
        }
    }

    /**
     * Limpa o cache dos dados do sistema
     */
    function clearCache() {
        systemModules.value = []
        systemActions.value = []
    }

    /**
     * Verifica se o usuário tem permissão para um módulo específico
     */
    function hasModulePermission(
        userPermissions: IUserPermissions,
        moduleId: number
    ): boolean {
        return userPermissions.modules.some(m => m.id_modulo === moduleId)
    }

    /**
     * Verifica se o usuário tem permissão para uma rota específica
     */
    function hasRoutePermission(
        userPermissions: IUserPermissions,
        routeId: number
    ): boolean {
        return userPermissions.routes.some(r => r.id_rota === routeId)
    }

    /**
     * Verifica se o usuário tem permissão para uma ação específica
     */
    function hasActionPermission(
        userPermissions: IUserPermissions,
        actionId: number
    ): boolean {
        return userPermissions.actions.some(a => a.id_acao_rota === actionId)
    }

    /**
     * Verifica se um módulo está ativo pelo path
     */
    function isModuleActiveByPath(modulePath: string): boolean {
        // Se não há módulos carregados, considerar inativo por padrão
        if (systemModules.value.length === 0) {
            return false
        }

        // Buscar o módulo pelo path e verificar se está ativo
        const module = systemModules.value.find(
            mod => mod.path_em_rota === modulePath
        )

        // Se não encontrar o módulo, considerar inativo
        if (!module) {
            return false
        }

        // Retornar o status ativo do módulo (deve ser explicitamente true)
        return module.ativo === true
    }

    /**
     * Verifica se um módulo está ativo pela descrição
     *
     * Esta função é usada pelo sistema de permissões para verificar se um módulo
     * está ativo antes de permitir acesso às suas rotas. Usa a descrição do módulo
     * como chave de identificação (ex: 'CADASTRO GERAL') em vez do path.
     *
     * @param moduleDescription - Descrição do módulo (ex: 'CADASTRO GERAL')
     * @returns true se o módulo estiver ativo, false caso contrário
     */
    function isModuleActiveByDescription(moduleDescription: string): boolean {
        // Se não há módulos carregados, considerar inativo por padrão
        if (systemModules.value.length === 0) {
            return false
        }

        // Buscar o módulo pela descrição e verificar se está ativo
        const module = systemModules.value.find(
            mod => mod.descricao === moduleDescription
        )

        // Se não encontrar o módulo, considerar inativo
        if (!module) {
            return false
        }

        // Retornar o status ativo do módulo (deve ser explicitamente true)
        return module.ativo === true
    }

    return {
        // Estado
        systemModules,
        systemActions,
        isLoading,
        error,

        // Métodos principais
        getPermissionStructure,
        fetchSystemModules,
        fetchSystemActions,

        // Métodos auxiliares
        buildUserPermissionStructure,
        buildSystemPermissionStructure,
        clearCache,

        // Métodos de verificação
        hasModulePermission,
        hasRoutePermission,
        hasActionPermission,
        isModuleActiveByPath,
        isModuleActiveByDescription
    }
}
