/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/ban-ts-comment */
import { ref, type Ref } from 'vue'
// Types
import type { Validator, GenericValidationResult } from 'src/interfaces/Form'
// Utils
import { Notify } from 'quasar'
import { i18n } from 'src/boot/i18n'
// @ts-ignore
const t = i18n.global.t

export function useFormUtils() {
    const loadingTabs = ref(false)

    /**
     * Convert object to FormData
     * @param data
     * @returns
     */
    const covertToFormData = (data: Record<string, unknown>) => {
        const formData = new FormData()
        for (const key in data) {
            formData.append(key, data[key] as any)
        }
        return formData
    }

    /**
     * Validate all validators
     * @param validators
     * @returns
     */
    const validateAll = async (
        validators: Validator[]
    ): Promise<{
        isValid: boolean
        errors: Record<string, string>
        results: GenericValidationResult[]
    }> => {
        const results = await Promise.all(
            validators.map(validate => validate())
        )

        const isValid = results.every(result => result.valid)
        const errors: Record<string, string> = {}

        results.forEach(result => {
            if (!result.valid && result.errors) {
                Object.assign(errors, result.errors)
            }
        })

        return {
            isValid,
            errors,
            results
        }
    }

    /**
     * Notify fields errors
     * @param errors
     * @returns
     * @example
     */
    const notifyFieldsErrors = (errors: Record<string, string>) => {
        const listErrors = Object.values(errors)
        const errorsFound = () => t('forms.titles.errorsFound')
        Notify.create({
            color: 'negative',
            message: errorsFound(),
            html: true,
            caption: `
                <div style="
                    margin: 8px;
                    padding: 16px;
                    background: white;
                    border-radius: 8px;
                    font-size: 14px;
                    color: #34495e;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
                    border-top: 3px solid #ff5252;
                ">
                    <ul style="
                        margin: 0;
                        padding: 0;
                        list-style: none;
                    ">
                        ${listErrors
                            .map(
                                error => `
                        <li style="
                            margin-bottom: 8px;
                            padding-left: 20px;
                            position: relative;
                            line-height: 1.5;
                        ">
                            <span style="
                                position: absolute;
                                left: 0;
                                top: 7px;
                                width: 8px;
                                height: 8px;
                                background-color: #ff5252;
                                border-radius: 2px;
                                transform: rotate(45deg);
                            "></span>
                            ${error}
                        </li>`
                            )
                            .join('')}
                    </ul>
                </div>
            `
        })
    }

    /**
     * Navega por todas as abas de uma pagina com a finalidade de renderizar todos os campos
     * @param allTabs
     * @param tab
     * @returns
     */
    const activateAllTabs = async ({
        allTabs,
        tab
    }: {
        allTabs: string[]
        tab: Ref<string>
    }) => {
        loadingTabs.value = true
        const activeTab = tab.value
        for (const tabName of allTabs) {
            if (tabName === activeTab) continue

            tab.value = tabName
            await new Promise(resolve => setTimeout(resolve, 1))
        }
        tab.value = activeTab
        loadingTabs.value = false
    }

    /**
     * Normaliza urls para https
     * @param url
     * @returns
     */
    const normalizeUrls = (url: string) => {
        // adiciona https caso seja http
        if (url.startsWith('http://')) {
            return url.replace('http://', 'https://')
        }
        return url
    }

    return {
        covertToFormData,
        validateAll,
        notifyFieldsErrors,
        activateAllTabs,
        loadingTabs,
        normalizeUrls
    }
}
