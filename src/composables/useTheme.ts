import { computed, ref } from 'vue'
import { Dark } from 'quasar'

export function useTheme() {
    const isDark = ref(Dark.isActive)

    const toggleTheme = () => {
        isDark.value = !isDark.value
        Dark.set(isDark.value)
        saveTheme()
    }

    const saveTheme = () => {
        localStorage.setItem('isDark', JSON.stringify(isDark.value))
    }

    const loadTheme = () => {
        const isDarkLS = localStorage.getItem('isDark')
        if (isDarkLS) {
            isDark.value = JSON.parse(isDarkLS)
            Dark.set(isDark.value)
        } else {
            isDark.value = false
            Dark.set(false)
        }
    }

    const isDarkMode = computed(() => {
        return Dark.isActive
    })

    return { isDark, isDarkMode, toggleTheme, loadTheme, saveTheme }
}
