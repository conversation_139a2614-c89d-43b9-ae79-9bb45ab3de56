// Interface base para dados de ajuda
export interface HelpData {
    id?: number
    titulo: string
    conteudo: string
    modulo: string
    rota: string
    ativo: boolean
}

// Interface para formulário de ajuda
export interface FormHelpData {
    titulo: string
    conteudo: string
    modulo: string
    rota: string
    ativo: boolean
}

// Interface para props do formulário
export interface FormHelpProps {
    initialValues?: Partial<FormHelpData>
}

// Interface para exibição de ajuda com informações extras
export interface HelpExibicao extends HelpData {
    moduloDescricao: string
    rotaDescricao: string
}

// Interface para parâmetros de busca
export interface HelpSearchParams {
    modulo?: string
    rota?: string
    ativo?: boolean
    termo?: string
}

// Interface para resposta da API
export interface HelpApiResponse {
    data: HelpData[]
    total: number
    page: number
    limit: number
}

// Interface para criação/atualização via API
export interface HelpCreateRequest {
    titulo: string
    conteudo: string
    modulo: string
    rota: string
    ativo: boolean
}

export interface HelpUpdateRequest extends HelpCreateRequest {
    id: number
}
