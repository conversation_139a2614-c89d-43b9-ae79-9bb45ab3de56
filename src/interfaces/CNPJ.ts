// Interface para os dados retornados pela API de consulta CNPJ
export interface CNPJData {
    // Dados básicos da empresa
    updated: string // Data de atualização
    taxId: string // CNPJ
    alias?: string | null // Nome Fantasia
    founded?: string // Data de fundação
    head?: boolean // Se é matriz
    statusDate?: string // Data da situação
    status?: {
        id: number
        text: string
    }

    // Endereço
    address?: {
        municipality?: number // Código da cidade
        street?: string // Logradouro
        number?: string // Número
        district?: string // Bairro
        city?: string // Nome da cidade
        state?: string // Estado
        details?: string | null // Complemento
        zip?: string // CEP
        country?: {
            id: number
            name: string
        }
    }

    // Contatos
    phones?: Array<{
        type: string // LANDLINE, MOBILE, etc.
        area: string
        number: string
    }>
    emails?: Array<{
        ownership: string // CORPORATE, etc.
        address: string
        domain: string
    }>

    // Atividade econômica
    mainActivity?: {
        id: number
        text: string
    }
    sideActivities?: Array<{
        id: number
        text: string
    }>

    // Informações da empresa
    company?: {
        id: number
        name: string
        equity?: number // Capital social
        nature?: {
            id: number
            text: string
        }
        size?: {
            id: number
            acronym: string
            text: string
        }
        simples?: {
            optant: boolean
            since: string | null
        }
        simei?: {
            optant: boolean
            since: string | null
        }
        members?: Array<{
            since: string
            person: {
                id: string
                type: string
                name: string
                taxId?: string
                age?: string
            }
            role: {
                id: number
                text: string
            }
        }>
    }

    // Registrations (Inscrições Estaduais)
    registrations?: Array<{
        number: string
        state: string
        enabled: boolean
        statusDate?: string
        status?: {
            id: number
            text: string
        }
        type?: {
            id: number
            text: string
        }
    }>

    // SUFRAMA
    suframa?: unknown[]
}

// Interface para o resultado da pesquisa
export interface CNPJSearchResult {
    success: boolean
    data?: CNPJData
    error?: string
    message?: string
}

// Interface para props do componente
export interface CNPJSearchProps {
    // Callback chamado quando uma empresa é selecionada
    onSelect?: (data: CNPJData) => void
    // Callback chamado quando a pesquisa é cancelada
    onCancel?: () => void
    // Se deve mostrar apenas o botão ou também permitir uso programático
    buttonOnly?: boolean
    // Texto customizado para o tooltip
    tooltipText?: string
    // Tamanho do botão
    size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
    // Cor do botão
    color?: string
    // Se o botão deve ser flat, outline, etc.
    variant?: 'flat' | 'outline' | 'unelevated' | 'elevated'
}
