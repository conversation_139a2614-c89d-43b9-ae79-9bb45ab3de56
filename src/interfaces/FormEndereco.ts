// Interface para dados de endereço
export interface EnderecoData {
    id?: string
    tipo: string
    pais: string
    cep?: string
    estado: string
    cidade: string
    logradouro: string
    numero: string
    bairro: string
    complemento?: string
    pontoReferencia?: string
}

// Interface para props do componente de endereço
export interface FormEnderecoProps {
    title?: string
    readonly?: boolean
    initialValues?: Partial<EnderecoData>
}

// Interface para dados do formulário de coleção de endereços
export interface FormColecaoEnderecosData {
    enderecos: EnderecoData[]
}

// Interface para props do componente de coleção de endereços
export interface FormColecaoEnderecosProps {
    title?: string
    readonly?: boolean
    initialValues?: EnderecoData[]
    maxEnderecos?: number
}

// Interface para item de opção
export interface OptionItem {
    label: string
    value: string
}

// Interface para resultado de validação
export interface ValidationResult {
    valid: boolean
    errors?: string[]
}
