import type { AxiosRequestConfig } from 'axios'

export type IHttpStatusCodeErrors = 400 | 401 | 403 | 404 | 500

export interface IErrorsMessages {
    400?: string
    401?: string
    403?: string
    404?: string
    500?: string
    503?: string
    noCode?: string
}

export interface ICustomAxiosRequestConfig extends AxiosRequestConfig {
    customErrorMessages?: IErrorsMessages | undefined | null
}

export type IHttpMethod = 'get' | 'post' | 'put' | 'delete'
