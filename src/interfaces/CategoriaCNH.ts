export interface CategoriaCNH {
    id: number
    codigo: string
    descricao: string
    ativo: boolean
    anexo?: string
    anexo_base64?: string
    created_at?: string
    updated_at?: string
}

export interface CategoriaCNHFormData {
    codigo: string
    descricao: string
    ativo: boolean
}

export interface CategoriaCNHApiResponse {
    count: number
    next: string | null
    previous: string | null
    results: CategoriaCNH[]
}
