// Interface para dados de novidades
export interface NovidadesData {
    id?: number
    titulo: string
    descricao: string
    modulo: string
    rota: string
    ativo: boolean
    dataPublicacao: string
    dataAtualizacao: string
}

// Interface para props do formulário de novidades
export interface FormNovidadesProps {
    initialValues?: Partial<NovidadesData>
}

// Interface para dados retornados pelo formulário
export interface FormNovidadesData {
    titulo: string
    descricao: string
    modulo: string
    rota: string
    ativo: boolean
}

// Interface para novidade vista pelo usuário
export interface NovidadeVista {
    novidadeId: number
    usuarioId: number
    dataVista: string
}

// Interface para exibição de novidades (com status de vista)
export interface NovidadeExibicao extends NovidadesData {
    vista: boolean
    dataVista?: string | undefined
}

// Interface para opções de módulos
export interface ModuloOption {
    label: string
    value: string
}

// Interface para dados mockados
export interface NovidadesMockData extends NovidadesData {
    id: number
    dataPublicacao: string
    dataAtualizacao: string
}
