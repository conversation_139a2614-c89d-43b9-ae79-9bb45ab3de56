// Interface para os dados retornados pela API de consulta CEP (ViaCEP)
export interface CEPData {
    cep: string // CEP no formato 00000-000
    logradouro: string // Nome da rua/avenida
    complemento?: string // Complemento do endereço
    bairro: string // Nome do bairro
    localidade: string // Nome da cidade
    uf: string // Sigla do estado
    ibge?: string // Código IBGE da cidade
    gia?: string // Código GIA
    ddd?: string // Código DDD da região
    siafi?: string // Código SIAFI
}

// Interface para o resultado da pesquisa
export interface CEPSearchResult {
    success: boolean
    data?: CEPData
    error?: string
    message?: string
}

// Interface para props do componente (se necessário no futuro)
export interface CEPSearchProps {
    // Callback chamado quando um CEP é encontrado
    onFound?: (data: CEPData) => void
    // Callback chamado quando ocorre erro na pesquisa
    onError?: (error: string) => void
    // Se deve fazer a pesquisa automaticamente ao digitar
    autoSearch?: boolean
    // Delay para debounce (em ms)
    debounceDelay?: number
}
