// Interface para as propriedades do RichTextEditor
export interface RichTextEditorProps {
    modelValue?: string
    placeholder?: string
    readonly?: boolean
    maxLength?: number
    showCharCount?: boolean
    minHeight?: string
    maxHeight?: string
    rows?: number
}

// Interface para os eventos emitidos pelo RichTextEditor
export interface RichTextEditorEmits {
    'update:modelValue': [value: string]
    'change': [value: string]
    'focus': []
    'blur': []
}

// Interface para os métodos expostos pelo RichTextEditor
export interface RichTextEditorMethods {
    focus: () => void
    blur: () => void
    getContent: () => string
    setContent: (content: string) => void
    insertText: (text: string) => void
    clear: () => void
}

// Interface para as opções de tamanho de fonte
export interface FontSizeOption {
    label: string
    value: string
}

// Interface para comandos de formatação
export interface FormatCommand {
    command: string
    value?: string
    icon: string
    tooltip: string
    isActive?: boolean
}

// Tipos para os modos de visualização
export type ViewMode = 'visual' | 'html'

// Interface para configuração do editor
export interface RichTextEditorConfig {
    toolbar?: {
        showBold?: boolean
        showItalic?: boolean
        showUnderline?: boolean
        showAlignment?: boolean
        showLists?: boolean
        showLinks?: boolean
        showFontSize?: boolean
        showTextColor?: boolean
        showClearFormat?: boolean
        showViewMode?: boolean
    }
    fontSizes?: FontSizeOption[]
    defaultFontSize?: string
    allowedTags?: string[]
    maxLength?: number
    autoSave?: boolean
    autoSaveInterval?: number
}

// Interface para validação do conteúdo
export interface RichTextValidation {
    isValid: boolean
    errors: string[]
    warnings: string[]
    characterCount: number
    wordCount: number
}

// Interface para estatísticas do conteúdo
export interface ContentStats {
    characters: number
    charactersNoSpaces: number
    words: number
    paragraphs: number
    sentences: number
}

// Interface para histórico de ações (undo/redo)
export interface EditorHistory {
    canUndo: boolean
    canRedo: boolean
    currentIndex: number
    maxHistory: number
}

// Interface para configuração de cores
export interface ColorPalette {
    textColors: string[]
    backgroundColors: string[]
    customColors?: string[]
}

// Interface para plugins/extensões
export interface EditorPlugin {
    name: string
    version: string
    enabled: boolean
    config?: Record<string, unknown>
    commands?: FormatCommand[]
}

// Interface para configuração de links
export interface LinkConfig {
    allowExternalLinks: boolean
    openInNewTab: boolean
    validateUrls: boolean
    allowedDomains?: string[]
}

// Interface para configuração de imagens (para futuras extensões)
export interface ImageConfig {
    allowImages: boolean
    maxFileSize: number
    allowedFormats: string[]
    uploadUrl?: string
    resizeImages: boolean
    maxWidth?: number
    maxHeight?: number
}

// Interface para configuração de tabelas (para futuras extensões)
export interface TableConfig {
    allowTables: boolean
    maxRows: number
    maxColumns: number
    defaultBorder: boolean
    allowStyling: boolean
}

// Interface completa de configuração do editor
export interface FullRichTextEditorConfig extends RichTextEditorConfig {
    colors?: ColorPalette
    links?: LinkConfig
    images?: ImageConfig
    tables?: TableConfig
    plugins?: EditorPlugin[]
    validation?: {
        required?: boolean
        minLength?: number
        maxLength?: number
        allowedTags?: string[]
        forbiddenTags?: string[]
    }
    accessibility?: {
        ariaLabels: boolean
        keyboardShortcuts: boolean
        screenReaderSupport: boolean
    }
}
