// Interfaces para o sistema de permissões

// Permissões do usuário - Módulos
export interface IUserModulePermission {
    id: number
    id_modulo_descricao: string
    id_pn_usuario: number
    id_modulo: number
}

// Permissões do usuário - Rotas
export interface IUserRoutePermission {
    id: number
    id_rota_descricao: string
    id_pn_usuario: number
    id_rota: number
}

// Permissões do usuário - Ações
export interface IUserActionPermission {
    id: number
    id_acao_rota_id_acao_descricao: string
    id_acao_rota_id_rota_descricao: string
    id_pn_usuario: number
    id_acao_rota: number
}

// Dados do sistema - Ação de uma rota
export interface ISystemActionRoute {
    id: number
    descricao: string
    ativo: boolean
    deleted_at: string | null
    id_rota: number
}

// Dados do sistema - Rota de um módulo
export interface ISystemRoute {
    id: number
    acao_rota: ISystemActionRoute[]
    descricao: string
    ativo: boolean
    deleted_at: string | null
    id_modulo: number
}

// Dados do sistema - Módulo
export interface ISystemModule {
    id: number
    ativo_formatado: string
    rota: ISystemRoute[]
    descricao: string
    path_em_rota: string
    ativo: boolean
}

// Dados do sistema - Ação (endpoint separado)
export interface ISystemAction {
    id: number
    descricao: string
    ativo: boolean
    deleted_at: string | null
    // Outros campos que possam existir na resposta da API
}

// Estrutura hierárquica final - Ação
export interface IPermissionAction {
    id: number
    descricao: string
    hasPermission: boolean
    ativo: boolean
}

// Estrutura hierárquica final - Rota
export interface IPermissionRoute {
    id: number
    descricao: string
    hasPermission: boolean
    ativo: boolean
    acoes: IPermissionAction[]
}

// Estrutura hierárquica final - Módulo
export interface IPermissionModule {
    id: number
    descricao: string
    path_em_rota: string
    hasPermission: boolean
    ativo: boolean
    rotas: IPermissionRoute[]
}

// Estrutura final de retorno
export interface IPermissionStructure {
    userPermissions: IPermissionModule[]
    systemPermissions: IPermissionModule[]
}

// Dados brutos das permissões do usuário
export interface IUserPermissions {
    modules: IUserModulePermission[]
    routes: IUserRoutePermission[]
    actions: IUserActionPermission[]
}

// Resposta da API de módulos
export interface IModulesApiResponse {
    results: ISystemModule[]
    count: number
    next: string | null
    previous: string | null
}

// Resposta da API de ações
export interface IActionsApiResponse {
    results: ISystemAction[]
    count: number
    next: string | null
    previous: string | null
}

// Interface para o perfil do usuário
export interface IUserProfile {
    id: number
    cargo?: string
    [key: string]: unknown
}

// Interface para o usuário com permissões
export interface IUserWithPermissions {
    id: number
    username: string
    nome: string
    sobrenome: string
    email: string
    admin?: boolean // Usuário contratante - acesso irrestrito a módulos/rotas/ações ativos
    staff?: boolean // SysAdmin - acesso irrestrito + rotas exclusivas para staff
    profile?: IUserProfile
    permissions?: IUserPermissions
    [key: string]: unknown
}

// Interface para mapeamento de rotas com módulos
export interface IRouteModuleMapping {
    [routePath: string]: {
        // IDs numéricos (deprecated - usar descrições)
        moduleId?: number
        routeId?: number

        // Descrições (recomendado - consistente entre ambientes)
        moduleDescription?: string // Ex: 'ESTOQUE', 'CADASTRO GERAL'
        routeDescription?: string // Ex: 'MARCA', 'MODELO', 'PEDIDO'

        // Configurações
        modulePath?: string
        staffOnly?: boolean // Rota exclusiva para staff (sysadmin)
        onlyAdmin?: boolean // Rota exclusiva para admin e staff
        requiresRoutePermission?: boolean // Indica se precisa de permissão específica de rota
    }
}
