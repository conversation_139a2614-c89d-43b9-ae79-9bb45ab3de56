// Interface para dados de telefone
export interface TelefoneData {
    id?: string
    tipo: string
    codigoPais: string
    ddd: string
    numero: string
    ramal?: string
    whatsapp?: boolean
}

// Interface para dados de email
export interface EmailData {
    id?: string
    email: string
}

// Interface para dados gerais do formulário
export interface FormContatoData {
    // ID único do contato
    id?: string

    // Informações Gerais
    nome: string
    ocupacao: string
    departamento: string
    pais: string
    estado: string
    cidade: string
    dataNascimento: string

    // Telefones (array)
    telefones: TelefoneData[]

    // Emails (array)
    emails: EmailData[]
}

// Interface para opções de select
export interface OptionItem {
    label: string
    value: string
    flag?: string
    phonecode?: string
    codigoTelefone?: string
    mascara?: string
    countryCode?: string
    stateCode?: string
}

// Interfaces removidas - dados virão do backend
// As interfaces PaisAmericaLatina, TipoTelefone, Ocupacao e Departamento
// foram removidas pois os dados serão fornecidos pelo backend

// Props do componente
export interface FormContatoProps {
    initialValues?: Partial<FormContatoData>
    title?: string
    readonly?: boolean
}

// Interface para dados do formulário de coleção de contatos
export interface FormColecaoContatosData {
    contatos: FormContatoData[]
}

// Interface para props do componente de coleção de contatos
export interface FormColecaoContatosProps {
    title?: string
    readonly?: boolean
    initialValues?: FormContatoData[]
    maxContatos?: number
}

// Interface para resultado de validação
export interface ValidationResult {
    valid: boolean
    errors: string[]
}
