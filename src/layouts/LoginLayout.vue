<template>
    <div class="login-layout-container">
        <!-- Header da página de login -->
        <div class="login-header q-pa-xs">
            <div class="header-content row items-center justify-between">
                <!-- Logo CorpSystem na esquerda -->
                <div class="flex items-center">
                    <q-img
                        :src="corpLogo"
                        alt="corp-logo"
                        width="100px"
                        height="28px"
                        fit="contain"
                        spinner-color="white"
                        no-spinner
                    >
                        <template v-slot:error>
                            <div
                                class="text-white text-body2 text-center q-pa-xs"
                            >
                                CORP SYSTEM
                            </div>
                        </template>
                    </q-img>
                </div>

                <!-- Controles na direita -->
                <div class="row items-center q-gutter-xs">
                    <LangChanger />
                    <ThemeChanger />
                </div>
            </div>
        </div>

        <!-- Conteúdo principal -->
        <div class="login-content">
            <router-view />
        </div>
    </div>
</template>

<script setup lang="ts">
import LangChanger from '@/components/LangChanger.vue'
import ThemeChanger from '@/components/ThemeChanger.vue'
import { useEnv } from '@/composables/useEnv'

const { getImgLoginSystem } = useEnv()
const corpLogo = getImgLoginSystem()
</script>

<style lang="scss" scoped>
@use 'sass:color';
.login-layout-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.login-header {
    background: linear-gradient(
        135deg,
        $primary 0%,
        color.adjust($primary, $lightness: -10%) 100%
    );
    min-height: 48px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    position: relative;
    flex-shrink: 0;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
}

.login-content {
    flex: 1;
    overflow: hidden;
    height: calc(100vh - 48px); /* Altura total menos altura do header */
}

/* Responsividade para mobile */
@media (max-width: 768px) {
    .login-layout-container {
        height: auto;
        min-height: 100vh;
        overflow: auto;
    }

    .login-content {
        flex: 1;
        overflow: auto;
        height: auto;
        min-height: calc(100vh - 48px);
    }
}

/* Responsividade usando classes do Quasar */

/* Modo escuro */
.body--dark .login-header {
    background: linear-gradient(
        135deg,
        $dark 0%,
        color.adjust($dark, $lightness: -5%) 100%
    );
}
</style>
