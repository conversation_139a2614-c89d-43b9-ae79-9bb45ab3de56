<template>
    <q-layout view="lHh Lpr lFf">
        <q-header bordered class="bg-primary text-secondary">
            <q-toolbar>
                <q-toolbar-title>{{
                    capitalize($t(`pages.titles.${routeName}`))
                }}</q-toolbar-title>

                <!-- Campo de busca completo para desktop -->
                <q-select
                    v-if="!isMobile"
                    v-model="selectedMenu"
                    dense
                    outlined
                    rounded
                    dark
                    color="secondary"
                    use-input
                    clearable
                    hide-dropdown-icon
                    class="input-box"
                    :label="
                        inputRouteInFocus ? '' : $t('forms.labels.searchRoute')
                    "
                    @focus="inputRouteInFocus = true"
                    @blur="inputRouteInFocus = false"
                    :options="filteredPages"
                    @filter="filterFn"
                    @update:model-value="updateRoute"
                ></q-select>

                <!-- Ícone de lupa para mobile -->
                <q-btn
                    v-else
                    flat
                    round
                    dense
                    icon="search"
                    @click="toggleSearchDialog"
                    class="q-ml-sm"
                >
                    <q-tooltip>{{ $t('forms.labels.searchRoute') }}</q-tooltip>
                </q-btn>

                <!-- Menu de A<PERSON>da -->
                <q-btn flat round dense icon="help" class="q-ml-sm">
                    <!-- Badge para novidades não lidas -->
                    <q-badge
                        v-if="novidadesStore.totalNovas > 0"
                        floating
                        color="red"
                        rounded
                    />

                    <q-tooltip class="bg-dark text-white">
                        {{ $t('help.tooltip') }}
                    </q-tooltip>

                    <q-menu>
                        <q-list style="min-width: 200px">
                            <q-item
                                clickable
                                v-close-popup
                                @click="openHelp"
                                :disable="!currentRouteHasHelp"
                            >
                                <q-item-section avatar>
                                    <q-icon name="help_outline" />
                                </q-item-section>
                                <q-item-section>
                                    <q-item-label>{{
                                        $t('help.aboutPage')
                                    }}</q-item-label>
                                    <q-item-label caption>{{
                                        currentRouteHasHelp
                                            ? $t('help.aboutPageDescription')
                                            : $t('help.noHelpAvailable')
                                    }}</q-item-label>
                                </q-item-section>
                            </q-item>

                            <q-separator />

                            <q-item clickable v-close-popup @click="openNews">
                                <q-item-section
                                    avatar
                                    class="relative-position"
                                >
                                    <q-icon name="new_releases">
                                        <!-- Badge para novidades não lidas no menu -->
                                        <q-badge
                                            v-if="novidadesStore.totalNovas > 0"
                                            floating
                                            color="red"
                                            rounded
                                            style="top: -2px; right: -2px"
                                        />
                                    </q-icon>
                                </q-item-section>
                                <q-item-section>
                                    <q-item-label>{{
                                        $t('help.news')
                                    }}</q-item-label>
                                    <q-item-label caption>{{
                                        $t('help.newsDescription')
                                    }}</q-item-label>
                                </q-item-section>
                            </q-item>
                        </q-list>
                    </q-menu>
                </q-btn>

                <q-btn
                    flat
                    round
                    dense
                    icon="manage_accounts"
                    @click="router.push('/configuracoes')"
                    class="q-ml-sm"
                />

                <!-- Avatar do usuário no header -->
                <q-btn
                    flat
                    round
                    dense
                    class="q-ml-sm"
                    @click="router.push('/profile')"
                >
                    <q-avatar size="32px">
                        <q-img
                            v-if="userPicture"
                            fit="contain"
                            :src="userPicture"
                            spinner-color="white"
                            alt="user-picture"
                        />
                        <q-icon
                            v-else
                            name="person"
                            size="sm"
                            color="secondary"
                        />
                    </q-avatar>
                    <q-tooltip class="bg-dark text-white">
                        {{ userFullName }}<br />{{ userEmail }}
                    </q-tooltip>
                </q-btn>

                <template v-if="isMobile">
                    <q-btn
                        flat
                        @click="toggleLeftDrawer"
                        round
                        dense
                        icon="menu"
                        class="q-ml-sm"
                    />
                </template>
            </q-toolbar>
        </q-header>

        <q-drawer
            v-model="leftDrawerOpen"
            :breakpoint="400"
            :mini="miniState"
            :width="250"
            :mini-width="50"
            show-if-above
            :mini-to-overlay="isMobile"
            class="bg-primary text-secondary overflow-hidden"
            side="left"
            bordered
        >
            <!-- Estrutura flexível para posicionar logout no final -->
            <div class="column full-height">
                <!-- Conteúdo principal do drawer -->
                <div class="col">
                    <q-list>
                        <q-item
                            :clickable="!isMobile"
                            dense
                            @click="toggleMiniState"
                            class="q-pa-xs text-secondary"
                            style="height: 50px"
                            v-ripple
                        >
                            <q-item-section v-if="!isMobile" avatar>
                                <q-icon
                                    :name="miniState ? 'menu' : 'menu_open'"
                                    size="md"
                                />
                            </q-item-section>

                            <q-item-section>
                                <q-img
                                    fit="contain"
                                    spinner-color="white"
                                    :src="companyLogo"
                                    alt="company-logo"
                                    class="q-my-xs"
                                />
                            </q-item-section>
                        </q-item>
                        <q-separator color="secondary" />

                        <q-scroll-area :style="{ height: scrollAreaHeight }">
                            <!-- Menu items (vazio durante carregamento) -->
                            <q-list v-if="shouldShowMenu" dense>
                                <template
                                    v-for="(item, index) in menuItems"
                                    :key="index"
                                >
                                    <q-item
                                        v-if="!item.subItens"
                                        clickable
                                        v-ripple
                                        :active="item.route === $route.path"
                                        :to="item.route"
                                        active-class="bg-secondary text-white"
                                        class="q-pa-xs text-secondary icon-item"
                                    >
                                        <q-tooltip
                                            v-if="miniState"
                                            anchor="center right"
                                            self="center left"
                                        >
                                            {{ item.label }}
                                        </q-tooltip>

                                        <q-item-section avatar>
                                            <q-icon
                                                :name="item.icon"
                                                size="sm"
                                            />
                                        </q-item-section>

                                        <q-item-section v-if="!miniState">
                                            {{ item.label }}
                                        </q-item-section>
                                    </q-item>

                                    <q-expansion-item
                                        v-else-if="!miniState"
                                        dense
                                        :icon="item.icon"
                                        :label="item.label"
                                        :header-class="{
                                            'bg-secondary text-white':
                                                isActiveParent(item),
                                            'text-secondary':
                                                !isActiveParent(item)
                                        }"
                                    >
                                        <menu-sub-items
                                            :items="item.subItens"
                                        />
                                    </q-expansion-item>

                                    <q-item
                                        v-else
                                        clickable
                                        v-ripple
                                        :active="isActiveParent(item)"
                                        class="text-secondary"
                                        active-class="bg-secondary text-white"
                                        @click="toggleSubMenu(index)"
                                    >
                                        <q-item-section avatar>
                                            <slot>
                                                <div
                                                    class="flex items-start no-wrap"
                                                >
                                                    <q-tooltip
                                                        v-if="miniState"
                                                        anchor="center right"
                                                        self="center left"
                                                    >
                                                        {{ item.label }}
                                                    </q-tooltip>

                                                    <q-icon
                                                        :name="item.icon"
                                                        size="sm"
                                                    />

                                                    <div
                                                        v-if="item.subItens"
                                                        class="submenu-indicator bg-primary rounded-borders"
                                                        :class="{
                                                            'bg-secondary':
                                                                isActiveParent(
                                                                    item
                                                                )
                                                        }"
                                                    >
                                                        <q-icon
                                                            :name="
                                                                activeSubMenu ===
                                                                index
                                                                    ? 'expand_less'
                                                                    : 'expand_more'
                                                            "
                                                            size="0.7em"
                                                        />
                                                    </div>
                                                </div>
                                            </slot>
                                        </q-item-section>

                                        <q-menu
                                            anchor="top right"
                                            self="top start"
                                            :offset="[10, 0]"
                                            @before-show="
                                                setActiveSubMenu(index)
                                            "
                                            @before-hide="clearActiveSubMenu"
                                            class="bg-primary q-pb-sm"
                                            square
                                        >
                                            <q-list>
                                                <q-item-label
                                                    header
                                                    class="text-weight-bold text-white"
                                                    >{{
                                                        item.label
                                                    }}</q-item-label
                                                >
                                                <menu-sub-items
                                                    :items="item.subItens"
                                                />
                                            </q-list>
                                        </q-menu>
                                    </q-item>
                                </template>
                            </q-list>
                        </q-scroll-area>
                    </q-list>
                </div>

                <!-- Botão de logout fixo na parte inferior -->
                <div class="col-auto">
                    <q-separator color="secondary" />
                    <q-list>
                        <q-item
                            clickable
                            v-ripple
                            @click="logout"
                            style="height: 36px"
                        >
                            <q-item-section avatar>
                                <q-icon name="logout" />
                            </q-item-section>

                            <q-item-section>
                                {{ $t('buttons.logout') }}
                            </q-item-section>
                        </q-item>
                    </q-list>
                </div>
            </div>
        </q-drawer>

        <q-page-container>
            <router-view />
        </q-page-container>

        <!-- Diálogo de busca para mobile -->
        <modal-template
            v-model="searchDialogOpen"
            :title="$t('forms.labels.searchRoute')"
            card-style="width: 100%; max-width: 400px; margin: 16px"
            transition-show="slide-down"
            transition-hide="slide-up"
            :persistent="false"
        >
            <q-select
                v-model="selectedMenu"
                dense
                outlined
                rounded
                color="primary"
                use-input
                clearable
                hide-dropdown-icon
                autofocus
                :label="$t('forms.labels.searchRoute')"
                :options="filteredPages"
                @filter="filterFn"
                @update:model-value="updateRouteAndCloseDialog"
            >
                <template v-slot:prepend>
                    <q-icon name="search" />
                </template>
            </q-select>

            <template #actions>
                <q-btn
                    flat
                    color="negative"
                    :label="$t('buttons.cancel')"
                    @click="searchDialogOpen = false"
                />
            </template>
        </modal-template>

        <q-footer class="bg-grey-6 text-white">
            <q-toolbar>
                <q-btn
                    round
                    dense
                    flat
                    icon="arrow_back"
                    @click="$router.go(-1)"
                >
                    <q-tooltip>{{ $t('buttons.goBack') }}</q-tooltip>
                </q-btn>

                <q-img
                    :src="corpLogo"
                    alt="corp-logo"
                    width="80px"
                    height="20px"
                    fit="contain"
                    spinner-color="white"
                    no-spinner
                    class="q-ml-xs"
                >
                    <template v-slot:error>
                        <div class="text-white text-body2 text-center q-pa-xs">
                            CORP SYSTEM
                        </div>
                    </template>
                </q-img>

                <div
                    class="text-center q-pa-xs text-caption text-white q-ml-auto"
                >
                    &copy;
                    {{
                        `${new Date().getFullYear()} ${$t('titles.app')}. ${$t('titles.copyright')}`
                    }}
                </div>
            </q-toolbar>
        </q-footer>
    </q-layout>
</template>

<script setup lang="ts">
// Vue
import {
    defineComponent,
    ref,
    computed,
    onMounted,
    watch,
    onBeforeUnmount
} from 'vue'
import { useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { useAuthStore } from '@/stores/auth'
import { useNovidadesStore } from '@/stores/novidades'
import { useHelpStore } from '@/stores/help'

import { stringUtils } from 'src/composables/stringUtils'
// Types
import type { IMenuItem } from '@/interfaces/Menu'
// Components
import ModalTemplate from '@/components/ModalTemplate.vue'
import MenuSubItems from '@/layouts/components/MenuSubItems.vue'
// Composables
import { useUser } from '@/composables/user'
import { useMenu } from '@/composables/useMenu'
import { useEnv } from '@/composables/useEnv'
import { usePermissions } from '@/composables/usePermissions'
import { useLoadingOverlay } from '@/composables/loadingOverlay'
const { getUserPicture, getUserEmail, getUserFullName } = useUser()
const {
    menuItems,
    systemPages,
    isActiveParent: checkActiveParent,
    shouldShowMenu
} = useMenu()
const { setLoading } = useLoadingOverlay()

defineComponent({
    name: 'AppDrawer'
})

// Utils
const $q = useQuasar()
const router = useRouter()
const routeName = computed(() => router.currentRoute.value.meta.title as string)
const isMobile = computed(() => $q.screen.lt.sm)
const currentRouteHasHelp = computed(() => {
    const currentRoute = router.currentRoute.value.fullPath
    return helpStore.rotaTemAjuda(currentRoute)
})
const auth = useAuthStore()
const novidadesStore = useNovidadesStore()
const helpStore = useHelpStore()

const { getLogoSystem } = useEnv()
const { getImgLoginSystem } = useEnv()
const companyLogo = getLogoSystem()
const corpLogo = getImgLoginSystem()
const { capitalize } = stringUtils()
// Header
const inputRouteInFocus = ref(false)
// User
const userPicture = getUserPicture() as string
const userFullName = getUserFullName() as string
const userEmail = getUserEmail() as string
// Drawer - agora vem do composable useMenu()
// Função para verificar se item pai está ativo - agora usa o composable
const isActiveParent = (item: IMenuItem): boolean => {
    return checkActiveParent(item, router.currentRoute.value.fullPath)
}

const leftDrawerOpen = ref(false)
const toggleLeftDrawer = () => {
    leftDrawerOpen.value = !leftDrawerOpen.value
}

const activeSubMenu = ref<number | null>(null)
const menuOpen = ref<Record<number, boolean>>({})
const setActiveSubMenu = (index: number) => {
    activeSubMenu.value = index
}
const clearActiveSubMenu = () => {
    activeSubMenu.value = null
}
const toggleSubMenu = (index: number) => {
    if (activeSubMenu.value === index) {
        activeSubMenu.value = null
        menuOpen.value[index] = false
    } else {
        activeSubMenu.value = index
        menuOpen.value[index] = true
    }
}
const handleClickOutside = (event: MouseEvent) => {
    const clickedOutside = !event.composedPath().some(el => {
        return (
            (el as HTMLElement)?.classList?.contains('q-menu') ||
            (el as HTMLElement)?.classList?.contains('q-item')
        )
    })

    if (clickedOutside) {
        activeSubMenu.value = null
        Object.keys(menuOpen.value).forEach(key => {
            menuOpen.value[parseInt(key)] = false
        })
    }
}

const miniState = isMobile.value ? ref(false) : ref(true)
watch(isMobile, () => {
    if (isMobile.value) {
        leftDrawerOpen.value = false
        miniState.value = false
    } else {
        leftDrawerOpen.value = true
        miniState.value = false
    }
})

const windowHeight = ref(window.innerHeight)

const scrollAreaHeight = computed(() => {
    const availableHeight = windowHeight.value - 180
    return `${Math.max(availableHeight, 300)}px`
})

const toggleMiniState = () => {
    miniState.value = !miniState.value
}

const logout = () => {
    auth.logout()
}

// Função para abrir a ajuda
const openHelp = (): void => {
    const currentRoute = router.currentRoute.value.fullPath
    // Codificar a rota para usar como parâmetro da URL
    const encodedRoute = encodeURIComponent(currentRoute.replace(/^\//, ''))
    router.push({
        path: `/help/${encodedRoute}`,
        query: {
            returnTo: currentRoute
        }
    })
}

// Função para abrir novidades
const openNews = (): void => {
    // TODO: Implementar página de novidades
    router.push('/novidades')
}

const updateWindowHeight = () => {
    windowHeight.value = window.innerHeight
}

// Header
const selectedMenu = ref<string>('')
const searchDialogOpen = ref<boolean>(false)
// systemPages agora vem do composable useMenu()

// Estado para filtro de busca
const searchTerm = ref<string>('')
const filteredPages = computed(() => {
    if (!searchTerm.value) {
        return systemPages.value
    }
    return systemPages.value.filter(
        option =>
            option.label.toLowerCase().indexOf(searchTerm.value.toLowerCase()) >
            -1
    )
})
const filterFn = (val: string, update: (fn: () => void) => void) => {
    update(() => {
        searchTerm.value = val
    })
}
const updateRoute = (page: { route: string }) => {
    if (page) {
        const { route } = page
        selectedMenu.value = ''
        router.push(route)
    }
}

// Métodos para busca mobile
const toggleSearchDialog = (): void => {
    searchDialogOpen.value = !searchDialogOpen.value
}

const updateRouteAndCloseDialog = (page: { route: string }): void => {
    updateRoute(page)
    searchDialogOpen.value = false
}

onMounted(async () => {
    window.addEventListener('resize', updateWindowHeight)
    document.addEventListener('click', handleClickOutside)
    if (isMobile.value) {
        leftDrawerOpen.value = false
        miniState.value = false
    }

    // Carregar módulos IMEDIATAMENTE se estiver logado usando o loading overlay
    // Esta implementação garante que:
    // 1. Loading overlay é mostrado durante carregamento
    // 2. Menu fica vazio até módulos serem carregados (shouldShowMenu = false)
    // 3. Após carregamento, menu mostra apenas módulos ativos
    // 4. Previne flash de conteúdo não autorizado
    if (auth.token) {
        await setLoading(async () => {
            const { fetchSystemModules } = usePermissions()
            try {
                await fetchSystemModules()
            } catch {
                // Silenciosamente ignorar erro para não quebrar a UX
            }
        })
    }
})

onBeforeUnmount(() => {
    document.removeEventListener('click', handleClickOutside)
    window.removeEventListener('resize', updateWindowHeight)
})
</script>

<style scoped>
/* Garantir que inputs e selects tenham exatamente o mesmo tamanho */
:deep(.input-box.q-field .q-field__control),
:deep(.input-box.q-select .q-field__control) {
    height: 32px !important;
    min-height: 32px !important;
    max-height: 32px !important;
    font-size: 16px;
}

:deep(.input-box.q-field .q-field__marginal),
:deep(.input-box.q-select .q-field__marginal) {
    height: 32px !important;
    font-size: 16px;
}

/* Controlar elementos internos do select para igualar ao input */
:deep(.input-box.q-select .q-field__native) {
    height: 30px !important;
    min-height: 30px !important;
    line-height: 30px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
}

:deep(.input-box.q-select .q-field__control-container) {
    height: 30px !important;
    min-height: 30px !important;
    display: flex !important;
    align-items: center !important;
}

:deep(.input-box.q-select .q-select__selected) {
    height: 30px !important;
    line-height: 30px !important;
    display: flex !important;
    align-items: center !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Forçar alinhamento do texto selecionado */
:deep(.input-box.q-select .q-select__selected span) {
    line-height: 30px !important;
    display: flex !important;
    align-items: center !important;
    height: 30px !important;
}

/* Ajustar posicionamento do label para ficar alinhado */
:deep(.input-box .q-field__label) {
    top: 50% !important;
    transform: translateY(-50%) !important;
    line-height: 1 !important;
}

/* Quando o campo tem valor, manter label no topo */
:deep(.input-box.q-field--float .q-field__label) {
    top: 0 !important;
    transform: translateY(-50%) !important;
}

/* Correção específica para select com valor */
:deep(.input-box.q-select.q-field--float .q-field__label),
:deep(.input-box.q-select.q-field--filled .q-field__label) {
    top: 0 !important;
    transform: translateY(-50%) !important;
    font-size: 12px !important;
}

/* Garantir que o conteúdo do select fique centralizado */
:deep(.input-box.q-select.q-field--float .q-field__native),
:deep(.input-box.q-select.q-field--filled .q-field__native) {
    padding-top: 6px !important;
    display: flex !important;
    align-items: center !important;
}

/* Ajustar também o container de controle */
:deep(.input-box.q-select.q-field--float .q-field__control-container),
:deep(.input-box.q-select.q-field--filled .q-field__control-container) {
    padding-top: 1px !important;
    display: flex !important;
    align-items: center !important;
}

/* Ajustar o texto selecionado especificamente */
:deep(.input-box.q-select.q-field--float .q-select__selected),
:deep(.input-box.q-select.q-field--filled .q-select__selected) {
    margin-top: 1px !important;
    height: 24px !important;
    line-height: 24px !important;
    display: flex !important;
    align-items: center !important;
}

/* Adicione no seu arquivo de estilos */
.relative-position {
    position: relative;
}

.absolute-top-right {
    position: absolute;
    top: 6px;
    right: 13px;
}

/* Indicador de submenu posicionado em cima do ícone (centralizado) */
.submenu-indicator {
    position: absolute;
    left: 62%; /* Centralizar horizontalmente */
    transform: translateX(-50%); /* Ajuste fino para centralização */
    width: 10px; /* Tamanho ligeiramente maior para visibilidade */
    height: 10px; /* Tamanho ligeiramente maior para visibilidade */
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1; /* Garantir que fique acima do ícone */
}

.rounded-borders {
    border-radius: 50%;
}

/* Reduzir altura das linhas dos menus */
:deep(.q-item) {
    min-height: 32px !important; /* Reduzir de ~48px para 32px */
    padding: 4px 8px !important; /* Padding compacto */
}

:deep(.q-item--dense) {
    min-height: 28px !important; /* Ainda menor para itens dense */
    padding: 2px 8px !important;
}

/* Reduzir espaço dos avatars e aproximar texto */
:deep(.q-item__section--avatar) {
    min-width: 24px !important; /* Reduzir largura do avatar */
    padding-right: 4px !important; /* Reduzir espaçamento à direita */
    margin-right: 0 !important; /* Remover margem */
}

/* Aproximar texto mais à esquerda */
:deep(.q-item__section--main) {
    padding-left: 0 !important; /* Remover padding esquerdo */
    margin-left: 0 !important; /* Remover margem esquerda */
}

/* Aproximar logo do ícone de expandir - mais à esquerda */
:deep(.q-item__section--avatar + .q-item__section) {
    padding-left: 0 !important; /* Remover padding para máxima aproximação */
}

/* Reduzir altura dos expansion items */
:deep(.q-expansion-item__container .q-item) {
    min-height: 28px !important;
    padding: 2px 8px !important;
}

/* Reduzir altura dos sub-items */
:deep(.q-list .q-item) {
    min-height: 30px !important;
}

/* Reduzir altura do footer */
:deep(.q-footer .q-toolbar) {
    min-height: 32px !important; /* Reduzir de ~56px para 32px */
    padding: 4px 8px !important; /* Padding compacto */
}

/* Reduzir tamanho dos elementos do footer */
:deep(.q-footer .q-btn) {
    width: 28px !important;
    height: 28px !important;
    min-width: 28px !important;
    min-height: 28px !important;
}

:deep(.q-footer .q-img) {
    height: 20px !important; /* Reduzir altura da logo */
}

:deep(.q-footer .text-caption) {
    font-size: 0.65rem !important; /* Texto menor */
    line-height: 1.2 !important;
    padding: 2px 4px !important; /* Padding menor */
}
</style>
