<template>
    <template v-for="(item, index) in items" :key="index">
        <q-item
            v-if="!item.subItens"
            clickable
            v-ripple
            :active="item.route === $route.path"
            :to="item.route"
            active-class="bg-secondary text-white"
            dense
            class="q-ml-md"
            :class="{
                'text-secondary': !isActiveParent(item)
            }"
        >
            <q-item-section avatar>
                <q-icon :name="item.icon" size="sm" />
            </q-item-section>

            <q-item-section>
                {{ item.label }}
            </q-item-section>
        </q-item>

        <q-expansion-item
            v-else
            dense
            :icon="item.icon"
            :label="item.label"
            class="q-ml-md text-secondary"
            :header-class="{
                'bg-secondary text-white': isActiveParent(item),
                'text-secondary': !isActiveParent(item)
            }"
            :expand-icon-class="{
                'bg-secondary text-white': isActiveParent(item),
                'text-secondary': !isActiveParent(item)
            }"
        >
            <q-list>
                <menu-sub-items :items="item.subItens" />
            </q-list>
        </q-expansion-item>
    </template>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import { defineComponent } from 'vue'
import { useRoute } from 'vue-router'
//types
import type { IMenuItem } from '@/interfaces/Menu'

defineComponent({
    name: 'MenuSubItems'
})

defineProps({
    items: {
        type: Array as PropType<IMenuItem[]>,
        required: true
    }
})

const route = useRoute()

const isActiveParent = (item: IMenuItem): boolean => {
    if (!item.subItens) return false

    const checkSubItems = (items: IMenuItem[]): boolean => {
        return items.some(subItem => {
            if (subItem.route === route.path) return true
            if (subItem.subItens) return checkSubItems(subItem.subItens)
            return false
        })
    }

    return checkSubItems(item.subItens)
}
</script>
