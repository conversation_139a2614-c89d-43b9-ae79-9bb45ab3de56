<template>
    <q-layout view="hHh lpR fFf">
        <!-- Header compacto -->
        <q-header elevated class="bg-primary text-white">
            <q-toolbar class="help-toolbar-compact">
                <!-- Bo<PERSON>ão voltar -->
                <q-btn
                    flat
                    round
                    dense
                    icon="arrow_back"
                    @click="goBack"
                    class="q-mr-sm"
                >
                    <q-tooltip class="bg-dark">
                        {{ $t('buttons.back') }}
                    </q-tooltip>
                </q-btn>

                <!-- Título centralizado -->
                <q-toolbar-title class="text-center">
                    <div class="help-title-compact">
                        <q-icon name="help" size="sm" class="q-mr-xs" />
                        <span class="help-title-text">
                            {{ title || $t('help.title') }}
                        </span>
                    </div>
                </q-toolbar-title>

                <!-- Espaço para manter centralização -->
                <div style="width: 40px"></div>
            </q-toolbar>
        </q-header>

        <!-- Con<PERSON>údo principal -->
        <q-page-container>
            <q-page class="help-page-content">
                <slot />
            </q-page>
        </q-page-container>
    </q-layout>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

// Props
interface Props {
    title?: string | undefined
}

defineProps<Props>()

// Composables
const router = useRouter()

// Métodos
const goBack = () => {
    if (window.history.length > 1) {
        router.go(-1)
    } else {
        router.push('/')
    }
}
</script>

<style scoped>
/* Toolbar compacto */
.help-toolbar-compact {
    min-height: 48px !important;
    padding: 0 12px;
}

/* Título compacto */
.help-title-compact {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 600;
}

.help-title-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300px;
}

/* Conteúdo da página */
.help-page-content {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: calc(100vh - 48px);
}

/* Responsividade */
@media (max-width: 768px) {
    .help-toolbar-compact {
        min-height: 44px !important;
        padding: 0 8px;
    }

    .help-title-compact {
        font-size: 14px;
    }

    .help-title-text {
        max-width: 200px;
    }

    .help-page-content {
        padding: 12px;
        min-height: calc(100vh - 44px);
    }
}
</style>
