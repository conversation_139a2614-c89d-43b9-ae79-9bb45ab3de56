/* Sistema de Nuvens Animadas */
.clouds-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.cloud {
    position: absolute;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50px;
    opacity: 0.6;
}

.cloud::before,
.cloud::after {
    content: '';
    position: absolute;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50px;
}
.cloud-top-left {
    width: 280px;
    height: 90px;
    top: 15%;
    left: 5%;
    animation: circularMotion1 120s linear infinite;
}

.cloud-top-left::before {
    width: 140px;
    height: 140px;
    top: -70px;
    left: 40px;
}

.cloud-top-left::after {
    width: 160px;
    height: 110px;
    top: -40px;
    right: 40px;
}

.cloud-top-right {
    width: 150px;
    height: 50px;
    top: 20%;
    right: 5%;
    animation: circularMotion2 150s linear infinite;
}

.cloud-top-right::before {
    width: 75px;
    height: 75px;
    top: -37px;
    left: 25px;
}

.cloud-top-right::after {
    width: 90px;
    height: 60px;
    top: -20px;
    right: 25px;
}

.cloud-bottom-left {
    width: 320px;
    height: 100px;
    bottom: 20%;
    left: 8%;
    animation: circularMotion3 90s linear infinite;
}

.cloud-bottom-left::before {
    width: 160px;
    height: 160px;
    top: -80px;
    left: 50px;
}

.cloud-bottom-left::after {
    width: 180px;
    height: 130px;
    top: -50px;
    right: 50px;
}

/* Nuvem Inferior Direita - Pequena */
.cloud-bottom-right {
    width: 120px;
    height: 40px;
    bottom: 15%;
    right: 8%;
    animation: circularMotion4 110s linear infinite;
}

.cloud-bottom-right::before {
    width: 60px;
    height: 60px;
    top: -30px;
    left: 20px;
}

.cloud-bottom-right::after {
    width: 70px;
    height: 45px;
    top: -15px;
    right: 20px;
}

/* ===================================
   NUVENS ADICIONAIS (6 extras)
   =================================== */

/* Nuvem Centro Esquerda - Grande com Forma Orgânica */
.cloud-center-left {
    width: 240px;
    height: 80px;
    top: 50%;
    left: 15%;
    animation: circularMotion5 130s linear infinite;
    border-radius: 80px 40px 60px 30px;
}

.cloud-center-left::before {
    width: 120px;
    height: 120px;
    top: -60px;
    left: 30px;
    border-radius: 60px 80px 40px 70px;
}

.cloud-center-left::after {
    width: 140px;
    height: 90px;
    top: -30px;
    right: 30px;
    border-radius: 45px 70px 35px 55px;
}

/* Nuvem Centro Direita - Pequena Assimétrica */
.cloud-center-right {
    width: 100px;
    height: 35px;
    top: 45%;
    right: 15%;
    animation: circularMotion6 100s linear infinite;
    border-radius: 25px 60px 20px 40px;
}

.cloud-center-right::before {
    width: 50px;
    height: 50px;
    top: -25px;
    left: 15px;
    border-radius: 30px 40px 25px 35px;
}

.cloud-center-right::after {
    width: 60px;
    height: 40px;
    top: -12px;
    right: 15px;
    border-radius: 20px 35px 15px 25px;
}

/* Nuvem Topo Centro - Colossal */
.cloud-top-center {
    width: 380px;
    height: 120px;
    top: 5%;
    left: 45%;
    animation: circularMotion7 180s linear infinite;
    border-radius: 120px 60px 80px 40px;
}

.cloud-top-center::before {
    width: 190px;
    height: 190px;
    top: -95px;
    left: 50px;
    border-radius: 95px 120px 70px 100px;
}

.cloud-top-center::after {
    width: 220px;
    height: 140px;
    top: -50px;
    right: 50px;
    border-radius: 70px 110px 50px 80px;
}

/* Nuvem Base Centro - Média Orgânica */
.cloud-bottom-center {
    width: 200px;
    height: 65px;
    bottom: 5%;
    left: 50%;
    animation: circularMotion8 140s linear infinite;
    border-radius: 65px 30px 45px 25px;
}

.cloud-bottom-center::before {
    width: 100px;
    height: 100px;
    top: -50px;
    left: 25px;
    border-radius: 50px 65px 35px 55px;
}

.cloud-bottom-center::after {
    width: 120px;
    height: 75px;
    top: -25px;
    right: 25px;
    border-radius: 37px 60px 25px 45px;
}

/* Nuvem Extremo Esquerdo - Muito Pequena */
.cloud-far-left {
    width: 80px;
    height: 28px;
    top: 35%;
    left: -5%;
    animation: circularMotion9 200s linear infinite;
    border-radius: 28px 15px 20px 12px;
}

.cloud-far-left::before {
    width: 40px;
    height: 40px;
    top: -20px;
    left: 8px;
    border-radius: 20px 25px 15px 22px;
}

.cloud-far-left::after {
    width: 50px;
    height: 32px;
    top: -8px;
    right: 8px;
    border-radius: 16px 28px 12px 20px;
}

/* Nuvem Extremo Direito - Gigante Fluida */
.cloud-far-right {
    width: 300px;
    height: 95px;
    top: 40%;
    right: -5%;
    animation: circularMotion10 160s linear infinite;
    border-radius: 95px 50px 70px 35px;
}

.cloud-far-right::before {
    width: 150px;
    height: 150px;
    top: -75px;
    left: 40px;
    border-radius: 75px 95px 55px 85px;
}

.cloud-far-right::after {
    width: 180px;
    height: 110px;
    top: -40px;
    right: 40px;
    border-radius: 55px 90px 40px 65px;
}

/* Animações circulares */
@keyframes circularMotion1 {
    0% {
        transform: translate(0, 0);
    }
    25% {
        transform: translate(15px, -10px);
    }
    50% {
        transform: translate(0, -20px);
    }
    75% {
        transform: translate(-15px, -10px);
    }
    100% {
        transform: translate(0, 0);
    }
}

@keyframes circularMotion2 {
    0% {
        transform: translate(0, 0);
    }
    25% {
        transform: translate(-20px, 15px);
    }
    50% {
        transform: translate(-40px, 0);
    }
    75% {
        transform: translate(-20px, -15px);
    }
    100% {
        transform: translate(0, 0);
    }
}

/* Movimento Circular 3 - Raio grande, sentido horário */
@keyframes circularMotion3 {
    0% {
        transform: translate(0, 0);
    }
    25% {
        transform: translate(25px, -20px);
    }
    50% {
        transform: translate(0, -40px);
    }
    75% {
        transform: translate(-25px, -20px);
    }
    100% {
        transform: translate(0, 0);
    }
}

/* Movimento Circular 4 - Raio pequeno, sentido anti-horário */
@keyframes circularMotion4 {
    0% {
        transform: translate(0, 0);
    }
    25% {
        transform: translate(-12px, 8px);
    }
    50% {
        transform: translate(-24px, 0);
    }
    75% {
        transform: translate(-12px, -8px);
    }
    100% {
        transform: translate(0, 0);
    }
}

/* Movimento Circular 5 - Raio médio-grande, sentido horário */
@keyframes circularMotion5 {
    0% {
        transform: translate(0, 0);
    }
    25% {
        transform: translate(30px, -25px);
    }
    50% {
        transform: translate(0, -50px);
    }
    75% {
        transform: translate(-30px, -25px);
    }
    100% {
        transform: translate(0, 0);
    }
}

/* Movimento Circular 6 - Raio muito pequeno, sentido anti-horário */
@keyframes circularMotion6 {
    0% {
        transform: translate(0, 0);
    }
    25% {
        transform: translate(-8px, 6px);
    }
    50% {
        transform: translate(-16px, 0);
    }
    75% {
        transform: translate(-8px, -6px);
    }
    100% {
        transform: translate(0, 0);
    }
}

/* Movimento Circular 7 - Raio muito grande, sentido horário */
@keyframes circularMotion7 {
    0% {
        transform: translate(0, 0);
    }
    25% {
        transform: translate(35px, -30px);
    }
    50% {
        transform: translate(0, -60px);
    }
    75% {
        transform: translate(-35px, -30px);
    }
    100% {
        transform: translate(0, 0);
    }
}

/* Movimento Circular 8 - Raio médio, sentido anti-horário */
@keyframes circularMotion8 {
    0% {
        transform: translate(0, 0);
    }
    25% {
        transform: translate(-18px, 12px);
    }
    50% {
        transform: translate(-36px, 0);
    }
    75% {
        transform: translate(-18px, -12px);
    }
    100% {
        transform: translate(0, 0);
    }
}

/* Movimento Circular 9 - Raio gigante, sentido horário */
@keyframes circularMotion9 {
    0% {
        transform: translate(0, 0);
    }
    25% {
        transform: translate(40px, -35px);
    }
    50% {
        transform: translate(0, -70px);
    }
    75% {
        transform: translate(-40px, -35px);
    }
    100% {
        transform: translate(0, 0);
    }
}

/* Movimento Circular 10 - Raio grande, sentido anti-horário */
@keyframes circularMotion10 {
    0% {
        transform: translate(0, 0);
    }
    25% {
        transform: translate(-28px, 20px);
    }
    50% {
        transform: translate(-56px, 0);
    }
    75% {
        transform: translate(-28px, -20px);
    }
    100% {
        transform: translate(0, 0);
    }
}

/* ===================================
   RESPONSIVIDADE
   ===================================*/

/* Mobile - Ocultar nuvens menores */
@media (max-width: 768px) {
    .cloud-far-left,
    .cloud-far-right,
    .cloud-center-left,
    .cloud-center-right {
        display: none;
    }

    .cloud {
        transform: scale(0.7);
    }
}

/* Mobile pequeno - Ocultar mais nuvens */
@media (max-width: 480px) {
    .clouds-container {
        display: none;
    }
}
