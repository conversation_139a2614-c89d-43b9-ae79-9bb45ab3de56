// app global css in SCSS form

/* Importar sistema de nuvens */
@import './clouds.scss';

/* Background global do sistema */
body {
    background: linear-gradient(135deg, #e8eaf0 0%, #d6dae5 100%);
    min-height: 100vh;
}

/* Background para modo escuro */
.body--dark {
    background: linear-gradient(135deg, #1e3a5f 0%, #2a4a73 100%);
    min-height: 100vh;
}

/* Garantir que o q-page tenha background transparente para mostrar o gradient */
.q-page {
    background: transparent !important;
}
/* Remove todas as setas de scroll */
::-webkit-scrollbar-button {
    display: none;
    width: 0;
    height: 0;
}

/* Estilo principal do scroller */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    border: 2px solid transparent;
    background-clip: content-box;

    &:hover {
        background-color: rgba(255, 255, 255, 0.5);
    }
}

/* Para Firefox */
* {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

/* Estilo específico para QScrollArea do Quasar */
.q-scroll-area {
    &__thumb {
        background-color: rgba(255, 255, 255, 0.3) !important;
        border-radius: 4px !important;
        border: 2px solid transparent !important;
        background-clip: content-box !important;

        &:hover {
            background-color: rgba(255, 255, 255, 0.5) !important;
        }
    }

    &__bar {
        right: 2px !important;
        bottom: 2px !important;
        width: 6px !important;
        height: 6px !important;
        background: transparent !important;
        opacity: 1 !important;

        &--v {
            width: 6px !important;
            border-radius: 3px !important;
        }

        &--h {
            height: 6px !important;
            border-radius: 3px !important;
        }
    }
}

/* Ajustes para dark mode */
.body--dark {
    ::-webkit-scrollbar-thumb {
        background-color: rgba(200, 200, 200, 0.3);

        &:hover {
            background-color: rgba(200, 200, 200, 0.5);
        }
    }

    .q-scroll-area__thumb {
        background-color: rgba(200, 200, 200, 0.3) !important;

        &:hover {
            background-color: rgba(200, 200, 200, 0.5) !important;
        }
    }

    * {
        scrollbar-color: rgba(200, 200, 200, 0.3) transparent;
    }
}

/* Força direção LTR globalmente para Rich Text Editor */
.rich-text-content,
.rich-text-content *,
.force-ltr,
.force-ltr * {
    direction: ltr !important;
    text-align: left !important;
    unicode-bidi: normal !important;
    writing-mode: horizontal-tb !important;
}

/* Força direção LTR em contenteditable */
[contenteditable='true'] {
    direction: ltr !important;
    text-align: left !important;
    unicode-bidi: normal !important;
}

/* Força direção LTR em todos os estados do contenteditable */
[contenteditable='true']:focus,
[contenteditable='true']:active,
[contenteditable='true']:hover {
    direction: ltr !important;
    text-align: left !important;
    unicode-bidi: normal !important;
}
