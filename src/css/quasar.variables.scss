// Quasar SCSS (& Sass) Variables
// --------------------------------------------------
// To customize the look and feel of this app, you can override
// the Sass/SCSS variables found in Quasar's source Sass/SCSS files.

// Check documentation for full list of Quasar variables

// Your own variables (that are declared here) and Quasar's own
// ones will be available out of the box in your .vue/.scss/.sass files

// It's highly recommended to change the default colors
// to match your app's branding.
// Tip: Use the "Theme Builder" on Quasar's documentation website.

$primary: #204065;
$secondary: #bba47a;
$accent: #e27145;

$dark: #21262c; // Fundo principal (modo escuro)
$dark-page: #21262c; // Fundo das páginas (modo escuro)

$positive: #53a857; // Verde suave
$negative: #e05d5d; // Vermelho suave
$info: #2979ff; // Azul vibrante
$warning: #ffc107; // <PERSON><PERSON> médio
