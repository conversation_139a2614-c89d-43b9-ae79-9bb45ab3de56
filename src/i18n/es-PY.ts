export const esPYDateTimeFormat = {
    short: { day: 'numeric', month: 'short', year: 'numeric' },
    long: { day: 'numeric', month: 'long', year: 'numeric', weekday: 'long' }
}

export const esPYNumberFormat = {
    currency: {
        style: 'currency',
        currency: 'PYG',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    },
    percent: {
        style: 'percent',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }
}

export const esPY = {
    themes: {
        light: 'Claro',
        dark: 'Oscuro',
        ChangeToLight: 'Cambiar a tema claro',
        ChangeToDark: 'Cambiar a tema oscuro'
    },
    locale: {
        changeLanguage: 'Cambiar idioma',
        selectLanguage: 'Seleccionar idioma'
    },
    titles: {
        app: 'Corpsystem',
        login: 'Iniciar sesión',
        slogan: 'CorpERP - El mejor sistema ERP para tu empresa.',
        copyright: 'Todos los derechos reservados.',
        pageNotFound: 'Página no encontrada'
    },
    messages: {
        pageNotFound: 'La página que estás buscando no se ha encontrado.',
        checkRequiredFields: 'Verifique los campos obligatorios',
        updatedSuccessfully: '¡Actualizado exitosamente!',
        createdSuccessfully: '¡Creado exitosamente!',
        deletedSuccessfully: '¡Eliminado exitosamente!',
        activatedSuccessfully: '¡Activado exitosamente!',
        deactivatedSuccessfully: '¡Desactivado exitosamente!',
        errorSaving: 'Error al guardar'
    },
    forms: {
        labels: {
            title: 'Título',
            module: 'Módulo',
            route: 'Ruta',
            publishDate: 'Fecha de Publicación',
            name: 'Nombre',
            nameRequired: 'Nombre *',
            surname: 'Apellido',
            email: 'Correo electrónico',
            password: 'Contraseña',
            username: 'Nombre de usuario',
            searchRoute: 'Buscar ruta',
            role: 'Rol',
            companyName: 'Nombre de la empresa',
            companyNameRequired: 'Nombre de la empresa *',
            legalPerson: 'Persona jurídica',
            naturalPerson: 'Persona natural',
            docLegalPerson: 'RUC',
            docNaturalPerson: 'CI',
            active: 'Activo',
            indicatorStateEnrollmentRecipient: 'Indicador de IE Destinatario',
            site: 'Sitio web',
            region: 'Región',
            company: 'Empresa',
            lineOfActivity: 'Rama de actividad',
            typeOfRelationship: 'Tipo de relación',
            dateOfBith: 'Fecha de nacimiento',
            dateOfFounding: 'Fecha de fundación',
            nickname: 'Apodo',
            maritalStatus: 'Estado civil',
            fantasyName: 'Nombre fantasia',
            stateRegistrationStatus: 'Estado de la inscripción estatal',
            stateRegistration: 'Inscripción estatal',
            municipalRegistration: 'Inscripción municipal',
            admin: 'Administrador',
            permissionProfileName: 'Nombre del perfil de permisos',
            permissionGroupName: 'Nombre del grupo de permisos',
            permissionGroupCode: 'Código del grupo de permisos',
            description: 'Descripción',
            abbreviation: 'Abreviación',
            permissionGroups: 'Grupos de permisos',
            remove: 'Eliminar',
            smtpServer: 'Servidor de correo electrónico',
            smtpPort: 'Puerto SMTP',
            emailPassword: 'Contraseña de correo electrónico',
            useTLS: 'Usar TLS',
            useSSL: 'Usar SSL',
            commission: 'Comisión',
            discountPercentage: 'Porcentaje de descuento permitido',
            discountValue: 'Valor de descuento permitido',
            orderPurchaseLimit: 'Límite de compra por pedido',
            monthlyPurchaseLimit: 'Límite de compra mensual',
            category: 'Categoría',
            paymentCondition: 'Condición de pago',
            paymentModality: 'Modalidad de pago',
            transaction: 'Transacción',
            priceTable: 'Tabla de precios',
            seller: 'Vendedor',
            financialAccount: 'Cuenta financiera',
            representative: 'Representante',
            deliveryType: 'Tipo de entrega',
            freightIndicator: 'Indicador de flete',
            carrier: 'Transportadora',
            costCenter: 'Centro de costo',
            paymentMethodGroup: 'Grupo de forma de pago',
            salesBlock: 'Bloqueo de venta',
            daysForDueNotice: 'Días para aviso de vencimiento',
            daysForCollectionNotice: 'Días para aviso de cobro',
            billingDayLimit: 'Día límite para facturación',
            maxDiscountPercentage: 'Porcentaje de descuento máximo',
            minimumBillingValue: 'Valor de facturación mínimo',
            creditLimit: 'Límite de crédito',
            morning: 'Mañana',
            afternoon: 'Tarde',
            evening: 'Noche',
            start: 'Inicio',
            end: 'Fin',
            noResults: 'No se encontraron resultados',
            uniqueIdentifier: 'Identificador único del registro',
            itemDescription: 'Descripción detallada del elemento',
            categoryType: 'Categoría o tipo de clasificación',
            textContent: 'Contenido textual principal',
            activeStatus: 'Estado de activación del elemento',
            currentStatus: 'Estado actual del registro',
            availableActions: 'Acciones disponibles para el elemento',
            fullName: 'Nombre completo de la persona',
            emailAddress: 'Dirección de correo electrónico',
            phoneNumber: 'Número de teléfono',
            dateField: 'Campo de fecha',
            monetaryValue: 'Valor monetario',
            numericQuantity: 'Cantidad numérica',
            additionalNotes: 'Notas adicionales',
            creationDate: 'Fecha de creación',
            lastUpdate: 'Última actualización',
            tableColumn: 'Columna de la tabla',
            department: 'Departamento',
            sector: 'Sector',
            position: 'Cargo',
            admissionDate: 'Fecha de admisión',
            pis: 'PIS',
            voterCard: 'Título de elector',
            workCard: 'Cartera de trabajo y social',
            motherName: 'Nombre de la madre',
            male: 'Masculino',
            female: 'Femenino',
            gender: 'Género',
            qualityResponsible: 'Responsable de calidad',
            observations: 'Observaciones',
            passwordExpirationDays: 'Días para expirar contraseña',
            forceOperatorClosure: 'Forzar cierre de operador',
            viewOnlySeller: 'Ver solo datos del vendedor',
            viewOnlyBuyer: 'Ver solo datos del comprador',
            viewOnlyCashier: 'Ver solo datos del cajero',
            viewOnlyCompany: 'Ver solo datos de la empresa',
            viewOnlyRepresentative: 'Ver solo datos del representante',
            selectSeller: 'Seleccionar vendedor',
            selectBuyer: 'Seleccionar comprador',
            selectCashier: 'Seleccionar cajero',
            selectCompany: 'Seleccionar empresa',
            selectRepresentative: 'Seleccionar representante',
            currencySymbol: 'Gs.',
            currencyAcronym: 'PYG',
            buyer: 'Comprador',
            hourCost: 'Valor Costo Hora',
            addressType: 'Tipo de Dirección',
            country: 'País',
            zipCode: 'Código Postal',
            state: 'Estado',
            city: 'Ciudad',
            street: 'Calle',
            number: 'Número',
            neighborhood: 'Barrio',
            complement: 'Complemento',
            referencePoint: 'Punto de Referencia',
            documentType: 'Tipo de Documento',
            documentNumber: 'Número de Documento',
            occupation: 'Ocupación',
            birthDate: 'Fecha de Nacimiento',
            phoneType: 'Tipo de Teléfono',
            areaCode: 'Código de Área',
            extension: 'Extensión',
            phone: 'Teléfono',
            businessPartner: 'Socio de Negocio que Refirió',
            mainContact: 'Contacto Principal de la Cuenta/Prospecto',
            employeeCount: 'Número de Empleados',
            annualRevenue: 'Facturación Anual',
            type: 'Tipo',
            content: 'Contenido',
            column: 'Columna',
            id: 'ID',
            actions: 'Acciones',
            search: 'Buscar',
            status: 'Estado',
            inactive: 'Inactivo',
            brand: 'Marca',
            model: 'Modelo',
            difficultyLevel: 'Nivel de Dificultad',
            attachment: 'Adjunto',
            attachmentPreview: 'Vista previa del adjunto',
            imageLoadError: 'Error al cargar imagen',
            selectFile: 'Seleccionar archivo',
            rememberMe: 'Recordarme',
            forgotPassword: '¿Olvidó la contraseña?',
            or: 'o',
            loginWith: 'Iniciar sesión con',
            loading: 'Cargando...',
            typeToSearch: 'Escriba para buscar',
            code: 'Código',
            codeDescription: 'Código/Descripción',
            firstName: 'Nombre',
            lastName: 'Apellido',
            nameEmail: 'Nombre/Email',
            countryAbbreviation: 'Abreviación',
            countryCode: 'Código',
            stateAbbreviation: 'Abreviación del Estado',
            ibgeCode: 'Código IBGE',
            iss: 'ISS (%)'
        },
        masks: {
            docLegalPerson: '###########',
            docNaturalPerson: '#########'
        },
        addressTypes: {
            residential: 'Residencial',
            commercial: 'Comercial',
            delivery: 'Entrega',
            billing: 'Facturación',
            other: 'Otro'
        },
        documentTypes: {
            cpf: 'CPF',
            cnpj: 'CNPJ',
            rg: 'Cédula de Identidad',
            passport: 'Pasaporte',
            other: 'Otro'
        },
        occupations: {
            manager: 'Gerente',
            director: 'Director',
            analyst: 'Analista',
            developer: 'Desarrollador',
            assistant: 'Asistente',
            coordinator: 'Coordinador',
            consultant: 'Consultor',
            other: 'Otro'
        },
        departments: {
            administrative: 'Administrativo',
            financial: 'Financiero',
            commercial: 'Comercial',
            marketing: 'Marketing',
            humanResources: 'Recursos Humanos',
            technology: 'Tecnología',
            operations: 'Operaciones',
            logistics: 'Logística',
            other: 'Otro'
        },
        phoneTypes: {
            mobile: 'Móvil',
            home: 'Casa',
            work: 'Trabajo',
            other: 'Otro'
        },
        messages: {
            onlyStatusField:
                'Este formulario contiene solo el campo de estado.',
            noPhones:
                'No hay teléfonos registrados. Haga clic en el botón "Agregar Teléfono" para comenzar.',
            noEmails:
                'No hay correos electrónicos registrados. Haga clic en el botón "Agregar Email" para comenzar.'
        },
        titles: {
            description: 'Descripción',
            userRegistration: 'Registro de usuario',
            userInfo: 'Información del usuario',
            accessAndPermissions: 'Acceso y permisos',
            restrictions: 'Restricciones',
            emailConfigs: 'Configuraciones de correo electrónico',
            details: 'Detalles',
            errorsFound: 'Errores encontrados',
            permissionsManagement: 'Gestión de permisos',
            permissionGroupManagement: 'Gestión de grupos de permisos',
            modulePermissions: 'Permisos por módulo',
            basicInfo: 'Información básica',
            userPreferences: 'Preferencias del usuario',
            userPermissions: 'Permisos del usuario',
            permissionGroups: 'Grupos de permisos',
            viewRestrictions: 'Restricciones de visualización',
            customPermissions: 'Permisos personalizados',
            userCredentials: 'Credenciales del usuario',
            emailConfig: 'Configuración de correo electrónico',
            basicForm: 'Formulario básico',
            sellerForm: 'Formulario de vendedor',
            buyerForm: 'Formulario de comprador',
            customerForm: 'Formulario de cliente',
            supplierForm: 'Formulario de proveedor',
            employeeForm: 'Formulario de empleado',
            driverForm: 'Formulario de conductor',
            technicianForm: 'Formulario de técnico',
            addressForm: 'Formulario de dirección',
            documentForm: 'Formulario de documento',
            contactForm: 'Formulario de contacto',
            crmProspectForm: 'Formulario de CRM/Prospecto',
            professionalInfo: 'Información profesional',
            limitsAndSettings: 'Límites y configuraciones',
            businessHours: 'Horarios de funcionamiento',
            additionalInfo: 'Información adicional',
            phones: 'Teléfonos',
            emails: 'Correos electrónicos',
            brandModel: 'Vincular Modelo a Marca',
            modelInfo: 'Información del Modelo',
            ncmForm: 'Formulario NCM',
            categoriaCNHForm: 'Formulario de Categoría CNH',
            cityForm: 'Formulario de Ciudad',
            standardizedTextForm: 'Formulario de Texto Estandarizado',
            newsForm: 'Formulario de Noticias',
            content: 'Contenido'
        },
        sections: {
            basicInfo: 'Información Básica',
            location: 'Ubicación',
            addressDetails: 'Detalles de Dirección'
        },
        placeholders: {
            standardizedTextContent:
                'Ingrese el contenido del texto estandarizado...',
            newsDescription: 'Ingrese el contenido de la noticia...',
            selectModuleFirst: 'Seleccione un módulo primero',
            selectRoute: 'Seleccione una ruta',
            ncmCode: 'Ingrese el código NCM (formato: 0000.00.00)',
            ncmDescription: 'Ingrese la descripción del NCM'
        },
        hints: {
            difficultyLevel: 'Nivel del 1 al 10',
            attachment:
                'Máximo 1 archivo. Formatos: jpg, jpeg, png, pdf, pfx, txt, crt, ret, doc, docx, xls, xlsx, msg. Tamaño máximo: 2MB'
        },
        errors: {
            loadModelsError: 'Error al cargar modelos',
            loadCountriesError: 'Error al cargar países',
            loadStatesError: 'Error al cargar estados',
            maxFileSize: 'Archivo demasiado grande. Tamaño máximo: 2MB',
            invalidFileType: 'Tipo de archivo no permitido'
        }
    },
    buttons: {
        login: 'Iniciar sesión',
        goHome: 'Ir a la página de inicio',
        goBack: 'Volver',
        confirm: 'Confirmar',
        cancel: 'Cancelar',
        apply: 'Aplicar',
        reset: 'Restablecer',
        addPhone: 'Agregar Teléfono',
        addEmail: 'Agregar Email',
        testConnection: 'Probar conexión',
        edit: 'Editar',
        viewProfile: 'Ver perfil',
        add: 'Agregar',
        delete: 'Eliminar',
        deleteSelected: 'Eliminar seleccionados ({count})',
        deleteSelectedDescription: 'Eliminar los elementos seleccionados',
        refresh: 'Actualizar',
        clear: 'Limpiar',
        filter: 'Filtrar',
        save: 'Guardar',
        export: 'Exportar',
        import: 'Importar',
        actions: 'Acciones',
        linkModel: 'Vincular modelo',
        close: 'Cerrar',
        loadMore: 'Cargar más',
        view: 'Ver',
        remove: 'Eliminar',
        editSelected: 'Editar seleccionado',
        editSelectedDescription: 'Editar el elemento seleccionado',
        activate: 'Activar',
        deactivate: 'Desactivar',
        activateDescription: 'Activar el elemento seleccionado',
        deactivateDescription: 'Desactivar el elemento seleccionado',
        viewAttachment: 'Ver adjunto',
        viewAttachmentDescription: 'Ver adjunto del elemento seleccionado',
        linkModelToSelected: 'Vincular modelo',
        linkModelDescription: 'Vincular modelo al elemento seleccionado',
        logout: 'Salir',
        back: 'Volver',
        backToPage: 'Volver a la página',
        retry: 'Intentar de nuevo'
    },
    help: {
        tooltip: 'Ayuda',
        title: 'Ayuda del Sistema',
        subtitle: 'Documentación y guías de usuario',
        loading: 'Cargando ayuda...',
        error: 'Error al cargar ayuda',
        loadError: 'No se pudo cargar el contenido de ayuda',
        noContent: 'No hay contenido de ayuda disponible',
        noContentDescription:
            'El contenido de ayuda para esta página aún no está disponible',
        aboutPage: 'Sobre la Página',
        aboutPageDescription: 'Ayuda específica de esta página',
        noHelpAvailable: 'No hay ayuda disponible para esta página',
        news: 'Novedades',
        newsDescription: 'Últimas actualizaciones del sistema'
    },
    pages: {
        titles: {
            home: 'Inicio',
            login: 'Iniciar sesión',
            pageNotFound: 'Página no encontrada',
            dev: 'Desarrollo',
            profile: 'Perfil',
            newsView: 'Noticias',
            standardizedTexts: 'Textos estandarizados',
            generalRegistration: 'Registros generales',
            productQuery: 'Inventario',
            productBrand: 'Marca',
            productModel: 'Modelo',
            ncm: 'NCM',
            accessConfig: 'Configuración de acceso',
            accessUsers: 'Usuarios',
            country: 'País',
            categoryCNH: 'Categoría de CNH',
            state: 'Estado',
            city: 'Ciudad',
            region: 'Región',
            addressType: 'Tipo de dirección',
            documentType: 'Tipo de documento',
            paymentGroup: 'Grupo de pago',
            activitySector: 'Sector de actividad',
            historicLaunch: 'Lanzamiento histórico',
            family: 'Familia',
            group: 'Grupo',
            subgroup: 'Subgrupo',
            sale: 'Venta',
            customerCategory: 'Categoría de cliente',
            discountGroup: 'Grupo de descuento',
            project: 'Proyecto',
            application: 'Aplicación',
            installationType: 'Tipo de instalación',
            profileType: 'Tipo de perfil',
            materialType: 'Tipo de material',
            projectGroup: 'Grupo de proyecto',
            materialGroup: 'Grupo de material',
            os: 'Sistema operativo',
            title: 'Título',
            equipmentPart: 'Parte del equipo',
            defect: 'Defecto',
            quality: 'Calidad',
            cause: 'Causa',
            equipmentFamily: 'Familia de equipos',
            finance: 'Finanzas',
            billingPortfolio: 'Cartera de facturación',
            production: 'Producción',
            checklist: 'Lista de verificación',
            operation: 'Operación',
            nonConformity: 'No conformidad',
            config: 'Configuración',
            userProfile: 'Perfil de usuario',
            module: 'Módulo',
            rh: 'RH',
            position: 'Cargo',
            education: 'Educación',
            specialty: 'Especialidad',
            skill: 'Habilidad',
            department: 'Departamento',
            origin: 'Origen',
            crm: 'CRM',
            settings: 'Configuraciones',
            opportunity: 'Oportunidad',
            news: 'Noticias',
            help: 'Ayuda',
            common: {
                register: 'Registro'
            }
        },
        standardizedTexts: {
            title: 'Textos estandarizados',
            subtitle: 'Gestionar textos estandarizados del sistema',
            list: 'Lista de textos estandarizados',
            createTitle: 'Nuevo texto estandarizado',
            editTitle: 'Editar texto estandarizado',
            messages: {
                createSuccess: '¡Texto estandarizado creado exitosamente!',
                updateSuccess: '¡Texto estandarizado actualizado exitosamente!',
                deleteSuccess: '¡Texto estandarizado eliminado exitosamente!',
                deleteMultipleSuccess:
                    '¡Textos estandarizados eliminados exitosamente!'
            },
            errors: {
                loadError: 'Error al cargar datos del texto estandarizado',
                saveError: 'Error al guardar texto estandarizado',
                deleteError: 'Error al eliminar texto estandarizado',
                deleteMultipleError: 'Error al eliminar textos estandarizados',
                notFound: 'Texto estandarizado no encontrado'
            }
        },
        news: {
            title: 'Noticias',
            subtitle: 'Gestionar noticias del sistema',
            list: 'Lista de noticias',
            createTitle: 'Nueva noticia',
            editTitle: 'Editar noticia',
            messages: {
                createSuccess: '¡Noticia creada exitosamente!',
                updateSuccess: '¡Noticia actualizada exitosamente!',
                deleteSuccess: '¡Noticia eliminada exitosamente!',
                deleteMultipleSuccess: '¡Noticias eliminadas exitosamente!'
            },
            errors: {
                loadError: 'Error al cargar datos de la noticia',
                saveError: 'Error al guardar noticia',
                deleteError: 'Error al eliminar noticia',
                deleteMultipleError: 'Error al eliminar noticias',
                notFound: 'Noticia no encontrada'
            }
        },
        newsView: {
            title: 'Noticias del Sistema',
            subtitle:
                'Mantente al día con las últimas actualizaciones y mejoras',
            noNews: 'No se encontraron noticias',
            noNewsDescription:
                'Intenta ajustar los filtros o verifica nuevamente más tarde',
            filters: {
                status: 'Estado',
                module: 'Módulo',
                search: 'Buscar noticias...',
                period: 'Período',
                sortBy: 'Ordenar por',
                startDate: 'Fecha inicio',
                endDate: 'Fecha fin',
                searchContent: 'Buscar en contenido...',
                allModules: 'Todos los módulos'
            },
            periods: {
                all: 'Todos los períodos',
                week: 'Última semana',
                month: 'Último mes',
                quarter: 'Últimos 3 meses',
                year: 'Último año'
            },
            sorting: {
                dateDesc: 'Más recientes primero',
                dateAsc: 'Más antiguas primero',
                titleAsc: 'Título A-Z',
                titleDesc: 'Título Z-A',
                moduleAsc: 'Módulo A-Z',
                statusNew: 'No leídas primero'
            },
            status: {
                all: 'Todas',
                new: 'Nuevas',
                viewed: 'Vistas',
                newLabel: 'Nueva',
                viewedLabel: 'Vista'
            },
            actions: {
                markAsViewed: 'Marcar como vista',
                markAllAsViewed: 'Marcar Todas',
                clearFilters: 'Limpiar',
                viewDetails: 'Ver detalles',
                close: 'Cerrar'
            },
            messages: {
                markedAsViewed: '¡Noticia marcada como vista!',
                allMarkedAsViewed:
                    '¡Todas las noticias fueron marcadas como vistas!',
                markViewedError: 'Error al marcar noticia como vista',
                markAllViewedError: 'Error al marcar noticias como vistas'
            },
            errors: {
                loadError: 'Error al cargar noticias'
            }
        },
        help: {
            title: 'Gestión de Ayudas',
            createTitle: 'Nueva Ayuda',
            editTitle: 'Editar Ayuda',
            subtitle: 'Gestionar contenido de ayuda del sistema',
            messages: {
                createdSuccessfully: '¡Ayuda creada con éxito!',
                updatedSuccessfully: '¡Ayuda actualizada con éxito!',
                deletedSuccessfully: '¡Ayuda eliminada con éxito!',
                activatedSuccessfully: '¡Ayuda activada con éxito!',
                deactivatedSuccessfully: '¡Ayuda desactivada con éxito!',
                errorSaving: 'Error al guardar ayuda',
                errorDeleting: 'Error al eliminar ayuda',
                errorUpdating: 'Error al actualizar ayuda',
                errorLoading: 'Error al cargar ayudas'
            }
        },
        helpView: {
            title: 'Ayuda del Sistema',
            subtitle: 'Documentación y orientaciones para uso de la página',
            noHelp: 'No hay ayuda disponible',
            noHelpDescription:
                'No hay contenido de ayuda disponible para esta página',
            lastUpdate: 'Última actualización',
            errors: {
                loadError: 'Error al cargar contenido de ayuda'
            }
        },
        home: {
            welcome: {
                title: 'Bienvenido a CorpERP',
                subtitle:
                    '¡Que tengas un excelente día de trabajo! Aquí están las últimas novedades de la empresa.',
                dashboardButton: 'Panel de Control',
                helpButton: 'Ayuda'
            },
            noticeBoard: {
                title: 'Tablón de Avisos',
                addNotice: 'Agregar Aviso',
                addNoticeTooltip: 'Agregar Aviso',
                emptyState:
                    'No hay avisos en este momento. ¿Qué tal agregar el primero?',
                priority: {
                    high: 'Alta',
                    medium: 'Media',
                    low: 'Baja'
                }
            },
            statistics: {
                title: 'Estadísticas',
                users: 'Usuarios',
                notices: 'Avisos',
                pending: 'Pendientes',
                completed: 'Completadas'
            },
            addNoticeModal: {
                title: 'Nuevo Aviso',
                titleField: 'Título del Aviso',
                priorityField: 'Prioridad',
                contentField: 'Contenido',
                authorField: 'Autor',
                cancelButton: 'Cancelar',
                publishButton: 'Publicar',
                successMessage: '¡Aviso publicado con éxito!',
                validation: {
                    titleRequired: 'El título es requerido',
                    contentRequired: 'El contenido es requerido',
                    authorRequired: 'El autor es requerido'
                }
            },
            editNoticeModal: {
                title: 'Editar Aviso',
                titleField: 'Título del Aviso',
                priorityField: 'Prioridad',
                contentField: 'Contenido',
                authorField: 'Autor',
                cancelButton: 'Cancelar',
                saveButton: 'Guardar',
                successMessage: '¡Aviso actualizado con éxito!',
                validation: {
                    titleRequired: 'El título es obligatorio',
                    contentRequired: 'El contenido es obligatorio',
                    authorRequired: 'El autor es obligatorio'
                }
            },
            deleteNoticeDialog: {
                title: 'Confirmar Eliminación',
                message: '¿Está seguro que desea eliminar el aviso "{title}"?',
                cancelButton: 'Cancelar',
                confirmButton: 'Eliminar',
                successMessage: '¡Aviso eliminado con éxito!'
            }
        },
        productBrands: {
            title: 'Marcas',
            subtitle: 'Gestionar las marcas del sistema',
            list: 'Lista de marcas',
            createTitle: 'Nueva marca',
            editTitle: 'Editar marca',
            messages: {
                createSuccess: '¡Marca creada con éxito!',
                updateSuccess: '¡Marca actualizada con éxito!',
                deleteSuccess: '¡Marca eliminada con éxito!',
                deleteMultipleSuccess: '¡Marcas eliminadas con éxito!',
                deactivateSuccess: '¡Marca desactivada con éxito!',
                activateSuccess: '¡Marca activada con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos de la marca',
                saveError: 'Error al guardar marca',
                deleteError: 'Error al eliminar marca',
                deleteMultipleError: 'Error al eliminar marcas',
                notFound: 'Marca no encontrada',
                duplicateDescription:
                    'Ya existe una marca con la misma descripción'
            }
        },
        productModels: {
            title: 'Modelos',
            subtitle: 'Gestionar los modelos del sistema',
            list: 'Lista de modelos',
            createTitle: 'Nuevo modelo',
            editTitle: 'Editar modelo',
            messages: {
                createSuccess: '¡Modelo creado con éxito!',
                updateSuccess: '¡Modelo actualizado con éxito!',
                deleteSuccess: '¡Modelo eliminado con éxito!',
                deleteMultipleSuccess: '¡Modelos eliminados con éxito!',
                activateSuccess: '¡Modelo activado con éxito!',
                deactivateSuccess: '¡Modelo desactivado con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del modelo',
                saveError: 'Error al guardar modelo',
                deleteError: 'Error al eliminar modelo',
                deleteMultipleError: 'Error al eliminar modelos',
                notFound: 'Modelo no encontrado',
                duplicateDescription:
                    'El campo descripción es único y ya existe en la base de datos.'
            }
        },
        ncm: {
            title: 'NCM',
            subtitle: 'Gestionar los códigos NCM del sistema',
            list: 'Lista de NCMs',
            createTitle: 'Nuevo NCM',
            editTitle: 'Editar NCM',
            form: {
                title: 'Formulario NCM'
            },
            messages: {
                createSuccess: '¡NCM creado con éxito!',
                updateSuccess: '¡NCM actualizado con éxito!',
                deleteSuccess: '¡NCM eliminado con éxito!',
                deleteMultipleSuccess: '¡NCMs eliminados con éxito!',
                activateSuccess: '¡NCM activado con éxito!',
                deactivateSuccess: '¡NCM desactivado con éxito!',
                exportNotImplemented:
                    'Funcionalidad de exportación aún no implementada'
            },
            errors: {
                loadError: 'Error al cargar datos del NCM',
                saveError: 'Error al guardar NCM',
                deleteError: 'Error al eliminar NCM',
                deleteMultipleError: 'Error al eliminar NCMs',
                notFound: 'NCM no encontrado'
            }
        },
        users: {
            title: 'Usuarios',
            subtitle: 'Gestionar los usuarios del sistema',
            list: 'Lista de usuarios',
            errors: {
                loadError: 'Error al cargar datos de usuarios'
            }
        },
        countries: {
            title: 'Países',
            subtitle: 'Gestionar los países del sistema',
            list: 'Lista de países',
            createTitle: 'Nuevo país',
            editTitle: 'Editar país',
            form: {
                title: 'Formulario País'
            },
            messages: {
                createSuccess: '¡País creado con éxito!',
                updateSuccess: '¡País actualizado con éxito!',
                deleteSuccess: '¡País eliminado con éxito!',
                deleteMultipleSuccess: '¡Países eliminados con éxito!',
                exportNotImplemented:
                    'Funcionalidad de exportación aún no implementada'
            },
            errors: {
                loadError: 'Error al cargar datos del país',
                saveError: 'Error al guardar país',
                deleteError: 'Error al eliminar país',
                deleteMultipleError: 'Error al eliminar países',
                notFound: 'País no encontrado'
            }
        },
        region: {
            title: 'Regiones',
            subtitle: 'Gestionar las regiones del sistema',
            list: 'Lista de regiones',
            createTitle: 'Nueva región',
            editTitle: 'Editar región',
            form: {
                title: 'Formulario de Región'
            },
            messages: {
                createSuccess: '¡Región creada exitosamente!',
                updateSuccess: '¡Región actualizada exitosamente!',
                deleteSuccess: '¡Región eliminada exitosamente!',
                deleteMultipleSuccess: '¡Regiones eliminadas exitosamente!'
            },
            errors: {
                loadError: 'Error al cargar datos de la región',
                saveError: 'Error al guardar región',
                deleteError: 'Error al eliminar región',
                deleteMultipleError: 'Error al eliminar regiones',
                notFound: 'Región no encontrada'
            }
        },
        states: {
            title: 'Estados',
            subtitle: 'Gestionar estados del sistema',
            list: 'Lista de estados',
            createTitle: 'Nuevo estado',
            editTitle: 'Editar estado',
            form: {
                title: 'Formulario de Estado'
            },
            messages: {
                createSuccess: '¡Estado creado exitosamente!',
                updateSuccess: '¡Estado actualizado exitosamente!',
                deleteSuccess: '¡Estado eliminado exitosamente!',
                deleteMultipleSuccess: '¡Estados eliminados exitosamente!'
            },
            errors: {
                loadError: 'Error al cargar datos del estado',
                saveError: 'Error al guardar estado',
                deleteError: 'Error al eliminar estado',
                deleteMultipleError: 'Error al eliminar estados',
                notFound: 'Estado no encontrado'
            }
        },
        cities: {
            title: 'Ciudades',
            subtitle: 'Gestionar ciudades del sistema',
            list: 'Lista de ciudades',
            createTitle: 'Nueva ciudad',
            editTitle: 'Editar ciudad',
            form: {
                title: 'Formulario Ciudad'
            },
            messages: {
                createSuccess: '¡Ciudad creada con éxito!',
                updateSuccess: '¡Ciudad actualizada con éxito!',
                deleteSuccess: '¡Ciudad eliminada con éxito!',
                deleteMultipleSuccess: '¡Ciudades eliminadas con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos de la ciudad',
                saveError: 'Error al guardar ciudad',
                deleteError: 'Error al eliminar ciudad',
                deleteMultipleError: 'Error al eliminar ciudades',
                notFound: 'Ciudad no encontrada'
            }
        },
        addressType: {
            title: 'Tipos de dirección',
            subtitle: 'Gestionar tipos de dirección del sistema',
            list: 'Lista de tipos de dirección',
            createTitle: 'Nuevo tipo de dirección',
            editTitle: 'Editar tipo de dirección',
            form: {
                title: 'Formulario de Tipo de Dirección'
            },
            messages: {
                createSuccess: '¡Tipo de dirección creado con éxito!',
                updateSuccess: '¡Tipo de dirección actualizado con éxito!',
                deleteSuccess: '¡Tipo de dirección eliminado con éxito!',
                deleteMultipleSuccess:
                    '¡Tipos de dirección eliminados con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del tipo de dirección',
                saveError: 'Error al guardar tipo de dirección',
                deleteError: 'Error al eliminar tipo de dirección',
                deleteMultipleError: 'Error al eliminar tipos de dirección',
                notFound: 'Tipo de dirección no encontrado',
                duplicateDescription:
                    'Ya existe un tipo de dirección con esta descripción.'
            }
        },
        categoriaCNH: {
            title: 'Categorías de CNH',
            subtitle: 'Gestionar categorías de CNH del sistema',
            list: 'Lista de categorías de CNH',
            createTitle: 'Nueva categoría de CNH',
            editTitle: 'Editar categoría de CNH',
            form: {
                title: 'Formulario de Categoría de CNH'
            },
            messages: {
                createSuccess: '¡Categoría de CNH creada con éxito!',
                updateSuccess: '¡Categoría de CNH actualizada con éxito!',
                deleteSuccess: '¡Categoría de CNH eliminada con éxito!',
                deleteMultipleSuccess:
                    '¡Categorías de CNH eliminadas con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos de la categoría de CNH',
                saveError: 'Error al guardar categoría de CNH',
                deleteError: 'Error al eliminar categoría de CNH',
                deleteMultipleError: 'Error al eliminar categorías de CNH'
            }
        },
        documentType: {
            title: 'Tipos de documento',
            subtitle: 'Gestionar tipos de documento del sistema',
            list: 'Lista de tipos de documento',
            createTitle: 'Nuevo tipo de documento',
            editTitle: 'Editar tipo de documento',
            form: {
                title: 'Formulario de Tipo de Documento'
            },
            messages: {
                createSuccess: '¡Tipo de documento creado con éxito!',
                updateSuccess: '¡Tipo de documento actualizado con éxito!',
                deleteSuccess: '¡Tipo de documento eliminado con éxito!',
                deleteMultipleSuccess:
                    '¡Tipos de documento eliminados con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del tipo de documento',
                saveError: 'Error al guardar tipo de documento',
                deleteError: 'Error al eliminar tipo de documento',
                deleteMultipleError: 'Error al eliminar tipos de documento',
                notFound: 'Tipo de documento no encontrado',
                duplicateDescription:
                    'Ya existe un tipo de documento con esta descripción.'
            }
        },
        paymentGroup: {
            title: 'Grupos de pago',
            subtitle: 'Gestionar grupos de pago del sistema',
            list: 'Lista de grupos de pago',
            createTitle: 'Nuevo grupo de pago',
            editTitle: 'Editar grupo de pago',
            form: {
                title: 'Formulario de Grupo de Pago'
            },
            messages: {
                createSuccess: '¡Grupo de pago creado con éxito!',
                updateSuccess: '¡Grupo de pago actualizado con éxito!',
                deleteSuccess: '¡Grupo de pago eliminado con éxito!',
                deleteMultipleSuccess: '¡Grupos de pago eliminados con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del grupo de pago',
                saveError: 'Error al guardar grupo de pago',
                deleteError: 'Error al eliminar grupo de pago',
                deleteMultipleError: 'Error al eliminar grupos de pago',
                notFound: 'Grupo de pago no encontrado',
                duplicateDescription:
                    'Ya existe un grupo de pago con esta descripción.'
            }
        },
        activitySector: {
            title: 'Sectores de actividad',
            subtitle: 'Gestionar sectores de actividad del sistema',
            list: 'Lista de sectores de actividad',
            createTitle: 'Nuevo sector de actividad',
            editTitle: 'Editar sector de actividad',
            form: {
                title: 'Formulario de Sector de Actividad'
            },
            messages: {
                createSuccess: '¡Sector de actividad creado con éxito!',
                updateSuccess: '¡Sector de actividad actualizado con éxito!',
                deleteSuccess: '¡Sector de actividad eliminado con éxito!',
                deleteMultipleSuccess:
                    '¡Sectores de actividad eliminados con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del sector de actividad',
                saveError: 'Error al guardar sector de actividad',
                deleteError: 'Error al eliminar sector de actividad',
                deleteMultipleError: 'Error al eliminar sectores de actividad',
                notFound: 'Sector de actividad no encontrado'
            }
        },
        historicLaunch: {
            title: 'Histórico de lanzamientos',
            subtitle: 'Gestionar historial de lanzamientos del sistema',
            list: 'Lista de historial de lanzamientos',
            createTitle: 'Nuevo historial de lanzamiento',
            editTitle: 'Editar historial de lanzamiento',
            form: {
                title: 'Formulario de Historial de Lanzamiento'
            },
            messages: {
                createSuccess: '¡Historial de lanzamiento creado con éxito!',
                updateSuccess:
                    '¡Historial de lanzamiento actualizado con éxito!',
                deleteSuccess: '¡Historial de lanzamiento eliminado con éxito!',
                deleteMultipleSuccess:
                    '¡Historiales de lanzamiento eliminados con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del historial de lanzamiento',
                saveError: 'Error al guardar historial de lanzamiento',
                deleteError: 'Error al eliminar historial de lanzamiento',
                deleteMultipleError:
                    'Error al eliminar historiales de lanzamiento',
                notFound: 'Historial de lanzamiento no encontrado'
            }
        },
        family: {
            title: 'Familias',
            subtitle: 'Gestionar familias del sistema',
            list: 'Lista de familias',
            createTitle: 'Nueva familia',
            editTitle: 'Editar familia',
            form: {
                title: 'Formulario de Familia'
            },
            messages: {
                createSuccess: '¡Familia creada con éxito!',
                updateSuccess: '¡Familia actualizada con éxito!',
                deleteSuccess: '¡Familia eliminada con éxito!',
                deleteMultipleSuccess: '¡Familias eliminadas con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos de la familia',
                saveError: 'Error al guardar familia',
                deleteError: 'Error al eliminar familia',
                deleteMultipleError: 'Error al eliminar familias',
                notFound: 'Familia no encontrada',
                duplicateDescription:
                    'Ya existe una familia con esta descripción.'
            }
        },
        group: {
            title: 'Grupos',
            subtitle: 'Gestionar grupos del sistema',
            list: 'Lista de grupos',
            createTitle: 'Nuevo grupo',
            editTitle: 'Editar grupo',
            form: {
                title: 'Formulario de Grupo'
            },
            messages: {
                createSuccess: '¡Grupo creado con éxito!',
                updateSuccess: '¡Grupo actualizado con éxito!',
                deleteSuccess: '¡Grupo eliminado con éxito!',
                notFound: 'Grupo no encontrado',
                deleteMultipleSuccess: '¡Grupos eliminados con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del grupo',
                saveError: 'Error al guardar grupo',
                deleteError: 'Error al eliminar grupo',
                deleteMultipleError: 'Error al eliminar grupos',
                duplicateDescription: 'Ya existe un grupo con esta descripción.'
            }
        },
        subgroup: {
            title: 'Subgrupos',
            subtitle: 'Gestionar subgrupos del sistema',
            list: 'Lista de subgrupos',
            createTitle: 'Nuevo subgrupo',
            editTitle: 'Editar subgrupo',
            form: {
                title: 'Formulario de Subgrupo'
            },
            messages: {
                createSuccess: '¡Subgrupo creado con éxito!',
                updateSuccess: '¡Subgrupo actualizado con éxito!',
                deleteSuccess: '¡Subgrupo eliminado con éxito!',
                deleteMultipleSuccess: '¡Subgrupos eliminados con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del subgrupo',
                saveError: 'Error al guardar subgrupo',
                deleteError: 'Error al eliminar subgrupo',
                deleteMultipleError: 'Error al eliminar subgrupos',
                notFound: 'Subgrupo no encontrado',
                duplicateDescription:
                    'Ya existe un subgrupo con esta descripción.'
            }
        },
        customerCategory: {
            title: 'Categorías de clientes',
            subtitle: 'Gestionar categorías de clientes del sistema',
            list: 'Lista de categorías de clientes',
            createTitle: 'Nueva categoría de cliente',
            editTitle: 'Editar categoría de cliente',
            form: {
                title: 'Formulario de Categoría de Cliente'
            },
            messages: {
                createSuccess: '¡Categoría de cliente creada con éxito!',
                updateSuccess: '¡Categoría de cliente actualizada con éxito!',
                deleteSuccess: '¡Categoría de cliente eliminada con éxito!',
                deleteMultipleSuccess:
                    '¡Categorías de cliente eliminadas con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos de la categoría de cliente',
                saveError: 'Error al guardar categoría de cliente',
                deleteError: 'Error al eliminar categoría de cliente',
                deleteMultipleError: 'Error al eliminar categorías de cliente',
                notFound: 'Categoría de cliente no encontrada',
                duplicateDescription:
                    'Ya existe una categoría de cliente con esta descripción.'
            }
        },
        discountGroup: {
            title: 'Grupo de Descuento',
            subtitle: 'Gestionar grupos de descuento del sistema',
            list: 'Lista de grupos de descuento',
            createTitle: 'Nuevo grupo de descuento',
            editTitle: 'Editar grupo de descuento',
            form: {
                title: 'Formulario de Grupo de Descuento'
            },
            messages: {
                createSuccess: '¡Grupo de descuento creado con éxito!',
                updateSuccess: '¡Grupo de descuento actualizado con éxito!',
                deleteSuccess: '¡Grupo de descuento eliminado con éxito!',
                deleteMultipleSuccess:
                    '¡Grupos de descuento eliminados con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del grupo de descuento',
                saveError: 'Error al guardar grupo de descuento',
                deleteError: 'Error al eliminar grupo de descuento',
                deleteMultipleError: 'Error al eliminar grupos de descuento',
                notFound: 'Grupo de descuento no encontrado',
                duplicateDescription:
                    'Ya existe un grupo de descuento con esta descripción.'
            }
        },
        application: {
            title: 'Aplicaciones',
            subtitle: 'Gestionar aplicaciones del sistema',
            list: 'Lista de aplicaciones',
            createTitle: 'Nueva aplicación',
            editTitle: 'Editar aplicación',
            form: {
                title: 'Formulario de Aplicación'
            },
            messages: {
                createSuccess: '¡Aplicación creada con éxito!',
                updateSuccess: '¡Aplicación actualizada con éxito!',
                deleteSuccess: '¡Aplicación eliminada con éxito!',
                deleteMultipleSuccess: '¡Aplicaciones eliminadas con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos de la aplicación',
                saveError: 'Error al guardar aplicación',
                deleteError: 'Error al eliminar aplicación',
                deleteMultipleError: 'Error al eliminar aplicaciones',
                notFound: 'Aplicación no encontrada',
                duplicateDescription:
                    'Ya existe una aplicación con esta descripción.'
            }
        },
        installationType: {
            title: 'Tipos de Instalación',
            subtitle: 'Gestionar tipos de instalación del sistema',
            list: 'Lista de tipos de instalación',
            createTitle: 'Nuevo tipo de instalación',
            editTitle: 'Editar tipo de instalación',
            form: {
                title: 'Formulario de Tipo de Instalación'
            },
            messages: {
                createSuccess: '¡Tipo de instalación creado con éxito!',
                updateSuccess: '¡Tipo de instalación actualizado con éxito!',
                deleteSuccess: '¡Tipo de instalación eliminado con éxito!',
                deleteMultipleSuccess:
                    '¡Tipos de instalación eliminados con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del tipo de instalación',
                saveError: 'Error al guardar tipo de instalación',
                deleteError: 'Error al eliminar tipo de instalación',
                deleteMultipleError: 'Error al eliminar tipos de instalación',
                notFound: 'Tipo de instalación no encontrado',
                duplicateDescription:
                    'Ya existe un tipo de instalación con esta descripción.'
            }
        },
        profileType: {
            title: 'Tipos de Perfil',
            subtitle: 'Gestionar tipos de perfil del sistema',
            list: 'Lista de tipos de perfil',
            createTitle: 'Nuevo tipo de perfil',
            editTitle: 'Editar tipo de perfil',
            form: {
                title: 'Formulario de Tipo de Perfil'
            },
            messages: {
                createSuccess: '¡Tipo de perfil creado con éxito!',
                updateSuccess: '¡Tipo de perfil actualizado con éxito!',
                deleteSuccess: '¡Tipo de perfil eliminado con éxito!',
                deleteMultipleSuccess: '¡Tipos de perfil eliminados con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del tipo de perfil',
                saveError: 'Error al guardar tipo de perfil',
                deleteError: 'Error al eliminar tipo de perfil',
                deleteMultipleError: 'Error al eliminar tipos de perfil',
                notFound: 'Tipo de perfil no encontrado',
                duplicateDescription:
                    'Ya existe un tipo de perfil con esta descripción.'
            }
        },
        materialType: {
            title: 'Tipos de Material',
            subtitle: 'Gestionar tipos de material del sistema',
            list: 'Lista de tipos de material',
            createTitle: 'Nuevo tipo de material',
            editTitle: 'Editar tipo de material',
            form: {
                title: 'Formulario de Tipo de Material'
            },
            messages: {
                createSuccess: '¡Tipo de material creado con éxito!',
                updateSuccess: '¡Tipo de material actualizado con éxito!',
                deleteSuccess: '¡Tipo de material eliminado con éxito!',
                deleteMultipleSuccess:
                    '¡Tipos de material eliminados con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del tipo de material',
                saveError: 'Error al guardar tipo de material',
                deleteError: 'Error al eliminar tipo de material',
                deleteMultipleError: 'Error al eliminar tipos de material',
                notFound: 'Tipo de material no encontrado',
                duplicateDescription:
                    'Ya existe un tipo de material con esta descripción.'
            }
        },
        projectGroup: {
            title: 'Grupos de Proyectos',
            subtitle: 'Gestionar grupos de proyectos del sistema',
            list: 'Lista de grupos de proyectos',
            createTitle: 'Nuevo grupo de proyectos',
            editTitle: 'Editar grupo de proyectos',
            form: {
                title: 'Formulario de Grupo de Proyectos'
            },
            messages: {
                createSuccess: '¡Grupo de proyectos creado con éxito!',
                updateSuccess: '¡Grupo de proyectos actualizado con éxito!',
                deleteSuccess: '¡Grupo de proyectos eliminado con éxito!',
                deleteMultipleSuccess:
                    '¡Grupos de proyectos eliminados con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del grupo de proyectos',
                saveError: 'Error al guardar grupo de proyectos',
                deleteError: 'Error al eliminar grupo de proyectos',
                deleteMultipleError: 'Error al eliminar grupos de proyectos',
                notFound: 'Grupo de proyectos no encontrado',
                duplicateDescription:
                    'Ya existe un grupo de proyectos con esta descripción.'
            }
        },
        materialGroup: {
            title: 'Grupos de Materiales',
            subtitle: 'Gestionar grupos de materiales del sistema',
            list: 'Lista de grupos de materiales',
            createTitle: 'Nuevo grupo de materiales',
            editTitle: 'Editar grupo de materiales',
            form: {
                title: 'Formulario de Grupo de Materiales'
            },
            messages: {
                createSuccess: '¡Grupo de materiales creado con éxito!',
                updateSuccess: '¡Grupo de materiales actualizado con éxito!',
                deleteSuccess: '¡Grupo de materiales eliminado con éxito!',
                deleteMultipleSuccess:
                    '¡Grupos de materiales eliminados con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del grupo de materiales',
                saveError: 'Error al guardar grupo de materiales',
                deleteError: 'Error al eliminar grupo de materiales',
                deleteMultipleError: 'Error al eliminar grupos de materiales',
                notFound: 'Grupo de materiales no encontrado',
                duplicateDescription:
                    'Ya existe un grupo de materiales con esta descripción.'
            }
        },
        title: {
            title: 'Títulos',
            subtitle: 'Gestionar títulos del sistema',
            list: 'Lista de títulos',
            createTitle: 'Nuevo título',
            editTitle: 'Editar título',
            form: {
                title: 'Formulario de Título'
            },
            messages: {
                createSuccess: '¡Título creado con éxito!',
                updateSuccess: '¡Título actualizado con éxito!',
                deleteSuccess: '¡Título eliminado con éxito!',
                deleteMultipleSuccess: '¡Títulos eliminados con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del título',
                saveError: 'Error al guardar título',
                deleteError: 'Error al eliminar título',
                deleteMultipleError: 'Error al eliminar títulos',
                notFound: 'Título no encontrado',
                duplicateDescription:
                    'Ya existe un título con esta descripción.'
            }
        },
        equipmentPart: {
            title: 'Partes de Equipos',
            subtitle: 'Gestionar partes de equipos del sistema',
            list: 'Lista de partes de equipos',
            createTitle: 'Nueva parte de equipo',
            editTitle: 'Editar parte de equipo',
            form: {
                title: 'Formulario de Parte de Equipo'
            },
            messages: {
                createSuccess: '¡Parte de equipo creado con éxito!',
                updateSuccess: '¡Parte de equipo actualizado con éxito!',
                deleteSuccess: '¡Parte de equipo eliminado con éxito!',
                deleteMultipleSuccess: '¡Partes de equipo eliminados con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos de la parte de equipo',
                saveError: 'Error al guardar parte de equipo',
                deleteError: 'Error al eliminar parte de equipo',
                deleteMultipleError: 'Error al eliminar partes de equipo',
                notFound: 'Parte de equipo no encontrado',
                duplicateDescription:
                    'Ya existe una parte de equipo con esta descripción.'
            }
        },
        defect: {
            title: 'Defectos',
            subtitle: 'Gestionar defectos del sistema',
            list: 'Lista de defectos',
            createTitle: 'Nuevo defecto',
            editTitle: 'Editar defecto',
            form: {
                title: 'Formulario de Defecto'
            },
            messages: {
                createSuccess: '¡Defecto creado con éxito!',
                updateSuccess: '¡Defecto actualizado con éxito!',
                deleteSuccess: '¡Defecto eliminado con éxito!',
                deleteMultipleSuccess: '¡Defectos eliminados con éxito!',
                deactivateSuccess: '¡Defecto desactivado con éxito!',
                activateSuccess: '¡Defecto activado con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del defecto',
                saveError: 'Error al guardar defecto',
                deleteError: 'Error al eliminar defecto',
                deleteMultipleError: 'Error al eliminar defectos',
                notFound: 'Defecto no encontrado',
                duplicateDescription:
                    'Ya existe un defecto con esta descripción.'
            }
        },
        cause: {
            title: 'Causas',
            subtitle: 'Gestionar causas del sistema',
            list: 'Lista de causas',
            createTitle: 'Nueva causa',
            editTitle: 'Editar causa',
            form: {
                title: 'Formulario de Causa'
            },
            messages: {
                createSuccess: '¡Causa creada con éxito!',
                updateSuccess: '¡Causa actualizada con éxito!',
                deleteSuccess: '¡Causa eliminada con éxito!',
                deleteMultipleSuccess: '¡Causas eliminadas con éxito!',
                deactivateSuccess: '¡Causa desactivada con éxito!',
                activateSuccess: '¡Causa activada con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos de la causa',
                saveError: 'Error al guardar causa',
                deleteError: 'Error al eliminar causa',
                deleteMultipleError: 'Error al eliminar causas',
                notFound: 'Causa no encontrado',
                duplicateDescription:
                    'Ya existe una causa con esta descripción.'
            }
        },
        equipmentFamily: {
            title: 'Familias de Equipos',
            subtitle: 'Gestionar familias de equipos del sistema',
            list: 'Lista de familias de equipos',
            createTitle: 'Nueva familia de equipos',
            editTitle: 'Editar familia de equipos',
            form: {
                title: 'Formulario de Familia de Equipos'
            },
            messages: {
                createSuccess: '¡Familia de equipos creada con éxito!',
                updateSuccess: '¡Familia de equipos actualizada con éxito!',
                deleteSuccess: '¡Familia de equipos eliminada con éxito!',
                deleteMultipleSuccess:
                    '¡Familias de equipos eliminadas con éxito!',
                deactivateSuccess: '¡Familia de equipos desactivada con éxito!',
                activateSuccess: '¡Familia de equipos activada con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos de la familia de equipos',
                saveError: 'Error al guardar familia de equipos',
                deleteError: 'Error al eliminar familia de equipos',
                deleteMultipleError: 'Error al eliminar familias de equipos',
                notFound: 'Familia de equipos no encontrado',
                duplicateDescription:
                    'Ya existe una familia de equipos con esta descripción.'
            }
        },
        billingPortfolio: {
            title: 'Portafolio de Facturación',
            subtitle: 'Gestionar portafolio de facturación del sistema',
            list: 'Lista de portafolio de facturación',
            createTitle: 'Nuevo portafolio de facturación',
            editTitle: 'Editar portafolio de facturación',
            form: {
                title: 'Formulario de Portafolio de Facturación'
            },
            messages: {
                createSuccess: '¡Portafolio de facturación creado con éxito!',
                updateSuccess:
                    '¡Portafolio de facturación actualizado con éxito!',
                deleteSuccess:
                    '¡Portafolio de facturación eliminado con éxito!',
                deleteMultipleSuccess:
                    '¡Portafolios de facturación eliminados con éxito!',
                deactivateSuccess:
                    '¡Portafolio de facturación desactivado con éxito!',
                activateSuccess:
                    '¡Portafolio de facturación activado con éxito!'
            },
            errors: {
                loadError:
                    'Error al cargar datos del portafolio de facturación',
                saveError: 'Error al guardar portafolio de facturación',
                deleteError: 'Error al eliminar portafolio de facturación',
                deleteMultipleError:
                    'Error al eliminar portafolios de facturación',
                notFound: 'Portafolio de facturación no encontrado',
                duplicateDescription:
                    'Ya existe un portafolio de facturación con esta descripción.'
            }
        },
        checklist: {
            title: 'Checklist',
            subtitle: 'Gestionar checklist del sistema',
            list: 'Lista de checklist',
            createTitle: 'Nuevo checklist',
            editTitle: 'Editar checklist',
            form: {
                title: 'Formulario de Checklist'
            },
            messages: {
                createSuccess: '¡Checklist creado con éxito!',
                updateSuccess: '¡Checklist actualizado con éxito!',
                deleteSuccess: '¡Checklist eliminado con éxito!',
                deleteMultipleSuccess: '¡Checklists eliminados con éxito!',
                deactivateSuccess: '¡Checklist desactivado con éxito!',
                activateSuccess: '¡Checklist activado con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del checklist',
                saveError: 'Error al guardar checklist',
                deleteError: 'Error al eliminar checklist',
                deleteMultipleError: 'Error al eliminar checklists',
                notFound: 'Checklist no encontrado',
                duplicateDescription:
                    'Ya existe un checklist con esta descripción.'
            }
        },
        operations: {
            title: 'Operaciones',
            subtitle: 'Gestionar operaciones del sistema',
            list: 'Lista de operaciones',
            createTitle: 'Nueva operación',
            editTitle: 'Editar operación',
            form: {
                title: 'Formulario de Operación'
            },
            messages: {
                createSuccess: '¡Operación creada con éxito!',
                updateSuccess: '¡Operación actualizada con éxito!',
                deleteSuccess: '¡Operación eliminada con éxito!',
                deleteMultipleSuccess: '¡Operaciones eliminadas con éxito!',
                deactivateSuccess: '¡Operación desactivada con éxito!',
                activateSuccess: '¡Operación activada con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos de la operación',
                saveError: 'Error al guardar operación',
                deleteError: 'Error al eliminar operación',
                deleteMultipleError: 'Error al eliminar operaciones',
                notFound: 'Operación no encontrada',
                duplicateDescription:
                    'Ya existe una operación con esta descripción.'
            }
        },
        nonConformity: {
            title: 'No Conformidades',
            subtitle: 'Gestionar no conformidades del sistema',
            list: 'Lista de no conformidades',
            createTitle: 'Nueva no conformidad',
            editTitle: 'Editar no conformidad',
            form: {
                title: 'Formulario de No Conformidad'
            },
            messages: {
                createSuccess: '¡No conformidad creada con éxito!',
                updateSuccess: '¡No conformidad actualizada con éxito!',
                deleteSuccess: '¡No conformidad eliminada con éxito!',
                deleteMultipleSuccess:
                    '¡No conformidades eliminadas con éxito!',
                deactivateSuccess: '¡No conformidad desactivada con éxito!',
                activateSuccess: '¡No conformidad activada con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos de la no conformidad',
                saveError: 'Error al guardar no conformidad',
                deleteError: 'Error al eliminar no conformidad',
                deleteMultipleError: 'Error al eliminar no conformidades',
                notFound: 'No conformidad no encontrada',
                duplicateDescription:
                    'Ya existe una no conformidad con esta descripción.'
            }
        },
        userProfile: {
            title: 'Perfil de usuario',
            subtitle: 'Gestionar perfil de usuario',
            list: 'Lista de perfiles de usuario',
            createTitle: 'Nuevo perfil de usuario',
            editTitle: 'Editar perfil de usuario',
            form: {
                title: 'Formulario de Perfil de Usuario'
            },
            messages: {
                createSuccess: '¡Perfil de usuario creado con éxito!',
                updateSuccess: '¡Perfil de usuario actualizado con éxito!',
                deleteSuccess: '¡Perfil de usuario eliminado con éxito!',
                deleteMultipleSuccess:
                    '¡Perfiles de usuario eliminados con éxito!',
                deactivateSuccess: '¡Perfil de usuario desactivado con éxito!',
                activateSuccess: '¡Perfil de usuario activado con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del perfil de usuario',
                saveError: 'Error al guardar perfil de usuario',
                deleteError: 'Error al eliminar perfil de usuario',
                deleteMultipleError: 'Error al eliminar perfiles de usuario',
                notFound: 'Perfil de usuario no encontrado',
                duplicateDescription:
                    'Ya existe un perfil de usuario con esta descripción.'
            }
        },
        module: {
            title: 'Módulo',
            subtitle: 'Gestionar módulo',
            list: 'Lista de módulos',
            createTitle: 'Nuevo módulo',
            editTitle: 'Editar módulo',
            form: {
                title: 'Formulario de Módulo'
            },
            messages: {
                createSuccess: '¡Módulo creado con éxito!',
                updateSuccess: '¡Módulo actualizado con éxito!',
                deleteSuccess: '¡Módulo eliminado con éxito!',
                deleteMultipleSuccess: '¡Módulos eliminados con éxito!',
                deactivateSuccess: '¡Módulo desactivado con éxito!',
                activateSuccess: '¡Módulo activado con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del módulo',
                saveError: 'Error al guardar módulo',
                deleteError: 'Error al eliminar módulo',
                deleteMultipleError: 'Error al eliminar módulo',
                notFound: 'Módulo no encontrado',
                duplicateDescription:
                    'Ya existe un módulo con esta descripción.'
            }
        },
        position: {
            title: 'Cargo',
            subtitle: 'Gestionar cargo',
            list: 'Lista de cargos',
            createTitle: 'Nuevo cargo',
            editTitle: 'Editar cargo',
            form: {
                title: 'Formulario de Cargo'
            },
            messages: {
                createSuccess: '¡Cargo creado con éxito!',
                updateSuccess: '¡Cargo actualizado con éxito!',
                deleteSuccess: '¡Cargo eliminado con éxito!',
                deleteMultipleSuccess: '¡Cargos eliminados con éxito!',
                deactivateSuccess: '¡Cargo desactivado con éxito!',
                activateSuccess: '¡Cargo activado con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del cargo',
                saveError: 'Error al guardar cargo',
                deleteError: 'Error al eliminar cargo',
                deleteMultipleError: 'Error al eliminar cargos',
                notFound: 'Cargo no encontrado',
                duplicateDescription: 'Ya existe un cargo con esta descripción.'
            }
        },
        education: {
            title: 'Educación',
            subtitle: 'Gestionar educación',
            list: 'Lista de educación',
            createTitle: 'Nueva educación',
            editTitle: 'Editar educación',
            form: {
                title: 'Formulario de Educación'
            },
            messages: {
                createSuccess: '¡Educación creada con éxito!',
                updateSuccess: '¡Educación actualizada con éxito!',
                deleteSuccess: '¡Educación eliminada con éxito!',
                deleteMultiple: '¡Educación eliminada con éxito!',
                deactivateSuccess: '¡Educación desactivada con éxito!',
                activateSuccess: '¡Educación activada con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos de la educación',
                saveError: 'Error al guardar educación',
                deleteError: 'Error al eliminar educación',
                deleteMultipleError: 'Error al eliminar educación',
                notFound: 'Educación no encontrada',
                duplicateDescription:
                    'Ya existe una educación con esta descripción.'
            }
        },
        specialty: {
            title: 'Especialidad',
            subtitle: 'Gestionar especialidad',
            list: 'Lista de especialidades',
            createTitle: 'Nueva especialidad',
            editTitle: 'Editar especialidad',
            form: {
                title: 'Formulario de Especialidad'
            },
            messages: {
                createSuccess: '¡Especialidad creada con éxito!',
                updateSuccess: '¡Especialidad actualizada con éxito!',
                deleteSuccess: '¡Especialidad eliminada con éxito!',
                deleteMultipleSuccess: '¡Especialidades eliminadas con éxito!',
                deactivateSuccess: '¡Especialidad desactivada con éxito!',
                activateSuccess: '¡Especialidad activada con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos de la especialidad',
                saveError: 'Error al guardar especialidad',
                deleteError: 'Error al eliminar especialidad',
                deleteMultipleError: 'Error al eliminar especialidades',
                notFound: 'Especialidad no encontrada',
                duplicateDescription:
                    'Ya existe una especialidad con esta descripción.'
            }
        },
        skill: {
            title: 'Habilidad',
            subtitle: 'Gestionar habilidad',
            list: 'Lista de habilidades',
            createTitle: 'Nueva habilidad',
            editTitle: 'Editar habilidad',
            form: {
                title: 'Formulario de Habilidad'
            },
            messages: {
                createSuccess: '¡Habilidad creada con éxito!',
                updateSuccess: '¡Habilidad actualizada con éxito!',
                deleteSuccess: '¡Habilidad eliminada con éxito!',
                deleteMultipleSuccess: '¡Habilidades eliminadas con éxito!',
                deactivateSuccess: '¡Habilidad desactivada con éxito!',
                activateSuccess: '¡Habilidad activada con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos de la habilidad',
                saveError: 'Error al guardar habilidad',
                deleteError: 'Error al eliminar habilidad',
                deleteMultipleError: 'Error al eliminar habilidades',
                notFound: 'Habilidad no encontrada',
                duplicateDescription:
                    'Ya existe una habilidad con esta descripción.'
            }
        },
        department: {
            title: 'Departamento',
            subtitle: 'Gestionar departamento',
            list: 'Lista de departamentos',
            createTitle: 'Nuevo departamento',
            editTitle: 'Editar departamento',
            form: {
                title: 'Formulario de Departamento'
            },
            messages: {
                createSuccess: '¡Departamento creado con éxito!',
                updateSuccess: '¡Departamento actualizado con éxito!',
                deleteSuccess: '¡Departamento eliminado con éxito!',
                deleteMultipleSuccess: '¡Departamentos eliminados con éxito!',
                deactivateSuccess: '¡Departamento desactivado con éxito!',
                activateSuccess: '¡Departamento activado con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del departamento',
                saveError: 'Error al guardar departamento',
                deleteError: 'Error al eliminar departamento',
                deleteMultipleError: 'Error al eliminar departamentos',
                notFound: 'Departamento no encontrado',
                duplicateDescription:
                    'Ya existe un departamento con esta descripción.'
            }
        },
        origin: {
            title: 'Origens',
            subtitle: 'Gestionar origens del sistema',
            list: 'Lista de origens',
            createTitle: 'Nuevo origen',
            editTitle: 'Editar origen',
            form: {
                title: 'Formulario de Origens'
            },
            messages: {
                createSuccess: 'Origens creado con éxito!',
                updateSuccess: 'Origens actualizado con éxito!',
                deleteSuccess: 'Origens eliminado con éxito!',
                deleteMultipleSuccess: 'Origens eliminados con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos del origen',
                saveError: 'Error al guardar origen',
                deleteError: 'Error al eliminar origen',
                deleteMultipleError: 'Error al eliminar origens',
                notFound: 'Origen no encontrado',
                duplicateDescription:
                    'Ya existe un origen con esta descripción.'
            }
        },
        opportunity: {
            title: 'Oportunidad',
            subtitle: 'Gestionar oportunidad',
            list: 'Lista de oportunidades',
            createTitle: 'Nueva oportunidad',
            editTitle: 'Editar oportunidad',
            form: {
                title: 'Formulario de Oportunidad'
            },
            messages: {
                createSuccess: '¡Oportunidad creada con éxito!',
                updateSuccess: '¡Oportunidad actualizada con éxito!',
                deleteSuccess: '¡Oportunidad eliminada con éxito!',
                deleteMultipleSuccess: '¡Oportunidades eliminadas con éxito!',
                deactivateSuccess: '¡Oportunidad desactivada con éxito!',
                activateSuccess: '¡Oportunidad activada con éxito!'
            },
            errors: {
                loadError: 'Error al cargar datos de la oportunidad',
                saveError: 'Error al guardar oportunidad',
                deleteError: 'Error al eliminar oportunidad',
                deleteMultipleError: 'Error al eliminar oportunidades',
                notFound: 'Oportunidad no encontrada',
                duplicateDescription:
                    'Ya existe una oportunidad con esta descripción.'
            }
        },
        systemConfig: {
            themeBuilder: {
                title: 'Personalización de Tema',
                subtitle:
                    'Configure los colores y el modo de visualización del sistema',
                sections: {
                    mainColors: 'Colores Principales',
                    statusColors: 'Colores de Estado',
                    darkMode: 'Modo Oscuro',
                    presets: 'Temas Predefinidos'
                },
                colors: {
                    primary: 'Color Primario',
                    secondary: 'Color Secundario',
                    accent: 'Color de Acento',
                    positive: 'Éxito',
                    negative: 'Error',
                    warning: 'Advertencia',
                    info: 'Información',
                    dark: 'Fondo Oscuro',
                    darkPage: 'Página Oscura'
                },
                presets: {
                    default: 'Predeterminado',
                    ocean: 'Océano',
                    forest: 'Bosque',
                    sunset: 'Atardecer',
                    night: 'Nocturno'
                },
                actions: {
                    save: 'Guardar Configuraciones',
                    reset: 'Restaurar Predeterminado'
                },
                messages: {
                    presetApplied: 'Preset "{name}" aplicado',
                    themeSaved: '¡Tema guardado con éxito!',
                    themeReset: 'Tema restablecido al predeterminado'
                }
            }
        }
    },
    tables: {
        headers: {
            actions: 'Acciones'
        },
        tableOptions: {
            itemsPerPage: 'Elementos por página',
            itemsShowed: 'Mostrando {first} - {last} de {total} elementos',
            allItems: 'Todos',
            configureColumns: 'Configurar columnas',
            hideColumn: 'Ocultar columna',
            moreActions: 'Más acciones'
        }
    },
    errors: {
        loadModelsError: 'Error al cargar modelos',
        validationError: 'Por favor, corrija los errores en el formulario',
        requests: {
            generic: {
                400: 'Solicitud incorrecta. Por favor, verifique su entrada e intente nuevamente.',
                401: 'No autorizado. Por favor, inicie sesión.',
                403: 'Prohibido. No tiene permiso para realizar esta acción.',
                404: 'No encontrado. No se pudo encontrar el recurso solicitado.',
                500: 'Error interno del servidor. Por favor, inténtelo de nuevo más tarde.',
                503: 'Servicio no disponible. Por favor, inténtelo de nuevo más tarde.',
                noCode: 'Error desconocido. Por favor, inténtelo de nuevo más tarde.'
            },
            login: {
                400: 'Correo electrónico o contraseña inválidos.'
            },
            user: {
                401: 'Correo electrónico o contraseña inválidos.'
            }
        }
    },
    pwa: {
        updateAvailable:
            'Nueva actualización disponible. Haga clic en el botón de recarga para actualizar la página.',
        refresh: 'Recargar'
    },
    user: {
        fullName: 'Nombre completo',
        username: 'Nombre de usuario',
        notAvailable: 'No disponible',
        status: {
            label: 'Estado',
            online: 'En línea',
            offline: 'Desconectado',
            away: 'Ausente',
            busy: 'Ocupado',
            invisible: 'Invisible'
        }
    },
    notifications: {
        pleaseFixErrors: 'Por favor, corrija los errores antes de continuar',
        emailTestSuccess: '¡Prueba de conexión de correo electrónico exitosa!',
        featureNotImplemented: 'Funcionalidad aún no implementada'
    },
    dialogs: {
        confirmDelete: {
            title: 'Confirmar eliminación',
            message: '¿Está seguro de que desea eliminar "{item}"?'
        },
        confirmDeleteMultiple: {
            title: 'Confirmar eliminación múltiple',
            message:
                '¿Está seguro de que desea eliminar {count} elementos seleccionados?'
        },
        confirmStatusChange: {
            title: 'Confirmar cambio de estado',
            message: '¿Está seguro de que desea {action} "{item}"?'
        }
    },
    validate: {
        requiredName: 'Nombre es requerido.',
        invalidName: 'Nombre debe tener al menos 3 caracteres.',
        requiredSurname: 'Apellido es requerido.',
        invalidSurname: 'Apellido debe tener al menos 3 caracteres.',
        invalidEmail: 'Correo electrónico inválido.',
        requiredEmail: 'Correo electrónico es requerido.',
        invalidPassword: 'Contraseña debe tener al menos 5 caracteres.',
        requiredPassword: 'Contraseña es requerida.',
        requiredUsername: 'Nombre de usuario es requerido.',
        invalidUsername: 'Nombre de usuario debe tener al menos 3 caracteres.',
        requiredRole: 'Rol es requerido.',
        requiredCompanyName: 'Nombre de la empresa es requerido.',
        invalidCompanyName:
            'Nombre de la empresa debe tener al menos 3 caracteres.',
        docNaturalRequired: 'CI es requerido.',
        docNaturalInvalid: 'CI inválido.',
        docLegalRequired: 'RUC es requerido.',
        docLegalInvalid: 'RUC inválido.',
        requiredIndicatorStateEnrollmentRecipient:
            'Indicador de IE Destinatario es requerido.',
        requiredFantasyName: 'Nombre fantasia es requerido.',
        requiredPermissionProfileName:
            'Nombre del perfil de permisos es requerido.',
        invalidPermissionProfileName:
            'Nombre del perfil de permisos debe tener al menos 3 caracteres.',
        requiredPermissionGroupName:
            'Nombre del grupo de permisos es requerido.',
        invalidPermissionGroupName:
            'Nombre del grupo de permisos debe tener al menos 3 caracteres.',
        requiredPermissionGroupCode:
            'Código del grupo de permisos es requerido.',
        invalidPermissionGroupCode:
            'Código del grupo de permisos debe contener solo letras minúsculas, números y guiones bajos.',
        maxObservations: 'Observaciones no pueden exceder los 500 caracteres.',
        requiredCommission: 'Comisión es requerida.',
        invalidCommission: 'Comisión debe ser un número.',
        minCommission: 'Comisión no puede ser menor que 0.',
        maxCommission: 'Comisión no puede ser mayor que 100.',
        requiredDiscountPercentage: 'Porcentaje de descuento es requerido.',
        invalidDiscountPercentage:
            'Porcentaje de descuento debe ser un número.',
        minDiscountPercentage:
            'Porcentaje de descuento no puede ser menor que 0.',
        maxDiscountPercentage:
            'Porcentaje de descuento no puede ser mayor que 100.',
        requiredDiscountValue: 'Valor de descuento es requerido.',
        invalidDiscountValue: 'Valor de descuento debe ser un número.',
        minDiscountValue: 'Valor de descuento no puede ser menor que 0.',
        requiredPasswordExpirationDays:
            'Días para expirar contraseña es requerido.',
        invalidPasswordExpirationDays:
            'Días para expirar contraseña debe ser un número positivo.',
        requiredSeller: 'Vendedor es requerido.',
        requiredBuyer: 'Comprador es requerido.',
        requiredCashier: 'Cajero es requerido.',
        requiredCompany: 'Empresa es requerida.',
        requiredRepresentative: 'Representante es requerido.',
        requiredSmtpServer: 'Servidor de correo electrónico es requerido.',
        requiredSmtpPort: 'Puerto SMTP es requerido.',
        invalidSmtpPort: 'Puerto SMTP inválido.',
        requiredEmailPassword: 'Contraseña de correo electrónico es requerida.',
        fieldRequired: 'El campo {field} es requerido.',
        mustBeNumber: 'Este campo debe ser un número.',
        minLength: 'Debe tener al menos {min} caracteres.',
        maxDescription: 'Descripción no puede exceder los 250 caracteres.',
        invalidOrderPurchaseLimit:
            'Límite de compra por pedido debe ser un número.',
        minOrderPurchaseLimit:
            'Límite de compra por pedido no puede ser menor que 0.',
        requiredOrderPurchaseLimit: 'Límite de compra por pedido es requerido.',
        invalidMonthlyPurchaseLimit:
            'Límite de compra mensual debe ser un número.',
        minMonthlyPurchaseLimit:
            'Límite de compra mensual no puede ser menor que 0.',
        requiredMonthlyPurchaseLimit: 'Límite de compra mensual es requerido.',
        requiredCategory: 'Categoría es requerida.',
        requiredPaymentCondition: 'Condición de pago es requerida.',
        requiredPaymentModality: 'Modalidad de pago es requerida.',
        requiredTransaction: 'Transacción es requerida.',
        requiredPriceTable: 'Tabla de precios es requerida.',
        requiredFinancialAccount: 'Cuenta financiera es requerida.',
        requiredDeliveryType: 'Tipo de entrega es requerido.',
        requiredFreightIndicator: 'Indicador de flete es requerido.',
        requiredCarrier: 'Transportadora es requerida.',
        requiredCostCenter: 'Centro de costo es requerido.',
        requiredPaymentMethodGroup: 'Grupo de forma de pago es requerido.',
        requiredDaysForDueNotice:
            'Días para aviso de vencimiento es requerido.',
        requiredDaysForCollectionNotice:
            'Días para aviso de cobro es requerido.',
        requiredBillingDayLimit: 'Día límite para facturación es requerido.',
        requiredMaxDiscountPercentage:
            'Porcentaje de descuento máximo es requerido.',
        requiredMinimumBillingValue:
            'Valor de facturación mínimo es requerido.',
        requiredCreditLimit: 'Límite de crédito es requerido.',
        minZero: 'Valor no puede ser menor que 0.',
        minOne: 'Valor no puede ser menor que 1.',
        maxThirtyOne: 'Valor no puede ser mayor que 31.',
        maxOneHundred: 'Valor no puede ser mayor que 100.',
        requiredMotherName: 'Nombre de la madre es requerido.',
        requiredAdmissionDate: 'Fecha de admisión es requerida.',
        requiredPIS: 'PIS es requerido.',
        requiredVoterCard: 'Título de elector es requerido.',
        requiredWorkCard: 'Cartera de trabajo es requerida.',
        requiredDepartment: 'Departamento es requerido.',
        requiredSector: 'Sector es requerido.',
        requiredPosition: 'Cargo es requerido.',
        requiredHourCost: 'Valor costo hora es requerido.',
        requiredAddressType: 'Tipo de dirección es requerido.',
        requiredZipCode: 'Código postal es requerido.',
        requiredCountry: 'País es requerido.',
        requiredState: 'Estado es requerido.',
        requiredCity: 'Ciudad es requerida.',
        requiredStreet: 'Calle es requerida.',
        requiredNumber: 'Número es requerido.',
        requiredNeighborhood: 'Barrio es requerido.',
        requiredDocumentType: 'Tipo de documento es requerido.',
        requiredDocumentNumber: 'Número de documento es requerido.',
        requiredOccupation: 'Ocupación es requerida.',
        requiredBirthDate: 'Fecha de nacimiento es requerida.',
        requiredPhoneType: 'Tipo de teléfono es requerido.',
        requiredAreaCode: 'Código de área es requerido.',
        requiredPhoneNumber: 'Número de teléfono es requerido.',
        requiredEmployeeCount: 'Número de empleados es requerido.',
        requiredAnnualRevenue: 'Facturación anual es requerida.',
        requiredMainContact: 'Contacto principal es requerido.',
        requiredDescription: 'Descripción es requerida.',
        requiredAbbreviation: 'Abreviación es requerida.',
        requiredTitle: 'Título es requerido.',
        requiredModule: 'Módulo es requerido.',
        requiredRoute: 'Ruta es requerida.',
        requiredContent: 'Contenido es requerido.',
        requiredType: 'Tipo es requerido.',
        requiredModel: 'Modelo es requerido',
        requiredDifficultyLevel: 'Nivel de dificultad es requerido (1-10)',
        requiredBrand: 'Marca es requerida',
        minDifficultyLevel: 'Nivel de dificultad debe ser mínimo 1',
        maxDifficultyLevel: 'Nivel de dificultad debe ser máximo 10',
        maxFileSize: 'Archivo muy grande. Máximo 2MB',
        invalidFileType: 'Tipo de archivo inválido',
        requiredCode: 'Código es requerido',
        ncmCodeFormat: 'Código NCM debe tener el formato 0000.00.00',
        requiredStatus: 'Estado es requerido',
        maxLength: 'Debe tener como máximo {max} caracteres'
    },
    cnpjSearch: {
        tooltip: 'Consultar CNPJ',
        title: 'Consulta de CNPJ',
        inputLabel: 'Ingrese el CNPJ',
        hint: 'Ingrese el CNPJ y presione Enter o haga clic en la lupa para buscar',
        clear: 'Limpiar campo',
        results: 'Resultados de la Búsqueda',
        basicInfo: 'Información Básica',
        address: 'Dirección',
        mainActivity: 'Actividad Principal',
        searching: 'Consultando CNPJ...',
        fields: {
            cnpj: 'CNPJ',
            companyName: 'Razón Social',
            tradeName: 'Nombre Comercial',
            status: 'Estado',
            street: 'Calle',
            zipCode: 'Código Postal',
            neighborhood: 'Barrio',
            city: 'Ciudad'
        },
        errors: {
            required: 'CNPJ es requerido',
            invalidFormat: 'CNPJ debe tener 14 dígitos',
            invalidCNPJ: 'CNPJ inválido',
            notFound: 'CNPJ no encontrado',
            searchError: 'Error al consultar CNPJ. Inténtelo de nuevo.'
        }
    },
    cepSearch: {
        searching: 'Consultando Código Postal...',
        searchingAddress: 'Buscando dirección...',
        errors: {
            required: 'Código Postal es requerido',
            invalidFormat: 'Código Postal debe tener 8 dígitos',
            invalidCEP: 'Código Postal inválido',
            notFound: 'Código Postal no encontrado',
            searchError: 'Error al consultar Código Postal. Inténtelo de nuevo.'
        },
        success: {
            addressFound: 'Dirección encontrada y completada automáticamente'
        },
        onlyBrazil: 'Búsqueda de código postal disponible solo para Brasil'
    },
    modals: {
        marcaModelo: {
            title: 'Marca x Modelo',
            selectedBrand: 'Marca Seleccionada',
            manageModels: 'Gestionar modelos vinculados a esta marca',
            form: {
                title: 'Vincular Modelo a Marca',
                editTitle: 'Editar Vinculación Marca-Modelo'
            },
            table: {
                title: 'Modelos Vinculados'
            },
            messages: {
                createSuccess: '¡Modelo vinculado a marca con éxito!',
                updateSuccess: '¡Vinculación actualizada con éxito!',
                deleteSuccess: '¡Vinculación eliminada con éxito!',
                deleteMultipleSuccess: '¡Vinculaciones eliminadas con éxito!',
                linkSuccess: '¡Vinculación realizada con éxito!'
            },
            errors: {
                saveError: 'Error al vincular modelo a marca',
                updateError: 'Error al actualizar vinculación',
                deleteError: 'Error al eliminar vinculación',
                deleteMultipleError: 'Error al eliminar vinculaciones',
                loadError: 'Error al cargar vinculaciones',
                loadModelosError: 'Error al cargar modelos',
                notFound: 'Vinculación no encontrada'
            }
        },
        vincularModelo: {
            title: 'Vincular Modelo a Marca'
        }
    },
    imageViewer: {
        title: 'Visor de Imagen',
        zoomIn: 'Ampliar',
        zoomOut: 'Reducir',
        fitScreen: 'Ajustar a Pantalla',
        fullscreen: 'Pantalla Completa',
        loadError: 'Error al cargar imagen'
    },
    contactForm: {
        titles: {
            main: 'Formulario de Contacto',
            generalInfo: 'Información General',
            phones: 'Teléfonos',
            emails: 'Correos Electrónicos'
        },
        labels: {
            name: 'Nombre Completo',
            occupation: 'Ocupación',
            department: 'Departamento',
            birthDate: 'Fecha de Nacimiento',
            phoneType: 'Tipo de Teléfono',
            countryCode: 'País',
            areaCode: 'Código de Área',
            phoneNumber: 'Número',
            extension: 'Extensión',
            email: 'Correo Electrónico',
            addPhone: 'Agregar Teléfono',
            addEmail: 'Agregar Correo',
            removePhone: 'Eliminar Teléfono',
            removeEmail: 'Eliminar Correo',
            whatsappMessages: 'Recepción automática de mensajes vía WhatsApp'
        },
        messages: {
            noPhones:
                'No hay teléfonos registrados. Haga clic en "Agregar Teléfono" para comenzar.',
            noEmails:
                'No hay correos registrados. Haga clic en "Agregar Correo" para comenzar.',
            confirmRemovePhone: '¿Desea eliminar este teléfono?',
            confirmRemoveEmail: '¿Desea eliminar este correo?',
            phoneAdded: 'Teléfono agregado exitosamente',
            emailAdded: 'Correo agregado exitosamente',
            phoneRemoved: 'Teléfono eliminado exitosamente',
            emailRemoved: 'Correo eliminado exitosamente'
        },
        validation: {
            requiredName: 'Nombre es requerido',
            requiredOccupation: 'Ocupación es requerida',
            requiredDepartment: 'Departamento es requerido',
            requiredBirthDate: 'Fecha de nacimiento es requerida',
            requiredPhoneType: 'Tipo de teléfono es requerido',
            requiredCountryCode: 'País es requerido',
            requiredAreaCode: 'Código de área es requerido',
            requiredPhoneNumber: 'Número de teléfono es requerido',
            invalidExtension: 'La extensión debe tener al menos 2 dígitos',
            requiredEmail: 'Correo electrónico es requerido',
            invalidEmail: 'Correo electrónico inválido',
            minPhones: 'Al menos un teléfono es requerido',
            minEmails: 'Al menos un correo electrónico es requerido'
        }
    },
    addressCollection: {
        titles: {
            main: 'Colección de Direcciones',
            addAddress: 'Agregar Dirección',
            editAddress: 'Editar Dirección'
        },
        labels: {
            addAddress: 'Agregar Dirección',
            editAddress: 'Editar Dirección',
            removeAddress: 'Eliminar Dirección',
            fullAddress: 'Dirección Completa',
            cityState: 'Ciudad/Estado'
        },
        messages: {
            noAddresses: 'No hay direcciones registradas',
            noAddressHint: 'Haga clic en "Agregar Dirección" para comenzar',
            confirmRemoveAddress: '¿Desea eliminar esta dirección?',
            addressAdded: 'Dirección agregada exitosamente',
            addressUpdated: 'Dirección actualizada exitosamente',
            addressRemoved: 'Dirección eliminada exitosamente',
            saveError: 'Error al guardar dirección. Inténtelo de nuevo.'
        },
        validation: {
            minAddresses: 'Al menos una dirección es requerida'
        }
    },
    contactCollection: {
        titles: {
            main: 'Colección de Contactos',
            addContact: 'Agregar Contacto',
            editContact: 'Editar Contacto'
        },
        labels: {
            addContact: 'Agregar Contacto',
            editContact: 'Editar Contacto',
            removeContact: 'Eliminar Contacto',
            contactInfo: 'Información del Contacto',
            location: 'Ubicación',
            contactMethods: 'Métodos de Contacto'
        },
        messages: {
            noContacts: 'No hay contactos registrados',
            noContactHint: 'Haga clic en "Agregar Contacto" para comenzar',
            confirmRemoveContact: '¿Desea eliminar este contacto?',
            contactAdded: 'Contacto agregado exitosamente',
            contactUpdated: 'Contacto actualizado exitosamente',
            contactRemoved: 'Contacto eliminado exitosamente',
            saveError: 'Error al guardar contacto. Inténtelo de nuevo.'
        },
        validation: {
            minContacts: 'Al menos un contacto es requerido'
        }
    },
    richEditor: {
        bold: 'Negrita',
        italic: 'Cursiva',
        underline: 'Subrayado',
        strikethrough: 'Tachado',
        alignLeft: 'Alinear a la Izquierda',
        alignCenter: 'Centrar',
        alignRight: 'Alinear a la Derecha',
        bulletList: 'Lista con Viñetas',
        numberedList: 'Lista Numerada',
        insertLink: 'Insertar Enlace',
        clearFormat: 'Limpiar Formato',
        fontSize: 'Tamaño',
        fontFamily: 'Fuente',
        textColor: 'Color del Texto',
        backgroundColor: 'Color de Fondo',
        subscript: 'Subíndice',
        superscript: 'Superíndice',
        heading: 'Encabezado',
        heading1: 'Encabezado 1',
        heading2: 'Encabezado 2',
        heading3: 'Encabezado 3',
        heading4: 'Encabezado 4',
        heading5: 'Encabezado 5',
        heading6: 'Encabezado 6',
        blockquote: 'Cita',
        code: 'Código en Línea',
        codeBlock: 'Bloque de Código',
        insertImage: 'Insertar Imagen',
        table: 'Tabla',
        table2x2: 'Tabla 2x2',
        table3x3: 'Tabla 3x3',
        table4x4: 'Tabla 4x4',
        customTable: 'Tabla Personalizada',
        horizontalRule: 'Línea Horizontal',
        lineBreak: 'Salto de Línea',
        indent: 'Aumentar Sangría',
        outdent: 'Disminuir Sangría',
        undo: 'Deshacer',
        redo: 'Rehacer',
        selectAll: 'Seleccionar Todo',
        visualMode: 'Modo Visual',
        htmlMode: 'Modo HTML',
        enterUrl: 'Ingrese la URL del enlace:',
        enterImageUrl: 'Ingrese la URL de la imagen:',
        enterImageAlt: 'Ingrese el texto alternativo de la imagen:',
        enterRows: 'Número de filas:',
        enterCols: 'Número de columnas:',
        characters: 'caracteres',
        words: 'palabras',
        placeholder: 'Escriba su texto aquí...'
    },

    advancedTable: {
        manageColumns: 'Gestionar Columnas',
        manageColumnsTooltip: 'Reordenar y ocultar columnas',
        reset: 'Restablecer',
        resetTooltip: 'Restaurar configuración predeterminada',
        dragToReorder: 'Arrastre para reordenar columnas',
        hideColumn: 'Ocultar columna',
        showColumn: 'Mostrar columna',
        notifications: {
            loadError: 'Error al cargar configuración de columnas',
            saveError: 'Error al guardar configuración de columnas',
            configApplied: 'Configuración de columnas aplicada con éxito',
            configReset: 'Configuración de columnas restaurada por defecto'
        }
    },

    dataTable: {
        manageColumns: 'Gestionar Columnas',
        manageColumnsTooltip: 'Reordenar y ocultar columnas',
        reset: 'Restablecer',
        resetTooltip: 'Restaurar configuración predeterminada',
        dragToReorder: 'Arrastre para reordenar columnas',
        hideColumn: 'Ocultar columna',
        showColumn: 'Mostrar columna',
        pagination: {
            rowsPerPageLabel: 'Filas por página:',
            paginationLabel: '{first}-{last} de {total}',
            selectedRowsLabel: {
                none: 'Ninguna fila seleccionada',
                one: '1 fila seleccionada',
                many: '{count} filas seleccionadas'
            }
        },
        notifications: {
            loadError: 'Error al cargar configuración de columnas',
            saveError: 'Error al guardar configuración de columnas',
            configApplied: 'Configuración de columnas aplicada con éxito',
            configReset: 'Configuración de columnas restaurada por defecto'
        }
    },
    reports: {
        modal: {
            title: 'Reportes',
            reportType: 'Tipo de Reporte',
            format: 'Formato',
            info: 'Información',
            table: 'Tabla',
            columns: 'Columnas',
            columnsVisible: 'columnas visibles',
            filters: 'Filtros',
            filtersApplied: 'filtros aplicados',
            groupColumn: 'Columna de Agrupación',
            selectGroupColumn: 'Seleccione columna para agrupar',
            summaryColumn: 'Columna de Resumen',
            selectSummaryColumn: 'Seleccione columna para resumir',
            generate: 'Generar Reporte'
        },
        types: {
            simple: 'Simples',
            grouped: 'Simple Agrupado por Columna',
            summary: 'Resumen Simple'
        },
        summary: {
            item: 'Ítem',
            quantity: 'Cantidad',
            percentage: '% Part.',
            total: 'TOTAL'
        },
        formats: {
            excel: 'Excel (.xls)',
            pdf: 'PDF'
        },
        errors: {
            fetchData: 'Error al obtener datos para el informe',
            generateExcel: 'Error al generar informe Excel',
            generatePdf: 'Error al generar informe PDF',
            unsupportedType: 'Tipo de informe no soportado',
            unsupportedFormat: 'Formato no soportado'
        },
        success: {
            generated: 'Informe generado con éxito'
        }
    }
}
