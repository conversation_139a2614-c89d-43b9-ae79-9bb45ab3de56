export const enUSDateTimeFormat = {
    short: { year: 'numeric', month: '2-digit', day: '2-digit' },
    long: { year: 'numeric', month: 'long', day: 'numeric' }
}

export const enUSNumberFormat = {
    currency: {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    },
    percent: {
        style: 'percent',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }
}

export const enUS = {
    themes: {
        light: 'Light',
        dark: 'Dark',
        ChangeToLight: 'Change to light theme',
        ChangeToDark: 'Change to dark theme'
    },
    locale: {
        changeLanguage: 'Change language',
        selectLanguage: 'Select language'
    },
    titles: {
        app: 'Corpsystem',
        login: 'Login',
        slogan: 'CorpERP - The best ERP system for your company.',
        copyright: 'All rights reserved.',
        pageNotFound: 'Page not found'
    },
    messages: {
        pageNotFound: 'The page you are looking for was not found.',
        checkRequiredFields: 'Check required fields',
        updatedSuccessfully: 'Updated successfully!',
        createdSuccessfully: 'Created successfully!',
        deletedSuccessfully: 'Deleted successfully!',
        activatedSuccessfully: 'Activated successfully!',
        deactivatedSuccessfully: 'Deactivated successfully!',
        errorSaving: 'Error saving'
    },
    forms: {
        labels: {
            title: 'Title',
            module: 'Module',
            route: 'Route',
            publishDate: 'Publish Date',
            name: 'Name',
            nameRequired: 'Name *',
            surname: 'Surname',
            email: 'E-mail',
            password: 'Password',
            username: 'Username',
            searchRoute: 'Search route',
            role: 'Role',
            companyName: 'Company name',
            companyNameRequired: 'Company name *',
            legalPerson: 'Legal person',
            docLegalPerson: 'EIN',
            docNaturalPerson: 'SSN',
            naturalPerson: 'Natural person',
            active: 'Active',
            indicatorStateEnrollmentRecipient: 'Indicator of IE Recipient',
            site: 'Site',
            region: 'Region',
            company: 'Company',
            lineOfActivity: 'Line of activity',
            typeOfRelationship: 'Type of relationship',
            dateOfBith: 'Date of birth',
            dateOfFounding: 'Date of founding',
            nickname: 'Nickname',
            maritalStatus: 'Marital status',
            fantasyName: 'Fantasy name',
            stateRegistrationStatus: 'State registration status',
            stateRegistration: 'State registration',
            municipalRegistration: 'Municipal registration',
            admin: 'Admin',
            permissionProfileName: 'Permission profile name',
            permissionGroupName: 'Permission group name',
            permissionGroupCode: 'Group code',
            description: 'Description',
            abbreviation: 'Abbreviation',
            observations: 'Observations',
            permissionGroups: 'Permission groups',
            passwordExpirationDays: 'Password expiration days',
            forceOperatorClosure: 'Force operator closure',
            viewOnlySeller: 'View only seller data',
            viewOnlyBuyer: 'View only buyer data',
            viewOnlyCashier: 'View only cashier data',
            viewOnlyCompany: 'View only company data',
            viewOnlyRepresentative: 'View only representative data',
            selectSeller: 'Select seller',
            selectBuyer: 'Select buyer',
            selectCashier: 'Select cashier',
            selectCompany: 'Select company',
            selectRepresentative: 'Select representative',
            remove: 'Remove',
            smtpServer: 'Email Server',
            smtpPort: 'SMTP Port',
            emailPassword: 'Email Password',
            useTLS: 'Use TLS',
            useSSL: 'Use SSL',
            commission: 'Commission',
            discountPercentage: 'Allowed Discount Percentage',
            discountValue: 'Allowed Discount Value',
            orderPurchaseLimit: 'Order Purchase Limit',
            monthlyPurchaseLimit: 'Monthly Purchase Limit',
            category: 'Category',
            paymentCondition: 'Payment Condition',
            paymentModality: 'Payment Modality',
            transaction: 'Transaction',
            priceTable: 'Price Table',
            seller: 'Seller',
            financialAccount: 'Financial Account',
            representative: 'Representative',
            deliveryType: 'Delivery Type',
            freightIndicator: 'Freight Indicator',
            carrier: 'Carrier',
            costCenter: 'Cost Center',
            paymentMethodGroup: 'Payment Method Group',
            salesBlock: 'Sales Block',
            daysForDueNotice: 'Days for Due Notice',
            daysForCollectionNotice: 'Days for Collection Notice',
            billingDayLimit: 'Billing Day Limit',
            maxDiscountPercentage: 'Maximum Discount Percentage',
            minimumBillingValue: 'Minimum Billing Value',
            creditLimit: 'Credit Limit',
            morning: 'Morning',
            afternoon: 'Afternoon',
            evening: 'Evening',
            start: 'Start',
            end: 'End',
            noResults: 'No results found',
            uniqueIdentifier: 'Unique record identifier',
            itemDescription: 'Detailed item description',
            categoryType: 'Category or classification type',
            textContent: 'Main text content',
            activeStatus: 'Item activation status',
            currentStatus: 'Current record status',
            availableActions: 'Available actions for the item',
            fullName: 'Full name of the person',
            emailAddress: 'Email address',
            phoneNumber: 'Phone number',
            dateField: 'Date field',
            monetaryValue: 'Monetary value',
            numericQuantity: 'Numeric quantity',
            additionalNotes: 'Additional notes',
            creationDate: 'Creation date',
            lastUpdate: 'Last update',
            tableColumn: 'Table column',
            department: 'Department',
            sector: 'Sector',
            position: 'Position',
            admissionDate: 'Admission Date',
            pis: 'PIS',
            voterCard: 'Voter Card',
            workCard: 'Work and Social Card',
            motherName: "Mother's Name",
            male: 'Male',
            female: 'Female',
            gender: 'Gender',
            qualityResponsible: 'Quality Responsible',
            currencySymbol: '$',
            currencyAcronym: 'USD',
            buyer: 'Buyer',
            hourCost: 'Hour Cost Value',
            addressType: 'Address Type',
            country: 'Country',
            zipCode: 'ZIP Code',
            state: 'State',
            city: 'City',
            street: 'Street',
            number: 'Number',
            neighborhood: 'Neighborhood',
            complement: 'Complement',
            referencePoint: 'Reference Point',
            documentType: 'Document Type',
            documentNumber: 'Document Number',
            occupation: 'Occupation',
            birthDate: 'Birth Date',
            phoneType: 'Phone Type',
            areaCode: 'Area Code',
            extension: 'Extension',
            phone: 'Phone',
            businessPartner: 'Referring Business Partner',
            mainContact: 'Main Account/Prospect Contact',
            employeeCount: 'Number of Employees',
            annualRevenue: 'Annual Revenue',
            type: 'Type',
            content: 'Content',
            column: 'Column',
            id: 'ID',
            actions: 'Actions',
            search: 'Search',
            status: 'Status',
            inactive: 'Inactive',
            brand: 'Brand',
            model: 'Model',
            difficultyLevel: 'Difficulty Level',
            attachment: 'Attachment',
            attachmentPreview: 'Attachment preview',
            imageLoadError: 'Error loading image',
            selectFile: 'Select file',
            rememberMe: 'Remember me',
            forgotPassword: 'Forgot password?',
            or: 'or',
            loginWith: 'Login with',
            loading: 'Loading...',
            typeToSearch: 'Type to search',
            code: 'Code',
            codeDescription: 'Code/Description',
            firstName: 'First Name',
            lastName: 'Last Name',
            nameEmail: 'Name/Email',
            countryAbbreviation: 'Abbreviation',
            countryCode: 'Code',
            stateAbbreviation: 'State Abbreviation',
            ibgeCode: 'IBGE Code',
            iss: 'ISS (%)'
        },
        masks: {
            docLegalPerson: '##-#######',
            docNaturalPerson: '###-##-####'
        },
        addressTypes: {
            residential: 'Residential',
            commercial: 'Commercial',
            delivery: 'Delivery',
            billing: 'Billing',
            other: 'Other'
        },
        documentTypes: {
            cpf: 'CPF',
            cnpj: 'CNPJ',
            rg: 'ID Card',
            passport: 'Passport',
            other: 'Other'
        },
        occupations: {
            manager: 'Manager',
            director: 'Director',
            analyst: 'Analyst',
            developer: 'Developer',
            assistant: 'Assistant',
            coordinator: 'Coordinator',
            consultant: 'Consultant',
            other: 'Other'
        },
        departments: {
            administrative: 'Administrative',
            financial: 'Financial',
            commercial: 'Commercial',
            marketing: 'Marketing',
            humanResources: 'Human Resources',
            technology: 'Technology',
            operations: 'Operations',
            logistics: 'Logistics',
            other: 'Other'
        },
        phoneTypes: {
            mobile: 'Mobile',
            home: 'Home',
            work: 'Work',
            other: 'Other'
        },
        messages: {
            onlyStatusField: 'This form contains only the status field.',
            noPhones:
                'No phones registered. Click the "Add Phone" button to start.',
            noEmails:
                'No emails registered. Click the "Add Email" button to start.'
        },
        titles: {
            description: 'Description',
            userRegistration: 'User registration',
            userInfo: 'User information',
            accessAndPermissions: 'Access and permissions',
            restrictions: 'Restrictions',
            emailConfigs: 'Email configurations',
            details: 'Details',
            errorsFound: 'Errors found',
            permissionsManagement: 'Permissions management',
            permissionGroupManagement: 'Permission group management',
            modulePermissions: 'Module permissions',
            basicInfo: 'Basic information',
            userPreferences: 'User preferences',
            userPermissions: 'User permissions',
            permissionGroups: 'Permission groups',
            viewRestrictions: 'View restrictions',
            customPermissions: 'Custom permissions',
            userCredentials: 'User credentials',
            emailConfig: 'Email Configuration',
            basicForm: 'Basic Form',
            sellerForm: 'Seller Form',
            buyerForm: 'Buyer Form',
            customerForm: 'Customer Form',
            supplierForm: 'Supplier Form',
            employeeForm: 'Employee Form',
            driverForm: 'Driver Form',
            technicianForm: 'Technician Form',
            addressForm: 'Address Form',
            documentForm: 'Document Form',
            contactForm: 'Contact Form',
            crmProspectForm: 'CRM/Prospect Form',
            professionalInfo: 'Professional Information',
            limitsAndSettings: 'Limits and Settings',
            businessHours: 'Business Hours',
            additionalInfo: 'Additional Information',
            phones: 'Phones',
            emails: 'Emails',
            brandModel: 'Link Model to Brand',
            modelInfo: 'Model Information',
            ncmForm: 'NCM Form',
            categoriaCNHForm: 'CNH Category Form',
            cityForm: 'City Form',
            standardizedTextForm: 'Standardized Text Form',
            newsForm: 'News Form',
            content: 'Content'
        },
        sections: {
            basicInfo: 'Basic Information',
            location: 'Location',
            addressDetails: 'Address Details'
        },
        placeholders: {
            standardizedTextContent: 'Enter the standardized text content...',
            newsDescription: 'Enter the news content...',
            selectModuleFirst: 'Select a module first',
            selectRoute: 'Select a route',
            ncmCode: 'Enter NCM code (format: 0000.00.00)',
            ncmDescription: 'Enter NCM description'
        },
        hints: {
            difficultyLevel: 'Level from 1 to 10',
            attachment:
                'Maximum 1 file. Formats: jpg, jpeg, png, pdf, pfx, txt, crt, ret, doc, docx, xls, xlsx, msg. Maximum size: 2MB'
        },
        errors: {
            loadModelsError: 'Error loading models',
            loadCountriesError: 'Error loading countries',
            loadStatesError: 'Error loading states',
            maxFileSize: 'File too large. Maximum size: 2MB',
            invalidFileType: 'File type not allowed'
        }
    },
    buttons: {
        login: 'Login',
        goHome: 'Go to home page',
        goBack: 'Go back',
        confirm: 'Confirm',
        cancel: 'Cancel',
        apply: 'Apply',
        reset: 'Reset',
        addPhone: 'Add Phone',
        addEmail: 'Add Email',
        testConnection: 'Test Connection',
        edit: 'Edit',
        viewProfile: 'View Profile',
        add: 'Add',
        delete: 'Delete',
        deleteSelected: 'Delete selected ({count})',
        deleteSelectedDescription: 'Delete the selected items',
        refresh: 'Refresh',
        clear: 'Clear',
        filter: 'Filter',
        save: 'Save',
        export: 'Export',
        import: 'Import',
        actions: 'Actions',
        linkModel: 'Link model',
        close: 'Close',
        loadMore: 'Load more',
        view: 'View',
        remove: 'Remove',
        editSelected: 'Edit selected',
        editSelectedDescription: 'Edit the selected item',
        activate: 'Activate',
        deactivate: 'Deactivate',
        activateDescription: 'Activate the selected item',
        deactivateDescription: 'Deactivate the selected item',
        viewAttachment: 'View attachment',
        viewAttachmentDescription: 'View attachment of the selected item',
        linkModelToSelected: 'Link model',
        linkModelDescription: 'Link model to the selected item',
        logout: 'Logout',
        back: 'Back',
        backToPage: 'Back to page',
        retry: 'Try again'
    },
    help: {
        tooltip: 'Help',
        title: 'System Help',
        subtitle: 'Documentation and user guides',
        loading: 'Loading help...',
        error: 'Error loading help',
        loadError: 'Could not load help content',
        noContent: 'No help content available',
        noContentDescription: 'Help content for this page is not yet available',
        aboutPage: 'About Page',
        aboutPageDescription: 'Page-specific help',
        noHelpAvailable: 'No help available for this page',
        news: 'News',
        newsDescription: 'Latest system updates'
    },
    pages: {
        titles: {
            home: 'Home',
            login: 'Login',
            pageNotFound: 'Page not found',
            dev: 'Dev',
            profile: 'Profile',
            newsView: 'News',
            standardizedTexts: 'Standardized texts',
            generalRegistration: 'General registration',
            productQuery: 'Inventory',
            productBrand: 'Brand',
            productModel: 'Model',
            ncm: 'NCM',
            accessConfig: 'Access configuration',
            accessUsers: 'Users',
            country: 'Country',
            categoryCNH: 'CNH category',
            state: 'State',
            city: 'City',
            region: 'Region',
            addressType: 'Address type',
            documentType: 'Document type',
            paymentGroup: 'Payment group',
            activitySector: 'Activity sector',
            historicLaunch: 'Historic launch',
            family: 'Family',
            group: 'Group',
            subgroup: 'Subgroup',
            sale: 'Sale',
            customerCategory: 'Customer category',
            discountGroup: 'Discount group',
            project: 'Project',
            application: 'Application',
            installationType: 'Installation type',
            profileType: 'Profile type',
            materialType: 'Material type',
            projectGroup: 'Project group',
            materialGroup: 'Material group',
            os: 'OS',
            title: 'Title',
            equipmentPart: 'Equipment part',
            defect: 'Defect',
            quality: 'Quality',
            cause: 'Cause',
            equipmentFamily: 'Equipment family',
            finance: 'Finance',
            billingPortfolio: 'Billing portfolio',
            production: 'Production',
            checklist: 'Checklist',
            operation: 'Operation',
            nonConformity: 'Non-conformity',
            config: 'Configuration',
            userProfile: 'User profile',
            module: 'Module',
            rh: 'RH',
            position: 'Cargo',
            education: 'Education',
            specialty: 'Specialty',
            skill: 'Skill',
            department: 'Department',
            origin: 'Origin',
            crm: 'CRM',
            settings: 'Settings',
            oportunity: 'Opportunity',
            news: 'News',
            help: 'Help',
            common: {
                register: 'Register'
            }
        },
        standardizedTexts: {
            title: 'Standardized texts',
            subtitle: 'Manage system standardized texts',
            list: 'Standardized texts list',
            createTitle: 'New standardized text',
            editTitle: 'Edit standardized text',
            messages: {
                createSuccess: 'Standardized text created successfully!',
                updateSuccess: 'Standardized text updated successfully!',
                deleteSuccess: 'Standardized text deleted successfully!',
                deleteMultipleSuccess:
                    'Standardized texts deleted successfully!'
            },
            errors: {
                loadError: 'Error loading standardized text data',
                saveError: 'Error saving standardized text',
                deleteError: 'Error deleting standardized text',
                deleteMultipleError: 'Error deleting standardized texts',
                notFound: 'Standardized text not found'
            }
        },
        news: {
            title: 'News',
            subtitle: 'Manage system news',
            list: 'News list',
            createTitle: 'New news',
            editTitle: 'Edit news',
            messages: {
                createSuccess: 'News created successfully!',
                updateSuccess: 'News updated successfully!',
                deleteSuccess: 'News deleted successfully!',
                deleteMultipleSuccess: 'News deleted successfully!'
            },
            errors: {
                loadError: 'Error loading news data',
                saveError: 'Error saving news',
                deleteError: 'Error deleting news',
                deleteMultipleError: 'Error deleting news',
                notFound: 'News not found'
            }
        },
        newsView: {
            title: 'System News',
            subtitle: 'Stay updated with the latest updates and improvements',
            noNews: 'No news found',
            noNewsDescription: 'Try adjusting the filters or check again later',
            filters: {
                status: 'Status',
                module: 'Module',
                search: 'Search news...',
                period: 'Period',
                sortBy: 'Sort by',
                startDate: 'Start date',
                endDate: 'End date',
                searchContent: 'Search in content...',
                allModules: 'All modules'
            },
            periods: {
                all: 'All periods',
                week: 'Last week',
                month: 'Last month',
                quarter: 'Last 3 months',
                year: 'Last year'
            },
            sorting: {
                dateDesc: 'Most recent first',
                dateAsc: 'Oldest first',
                titleAsc: 'Title A-Z',
                titleDesc: 'Title Z-A',
                moduleAsc: 'Module A-Z',
                statusNew: 'Unread first'
            },
            status: {
                all: 'All',
                new: 'New',
                viewed: 'Viewed',
                newLabel: 'New',
                viewedLabel: 'Viewed'
            },
            actions: {
                markAsViewed: 'Mark as viewed',
                markAllAsViewed: 'Mark All',
                clearFilters: 'Clear',
                viewDetails: 'View details',
                close: 'Close'
            },
            messages: {
                markedAsViewed: 'News marked as viewed!',
                allMarkedAsViewed: 'All news marked as viewed!',
                markViewedError: 'Error marking news as viewed',
                markAllViewedError: 'Error marking all news as viewed'
            },
            errors: {
                loadError: 'Error loading news'
            }
        },
        help: {
            title: 'Help Management',
            createTitle: 'New Help',
            editTitle: 'Edit Help',
            subtitle: 'Manage system help content',
            messages: {
                createdSuccessfully: 'Help created successfully!',
                updatedSuccessfully: 'Help updated successfully!',
                deletedSuccessfully: 'Help deleted successfully!',
                activatedSuccessfully: 'Help activated successfully!',
                deactivatedSuccessfully: 'Help deactivated successfully!',
                errorSaving: 'Error saving help',
                errorDeleting: 'Error deleting help',
                errorUpdating: 'Error updating help',
                errorLoading: 'Error loading help'
            }
        },
        helpView: {
            title: 'System Help',
            subtitle: 'Documentation and guidance for page usage',
            noHelp: 'No help available',
            noHelpDescription: 'No help content available for this page',
            lastUpdate: 'Last update',
            errors: {
                loadError: 'Error loading help content'
            }
        },
        home: {
            welcome: {
                title: 'Welcome to CorpERP',
                subtitle:
                    'Have a great workday! Here are the latest company news.',
                dashboardButton: 'Dashboard',
                helpButton: 'Help'
            },
            noticeBoard: {
                title: 'Notice Board',
                addNotice: 'Add Notice',
                addNoticeTooltip: 'Add Notice',
                emptyState:
                    'No notices at the moment. How about adding the first one?',
                priority: {
                    high: 'High',
                    medium: 'Medium',
                    low: 'Low'
                }
            },
            statistics: {
                title: 'Statistics',
                users: 'Users',
                notices: 'Notices',
                pending: 'Pending',
                completed: 'Completed'
            },
            addNoticeModal: {
                title: 'New Notice',
                titleField: 'Notice Title',
                priorityField: 'Priority',
                contentField: 'Content',
                authorField: 'Author',
                cancelButton: 'Cancel',
                publishButton: 'Publish',
                successMessage: 'Notice published successfully!',
                validation: {
                    titleRequired: 'Title is required',
                    contentRequired: 'Content is required',
                    authorRequired: 'Author is required'
                }
            },
            editNoticeModal: {
                title: 'Edit Notice',
                titleField: 'Notice Title',
                priorityField: 'Priority',
                contentField: 'Content',
                authorField: 'Author',
                cancelButton: 'Cancel',
                saveButton: 'Save',
                successMessage: 'Notice updated successfully!',
                validation: {
                    titleRequired: 'Title is required',
                    contentRequired: 'Content is required',
                    authorRequired: 'Author is required'
                }
            },
            deleteNoticeDialog: {
                title: 'Confirm Deletion',
                message:
                    'Are you sure you want to delete the notice "{title}"?',
                cancelButton: 'Cancel',
                confirmButton: 'Delete',
                successMessage: 'Notice deleted successfully!'
            }
        },
        productBrands: {
            title: 'Brands',
            subtitle: 'Manage system brands',
            list: 'Brands list',
            createTitle: 'New brand',
            editTitle: 'Edit brand',
            messages: {
                createSuccess: 'Brand created successfully!',
                updateSuccess: 'Brand updated successfully!',
                deleteSuccess: 'Brand deleted successfully!',
                deleteMultipleSuccess: 'Brands deleted successfully!',
                deactivateSuccess: 'Brand deactivated successfully!',
                activateSuccess: 'Brand activated successfully!'
            },
            errors: {
                loadError: 'Error loading brand data',
                saveError: 'Error saving brand',
                deleteError: 'Error deleting brand',
                deleteMultipleError: 'Error deleting brands',
                notFound: 'Brand not found',
                duplicateDescription:
                    'Brand with the same description already exists'
            }
        },
        productModels: {
            title: 'Models',
            subtitle: 'Manage system models',
            list: 'Models list',
            createTitle: 'New model',
            editTitle: 'Edit model',
            messages: {
                createSuccess: 'Model created successfully!',
                updateSuccess: 'Model updated successfully!',
                deleteSuccess: 'Model deleted successfully!',
                deleteMultipleSuccess: 'Models deleted successfully!',
                activateSuccess: 'Model activated successfully!',
                deactivateSuccess: 'Model deactivated successfully!'
            },
            errors: {
                loadError: 'Error loading model data',
                saveError: 'Error saving model',
                deleteError: 'Error deleting model',
                deleteMultipleError: 'Error deleting models',
                notFound: 'Model not found',
                duplicateDescription:
                    'The description field is unique and already exists in the database.'
            }
        },
        ncm: {
            title: 'NCM',
            subtitle: 'Manage system NCM codes',
            list: 'NCM List',
            createTitle: 'New NCM',
            editTitle: 'Edit NCM',
            form: {
                title: 'NCM Form'
            },
            messages: {
                createSuccess: 'NCM created successfully!',
                updateSuccess: 'NCM updated successfully!',
                deleteSuccess: 'NCM deleted successfully!',
                deleteMultipleSuccess: 'NCMs deleted successfully!',
                activateSuccess: 'NCM activated successfully!',
                deactivateSuccess: 'NCM deactivated successfully!',
                exportNotImplemented: 'Export functionality not yet implemented'
            },
            errors: {
                loadError: 'Error loading NCM data',
                saveError: 'Error saving NCM',
                deleteError: 'Error deleting NCM',
                deleteMultipleError: 'Error deleting NCMs',
                notFound: 'NCM not found'
            }
        },
        users: {
            title: 'Users',
            subtitle: 'Manage system users',
            list: 'Users list',
            errors: {
                loadError: 'Error loading user data'
            }
        },
        countries: {
            title: 'Countries',
            subtitle: 'Manage system countries',
            list: 'Countries list',
            createTitle: 'New country',
            editTitle: 'Edit country',
            form: {
                title: 'Country Form'
            },
            messages: {
                createSuccess: 'Country created successfully!',
                updateSuccess: 'Country updated successfully!',
                deleteSuccess: 'Country deleted successfully!',
                deleteMultipleSuccess: 'Countries deleted successfully!',
                exportNotImplemented: 'Export functionality not yet implemented'
            },
            errors: {
                loadError: 'Error loading country data',
                saveError: 'Error saving country',
                deleteError: 'Error deleting country',
                deleteMultipleError: 'Error deleting countries',
                notFound: 'Country not found'
            }
        },
        region: {
            title: 'Region',
            subtitle: 'Manage system region',
            list: 'Region list',
            createTitle: 'New region',
            editTitle: 'Edit region',
            form: {
                title: 'Region Form'
            },
            messages: {
                createSuccess: 'Region created successfully!',
                updateSuccess: 'Region updated successfully!',
                deleteSuccess: 'Region deleted successfully!',
                deleteMultipleSuccess: 'Regions deleted successfully!'
            },
            errors: {
                loadError: 'Error loading region data',
                saveError: 'Error saving region',
                deleteError: 'Error deleting region',
                deleteMultipleError: 'Error deleting regions',
                notFound: 'Region not found',
                duplicateDescription:
                    'The description field is unique and already exists in the database.'
            }
        },
        states: {
            title: 'States',
            subtitle: 'Manage system states',
            list: 'States list',
            createTitle: 'New state',
            editTitle: 'Edit state',
            form: {
                title: 'State Form'
            },
            messages: {
                createSuccess: 'State created successfully!',
                updateSuccess: 'State updated successfully!',
                deleteSuccess: 'State deleted successfully!',
                deleteMultipleSuccess: 'States deleted successfully!'
            },
            errors: {
                loadError: 'Error loading state data',
                saveError: 'Error saving state',
                deleteError: 'Error deleting state',
                deleteMultipleError: 'Error deleting states',
                notFound: 'State not found'
            }
        },
        cities: {
            title: 'Cities',
            subtitle: 'Manage system cities',
            list: 'Cities list',
            createTitle: 'New city',
            editTitle: 'Edit city',
            form: {
                title: 'City Form'
            },
            messages: {
                createSuccess: 'City created successfully!',
                updateSuccess: 'City updated successfully!',
                deleteSuccess: 'City deleted successfully!',
                deleteMultipleSuccess: 'Cities deleted successfully!'
            },
            errors: {
                loadError: 'Error loading city data',
                saveError: 'Error saving city',
                deleteError: 'Error deleting city',
                deleteMultipleError: 'Error deleting cities',
                notFound: 'City not found'
            }
        },
        addressType: {
            title: 'Address Type',
            subtitle: 'Manage system address type',
            list: 'Address Type list',
            createTitle: 'New address type',
            editTitle: 'Edit address type',
            form: {
                title: 'Address Type Form'
            },
            messages: {
                createSuccess: 'Address Type created successfully!',
                updateSuccess: 'Address Type updated successfully!',
                deleteSuccess: 'Address Type deleted successfully!',
                deleteMultipleSuccess: 'Address Types deleted successfully!'
            },
            errors: {
                loadError: 'Error loading address type data',
                saveError: 'Error saving address type',
                deleteError: 'Error deleting address type',
                deleteMultipleError: 'Error deleting address types',
                notFound: 'Address Type not found',
                duplicateDescription: 'Description already exists'
            }
        },
        categoriaCNH: {
            title: 'Categoria CNH',
            subtitle: 'Manage system categoria CNH',
            list: 'Categoria CNH list',
            createTitle: 'New categoria CNH',
            editTitle: 'Edit categoria CNH',
            form: {
                title: 'Categoria CNH Form'
            },
            messages: {
                createSuccess: 'Categoria CNH created successfully!',
                updateSuccess: 'Categoria CNH updated successfully!',
                deleteSuccess: 'Categoria CNH deleted successfully!',
                deleteMultipleSuccess: 'Categoria CNHs deleted successfully!'
            },
            errors: {
                loadError: 'Error loading categoria CNH data',
                saveError: 'Error saving categoria CNH',
                deleteError: 'Error deleting categoria CNH',
                deleteMultipleError: 'Error deleting categoria CNHs',
                notFound: 'Categoria CNH not found'
            }
        },
        documentType: {
            title: 'Document Type',
            subtitle: 'Manage system document type',
            list: 'Document Type list',
            createTitle: 'New document type',
            editTitle: 'Edit document type',
            form: {
                title: 'Document Type Form'
            },
            messages: {
                createSuccess: 'Document Type created successfully!',
                updateSuccess: 'Document Type updated successfully!',
                deleteSuccess: 'Document Type deleted successfully!',
                deleteMultipleSuccess: 'Document Types deleted successfully!'
            },
            errors: {
                loadError: 'Error loading document type data',
                saveError: 'Error saving document type',
                deleteError: 'Error deleting document type',
                deleteMultipleError: 'Error deleting document types',
                notFound: 'Document Type not found',
                duplicateDescription: 'Description already exists'
            }
        },
        paymentGroup: {
            title: 'Payment Group',
            subtitle: 'Manage system payment group',
            list: 'Payment Group list',
            createTitle: 'New payment group',
            editTitle: 'Edit payment group',
            form: {
                title: 'Payment Group Form'
            },
            messages: {
                createSuccess: 'Payment Group created successfully!',
                updateSuccess: 'Payment Group updated successfully!',
                deleteSuccess: 'Payment Group deleted successfully!',
                deleteMultipleSuccess: 'Payment Groups deleted successfully!'
            },
            errors: {
                loadError: 'Error loading payment group data',
                saveError: 'Error saving payment group',
                deleteError: 'Error deleting payment group',
                deleteMultipleError: 'Error deleting payment groups',
                notFound: 'Payment Group not found',
                duplicateDescription: 'Description already exists'
            }
        },
        activitySector: {
            title: 'Activity Sector',
            subtitle: 'Manage system activity sector',
            list: 'Activity Sector list',
            createTitle: 'New activity sector',
            editTitle: 'Edit activity sector',
            form: {
                title: 'Activity Sector Form'
            },
            messages: {
                createSuccess: 'Activity Sector created successfully!',
                updateSuccess: 'Activity Sector updated successfully!',
                deleteSuccess: 'Activity Sector deleted successfully!',
                deleteMultipleSuccess: 'Activity Sectors deleted successfully!'
            },
            errors: {
                loadError: 'Error loading activity sector data',
                saveError: 'Error saving activity sector',
                deleteError: 'Error deleting activity sector',
                deleteMultipleError: 'Error deleting activity sectors',
                notFound: 'Activity Sector not found',
                duplicateDescription: 'Description already exists'
            }
        },
        historicLaunch: {
            title: 'Historic Launch',
            subtitle: 'Manage system historic launch',
            list: 'Historic Launch list',
            createTitle: 'New historic launch',
            editTitle: 'Edit historic launch',
            form: {
                title: 'Historic Launch Form'
            },
            messages: {
                createSuccess: 'Historic Launch created successfully!',
                updateSuccess: 'Historic Launch updated successfully!',
                deleteSuccess: 'Historic Launch deleted successfully!',
                deleteMultipleSuccess: 'Historic Launches deleted successfully!'
            },
            errors: {
                loadError: 'Error loading historic launch data',
                saveError: 'Error saving historic launch',
                deleteError: 'Error deleting historic launch',
                deleteMultipleError: 'Error deleting historic launches',
                notFound: 'Historic Launch not found',
                duplicateDescription: 'Description already exists'
            }
        },
        family: {
            title: 'Family',
            subtitle: 'Manage system family',
            list: 'Family list',
            createTitle: 'New family',
            editTitle: 'Edit family',
            form: {
                title: 'Family Form'
            },
            messages: {
                createSuccess: 'Family created successfully!',
                updateSuccess: 'Family updated successfully!',
                deleteSuccess: 'Family deleted successfully!',
                deleteMultipleSuccess: 'Families deleted successfully!'
            },
            errors: {
                loadError: 'Error loading family data',
                saveError: 'Error saving family',
                deleteError: 'Error deleting family',
                deleteMultipleError: 'Error deleting families',
                notFound: 'Family not found',
                duplicateDescription: 'Description already exists'
            }
        },
        group: {
            title: 'Group',
            subtitle: 'Manage system group',
            list: 'Group list',
            createTitle: 'New group',
            editTitle: 'Edit group',
            form: {
                title: 'Group Form'
            },
            messages: {
                createSuccess: 'Group created successfully!',
                updateSuccess: 'Group updated successfully!',
                deleteSuccess: 'Group deleted successfully!',
                deleteMultipleSuccess: 'Groups deleted successfully!'
            },
            errors: {
                loadError: 'Error loading group data',
                saveError: 'Error saving group',
                deleteError: 'Error deleting group',
                deleteMultipleError: 'Error deleting groups',
                notFound: 'Group not found',
                duplicateDescription: 'Description already exists'
            }
        },
        subGroup: {
            title: 'Sub Group',
            subtitle: 'Manage system sub group',
            list: 'Sub Group list',
            createTitle: 'New sub group',
            editTitle: 'Edit sub group',
            form: {
                title: 'Sub Group Form'
            },
            messages: {
                createSuccess: 'Sub Group created successfully!',
                updateSuccess: 'Sub Group updated successfully!',
                deleteSuccess: 'Sub Group deleted successfully!',
                deleteMultipleSuccess: 'Sub Groups deleted successfully!'
            },
            errors: {
                loadError: 'Error loading sub group data',
                saveError: 'Error saving sub group',
                deleteError: 'Error deleting sub group',
                deleteMultipleError: 'Error deleting sub groups',
                notFound: 'Sub Group not found',
                duplicateDescription: 'Description already exists'
            }
        },
        customerCategory: {
            title: 'Customer Category',
            subtitle: 'Manage system customer category',
            list: 'Customer Category list',
            createTitle: 'New customer category',
            editTitle: 'Edit customer category',
            form: {
                title: 'Customer Category Form'
            },
            messages: {
                createSuccess: 'Customer Category created successfully!',
                updateSuccess: 'Customer Category updated successfully!',
                deleteSuccess: 'Customer Category deleted successfully!',
                deleteMultipleSuccess:
                    'Customer Categories deleted successfully!'
            },
            errors: {
                loadError: 'Error loading customer category data',
                saveError: 'Error saving customer category',
                deleteError: 'Error deleting customer category',
                deleteMultipleError: 'Error deleting customer categories',
                notFound: 'Customer Category not found',
                duplicateDescription: 'Description already exists'
            }
        },
        discountGroup: {
            title: 'Discount Group',
            subtitle: 'Manage system discount groups',
            list: 'Discount Group list',
            createTitle: 'New discount group',
            editTitle: 'Edit discount group',
            form: {
                title: 'Discount Group Form'
            },
            messages: {
                createSuccess: 'Discount Group created successfully!',
                updateSuccess: 'Discount Group updated successfully!',
                deleteSuccess: 'Discount Group deleted successfully!',
                deleteMultipleSuccess: 'Discount Groups deleted successfully!'
            },
            errors: {
                loadError: 'Error loading discount group data',
                saveError: 'Error saving discount group',
                deleteError: 'Error deleting discount group',
                deleteMultipleError: 'Error deleting discount groups',
                notFound: 'Discount Group not found',
                duplicateDescription: 'Description already exists'
            }
        },
        application: {
            title: 'Application',
            subtitle: 'Manage system applications',
            list: 'Application list',
            createTitle: 'New application',
            editTitle: 'Edit application',
            form: {
                title: 'Application Form'
            },
            messages: {
                createSuccess: 'Application created successfully!',
                updateSuccess: 'Application updated successfully!',
                deleteSuccess: 'Application deleted successfully!',
                deleteMultipleSuccess: 'Applications deleted successfully!'
            },
            errors: {
                loadError: 'Error loading application data',
                saveError: 'Error saving application',
                deleteError: 'Error deleting application',
                deleteMultipleError: 'Error deleting applications',
                notFound: 'Application not found',
                duplicateDescription: 'Description already exists'
            }
        },
        installationType: {
            title: 'Installation Type',
            subtitle: 'Manage system installation types',
            list: 'Installation Type list',
            createTitle: 'New installation type',
            editTitle: 'Edit installation type',
            form: {
                title: 'Installation Type Form'
            },
            messages: {
                createSuccess: 'Installation Type created successfully!',
                updateSuccess: 'Installation Type updated successfully!',
                deleteSuccess: 'Installation Type deleted successfully!',
                deleteMultipleSuccess:
                    'Installation Types deleted successfully!'
            },
            errors: {
                loadError: 'Error loading installation type data',
                saveError: 'Error saving installation type',
                deleteError: 'Error deleting installation type',
                deleteMultipleError: 'Error deleting installation types',
                notFound: 'Installation Type not found',
                duplicateDescription: 'Description already exists'
            }
        },
        profileType: {
            title: 'Profile Type',
            subtitle: 'Manage system profile types',
            list: 'Profile Type list',
            createTitle: 'New profile type',
            editTitle: 'Edit profile type',
            form: {
                title: 'Profile Type Form'
            },
            messages: {
                createSuccess: 'Profile Type created successfully!',
                updateSuccess: 'Profile Type updated successfully!',
                deleteSuccess: 'Profile Type deleted successfully!',
                deleteMultipleSuccess: 'Profile Types deleted successfully!'
            },
            errors: {
                loadError: 'Error loading profile type data',
                saveError: 'Error saving profile type',
                deleteError: 'Error deleting profile type',
                deleteMultipleError: 'Error deleting profile types',
                notFound: 'Profile Type not found',
                duplicateDescription: 'Description already exists'
            }
        },
        materialType: {
            title: 'Material Type',
            subtitle: 'Manage system material types',
            list: 'Material Type list',
            createTitle: 'New material type',
            editTitle: 'Edit material type',
            form: {
                title: 'Material Type Form'
            },
            messages: {
                createSuccess: 'Material Type created successfully!',
                updateSuccess: 'Material Type updated successfully!',
                deleteSuccess: 'Material Type deleted successfully!',
                deleteMultipleSuccess: 'Material Types deleted successfully!'
            },
            errors: {
                loadError: 'Error loading material type data',
                saveError: 'Error saving material type',
                deleteError: 'Error deleting material type',
                deleteMultipleError: 'Error deleting material types',
                notFound: 'Material Type not found',
                duplicateDescription: 'Description already exists'
            }
        },
        projectGroup: {
            title: 'Project Group',
            subtitle: 'Manage system project groups',
            list: 'Project Group list',
            createTitle: 'New project group',
            editTitle: 'Edit project group',
            form: {
                title: 'Project Group Form'
            },
            messages: {
                createSuccess: 'Project Group created successfully!',
                updateSuccess: 'Project Group updated successfully!',
                deleteSuccess: 'Project Group deleted successfully!',
                deleteMultipleSuccess: 'Project Groups deleted successfully!'
            },
            errors: {
                loadError: 'Error loading project group data',
                saveError: 'Error saving project group',
                deleteError: 'Error deleting project group',
                deleteMultipleError: 'Error deleting project groups',
                notFound: 'Project Group not found',
                duplicateDescription: 'Description already exists'
            }
        },
        materialGroup: {
            title: 'Material Group',
            subtitle: 'Manage system material groups',
            list: 'Material Group list',
            createTitle: 'New material group',
            editTitle: 'Edit material group',
            form: {
                title: 'Material Group Form'
            },
            messages: {
                createSuccess: 'Material Group created successfully!',
                updateSuccess: 'Material Group updated successfully!',
                deleteSuccess: 'Material Group deleted successfully!',
                deleteMultipleSuccess: 'Material Groups deleted successfully!'
            },
            errors: {
                loadError: 'Error loading material group data',
                saveError: 'Error saving material group',
                deleteError: 'Error deleting material group',
                deleteMultipleError: 'Error deleting material groups',
                notFound: 'Material Group not found',
                duplicateDescription: 'Description already exists'
            }
        },
        title: {
            title: 'Title',
            subtitle: 'Manage system titles',
            list: 'Title list',
            createTitle: 'New title',
            editTitle: 'Edit title',
            form: {
                title: 'Title Form'
            },
            messages: {
                createSuccess: 'Title created successfully!',
                updateSuccess: 'Title updated successfully!',
                deleteSuccess: 'Title deleted successfully!',
                deleteMultipleSuccess: 'Titles deleted successfully!'
            },
            errors: {
                loadError: 'Error loading title data',
                saveError: 'Error saving title',
                deleteError: 'Error deleting title',
                deleteMultipleError: 'Error deleting titles',
                notFound: 'Title not found',
                duplicateDescription: 'Description already exists'
            }
        },
        equipmentPart: {
            title: 'Equipment Part',
            subtitle: 'Manage system equipment parts',
            list: 'Equipment Part list',
            createTitle: 'New equipment part',
            editTitle: 'Edit equipment part',
            form: {
                title: 'Equipment Part Form'
            },
            messages: {
                createSuccess: 'Equipment Part created successfully!',
                updateSuccess: 'Equipment Part updated successfully!',
                deleteSuccess: 'Equipment Part deleted successfully!',
                deleteMultipleSuccess: 'Equipment Parts deleted successfully!'
            },
            errors: {
                loadError: 'Error loading equipment part data',
                saveError: 'Error saving equipment part',
                deleteError: 'Error deleting equipment part',
                deleteMultipleError: 'Error deleting equipment parts',
                notFound: 'Equipment Part not found',
                duplicateDescription: 'Description already exists'
            }
        },
        defect: {
            title: 'Defect',
            subtitle: 'Manage system defects',
            list: 'Defect list',
            createTitle: 'New defect',
            editTitle: 'Edit defect',
            form: {
                title: 'Defect Form'
            },
            messages: {
                createSuccess: 'Defect created successfully!',
                updateSuccess: 'Defect updated successfully!',
                deleteSuccess: 'Defect deleted successfully!',
                deleteMultipleSuccess: 'Defects deleted successfully!',
                activateSuccess: 'Defect activated successfully!',
                deactivateSuccess: 'Defect deactivated successfully!'
            },
            errors: {
                loadError: 'Error loading defect data',
                saveError: 'Error saving defect',
                deleteError: 'Error deleting defect',
                deleteMultipleError: 'Error deleting defects',
                notFound: 'Defect not found',
                duplicateDescription: 'Description already exists'
            }
        },
        cause: {
            title: 'Cause',
            subtitle: 'Manage system causes',
            list: 'Cause list',
            createTitle: 'New cause',
            editTitle: 'Edit cause',
            form: {
                title: 'Cause Form'
            },
            messages: {
                createSuccess: 'Cause created successfully!',
                updateSuccess: 'Cause updated successfully!',
                deleteSuccess: 'Cause deleted successfully!',
                deleteMultipleSuccess: 'Causes deleted successfully!',
                activateSuccess: 'Cause activated successfully!',
                deactivateSuccess: 'Cause deactivated successfully!'
            },
            errors: {
                loadError: 'Error loading cause data',
                saveError: 'Error saving cause',
                deleteError: 'Error deleting cause',
                deleteMultipleError: 'Error deleting causes',
                notFound: 'Cause not found',
                duplicateDescription: 'Description already exists'
            }
        },
        equipmentFamily: {
            title: 'Equipment Family',
            subtitle: 'Manage system equipment families',
            list: 'Equipment Family list',
            createTitle: 'New equipment family',
            editTitle: 'Edit equipment family',
            form: {
                title: 'Equipment Family Form'
            },
            messages: {
                createSuccess: 'Equipment Family created successfully!',
                updateSuccess: 'Equipment Family updated successfully!',
                deleteSuccess: 'Equipment Family deleted successfully!',
                deleteMultipleSuccess:
                    'Equipment Families deleted successfully!',
                activateSuccess: 'Equipment Family activated successfully!',
                deactivateSuccess: 'Equipment Family deactivated successfully!'
            },
            errors: {
                loadError: 'Error loading equipment family data',
                saveError: 'Error saving equipment family',
                deleteError: 'Error deleting equipment family',
                deleteMultipleError: 'Error deleting equipment families',
                notFound: 'Equipment Family not found',
                duplicateDescription: 'Description already exists'
            }
        },
        billingPortfolio: {
            title: 'Billing Portfolio',
            subtitle: 'Manage system billing portfolios',
            list: 'Billing Portfolio list',
            createTitle: 'New billing portfolio',
            editTitle: 'Edit billing portfolio',
            form: {
                title: 'Billing Portfolio Form'
            },
            messages: {
                createSuccess: 'Billing Portfolio created successfully!',
                updateSuccess: 'Billing Portfolio updated successfully!',
                deleteSuccess: 'Billing Portfolio deleted successfully!',
                deleteMultipleSuccess:
                    'Billing Portfolios deleted successfully!',
                activateSuccess: 'Billing Portfolio activated successfully!',
                deactivateSuccess: 'Billing Portfolio deactivated successfully!'
            },
            errors: {
                loadError: 'Error loading billing portfolio data',
                saveError: 'Error saving billing portfolio',
                deleteError: 'Error deleting billing portfolio',
                deleteMultipleError: 'Error deleting billing portfolios',
                notFound: 'Billing Portfolio not found',
                duplicateDescription: 'Description already exists'
            }
        },
        checklist: {
            title: 'Checklist',
            subtitle: 'Manage system checklists',
            list: 'Checklist list',
            createTitle: 'New checklist',
            editTitle: 'Edit checklist',
            form: {
                title: 'Checklist Form'
            },
            messages: {
                createSuccess: 'Checklist created successfully!',
                updateSuccess: 'Checklist updated successfully!',
                deleteSuccess: 'Checklist deleted successfully!',
                deleteMultipleSuccess: 'Checklists deleted successfully!',
                activateSuccess: 'Checklist activated successfully!',
                deactivateSuccess: 'Checklist deactivated successfully!'
            },
            errors: {
                loadError: 'Error loading checklist data',
                saveError: 'Error saving checklist',
                deleteError: 'Error deleting checklist',
                deleteMultipleError: 'Error deleting checklists',
                notFound: 'Checklist not found',
                duplicateDescription: 'Description already exists'
            }
        },
        operation: {
            title: 'Operation',
            subtitle: 'Manage system operations',
            list: 'Operation list',
            createTitle: 'New operation',
            editTitle: 'Edit operation',
            form: {
                title: 'Operation Form'
            },
            messages: {
                createSuccess: 'Operation created successfully!',
                updateSuccess: 'Operation updated successfully!',
                deleteSuccess: 'Operation deleted successfully!',
                deleteMultipleSuccess: 'Operations deleted successfully!',
                activateSuccess: 'Operation activated successfully!',
                deactivateSuccess: 'Operation deactivated successfully!'
            },
            errors: {
                loadError: 'Error loading operation data',
                saveError: 'Error saving operation',
                deleteError: 'Error deleting operation',
                deleteMultipleError: 'Error deleting operations',
                notFound: 'Operation not found',
                duplicateDescription: 'Description already exists'
            }
        },
        nonConformity: {
            title: 'Non-Conformity',
            subtitle: 'Manage system non-conformities',
            list: 'Non-Conformity list',
            createTitle: 'New non-conformity',
            editTitle: 'Edit non-conformity',
            form: {
                title: 'Non-Conformity Form'
            },
            messages: {
                createSuccess: 'Non-Conformity created successfully!',
                updateSuccess: 'Non-Conformity updated successfully!',
                deleteSuccess: 'Non-Conformity deleted successfully!',
                deleteMultipleSuccess: 'Non-Conformities deleted successfully!',
                activateSuccess: 'Non-Conformity activated successfully!',
                deactivateSuccess: 'Non-Conformity deactivated successfully!'
            },
            errors: {
                loadError: 'Error loading non-conformity data',
                saveError: 'Error saving non-conformity',
                deleteError: 'Error deleting non-conformity',
                deleteMultipleError: 'Error deleting non-conformities',
                notFound: 'Non-Conformity not found',
                duplicateDescription: 'Description already exists'
            }
        },
        userProfile: {
            title: 'User Profile',
            subtitle: 'Manage user profile',
            form: {
                title: 'User Profile Form'
            },
            messages: {
                updateSuccess: 'User profile updated successfully!',
                updateError: 'Error updating user profile',
                deleteSuccess: 'User profile deleted successfully!',
                deleteError: 'Error deleting user profile',
                deleteMultipleSuccess: 'User profiles deleted successfully!',
                deleteMultipleError: 'Error deleting user profiles',
                activateSuccess: 'User profile activated successfully!',
                deactivateSuccess: 'User profile deactivated successfully!'
            },
            errors: {
                loadError: 'Error loading user profile data',
                saveError: 'Error saving user profile',
                deleteError: 'Error deleting user profile',
                deleteMultipleError: 'Error deleting user profiles',
                notFound: 'User profile not found',
                duplicateDescription: 'Description already exists'
            }
        },
        module: {
            title: 'Module',
            subtitle: 'Manage module',
            form: {
                title: 'Module Form'
            },
            messages: {
                updateSuccess: 'Module updated successfully!',
                updateError: 'Error updating module',
                deleteSuccess: 'Module deleted successfully!',
                deleteError: 'Error deleting module',
                deleteMultipleSuccess: 'Module deleted successfully!',
                deleteMultipleError: 'Error deleting Modules',
                activateSuccess: 'Module activated successfully!',
                deactivateSuccess: 'Module deactivated successfully!'
            },
            errors: {
                loadError: 'Error loading module data',
                saveError: 'Error saving module',
                deleteError: 'Error deleting module',
                deleteMultipleError: 'Error deleting module',
                notFound: 'Module not found',
                duplicateDescription: 'Description already exists'
            }
        },
        position: {
            title: 'Position',
            subtitle: 'Manage position',
            list: 'Position list',
            createTitle: 'New position',
            editTitle: 'Edit position',
            form: {
                title: 'Position Form'
            },
            messages: {
                createSuccess: 'Position created successfully!',
                updateSuccess: 'Position updated successfully!',
                deleteSuccess: 'Position deleted successfully!',
                deleteMultipleSuccess: 'Positions deleted successfully!',
                activateSuccess: 'Position activated successfully!',
                deactivateSuccess: 'Position deactivated successfully!'
            },
            errors: {
                loadError: 'Error loading position data',
                saveError: 'Error saving position',
                deleteError: 'Error deleting position',
                deleteMultipleError: 'Error deleting positions',
                notFound: 'Position not found',
                duplicateDescription: 'Description already exists'
            }
        },
        education: {
            title: 'Education',
            subtitle: 'Manage education',
            list: 'Education list',
            createTitle: 'New education',
            editTitle: 'Edit education',
            form: {
                title: 'Education Form'
            },
            messages: {
                createSuccess: 'Education created successfully!',
                updateSuccess: 'Education updated successfully!',
                deleteSuccess: 'Education deleted successfully!',
                deleteMultipleSuccess: 'Educations deleted successfully!',
                activateSuccess: 'Education activated successfully!',
                deactivateSuccess: 'Education deactivated successfully!'
            },
            errors: {
                loadError: 'Error loading education data',
                saveError: 'Error saving education',
                deleteError: 'Error deleting education',
                deleteMultipleError: 'Error deleting educations',
                notFound: 'Education not found',
                duplicateDescription: 'Description already exists'
            }
        },
        specialty: {
            title: 'Specialty',
            subtitle: 'Manage specialty',
            list: 'Specialty list',
            createTitle: 'New specialty',
            editTitle: 'Edit specialty',
            form: {
                title: 'Specialty Form'
            },
            messages: {
                createSuccess: 'Specialty created successfully!',
                updateSuccess: 'Specialty updated successfully!',
                deleteSuccess: 'Specialty deleted successfully!',
                deleteMultipleSuccess: 'Specialties deleted successfully!',
                activateSuccess: 'Specialty activated successfully!',
                deactivateSuccess: 'Specialty deactivated successfully!'
            },
            errors: {
                loadError: 'Error loading specialty data',
                saveError: 'Error saving specialty',
                deleteError: 'Error deleting specialty',
                deleteMultipleError: 'Error deleting specialties',
                notFound: 'Specialty not found',
                duplicateDescription: 'Description already exists'
            }
        },
        skill: {
            title: 'Skill',
            subtitle: 'Manage skill',
            list: 'Skill list',
            createTitle: 'New skill',
            editTitle: 'Edit skill',
            form: {
                title: 'Skill Form'
            },
            messages: {
                createSuccess: 'Skill created successfully!',
                updateSuccess: 'Skill updated successfully!',
                deleteSuccess: 'Skill deleted successfully!',
                deleteMultipleSuccess: 'Skills deleted successfully!',
                activateSuccess: 'Skill activated successfully!',
                deactivateSuccess: 'Skill deactivated successfully!'
            },
            errors: {
                loadError: 'Error loading skill data',
                saveError: 'Error saving skill',
                deleteError: 'Error deleting skill',
                deleteMultipleError: 'Error deleting skills',
                notFound: 'Skill not found',
                duplicateDescription: 'Description already exists'
            }
        },
        department: {
            title: 'Department',
            subtitle: 'Manage department',
            list: 'Department list',
            createTitle: 'New department',
            editTitle: 'Edit department',
            form: {
                title: 'Department Form'
            },
            messages: {
                createSuccess: 'Department created successfully!',
                updateSuccess: 'Department updated successfully!',
                deleteSuccess: 'Department deleted successfully!',
                deleteMultipleSuccess: 'Departments deleted successfully!',
                activateSuccess: 'Department activated successfully!',
                deactivateSuccess: 'Department deactivated successfully!'
            },
            errors: {
                loadError: 'Error loading department data',
                saveError: 'Error saving department',
                deleteError: 'Error deleting department',
                deleteMultipleError: 'Error deleting departments',
                notFound: 'Department not found',
                duplicateDescription: 'Description already exists'
            }
        },
        origin: {
            title: 'Origin',
            subtitle: 'Manage system origin',
            list: 'Origin list',
            createTitle: 'New sub origin',
            editTitle: 'Edit origin',
            form: {
                title: 'Origin Form'
            },
            messages: {
                createSuccess: 'Origin created successfully!',
                updateSuccess: 'Origin updated successfully!',
                deleteSuccess: 'Origin deleted successfully!',
                deleteMultipleSuccess: 'Origins deleted successfully!'
            },
            errors: {
                loadError: 'Error loading origin data',
                saveError: 'Error saving origin',
                deleteError: 'Error deleting origin',
                deleteMultipleError: 'Error deleting origins',
                notFound: 'Origin not found',
                duplicateDescription: 'Description already exists'
            }
        },
        opportunity: {
            title: 'Opportunity',
            subtitle: 'Manage opportunity',
            list: 'Opportunity list',
            createTitle: 'New opportunity',
            editTitle: 'Edit opportunity',
            form: {
                title: 'Opportunity Form'
            },
            messages: {
                createSuccess: 'Opportunity created successfully!',
                updateSuccess: 'Opportunity updated successfully!',
                deleteSuccess: 'Opportunity deleted successfully!',
                deleteMultipleSuccess: 'Opportunities deleted successfully!',
                activateSuccess: 'Opportunity activated successfully!',
                deactivateSuccess: 'Opportunity deactivated successfully!'
            },
            errors: {
                loadError: 'Error loading opportunity data',
                saveError: 'Error saving opportunity',
                deleteError: 'Error deleting opportunity',
                deleteMultipleError: 'Error deleting opportunities',
                notFound: 'Opportunity not found',
                duplicateDescription: 'Description already exists'
            }
        },
        systemConfig: {
            themeBuilder: {
                title: 'Theme Customization',
                subtitle: 'Configure colors and display mode of the system',
                sections: {
                    mainColors: 'Main Colors',
                    statusColors: 'Status Colors',
                    darkMode: 'Dark Mode',
                    presets: 'Predefined Themes'
                },
                colors: {
                    primary: 'Primary Color',
                    secondary: 'Secondary Color',
                    accent: 'Accent Color',
                    positive: 'Success',
                    negative: 'Error',
                    warning: 'Warning',
                    info: 'Information',
                    dark: 'Dark Background',
                    darkPage: 'Dark Page'
                },
                presets: {
                    default: 'Default',
                    ocean: 'Ocean',
                    forest: 'Forest',
                    sunset: 'Sunset',
                    night: 'Night'
                },
                actions: {
                    save: 'Save Settings',
                    reset: 'Restore Default'
                },
                messages: {
                    presetApplied: 'Preset "{name}" applied',
                    themeSaved: 'Theme saved successfully!',
                    themeReset: 'Theme reset to default'
                }
            }
        }
    },
    tables: {
        headers: {
            actions: 'Actions'
        },
        tableOptions: {
            itemsPerPage: 'Items per page',
            itemsShowed: 'Showing {first} - {last} of {total} items',
            allItems: 'All',
            configureColumns: 'Configure columns',
            hideColumn: 'Hide column',
            moreActions: 'More actions'
        }
    },
    errors: {
        loadModelsError: 'Error loading models',
        validationError: 'Please fix the errors in the form',
        loadTypesError: 'Error loading types',
        loadBrandsError: 'Error loading brands',
        loadCountriesError: 'Error loading countries',
        loadStatesError: 'Error loading states',
        loadCitiesError: 'Error loading cities',
        loadRegionsError: 'Error loading regions',
        loadAddressTypesError: 'Error loading address types',
        loadDocumentTypesError: 'Error loading document types',
        loadOccupationsError: 'Error loading occupations',
        loadDepartmentsError: 'Error loading departments',
        loadPhoneTypesError: 'Error loading phone types',
        loadUsersError: 'Error loading users',
        loadPermissionGroupsError: 'Error loading permission groups',
        loadSellersError: 'Error loading sellers',
        loadBuyersError: 'Error loading buyers',
        loadCashiersError: 'Error loading cashiers',
        loadCompaniesError: 'Error loading companies',
        loadRepresentativesError: 'Error loading representatives',
        loadCategoriesError: 'Error loading categories',
        loadPaymentConditionsError: 'Error loading payment conditions',
        loadPaymentModalitiesError: 'Error loading payment modalities',
        loadTransactionsError: 'Error loading transactions',
        loadPriceTablesError: 'Error loading price tables',
        loadFinancialAccountsError: 'Error loading financial accounts',
        loadDeliveryTypesError: 'Error loading delivery types',
        loadFreightIndicatorsError: 'Error loading freight indicators',
        loadCarriersError: 'Error loading carriers',
        loadCostCentersError: 'Error loading cost centers',
        loadPaymentMethodGroupsError: 'Error loading payment method groups',
        loadStandardizedTextsError: 'Error loading standardized texts',
        loadNCMsError: 'Error loading NCMs',
        loadCNHCategoriesError: 'Error loading CNH categories',
        loadPaymentGroupsError: 'Error loading payment groups',
        loadActivitySectorsError: 'Error loading activity sectors',
        loadHistoricLaunchesError: 'Error loading historic launches',
        loadFamiliesError: 'Error loading families',
        loadGroupsError: 'Error loading groups',
        loadSubgroupsError: 'Error loading subgroups',
        loadCustomerCategoriesError: 'Error loading customer categories',
        loadDiscountGroupsError: 'Error loading discount groups',
        loadApplicationsError: 'Error loading applications',
        loadInstallationTypesError: 'Error loading installation types',
        loadProfileTypesError: 'Error loading profile types',
        loadMaterialTypesError: 'Error loading material types',
        loadProjectGroupsError: 'Error loading project groups',
        loadMaterialGroupsError: 'Error loading material groups',
        loadTitlesError: 'Error loading titles',
        loadEquipmentPartsError: 'Error loading equipment parts',
        requests: {
            generic: {
                400: 'Bad Request. Please check your input and try again.',
                401: 'Unauthorized. Please log in.',
                403: 'Forbidden. You do not have permission to perform this action.',
                404: 'Not Found. The requested resource could not be found.',
                500: 'Internal Server Error. Please try again later.',
                503: 'Service Unavailable. Please try again later.',
                noCode: 'Unknown Error. Please try again later.'
            },
            login: {
                400: 'Invalid e-mail or password.'
            },
            user: {
                401: 'User not authenticated.'
            }
        }
    },
    pwa: {
        updateAvailable: 'New content is available. Please refresh the page.',
        refresh: 'Refresh'
    },
    user: {
        fullName: 'Full name',
        username: 'Username',
        notAvailable: 'Not available',
        status: {
            label: 'Status',
            online: 'Online',
            offline: 'Offline',
            away: 'Away',
            busy: 'Busy',
            invisible: 'Invisible'
        }
    },
    notifications: {
        pleaseFixErrors: 'Please fix the errors before continuing',
        emailTestSuccess: 'Email connection test successful!',
        featureNotImplemented: 'Feature not yet implemented'
    },
    dialogs: {
        confirmDelete: {
            title: 'Confirm deletion',
            message: 'Are you sure you want to delete "{item}"?'
        },
        confirmDeleteMultiple: {
            title: 'Confirm multiple deletion',
            message: 'Are you sure you want to delete {count} selected items?'
        },
        confirmStatusChange: {
            title: 'Confirm status change',
            message: 'Are you sure you want to {action} "{item}"?'
        }
    },
    validate: {
        requiredName: 'Name is required.',
        invalidName: 'Name must have at least 3 characters.',
        requiredSurname: 'Surname is required.',
        invalidSurname: 'Surname must have at least 3 characters.',
        invalidEmail: 'Invalid e-mail.',
        requiredEmail: 'E-mail is required.',
        invalidPassword: 'Password must have at least 5 characters.',
        requiredPassword: 'Password is required.',
        requiredUsername: 'Username is required.',
        invalidUsername: 'Username must have at least 3 characters.',
        requiredRole: 'Role is required.',
        requiredCompanyName: 'Company name is required.',
        invalidCompanyName: 'Company name must have at least 3 characters.',
        docNaturalRequired: 'SSN is required.',
        docNaturalInvalid: 'SSN is invalid.',
        docLegalRequired: 'EIN is required.',
        docLegalInvalid: 'EIN is invalid.',
        requiredIndicatorStateEnrollmentRecipient:
            'Indicator of IE Recipient is required.',
        requiredFantasyName: 'Fantasy name is required.',
        requiredPermissionProfileName: 'Permission profile name is required.',
        invalidPermissionProfileName:
            'Permission profile name must have at least 3 characters.',
        requiredPermissionGroupName: 'Permission group name is required.',
        invalidPermissionGroupName:
            'Permission group name must have at least 3 characters.',
        requiredPermissionGroupCode: 'Permission group code is required.',
        invalidPermissionGroupCode:
            'Group code must contain only lowercase letters, numbers and underscores.',
        maxObservations: 'Observations cannot exceed 500 characters.',
        maxDescription: 'Description cannot exceed 250 characters.',
        invalidPasswordExpirationDays:
            'Password expiration days must be a positive number.',
        requiredSeller: 'Seller is required.',
        requiredBuyer: 'Buyer is required.',
        requiredCashier: 'Cashier is required.',
        requiredCompany: 'Company is required.',
        requiredRepresentative: 'Representative is required.',
        requiredSmtpServer: 'Email server is required.',
        requiredSmtpPort: 'SMTP port is required.',
        invalidSmtpPort: 'Invalid SMTP port.',
        requiredEmailPassword: 'Email password is required.',
        fieldRequired: 'The {field} field is required.',
        mustBeNumber: 'This field must be a number.',
        minLength: 'Must have at least {min} characters.',
        invalidCommission: 'Commission must be a number.',
        minCommission: 'Commission cannot be less than 0.',
        maxCommission: 'Commission cannot be greater than 100.',
        requiredCommission: 'Commission is required.',
        invalidDiscountPercentage: 'Discount percentage must be a number.',
        minDiscountPercentage: 'Discount percentage cannot be less than 0.',
        maxDiscountPercentage:
            'Discount percentage cannot be greater than 100.',
        requiredDiscountPercentage: 'Discount percentage is required.',
        invalidDiscountValue: 'Discount value must be a number.',
        minDiscountValue: 'Discount value cannot be less than 0.',
        requiredDiscountValue: 'Discount value is required.',
        invalidOrderPurchaseLimit: 'Order purchase limit must be a number.',
        minOrderPurchaseLimit: 'Order purchase limit cannot be less than 0.',
        requiredOrderPurchaseLimit: 'Order purchase limit is required.',
        invalidMonthlyPurchaseLimit: 'Monthly purchase limit must be a number.',
        minMonthlyPurchaseLimit:
            'Monthly purchase limit cannot be less than 0.',
        requiredMonthlyPurchaseLimit: 'Monthly purchase limit is required.',
        requiredCategory: 'Category is required.',
        requiredPaymentCondition: 'Payment condition is required.',
        requiredPaymentModality: 'Payment modality is required.',
        requiredTransaction: 'Transaction is required.',
        requiredPriceTable: 'Price table is required.',
        requiredFinancialAccount: 'Financial account is required.',
        requiredDeliveryType: 'Delivery type is required.',
        requiredFreightIndicator: 'Freight indicator is required.',
        requiredCarrier: 'Carrier is required.',
        requiredCostCenter: 'Cost center is required.',
        requiredPaymentMethodGroup: 'Payment method group is required.',
        requiredDaysForDueNotice: 'Days for due notice is required.',
        requiredDaysForCollectionNotice:
            'Days for collection notice is required.',
        requiredBillingDayLimit: 'Billing day limit is required.',
        requiredMaxDiscountPercentage:
            'Maximum discount percentage is required.',
        requiredMinimumBillingValue: 'Minimum billing value is required.',
        requiredCreditLimit: 'Credit limit is required.',
        minZero: 'Value cannot be less than 0.',
        minOne: 'Value cannot be less than 1.',
        maxThirtyOne: 'Value cannot be greater than 31.',
        maxOneHundred: 'Value cannot be greater than 100.',
        requiredMotherName: "Mother's name is required.",
        requiredAdmissionDate: 'Admission date is required.',
        requiredPIS: 'PIS is required.',
        requiredVoterCard: 'Voter card is required.',
        requiredWorkCard: 'Work card is required.',
        requiredDepartment: 'Department is required.',
        requiredSector: 'Sector is required.',
        requiredPosition: 'Position is required.',
        requiredHourCost: 'Hour cost value is required.',
        requiredAddressType: 'Address type is required.',
        requiredZipCode: 'ZIP code is required.',
        requiredCountry: 'Country is required.',
        requiredState: 'State is required.',
        requiredCity: 'City is required.',
        requiredStreet: 'Street is required.',
        requiredNumber: 'Number is required.',
        requiredNeighborhood: 'Neighborhood is required.',
        requiredDocumentType: 'Document type is required.',
        requiredDocumentNumber: 'Document number is required.',
        requiredOccupation: 'Occupation is required.',
        requiredBirthDate: 'Birth date is required.',
        requiredPhoneType: 'Phone type is required.',
        requiredAreaCode: 'Area code is required.',
        requiredPhoneNumber: 'Phone number is required.',
        requiredEmployeeCount: 'Number of employees is required.',
        requiredAnnualRevenue: 'Annual revenue is required.',
        requiredMainContact: 'Main contact is required.',
        requiredDescription: 'Description is required.',
        requiredAbbreviation: 'Abbreviation is required.',
        requiredTitle: 'Title is required.',
        requiredModule: 'Module is required.',
        requiredRoute: 'Route is required.',
        requiredContent: 'Content is required.',
        requiredType: 'Type is required.',
        requiredModel: 'Model is required',
        requiredDifficultyLevel: 'Difficulty level is required (1-10)',
        requiredBrand: 'Brand is required',
        minDifficultyLevel: 'Difficulty level must be at least 1',
        maxDifficultyLevel: 'Difficulty level must be at most 10',
        maxFileSize: 'File too large. Maximum 2MB',
        invalidFileType: 'Invalid file type',
        requiredCode: 'Code is required',
        ncmCodeFormat: 'NCM code must have format 0000.00.00',
        requiredStatus: 'Status is required',
        maxLength: 'Must have at most {max} characters'
    },
    cnpjSearch: {
        tooltip: 'Search CNPJ',
        title: 'CNPJ Search',
        inputLabel: 'Enter CNPJ',
        hint: 'Enter the CNPJ and press Enter or click the magnifying glass to search',
        clear: 'Clear field',
        results: 'Search Results',
        basicInfo: 'Basic Information',
        address: 'Address',
        mainActivity: 'Main Activity',
        searching: 'Searching CNPJ...',
        fields: {
            cnpj: 'CNPJ',
            companyName: 'Company Name',
            tradeName: 'Trade Name',
            status: 'Status',
            street: 'Street',
            zipCode: 'ZIP Code',
            neighborhood: 'Neighborhood',
            city: 'City'
        },
        errors: {
            required: 'CNPJ is required',
            invalidFormat: 'CNPJ must have 14 digits',
            invalidCNPJ: 'Invalid CNPJ',
            notFound: 'CNPJ not found',
            searchError: 'Error searching CNPJ. Please try again.'
        }
    },
    cepSearch: {
        searching: 'Searching ZIP Code...',
        searchingAddress: 'Looking up address...',
        errors: {
            required: 'ZIP Code is required',
            invalidFormat: 'ZIP Code must have 8 digits',
            invalidCEP: 'Invalid ZIP Code',
            notFound: 'ZIP Code not found',
            searchError: 'Error searching ZIP Code. Please try again.'
        },
        success: {
            addressFound: 'Address found and filled automatically'
        },
        onlyBrazil: 'ZIP code search available only for Brazil'
    },
    modals: {
        marcaModelo: {
            title: 'Brand x Model',
            selectedBrand: 'Selected Brand',
            manageModels: 'Manage models linked to this brand',
            form: {
                title: 'Link Model to Brand',
                editTitle: 'Edit Brand-Model Link'
            },
            table: {
                title: 'Linked Models'
            },
            messages: {
                createSuccess: 'Model linked to brand successfully!',
                updateSuccess: 'Link updated successfully!',
                deleteSuccess: 'Link removed successfully!',
                deleteMultipleSuccess: 'Links removed successfully!',
                linkSuccess: 'Link created successfully!'
            },
            errors: {
                saveError: 'Error linking model to brand',
                updateError: 'Error updating link',
                deleteError: 'Error removing link',
                deleteMultipleError: 'Error removing links',
                loadError: 'Error loading links',
                loadModelosError: 'Error loading models',
                notFound: 'Link not found'
            }
        },
        vincularModelo: {
            title: 'Link Model to Brand'
        }
    },
    imageViewer: {
        title: 'Image Viewer',
        zoomIn: 'Zoom In',
        zoomOut: 'Zoom Out',
        fitScreen: 'Fit to Screen',
        fullscreen: 'Fullscreen',
        loadError: 'Error loading image'
    },
    contactForm: {
        titles: {
            main: 'Contact Form',
            generalInfo: 'General Information',
            phones: 'Phones',
            emails: 'Emails'
        },
        labels: {
            name: 'Full Name',
            occupation: 'Occupation',
            department: 'Department',
            birthDate: 'Birth Date',
            phoneType: 'Phone Type',
            countryCode: 'Country',
            areaCode: 'Area Code',
            phoneNumber: 'Number',
            extension: 'Extension',
            email: 'Email',
            addPhone: 'Add Phone',
            addEmail: 'Add Email',
            removePhone: 'Remove Phone',
            removeEmail: 'Remove Email',
            whatsappMessages: 'Automatic WhatsApp message reception'
        },
        messages: {
            noPhones: 'No phones registered. Click "Add Phone" to start.',
            noEmails: 'No emails registered. Click "Add Email" to start.',
            confirmRemovePhone: 'Do you want to remove this phone?',
            confirmRemoveEmail: 'Do you want to remove this email?',
            phoneAdded: 'Phone added successfully',
            emailAdded: 'Email added successfully',
            phoneRemoved: 'Phone removed successfully',
            emailRemoved: 'Email removed successfully'
        },
        validation: {
            requiredName: 'Name is required',
            requiredOccupation: 'Occupation is required',
            requiredDepartment: 'Department is required',
            requiredBirthDate: 'Birth date is required',
            requiredPhoneType: 'Phone type is required',
            requiredCountryCode: 'Country is required',
            requiredAreaCode: 'Area code is required',
            requiredPhoneNumber: 'Phone number is required',
            invalidExtension: 'Extension must have at least 2 digits',
            requiredEmail: 'Email is required',
            invalidEmail: 'Invalid email',
            minPhones: 'At least one phone is required',
            minEmails: 'At least one email is required'
        }
    },
    addressCollection: {
        titles: {
            main: 'Address Collection',
            addAddress: 'Add Address',
            editAddress: 'Edit Address'
        },
        labels: {
            addAddress: 'Add Address',
            editAddress: 'Edit Address',
            removeAddress: 'Remove Address',
            fullAddress: 'Full Address',
            cityState: 'City/State'
        },
        messages: {
            noAddresses: 'No addresses registered',
            noAddressHint: 'Click "Add Address" to start',
            confirmRemoveAddress: 'Do you want to remove this address?',
            addressAdded: 'Address added successfully',
            addressUpdated: 'Address updated successfully',
            addressRemoved: 'Address removed successfully',
            saveError: 'Error saving address. Please try again.'
        },
        validation: {
            minAddresses: 'At least one address is required'
        }
    },
    contactCollection: {
        titles: {
            main: 'Contact Collection',
            addContact: 'Add Contact',
            editContact: 'Edit Contact'
        },
        labels: {
            addContact: 'Add Contact',
            editContact: 'Edit Contact',
            removeContact: 'Remove Contact',
            contactInfo: 'Contact Information',
            location: 'Location',
            contactMethods: 'Contact Methods'
        },
        messages: {
            noContacts: 'No contacts registered',
            noContactHint: 'Click "Add Contact" to start',
            confirmRemoveContact: 'Do you want to remove this contact?',
            contactAdded: 'Contact added successfully',
            contactUpdated: 'Contact updated successfully',
            contactRemoved: 'Contact removed successfully',
            saveError: 'Error saving contact. Please try again.'
        },
        validation: {
            minContacts: 'At least one contact is required'
        }
    },
    richEditor: {
        bold: 'Bold',
        italic: 'Italic',
        underline: 'Underline',
        strikethrough: 'Strikethrough',
        alignLeft: 'Align Left',
        alignCenter: 'Center',
        alignRight: 'Align Right',
        bulletList: 'Bullet List',
        numberedList: 'Numbered List',
        insertLink: 'Insert Link',
        clearFormat: 'Clear Format',
        fontSize: 'Size',
        fontFamily: 'Font',
        textColor: 'Text Color',
        backgroundColor: 'Background Color',
        subscript: 'Subscript',
        superscript: 'Superscript',
        heading: 'Heading',
        heading1: 'Heading 1',
        heading2: 'Heading 2',
        heading3: 'Heading 3',
        heading4: 'Heading 4',
        heading5: 'Heading 5',
        heading6: 'Heading 6',
        blockquote: 'Quote',
        code: 'Inline Code',
        codeBlock: 'Code Block',
        insertImage: 'Insert Image',
        table: 'Table',
        table2x2: 'Table 2x2',
        table3x3: 'Table 3x3',
        table4x4: 'Table 4x4',
        customTable: 'Custom Table',
        horizontalRule: 'Horizontal Rule',
        lineBreak: 'Line Break',
        indent: 'Increase Indent',
        outdent: 'Decrease Indent',
        undo: 'Undo',
        redo: 'Redo',
        selectAll: 'Select All',
        visualMode: 'Visual Mode',
        htmlMode: 'HTML Mode',
        enterUrl: 'Enter the link URL:',
        enterImageUrl: 'Enter the image URL:',
        enterImageAlt: 'Enter the image alt text:',
        enterRows: 'Number of rows:',
        enterCols: 'Number of columns:',
        characters: 'characters',
        words: 'words',
        placeholder: 'Type your text here...'
    },

    advancedTable: {
        manageColumns: 'Manage Columns',
        manageColumnsTooltip: 'Reorder and hide columns',
        reset: 'Reset',
        resetTooltip: 'Restore default configuration',
        dragToReorder: 'Drag to reorder columns',
        hideColumn: 'Hide column',
        showColumn: 'Show column',
        notifications: {
            loadError: 'Error loading column configuration',
            saveError: 'Error saving column configuration',
            configApplied: 'Column configuration applied successfully',
            configReset: 'Column configuration restored to default'
        }
    },

    dataTable: {
        manageColumns: 'Manage Columns',
        manageColumnsTooltip: 'Reorder and hide columns',
        reset: 'Reset',
        resetTooltip: 'Restore default configuration',
        dragToReorder: 'Drag to reorder columns',
        hideColumn: 'Hide column',
        showColumn: 'Show column',
        pagination: {
            rowsPerPageLabel: 'Rows per page:',
            paginationLabel: '{first}-{last} of {total}',
            selectedRowsLabel: {
                none: 'No rows selected',
                one: '1 row selected',
                many: '{count} rows selected'
            }
        },
        notifications: {
            loadError: 'Error loading column configuration',
            saveError: 'Error saving column configuration',
            configApplied: 'Column configuration applied successfully',
            configReset: 'Column configuration restored to default'
        }
    },
    reports: {
        modal: {
            title: 'Reports',
            reportType: 'Report Type',
            format: 'Format',
            info: 'Information',
            table: 'Table',
            columns: 'Columns',
            columnsVisible: 'visible columns',
            filters: 'Filters',
            filtersApplied: 'applied filters',
            groupColumn: 'Group Column',
            selectGroupColumn: 'Select column to group by',
            summaryColumn: 'Summary Column',
            selectSummaryColumn: 'Select column to summarize',
            generate: 'Generate Report'
        },
        types: {
            simple: 'Simple',
            grouped: 'Simple Grouped by Column',
            summary: 'Simple Summary'
        },
        summary: {
            item: 'Item',
            quantity: 'Quantity',
            percentage: '% Part.',
            total: 'TOTAL'
        },
        formats: {
            excel: 'Excel (.xls)',
            pdf: 'PDF'
        },
        errors: {
            fetchData: 'Error fetching report data',
            generateExcel: 'Error generating Excel report',
            generatePdf: 'Error generating PDF report',
            unsupportedType: 'Unsupported report type',
            unsupportedFormat: 'Unsupported format'
        },
        success: {
            generated: 'Report generated successfully'
        }
    }
}
