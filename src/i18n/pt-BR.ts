export const ptBRDateTimeFormat = {
    short: { day: '2-digit', month: '2-digit', year: 'numeric' },
    long: { day: '2-digit', month: 'long', year: 'numeric' }
}

export const ptBRNumberFormat = {
    currency: {
        style: 'currency',
        currency: 'BRL',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    },
    percent: {
        style: 'percent',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }
}

export const ptBR = {
    themes: {
        light: 'Claro',
        dark: 'Escuro',
        ChangeToLight: 'Mudar para o tema claro',
        ChangeToDark: 'Mudar para o tema escuro'
    },
    locale: {
        changeLanguage: 'Mudar idioma',
        selectLanguage: 'Selecione o idioma'
    },
    titles: {
        app: 'Corpsystem',
        login: 'Login',
        slogan: 'CorpERP - O melhor sistema ERP para sua empresa.',
        copyright: 'Todos os direitos reservados.',
        pageNotFound: 'Página não encontrada'
    },
    messages: {
        pageNotFound: 'A página que você está procurando não foi encontrada.',
        checkRequiredFields: 'Verifique os campos obrigatórios',
        updatedSuccessfully: 'Atualizado com sucesso!',
        createdSuccessfully: 'Criado com sucesso!',
        deletedSuccessfully: 'Excluído com sucesso!',
        activatedSuccessfully: 'Ativado com sucesso!',
        deactivatedSuccessfully: 'Desativado com sucesso!',
        errorSaving: 'Erro ao salvar'
    },
    forms: {
        labels: {
            title: 'Título',
            module: 'Módulo',
            route: 'Rota',
            publishDate: 'Data de Publicação',
            name: 'Nome',
            nameRequired: 'Nome *',
            surname: 'Sobrenome',
            email: 'E-mail',
            password: 'Senha',
            username: 'Nome de usuário',
            searchRoute: 'Buscar rota',
            role: 'Função',
            companyName: 'Razão social',
            companyNameRequired: 'Razão social *',
            legalPerson: 'Pessoa jurídica',
            naturalPerson: 'Pessoa física',
            docLegalPerson: 'CNPJ',
            docNaturalPerson: 'CPF',
            active: 'Ativo',
            indicatorStateEnrollmentRecipient: 'Indicador de IE Destinatário',
            site: 'Site',
            region: 'Região',
            company: 'Empresa',
            lineOfActivity: 'Ramo de atividade',
            typeOfRelationship: 'Tipo de relacionamento',
            dateOfBith: 'Data de nascimento',
            dateOfFounding: 'Data de fundação',
            nickname: 'Apelido',
            maritalStatus: 'Estado civil',
            fantasyName: 'Nome fantasia',
            stateRegistrationStatus: 'Estado da Inscrição Estadual',
            stateRegistration: 'Inscrição Estadual',
            municipalRegistration: 'Inscrição Municipal',
            admin: 'Administrador',
            permissionProfileName: 'Nome do perfil de permissão',
            permissionGroupName: 'Nome do grupo de permissão',
            permissionGroupCode: 'Código do grupo',
            description: 'Descrição',
            abbreviation: 'Abreviação',
            observations: 'Observações',
            permissionGroups: 'Grupos de permissões',
            passwordExpirationDays: 'Dias para expirar senha',
            forceOperatorClosure: 'Obrigar fechamento do operador',
            viewOnlySeller: 'Visualizar dados apenas do vendedor',
            viewOnlyBuyer: 'Visualizar dados apenas do comprador',
            viewOnlyCashier: 'Visualizar dados apenas do caixa',
            viewOnlyCompany: 'Visualizar dados apenas da empresa',
            viewOnlyRepresentative: 'Visualizar dados apenas do representante',
            selectSeller: 'Selecionar vendedor',
            selectBuyer: 'Selecionar comprador',
            selectCashier: 'Selecionar caixa',
            selectCompany: 'Selecionar empresa',
            selectRepresentative: 'Selecionar representante',
            remove: 'Remover',
            smtpServer: 'Servidor de E-mail',
            smtpPort: 'Porta SMTP',
            emailPassword: 'Senha do E-mail',
            useTLS: 'Usar TLS',
            useSSL: 'Usar SSL',
            commission: 'Comissão',
            discountPercentage: 'Percentual de Desconto Permitido',
            discountValue: 'Valor de Desconto Permitido',
            orderPurchaseLimit: 'Limite de Compra do Pedido',
            monthlyPurchaseLimit: 'Limite de Compra do Mês',
            category: 'Categoria',
            paymentCondition: 'Condição de Pagamento',
            paymentModality: 'Modalidade de Pagamento',
            transaction: 'Transação',
            priceTable: 'Tabela de Preço',
            seller: 'Vendedor',
            financialAccount: 'Conta Financeira',
            representative: 'Representante',
            deliveryType: 'Tipo de Entrega',
            freightIndicator: 'Indicador de Frete',
            carrier: 'Transportadora',
            costCenter: 'Centro de Custo',
            paymentMethodGroup: 'Grupo Forma de Pagamento',
            salesBlock: 'Bloqueio de Venda',
            daysForDueNotice: 'Dias para Aviso de Vencimento',
            daysForCollectionNotice: 'Dias para Aviso de Cobrança',
            billingDayLimit: 'Dia Limite para Faturamento',
            maxDiscountPercentage: 'Percentual de Desconto Máximo',
            minimumBillingValue: 'Valor do Faturamento Mínimo',
            creditLimit: 'Limite de Crédito',
            morning: 'Manhã',
            afternoon: 'Tarde',
            evening: 'Noite',
            start: 'Início',
            end: 'Fim',
            noResults: 'Nenhum resultado encontrado',
            uniqueIdentifier: 'Identificador único do registro',
            itemDescription: 'Descrição detalhada do item',
            categoryType: 'Categoria ou tipo de classificação',
            textContent: 'Conteúdo textual principal',
            activeStatus: 'Status de ativação do item',
            currentStatus: 'Estado atual do registro',
            availableActions: 'Ações disponíveis para o item',
            fullName: 'Nome completo da pessoa',
            emailAddress: 'Endereço de email',
            phoneNumber: 'Número de telefone',
            dateField: 'Campo de data',
            monetaryValue: 'Valor monetário',
            numericQuantity: 'Quantidade numérica',
            additionalNotes: 'Observações adicionais',
            creationDate: 'Data de criação',
            lastUpdate: 'Última atualização',
            tableColumn: 'Coluna da tabela',
            department: 'Departamento',
            sector: 'Setor',
            position: 'Cargo',
            admissionDate: 'Data de Admissão',
            pis: 'PIS',
            voterCard: 'Título de Eleitor',
            workCard: 'Carteira de Trabalho e Social',
            motherName: 'Nome da Mãe',
            male: 'Masculino',
            female: 'Feminino',
            gender: 'Gênero',
            qualityResponsible: 'Responsável de Qualidade',
            currencySymbol: 'R$',
            currencyAcronym: 'BRL',
            buyer: 'Comprador',
            hourCost: 'Valor Custo Hora',
            addressType: 'Tipo de Endereço',
            country: 'País',
            zipCode: 'CEP',
            state: 'Estado',
            city: 'Cidade',
            street: 'Logradouro',
            number: 'Número',
            neighborhood: 'Bairro',
            complement: 'Complemento',
            referencePoint: 'Ponto de Referência',
            documentType: 'Tipo de Documento',
            documentNumber: 'Número do Documento',
            occupation: 'Ocupação',
            birthDate: 'Data de Nascimento',
            phoneType: 'Tipo de Telefone',
            areaCode: 'DDD',
            extension: 'Ramal',
            phone: 'Telefone',
            businessPartner: 'Parceiro de Negócio que Indicou',
            mainContact: 'Contato Principal da Conta/Prospect',
            employeeCount: 'Número de Funcionários',
            annualRevenue: 'Faturamento Anual',
            type: 'Tipo',
            content: 'Conteúdo',
            column: 'Coluna',
            id: 'ID',
            actions: 'Ações',
            search: 'Pesquisar',
            status: 'Status',
            inactive: 'Inativo',
            brand: 'Marca',
            model: 'Modelo',
            difficultyLevel: 'Nível de Dificuldade',
            attachment: 'Anexo',
            attachmentPreview: 'Pré-visualização do anexo',
            imageLoadError: 'Erro ao carregar imagem',
            selectFile: 'Selecionar arquivo',
            rememberMe: 'Lembrar-me',
            forgotPassword: 'Esqueceu a senha?',
            or: 'ou',
            loginWith: 'Faça login com',
            loading: 'Carregando...',
            typeToSearch: 'Digite para pesquisar',
            code: 'Código',
            codeDescription: 'Código/Descrição',
            firstName: 'Nome',
            lastName: 'Sobrenome',
            nameEmail: 'Nome/E-mail',
            countryAbbreviation: 'Sigla',
            countryCode: 'Código',
            stateAbbreviation: 'Sigla do Estado',
            ibgeCode: 'Código IBGE',
            iss: 'ISS (%)'
        },
        masks: {
            docLegalPerson: '##.###.###/####-##',
            docNaturalPerson: '###.###.###-##'
        },
        addressTypes: {
            residential: 'Residencial',
            commercial: 'Comercial',
            delivery: 'Entrega',
            billing: 'Cobrança',
            other: 'Outro'
        },
        documentTypes: {
            cpf: 'CPF',
            cnpj: 'CNPJ',
            rg: 'RG',
            passport: 'Passaporte',
            other: 'Outro'
        },
        occupations: {
            manager: 'Gerente',
            director: 'Diretor',
            analyst: 'Analista',
            developer: 'Desenvolvedor',
            assistant: 'Assistente',
            coordinator: 'Coordenador',
            consultant: 'Consultor',
            other: 'Outro'
        },
        departments: {
            administrative: 'Administrativo',
            financial: 'Financeiro',
            commercial: 'Comercial',
            marketing: 'Marketing',
            humanResources: 'Recursos Humanos',
            technology: 'Tecnologia',
            operations: 'Operações',
            logistics: 'Logística',
            other: 'Outro'
        },
        phoneTypes: {
            mobile: 'Celular',
            home: 'Residencial',
            work: 'Comercial',
            other: 'Outro'
        },
        messages: {
            onlyStatusField: 'Este formulário contém apenas o campo de status.',
            noPhones:
                'Nenhum telefone cadastrado. Clique no botão "Adicionar Telefone" para começar.',
            noEmails:
                'Nenhum email cadastrado. Clique no botão "Adicionar Email" para começar.'
        },
        titles: {
            description: 'Descrição',
            userRegistration: 'Cadastro de usuário',
            userInfo: 'Informações do usuário',
            accessAndPermissions: 'Acesso e permissões',
            restrictions: 'Restrições',
            emailConfigs: 'Configurações de e-mail',
            details: 'Detalhes',
            errorsFound: 'Erros encontrados',
            permissionsManagement: 'Gerenciamento de permissões',
            permissionGroupManagement: 'Gerenciamento de grupo de permissões',
            modulePermissions: 'Permissões por módulo',
            basicInfo: 'Informações básicas',
            userPreferences: 'Preferências do usuário',
            userPermissions: 'Permissões do usuário',
            permissionGroups: 'Grupos de permissões',
            viewRestrictions: 'Restrições de visualização',
            customPermissions: 'Permissões avulsas',
            userCredentials: 'Credenciais do usuário',
            emailConfig: 'Configuração de E-mail',
            basicForm: 'Formulário Básico',
            helpForm: 'Formulário de Ajuda',
            sellerForm: 'Formulário de Vendedor',
            buyerForm: 'Formulário de Comprador',
            customerForm: 'Formulário de Cliente',
            supplierForm: 'Formulário de Fornecedor',
            employeeForm: 'Formulário de Funcionário',
            driverForm: 'Formulário de Motorista',
            technicianForm: 'Formulário de Técnico',
            addressForm: 'Formulário de Endereço',
            documentForm: 'Formulário de Documento',
            contactForm: 'Formulário de Contato',
            crmProspectForm: 'Formulário de CRM/Prospect',
            professionalInfo: 'Informações Profissionais',
            limitsAndSettings: 'Limites e Configurações',
            businessHours: 'Horários de Funcionamento',
            additionalInfo: 'Informações Adicionais',
            phones: 'Telefones',
            emails: 'Emails',
            standardizedTextForm: 'Formulário de Texto Padronizado',
            newsForm: 'Formulário de Novidades',
            content: 'Conteúdo',
            brandModel: 'Vincular Modelo à Marca',
            modelInfo: 'Informações do Modelo',
            ncmForm: 'Formulário NCM',
            categoriaCNHForm: 'Formulário de Categoria CNH',
            cityForm: 'Formulário de Cidade'
        },
        sections: {
            basicInfo: 'Informações Básicas',
            location: 'Localização',
            addressDetails: 'Detalhes do Endereço'
        },
        placeholders: {
            standardizedTextContent:
                'Digite o conteúdo do texto padronizado...',
            newsDescription: 'Digite o conteúdo da novidade...',
            selectModuleFirst: 'Selecione um módulo primeiro',
            selectRoute: 'Selecione uma rota',
            ncmCode: 'Digite o código NCM (formato: 0000.00.00)',
            ncmDescription: 'Digite a descrição do NCM',
            helpContent: 'Digite o conteúdo da ajuda...'
        },
        hints: {
            difficultyLevel: 'Nível de 1 a 10',
            attachment:
                'Máximo 1 arquivo. Formatos: jpg, jpeg, png, pdf, pfx, txt, crt, ret, doc, docx, xls, xlsx, msg. Tamanho máximo: 2MB'
        },
        errors: {
            loadModelsError: 'Erro ao carregar modelos',
            loadCountriesError: 'Erro ao carregar países',
            loadStatesError: 'Erro ao carregar estados',
            maxFileSize: 'Arquivo muito grande. Tamanho máximo: 2MB',
            invalidFileType: 'Tipo de arquivo não permitido'
        }
    },
    buttons: {
        login: 'Entrar',
        goHome: 'Ir para a página inicial',
        goBack: 'Voltar',
        confirm: 'Confirmar',
        cancel: 'Cancelar',
        apply: 'Aplicar',
        reset: 'Resetar',
        addPhone: 'Adicionar Telefone',
        addEmail: 'Adicionar Email',
        testConnection: 'Testar Conexão',
        edit: 'Editar',
        viewProfile: 'Ver Perfil',
        add: 'Adicionar',
        delete: 'Excluir',
        deleteSelected: 'Excluir selecionados ({count})',
        deleteSelectedDescription: 'Excluir os itens selecionados',
        generateReport: 'Gerar Relatório',
        generateReportDescription: 'Gerar relatório dos dados da tabela',
        refresh: 'Atualizar',
        clear: 'Limpar',
        filter: 'Filtrar',
        save: 'Salvar',
        export: 'Exportar',
        import: 'Importar',
        actions: 'Ações',
        linkModel: 'Vincular modelo',
        close: 'Fechar',
        loadMore: 'Carregar mais',
        view: 'Visualizar',
        remove: 'Remover',
        editSelected: 'Editar selecionado',
        editSelectedDescription: 'Editar o item selecionado',
        activate: 'Ativar',
        deactivate: 'Desativar',
        logout: 'Sair',
        activateDescription: 'Ativar o item selecionado',
        deactivateDescription: 'Desativar o item selecionado',
        viewAttachment: 'Visualizar anexo',
        viewAttachmentDescription: 'Visualizar anexo do item selecionado',
        linkModelToSelected: 'Vincular modelo',
        linkModelDescription: 'Vincular modelo ao item selecionado',
        back: 'Voltar',
        backToPage: 'Voltar à página',
        retry: 'Tentar novamente'
    },
    help: {
        tooltip: 'Ajuda',
        title: 'Ajuda do Sistema',
        subtitle: 'Documentação e guias de uso',
        loading: 'Carregando ajuda...',
        error: 'Erro ao carregar ajuda',
        loadError: 'Não foi possível carregar o conteúdo de ajuda',
        noContent: 'Nenhum conteúdo de ajuda disponível',
        noContentDescription:
            'O conteúdo de ajuda para esta página ainda não está disponível',
        aboutPage: 'Sobre a Página',
        aboutPageDescription: 'Ajuda específica desta página',
        noHelpAvailable: 'Nenhuma ajuda disponível para esta página',
        news: 'Novidades',
        newsDescription: 'Últimas atualizações do sistema'
    },
    pages: {
        titles: {
            home: 'Início',
            login: 'Login',
            pageNotFound: 'Página não encontrada',
            dev: 'Desenvolvimento',
            profile: 'Perfil',
            newsView: 'Novidades',
            standardizedTexts: 'Textos padronizados',
            generalRegistration: 'Cadastros gerais',
            productQuery: 'Estoque',
            productBrand: 'Marca',
            productModel: 'Modelo',
            ncm: 'NCM',
            accessConfig: 'Configuração de acesso',
            accessUsers: 'Usuários',
            country: 'País',
            categoryCNH: 'Categoria da CNH',
            state: 'Estado',
            city: 'Cidade',
            region: 'Região',
            addressType: 'Tipo de endereço',
            documentType: 'Tipo de documento',
            paymentGroup: 'Grupo de pagamento',
            activitySector: 'Ramo de atividade',
            historicLaunch: 'Histórico de lançamento',
            family: 'Família',
            group: 'Grupo',
            subgroup: 'Subgrupo',
            sale: 'Venda',
            customerCategory: 'Categoria de cliente',
            discountGroup: 'Grupo de desconto',
            project: 'Projeto',
            application: 'Aplicação',
            installationType: 'Tipo de instalação',
            profileType: 'Tipo de perfil',
            materialType: 'Tipo de material',
            projectGroup: 'Grupo de projeto',
            materialGroup: 'Grupo de material',
            os: 'OS',
            title: 'Título',
            equipmentPart: 'Parte do equipamento',
            defect: 'Defeito',
            quality: 'Qualidade',
            cause: 'Causa',
            equipmentFamily: 'Família do equipamento',
            finance: 'Financeiro',
            billingPortfolio: 'Carteira de cobrança',
            production: 'Produção',
            checklist: 'Checklist',
            operation: 'Operação',
            nonConformity: 'Não conformidade',
            config: 'Configuração',
            userProfile: 'Perfil de usuário',
            module: 'Módulo',
            rh: 'RH',
            position: 'Cargo',
            education: 'Escolaridade',
            specialty: 'Especialidade',
            skill: 'Habilidade',
            department: 'Departamento',
            origin: 'Origem',
            crm: 'CRM',
            opportunity: 'Oportunidade',
            news: 'Novidades',
            help: 'Ajudas',
            settings: 'Configurações',
            common: {
                register: 'Cadastro'
            }
        },
        standardizedTexts: {
            title: 'Textos padronizados',
            subtitle: 'Gerencie os textos padronizados do sistema',
            list: 'Lista de textos padronizados',
            createTitle: 'Novo texto padronizado',
            editTitle: 'Editar texto padronizado',
            messages: {
                createSuccess: 'Texto padronizado criado com sucesso!',
                updateSuccess: 'Texto padronizado atualizado com sucesso!',
                deleteSuccess: 'Texto padronizado excluído com sucesso!',
                deleteMultipleSuccess:
                    'Textos padronizados excluídos com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do texto padronizado',
                saveError: 'Erro ao salvar texto padronizado',
                deleteError: 'Erro ao excluir texto padronizado',
                deleteMultipleError: 'Erro ao excluir textos padronizados',
                notFound: 'Texto padronizado não encontrado'
            }
        },
        news: {
            title: 'Novidades',
            subtitle: 'Gerenciar novidades do sistema',
            list: 'Lista de novidades',
            createTitle: 'Nova novidade',
            editTitle: 'Editar novidade',
            messages: {
                createSuccess: 'Novidade criada com sucesso!',
                updateSuccess: 'Novidade atualizada com sucesso!',
                deleteSuccess: 'Novidade excluída com sucesso!',
                deleteMultipleSuccess: 'Novidades excluídas com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados da novidade',
                saveError: 'Erro ao salvar novidade',
                deleteError: 'Erro ao excluir novidade',
                deleteMultipleError: 'Erro ao excluir novidades',
                notFound: 'Novidade não encontrada'
            }
        },
        newsView: {
            title: 'Novidades do Sistema',
            subtitle: 'Fique por dentro das últimas atualizações e melhorias',
            noNews: 'Nenhuma novidade encontrada',
            noNewsDescription:
                'Tente ajustar os filtros ou verifique novamente mais tarde',
            filters: {
                status: 'Status',
                module: 'Módulo',
                search: 'Buscar novidades...',
                period: 'Período',
                sortBy: 'Ordenar por',
                startDate: 'Data início',
                endDate: 'Data fim',
                searchContent: 'Buscar no conteúdo...',
                allModules: 'Todos os módulos'
            },
            periods: {
                all: 'Todos os períodos',
                week: 'Última semana',
                month: 'Último mês',
                quarter: 'Últimos 3 meses',
                year: 'Último ano'
            },
            sorting: {
                dateDesc: 'Mais recentes primeiro',
                dateAsc: 'Mais antigas primeiro',
                titleAsc: 'Título A-Z',
                titleDesc: 'Título Z-A',
                moduleAsc: 'Módulo A-Z',
                statusNew: 'Não lidas primeiro'
            },
            status: {
                all: 'Todas',
                new: 'Novas',
                viewed: 'Vistas',
                newLabel: 'Nova',
                viewedLabel: 'Visualizada'
            },
            actions: {
                markAsViewed: 'Marcar como vista',
                markAllAsViewed: 'Marcar Todas',
                clearFilters: 'Limpar',
                viewDetails: 'Ver detalhes',
                close: 'Fechar'
            },
            messages: {
                markedAsViewed: 'Novidade marcada como vista!',
                allMarkedAsViewed:
                    'Todas as novidades foram marcadas como vistas!',
                markViewedError: 'Erro ao marcar novidade como vista',
                markAllViewedError: 'Erro ao marcar novidades como vistas'
            },
            errors: {
                loadError: 'Erro ao carregar novidades'
            }
        },
        help: {
            title: 'Gerenciamento de Ajudas',
            createTitle: 'Nova Ajuda',
            editTitle: 'Editar Ajuda',
            subtitle: 'Gerencie o conteúdo de ajuda do sistema',
            messages: {
                createdSuccessfully: 'Ajuda criada com sucesso!',
                updatedSuccessfully: 'Ajuda atualizada com sucesso!',
                deletedSuccessfully: 'Ajuda excluída com sucesso!',
                activatedSuccessfully: 'Ajuda ativada com sucesso!',
                deactivatedSuccessfully: 'Ajuda desativada com sucesso!',
                errorSaving: 'Erro ao salvar ajuda',
                errorDeleting: 'Erro ao excluir ajuda',
                errorUpdating: 'Erro ao atualizar ajuda',
                errorLoading: 'Erro ao carregar ajudas'
            }
        },

        reports: {
            modal: {
                title: 'Gerar Relatório',
                reportType: 'Tipo de Relatório',
                format: 'Formato',
                generate: 'Gerar Relatório',
                info: 'Informações do Relatório',
                table: 'Tabela',
                columns: 'Colunas',
                columnsVisible: 'colunas visíveis',
                filters: 'Filtros',
                filtersApplied: 'Filtros aplicados'
            },
            types: {
                simple: 'Relatório Simples',
                simpleDescription: 'Relatório básico com dados da tabela'
            },
            formats: {
                excel: 'Excel (.xls)',
                pdf: 'PDF'
            },
            errors: {
                fetchData: 'Erro ao buscar dados para o relatório',
                generateExcel: 'Erro ao gerar relatório Excel',
                generatePdf: 'Erro ao gerar relatório PDF',
                unsupportedType: 'Tipo de relatório não suportado',
                unsupportedFormat: 'Formato não suportado'
            },
            success: {
                generated: 'Relatório gerado com sucesso!'
            }
        },

        helpView: {
            title: 'Ajuda do Sistema',
            subtitle: 'Documentação e orientações para uso da página',
            noHelp: 'Nenhuma ajuda disponível',
            noHelpDescription:
                'Não há conteúdo de ajuda disponível para esta página',
            lastUpdate: 'Última atualização',
            errors: {
                loadError: 'Erro ao carregar conteúdo de ajuda'
            }
        },
        home: {
            welcome: {
                title: 'Bem-vindo ao CorpERP',
                subtitle:
                    'Tenha um ótimo dia de trabalho! Aqui estão as últimas novidades da empresa.',
                dashboardButton: 'Dashboard',
                helpButton: 'Ajuda'
            },
            noticeBoard: {
                title: 'Quadro de Avisos',
                addNotice: 'Adicionar Aviso',
                addNoticeTooltip: 'Adicionar Aviso',
                emptyState:
                    'Nenhum aviso no momento. Que tal adicionar o primeiro?',
                priority: {
                    high: 'Alta',
                    medium: 'Média',
                    low: 'Baixa'
                }
            },
            statistics: {
                title: 'Estatísticas',
                users: 'Usuários',
                notices: 'Avisos',
                pending: 'Pendentes',
                completed: 'Concluídas'
            },
            addNoticeModal: {
                title: 'Novo Aviso',
                titleField: 'Título do Aviso',
                priorityField: 'Prioridade',
                contentField: 'Conteúdo',
                authorField: 'Autor',
                cancelButton: 'Cancelar',
                publishButton: 'Publicar',
                successMessage: 'Aviso publicado com sucesso!',
                validation: {
                    titleRequired: 'Título é obrigatório',
                    contentRequired: 'Conteúdo é obrigatório',
                    authorRequired: 'Autor é obrigatório'
                }
            },
            editNoticeModal: {
                title: 'Editar Aviso',
                titleField: 'Título do Aviso',
                priorityField: 'Prioridade',
                contentField: 'Conteúdo',
                authorField: 'Autor',
                cancelButton: 'Cancelar',
                saveButton: 'Salvar',
                successMessage: 'Aviso atualizado com sucesso!',
                validation: {
                    titleRequired: 'Título é obrigatório',
                    contentRequired: 'Conteúdo é obrigatório',
                    authorRequired: 'Autor é obrigatório'
                }
            },
            deleteNoticeDialog: {
                title: 'Confirmar Exclusão',
                message: 'Tem certeza que deseja excluir o aviso "{title}"?',
                cancelButton: 'Cancelar',
                confirmButton: 'Excluir',
                successMessage: 'Aviso excluído com sucesso!'
            }
        },
        productBrands: {
            title: 'Marcas',
            subtitle: 'Gerencie as marcas do sistema',
            list: 'Lista de marcas',
            createTitle: 'Nova marca',
            editTitle: 'Editar marca',
            messages: {
                createSuccess: 'Marca criada com sucesso!',
                updateSuccess: 'Marca atualizada com sucesso!',
                deleteSuccess: 'Marca excluída com sucesso!',
                deleteMultipleSuccess: 'Marcas excluídas com sucesso!',
                deactivateSuccess: 'Marca desativada com sucesso!',
                activateSuccess: 'Marca ativada com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados da marca',
                saveError: 'Erro ao salvar marca',
                deleteError: 'Erro ao excluir marca',
                deleteMultipleError: 'Erro ao excluir marcas',
                notFound: 'Marca não encontrada',
                duplicateDescription: 'Já existe uma marca com este nome'
            }
        },
        productModels: {
            title: 'Modelos',
            subtitle: 'Gerencie os modelos do sistema',
            list: 'Lista de modelos',
            createTitle: 'Novo modelo',
            editTitle: 'Editar modelo',
            messages: {
                createSuccess: 'Modelo criado com sucesso!',
                updateSuccess: 'Modelo atualizado com sucesso!',
                deleteSuccess: 'Modelo excluído com sucesso!',
                deleteMultipleSuccess: 'Modelos excluídos com sucesso!',
                activateSuccess: 'Modelo ativado com sucesso!',
                deactivateSuccess: 'Modelo desativado com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do modelo',
                saveError: 'Erro ao salvar modelo',
                deleteError: 'Erro ao excluir modelo',
                deleteMultipleError: 'Erro ao excluir modelos',
                notFound: 'Modelo não encontrado',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        ncm: {
            title: 'NCM',
            subtitle: 'Gerencie os códigos NCM do sistema',
            list: 'Lista de NCMs',
            createTitle: 'Novo NCM',
            editTitle: 'Editar NCM',
            form: {
                title: 'Formulário NCM'
            },
            messages: {
                createSuccess: 'NCM criado com sucesso!',
                updateSuccess: 'NCM atualizado com sucesso!',
                deleteSuccess: 'NCM excluído com sucesso!',
                deleteMultipleSuccess: 'NCMs excluídos com sucesso!',
                activateSuccess: 'NCM ativado com sucesso!',
                deactivateSuccess: 'NCM desativado com sucesso!',
                exportNotImplemented:
                    'Funcionalidade de exportação ainda não implementada'
            },
            errors: {
                loadError: 'Erro ao carregar dados do NCM',
                saveError: 'Erro ao salvar NCM',
                deleteError: 'Erro ao excluir NCM',
                deleteMultipleError: 'Erro ao excluir NCMs',
                notFound: 'NCM não encontrado'
            }
        },
        users: {
            title: 'Usuários',
            subtitle: 'Gerencie os usuários do sistema',
            list: 'Lista de usuários',
            errors: {
                loadError: 'Erro ao carregar dados dos usuários'
            }
        },
        countries: {
            title: 'Países',
            subtitle: 'Gerencie os países do sistema',
            list: 'Lista de países',
            createTitle: 'Novo país',
            editTitle: 'Editar país',
            form: {
                title: 'Formulário País'
            },
            messages: {
                createSuccess: 'País criado com sucesso!',
                updateSuccess: 'País atualizado com sucesso!',
                deleteSuccess: 'País excluído com sucesso!',
                deleteMultipleSuccess: 'Países excluídos com sucesso!',
                exportNotImplemented:
                    'Funcionalidade de exportação ainda não implementada'
            },
            errors: {
                loadError: 'Erro ao carregar dados do país',
                saveError: 'Erro ao salvar país',
                deleteError: 'Erro ao excluir país',
                deleteMultipleError: 'Erro ao excluir países',
                notFound: 'País não encontrado'
            }
        },
        region: {
            title: 'Regiões',
            subtitle: 'Gerencie as regiões do sistema',
            list: 'Lista de regiões',
            createTitle: 'Nova região',
            editTitle: 'Editar região',
            form: {
                title: 'Formulário Região'
            },
            messages: {
                createSuccess: 'Região criada com sucesso!',
                updateSuccess: 'Região atualizada com sucesso!',
                deleteSuccess: 'Região excluída com sucesso!',
                deleteMultipleSuccess: 'Regiões excluídas com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados da região',
                saveError: 'Erro ao salvar região',
                deleteError: 'Erro ao excluir região',
                deleteMultipleError: 'Erro ao excluir regiões',
                notFound: 'Região não encontrada',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        states: {
            title: 'Estados',
            subtitle: 'Gerencie os estados do sistema',
            list: 'Lista de estados',
            createTitle: 'Novo estado',
            editTitle: 'Editar estado',
            form: {
                title: 'Formulário de Estado'
            },
            messages: {
                createSuccess: 'Estado criado com sucesso!',
                updateSuccess: 'Estado atualizado com sucesso!',
                deleteSuccess: 'Estado excluído com sucesso!',
                deleteMultipleSuccess: 'Estados excluídos com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do estado',
                saveError: 'Erro ao salvar estado',
                deleteError: 'Erro ao excluir estado',
                deleteMultipleError: 'Erro ao excluir estados',
                notFound: 'Estado não encontrado'
            }
        },
        cities: {
            title: 'Cidades',
            subtitle: 'Gerencie as cidades do sistema',
            list: 'Lista de cidades',
            createTitle: 'Nova cidade',
            editTitle: 'Editar cidade',
            form: {
                title: 'Formulário Cidade'
            },
            messages: {
                createSuccess: 'Cidade criada com sucesso!',
                updateSuccess: 'Cidade atualizada com sucesso!',
                deleteSuccess: 'Cidade excluída com sucesso!',
                deleteMultipleSuccess: 'Cidades excluídas com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados da cidade',
                saveError: 'Erro ao salvar cidade',
                deleteError: 'Erro ao excluir cidade',
                deleteMultipleError: 'Erro ao excluir cidades',
                notFound: 'Cidade não encontrada'
            }
        },
        addressType: {
            title: 'Tipos de Endereço',
            subtitle: 'Gerencie os tipos de endereço do sistema',
            list: 'Lista de tipos de endereço',
            createTitle: 'Novo tipo de endereço',
            editTitle: 'Editar tipo de endereço',
            form: {
                title: 'Formulário Tipo de Endereço'
            },
            messages: {
                createSuccess: 'Tipo de endereço criado com sucesso!',
                updateSuccess: 'Tipo de endereço atualizado com sucesso!',
                deleteSuccess: 'Tipo de endereço excluído com sucesso!',
                deleteMultipleSuccess:
                    'Tipos de endereço excluídos com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do tipo de endereço',
                saveError: 'Erro ao salvar tipo de endereço',
                deleteError: 'Erro ao excluir tipo de endereço',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        categoriaCNH: {
            title: 'Categoria CNH',
            subtitle: 'Gerencie as categorias de CNH do sistema',
            list: 'Lista de categorias CNH',
            createTitle: 'Nova categoria CNH',
            editTitle: 'Editar categoria CNH',
            form: {
                title: 'Formulário Categoria CNH'
            },
            messages: {
                createSuccess: 'Categoria CNH criada com sucesso!',
                updateSuccess: 'Categoria CNH atualizada com sucesso!',
                deleteSuccess: 'Categoria CNH excluída com sucesso!',
                deleteMultipleSuccess: 'Categorias CNH excluídas com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados da categoria CNH',
                saveError: 'Erro ao salvar categoria CNH',
                deleteError: 'Erro ao excluir categoria CNH',
                deleteMultipleError: 'Erro ao excluir categorias CNH',
                notFound: 'Categoria CNH não encontrada'
            }
        },
        documentType: {
            title: 'Tipos de Documento',
            subtitle: 'Gerencie os tipos de documento do sistema',
            list: 'Lista de tipos de documento',
            createTitle: 'Novo tipo de documento',
            editTitle: 'Editar tipo de documento',
            form: {
                title: 'Formulário Tipo de Documento'
            },
            messages: {
                createSuccess: 'Tipo de documento criado com sucesso!',
                updateSuccess: 'Tipo de documento atualizado com sucesso!',
                deleteSuccess: 'Tipo de documento excluído com sucesso!',
                deleteMultipleSuccess:
                    'Tipos de documento excluídos com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do tipo de documento',
                saveError: 'Erro ao salvar tipo de documento',
                deleteError: 'Erro ao excluir tipo de documento',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        paymentGroup: {
            title: 'Grupos de Pagamento',
            subtitle: 'Gerencie os grupos de pagamento do sistema',
            list: 'Lista de grupos de pagamento',
            createTitle: 'Novo grupo de pagamento',
            editTitle: 'Editar grupo de pagamento',
            form: {
                title: 'Formulário Grupo de Pagamento'
            },
            messages: {
                createSuccess: 'Grupo de pagamento criado com sucesso!',
                updateSuccess: 'Grupo de pagamento atualizado com sucesso!',
                deleteSuccess: 'Grupo de pagamento excluído com sucesso!',
                deleteMultipleSuccess:
                    'Grupos de pagamento excluídos com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do grupo de pagamento',
                saveError: 'Erro ao salvar grupo de pagamento',
                deleteError: 'Erro ao excluir grupo de pagamento',
                deleteMultipleError: 'Erro ao excluir grupos de pagamento',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        activitySector: {
            title: 'Ramo de Atividade',
            subtitle: 'Gerencie os ramos de atividade do sistema',
            list: 'Lista de ramos de atividade',
            createTitle: 'Novo ramo de atividade',
            editTitle: 'Editar ramo de atividade',
            form: {
                title: 'Formulário Ramo de Atividade'
            },
            messages: {
                createSuccess: 'Ramo de atividade criado com sucesso!',
                updateSuccess: 'Ramo de atividade atualizado com sucesso!',
                deleteSuccess: 'Ramo de atividade excluído com sucesso!',
                deleteMultipleSuccess:
                    'Ramos de atividade excluídos com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do ramo de atividade',
                saveError: 'Erro ao salvar ramo de atividade',
                deleteError: 'Erro ao excluir ramo de atividade',
                deleteMultipleError: 'Erro ao excluir ramos de atividade',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        historicLaunch: {
            title: 'Lançamentos de Históricos',
            subtitle: 'Gerencie os lançamentos de históricos do sistema',
            list: 'Lista de lançamentos de históricos',
            createTitle: 'Novo lançamento de histórico',
            editTitle: 'Editar lançamento de histórico',
            form: {
                title: 'Formulário Lançamento de Histórico'
            },
            messages: {
                createSuccess: 'Lançamento de histórico criado com sucesso!',
                updateSuccess:
                    'Lançamento de histórico atualizado com sucesso!',
                deleteSuccess: 'Lançamento de histórico excluído com sucesso!',
                deleteMultipleSuccess:
                    'Lançamentos de históricos excluídos com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do lançamento de histórico',
                saveError: 'Erro ao salvar lançamento de histórico',
                deleteError: 'Erro ao excluir lançamento de histórico',
                deleteMultipleError:
                    'Erro ao excluir lançamentos de históricos',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        family: {
            title: 'Famílias',
            subtitle: 'Gerencie as famílias do sistema',
            list: 'Lista de famílias',
            createTitle: 'Nova família',
            editTitle: 'Editar família',
            form: {
                title: 'Formulário Família'
            },
            messages: {
                createSuccess: 'Família criada com sucesso!',
                updateSuccess: 'Família atualizada com sucesso!',
                deleteSuccess: 'Família excluída com sucesso!',
                deleteMultipleSuccess: 'Famílias excluídas com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados da família',
                saveError: 'Erro ao salvar família',
                deleteError: 'Erro ao excluir família',
                deleteMultipleError: 'Erro ao excluir famílias',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.',
                notFound: 'Família não encontrada'
            }
        },
        group: {
            title: 'Grupo',
            subtitle: 'Gerencie os grupos do sistema',
            list: 'Lista de grupos',
            createTitle: 'Novo grupo',
            editTitle: 'Editar grupo',
            form: {
                title: 'Formulário Grupo'
            },
            messages: {
                createSuccess: 'Grupo criado com sucesso!',
                updateSuccess: 'Grupo atualizado com sucesso!',
                deleteSuccess: 'Grupo excluído com sucesso!',
                deleteMultipleSuccess: 'Grupos excluídos com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do grupo',
                saveError: 'Erro ao salvar grupo',
                deleteError: 'Erro ao excluir grupo',
                deleteMultipleError: 'Erro ao excluir grupos',
                notFound: 'Grupo não encontrado',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        subGroup: {
            title: 'Subgrupo',
            subtitle: 'Gerencie os subgrupos do sistema',
            list: 'Lista de subgrupos',
            createTitle: 'Novo subgrupo',
            editTitle: 'Editar subgrupo',
            form: {
                title: 'Formulário Subgrupo'
            },
            messages: {
                createSuccess: 'Subgrupo criado com sucesso!',
                updateSuccess: 'Subgrupo atualizado com sucesso!',
                deleteSuccess: 'Subgrupo excluído com sucesso!',
                deleteMultipleSuccess: 'Subgrupos excluídos com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do subgrupo',
                saveError: 'Erro ao salvar subgrupo',
                deleteError: 'Erro ao excluir subgrupo',
                deleteMultipleError: 'Erro ao excluir subgrupos',
                notFound: 'Subgrupo não encontrado',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        customerCategory: {
            title: 'Categoria de Cliente',
            subtitle: 'Gerencie as categorias de clientes do sistema',
            list: 'Lista de categorias de clientes',
            createTitle: 'Nova categoria de cliente',
            editTitle: 'Editar categoria de cliente',
            form: {
                title: 'Formulário Categoria de Cliente'
            },
            messages: {
                createSuccess: 'Categoria de cliente criada com sucesso!',
                updateSuccess: 'Categoria de cliente atualizada com sucesso!',
                deleteSuccess: 'Categoria de cliente excluída com sucesso!',
                deleteMultipleSuccess:
                    'Categorias de clientes excluídas com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados da categoria de cliente',
                saveError: 'Erro ao salvar categoria de cliente',
                deleteError: 'Erro ao excluir categoria de cliente',
                deleteMultipleError: 'Erro ao excluir categorias de clientes',
                notFound: 'Categoria de cliente não encontrada',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        discountGroup: {
            title: 'Grupo de Desconto',
            subtitle: 'Gerencie os grupos de desconto do sistema',
            list: 'Lista de grupos de desconto',
            createTitle: 'Novo grupo de desconto',
            editTitle: 'Editar grupo de desconto',
            form: {
                title: 'Formulário Grupo de Desconto'
            },
            messages: {
                createSuccess: 'Grupo de desconto criado com sucesso!',
                updateSuccess: 'Grupo de desconto atualizado com sucesso!',
                deleteSuccess: 'Grupo de desconto excluído com sucesso!',
                deleteMultipleSuccess:
                    'Grupos de desconto excluídos com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do grupo de desconto',
                saveError: 'Erro ao salvar grupo de desconto',
                deleteError: 'Erro ao excluir grupo de desconto',
                deleteMultipleError: 'Erro ao excluir grupos de desconto',
                notFound: 'Grupo de desconto não encontrado',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        application: {
            title: 'Aplicação',
            subtitle: 'Gerencie as aplicações do sistema',
            list: 'Lista de aplicações',
            createTitle: 'Nova aplicação',
            editTitle: 'Editar aplicação',
            form: {
                title: 'Formulário Aplicação'
            },
            messages: {
                createSuccess: 'Aplicação criada com sucesso!',
                updateSuccess: 'Aplicação atualizada com sucesso!',
                deleteSuccess: 'Aplicação excluída com sucesso!',
                deleteMultipleSuccess: 'Aplicações excluídas com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados da aplicação',
                saveError: 'Erro ao salvar aplicação',
                deleteError: 'Erro ao excluir aplicação',
                deleteMultipleError: 'Erro ao excluir aplicações',
                notFound: 'Aplicação não encontrada',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        installationType: {
            title: 'Tipo de Instalação',
            subtitle: 'Gerencie os tipos de instalação do sistema',
            list: 'Lista de tipos de instalação',
            createTitle: 'Novo tipo de instalação',
            editTitle: 'Editar tipo de instalação',
            form: {
                title: 'Formulário Tipo de Instalação'
            },
            messages: {
                createSuccess: 'Tipo de instalação criado com sucesso!',
                updateSuccess: 'Tipo de instalação atualizado com sucesso!',
                deleteSuccess: 'Tipo de instalação excluído com sucesso!',
                deleteMultipleSuccess:
                    'Tipos de instalação excluídos com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do tipo de instalação',
                saveError: 'Erro ao salvar tipo de instalação',
                deleteError: 'Erro ao excluir tipo de instalação',
                deleteMultipleError: 'Erro ao excluir tipos de instalação',
                notFound: 'Tipo de instalação não encontrado',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        profileType: {
            title: 'Tipo de Perfil',
            subtitle: 'Gerencie os tipos de perfil do sistema',
            list: 'Lista de tipos de perfil',
            createTitle: 'Novo tipo de perfil',
            editTitle: 'Editar tipo de perfil',
            form: {
                title: 'Formulário Tipo de Perfil'
            },
            messages: {
                createSuccess: 'Tipo de perfil criado com sucesso!',
                updateSuccess: 'Tipo de perfil atualizado com sucesso!',
                deleteSuccess: 'Tipo de perfil excluído com sucesso!',
                deleteMultipleSuccess: 'Tipos de perfil excluídos com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do tipo de perfil',
                saveError: 'Erro ao salvar tipo de perfil',
                deleteError: 'Erro ao excluir tipo de perfil',
                deleteMultipleError: 'Erro ao excluir tipos de perfil',
                notFound: 'Tipo de perfil não encontrado',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        materialType: {
            title: 'Tipo de Material',
            subtitle: 'Gerencie os tipos de material do sistema',
            list: 'Lista de tipos de material',
            createTitle: 'Novo tipo de material',
            editTitle: 'Editar tipo de material',
            form: {
                title: 'Formulário Tipo de Material'
            },
            messages: {
                createSuccess: 'Tipo de material criado com sucesso!',
                updateSuccess: 'Tipo de material atualizado com sucesso!',
                deleteSuccess: 'Tipo de material excluído com sucesso!',
                deleteMultipleSuccess:
                    'Tipos de material excluídos com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do tipo de material',
                saveError: 'Erro ao salvar tipo de material',
                deleteError: 'Erro ao excluir tipo de material',
                deleteMultipleError: 'Erro ao excluir tipos de material',
                notFound: 'Tipo de material não encontrado',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        projectGroup: {
            title: 'Grupo de Projetos',
            subtitle: 'Gerencie os grupos de projetos do sistema',
            list: 'Lista de grupos de projetos',
            createTitle: 'Novo grupo de projetos',
            editTitle: 'Editar grupo de projetos',
            form: {
                title: 'Formulário Grupo de Projetos'
            },
            messages: {
                createSuccess: 'Grupo de projetos criado com sucesso!',
                updateSuccess: 'Grupo de projetos atualizado com sucesso!',
                deleteSuccess: 'Grupo de projetos excluído com sucesso!',
                deleteMultipleSuccess:
                    'Grupos de projetos excluídos com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do grupo de projetos',
                saveError: 'Erro ao salvar grupo de projetos',
                deleteError: 'Erro ao excluir grupo de projetos',
                deleteMultipleError: 'Erro ao excluir grupos de projetos',
                notFound: 'Grupo de projetos não encontrado',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        materialGroup: {
            title: 'Grupo de Materiais',
            subtitle: 'Gerencie os grupos de materiais do sistema',
            list: 'Lista de grupos de materiais',
            createTitle: 'Novo grupo de materiais',
            editTitle: 'Editar grupo de materiais',
            form: {
                title: 'Formulário Grupo de Materiais'
            },
            messages: {
                createSuccess: 'Grupo de materiais criado com sucesso!',
                updateSuccess: 'Grupo de materiais atualizado com sucesso!',
                deleteSuccess: 'Grupo de materiais excluído com sucesso!',
                deleteMultipleSuccess:
                    'Grupos de materiais excluídos com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do grupo de materiais',
                saveError: 'Erro ao salvar grupo de materiais',
                deleteError: 'Erro ao excluir grupo de materiais',
                deleteMultipleError: 'Erro ao excluir grupos de materiais',
                notFound: 'Grupo de materiais não encontrado',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        title: {
            title: 'Título',
            subtitle: 'Gerencie os títulos do sistema',
            list: 'Lista de títulos',
            createTitle: 'Novo título',
            editTitle: 'Editar título',
            form: {
                title: 'Formulário de Título'
            },
            messages: {
                createSuccess: 'Título criado com sucesso!',
                updateSuccess: 'Título atualizado com sucesso!',
                deleteSuccess: 'Título excluído com sucesso!',
                deleteMultipleSuccess: 'Títulos excluídos com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do título',
                saveError: 'Erro ao salvar título',
                deleteError: 'Erro ao excluir título',
                deleteMultipleError: 'Erro ao excluir títulos',
                notFound: 'Título não encontrado',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        equipmentPart: {
            title: 'Parte do Equipamento',
            subtitle: 'Gerencie as partes do equipamento do sistema',
            list: 'Lista de partes do equipamento',
            createTitle: 'Nova parte do equipamento',
            editTitle: 'Editar parte do equipamento',
            form: {
                title: 'Formulário de Parte do Equipamento'
            },
            messages: {
                createSuccess: 'Parte do equipamento criada com sucesso!',
                updateSuccess: 'Parte do equipamento atualizada com sucesso!',
                deleteSuccess: 'Parte do equipamento excluída com sucesso!',
                deleteMultipleSuccess:
                    'Partes do equipamento excluídas com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados da parte do equipamento',
                saveError: 'Erro ao salvar parte do equipamento',
                deleteError: 'Erro ao excluir parte do equipamento',
                deleteMultipleError: 'Erro ao excluir partes do equipamento',
                notFound: 'Parte do equipamento não encontrada',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        defect: {
            title: 'Defeito',
            subtitle: 'Gerencie os defeitos do sistema',
            list: 'Lista de defeitos',
            createTitle: 'Novo defeito',
            editTitle: 'Editar defeito',
            form: {
                title: 'Formulário de Defeito'
            },
            messages: {
                createSuccess: 'Defeito criado com sucesso!',
                updateSuccess: 'Defeito atualizado com sucesso!',
                deleteSuccess: 'Defeito excluído com sucesso!',
                deleteMultipleSuccess: 'Defeitos excluídos com sucesso!',
                deactivateSuccess: 'Defeito desativado com sucesso!',
                activateSuccess: 'Defeito ativado com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do defeito',
                saveError: 'Erro ao salvar defeito',
                deleteError: 'Erro ao excluir defeito',
                deleteMultipleError: 'Erro ao excluir defeitos',
                notFound: 'Defeito não encontrado',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        cause: {
            title: 'Causa',
            subtitle: 'Gerencie as causas do sistema',
            list: 'Lista de causas',
            createTitle: 'Nova causa',
            editTitle: 'Editar causa',
            form: {
                title: 'Formulário de Causa'
            },
            messages: {
                createSuccess: 'Causa criada com sucesso!',
                updateSuccess: 'Causa atualizada com sucesso!',
                deleteSuccess: 'Causa excluída com sucesso!',
                deleteMultipleSuccess: 'Causas excluídas com sucesso!',
                deactivateSuccess: 'Causa desativada com sucesso!',
                activateSuccess: 'Causa ativada com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados da causa',
                saveError: 'Erro ao salvar causa',
                deleteError: 'Erro ao excluir causa',
                deleteMultipleError: 'Erro ao excluir causas',
                notFound: 'Causa não encontrada',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        equipmentFamily: {
            title: 'Família do Equipamento',
            subtitle: 'Gerencie as famílias de equipamentos do sistema',
            list: 'Lista de famílias de equipamentos',
            createTitle: 'Nova família de equipamentos',
            editTitle: 'Editar família de equipamentos',
            form: {
                title: 'Formulário de Família de Equipamentos'
            },
            messages: {
                createSuccess: 'Família de equipamentos criada com sucesso!',
                updateSuccess:
                    'Família de equipamentos atualizada com sucesso!',
                deleteSuccess: 'Família de equipamentos excluída com sucesso!',
                deleteMultipleSuccess:
                    'Famílias de equipamentos excluídas com sucesso!',
                deactivateSuccess:
                    'Família de equipamentos desativada com sucesso!',
                activateSuccess: 'Família de equipamentos ativada com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados da família de equipamentos',
                saveError: 'Erro ao salvar família de equipamentos',
                deleteError: 'Erro ao excluir família de equipamentos',
                deleteMultipleError: 'Erro ao excluir famílias de equipamentos',
                notFound: 'Família de equipamentos não encontrada',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        billingPortfolio: {
            title: 'Carteira de Cobrança',
            subtitle: 'Gerencie as carteiras de cobrança do sistema',
            list: 'Lista de carteiras de cobrança',
            createTitle: 'Nova carteira de cobrança',
            editTitle: 'Editar carteira de cobrança',
            form: {
                title: 'Formulário de Carteira de Cobrança'
            },
            messages: {
                createSuccess: 'Carteira de cobrança criada com sucesso!',
                updateSuccess: 'Carteira de cobrança atualizada com sucesso!',
                deleteSuccess: 'Carteira de cobrança excluída com sucesso!',
                deleteMultipleSuccess:
                    'Carteiras de cobrança excluídas com sucesso!',
                deactivateSuccess:
                    'Carteira de cobrança desativada com sucesso!',
                activateSuccess: 'Carteira de cobrança ativada com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados da carteira de cobrança',
                saveError: 'Erro ao salvar carteira de cobrança',
                deleteError: 'Erro ao excluir carteira de cobrança',
                deleteMultipleError: 'Erro ao excluir carteiras de cobrança',
                notFound: 'Carteira de cobrança não encontrada',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        checklist: {
            title: 'Checklist',
            subtitle: 'Gerencie os checklists do sistema',
            list: 'Lista de checklists',
            createTitle: 'Novo checklist',
            editTitle: 'Editar checklist',
            form: {
                title: 'Formulário de Checklist'
            },
            messages: {
                createSuccess: 'Checklist criado com sucesso!',
                updateSuccess: 'Checklist atualizado com sucesso!',
                deleteSuccess: 'Checklist excluído com sucesso!',
                deleteMultipleSuccess: 'Checklists excluídos com sucesso!',
                deactivateSuccess: 'Checklist desativado com sucesso!',
                activateSuccess: 'Checklist ativado com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do checklist',
                saveError: 'Erro ao salvar checklist',
                deleteError: 'Erro ao excluir checklist',
                deleteMultipleError: 'Erro ao excluir checklists',
                notFound: 'Checklist não encontrado',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        operation: {
            title: 'Operação',
            subtitle: 'Gerencie as operações do sistema',
            list: 'Lista de operações',
            createTitle: 'Nova operação',
            editTitle: 'Editar operação',
            form: {
                title: 'Formulário de Operação'
            },
            messages: {
                createSuccess: 'Operação criada com sucesso!',
                updateSuccess: 'Operação atualizada com sucesso!',
                deleteSuccess: 'Operação excluída com sucesso!',
                deleteMultipleSuccess: 'Operações excluídas com sucesso!',
                deactivateSuccess: 'Operação desativada com sucesso!',
                activateSuccess: 'Operação ativada com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados da operação',
                saveError: 'Erro ao salvar operação',
                deleteError: 'Erro ao excluir operação',
                deleteMultipleError: 'Erro ao excluir operações',
                notFound: 'Operação não encontrada',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        nonConformity: {
            title: 'Não Conformidade',
            subtitle: 'Gerencie as não conformidades do sistema',
            list: 'Lista de não conformidades',
            createTitle: 'Nova não conformidade',
            editTitle: 'Editar não conformidade',
            form: {
                title: 'Formulário de Não Conformidade'
            },
            messages: {
                createSuccess: 'Não conformidade criada com sucesso!',
                updateSuccess: 'Não conformidade atualizada com sucesso!',
                deleteSuccess: 'Não conformidade excluída com sucesso!',
                deleteMultipleSuccess:
                    'Não conformidades excluídas com sucesso!',
                deactivateSuccess: 'Não conformidade desativada com sucesso!',
                activateSuccess: 'Não conformidade ativada com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados da não conformidade',
                saveError: 'Erro ao salvar não conformidade',
                deleteError: 'Erro ao excluir não conformidade',
                deleteMultipleError: 'Erro ao excluir não conformidades',
                notFound: 'Não conformidade não encontrada',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        userProfile: {
            title: 'Perfil do Usuário',
            subtitle: 'Gerencie o perfil do usuário',
            list: 'Lista de perfis do usuário',
            createTitle: 'Novo perfil do usuário',
            editTitle: 'Editar perfil do usuário',
            form: {
                title: 'Formulário de Perfil do Usuário'
            },
            messages: {
                createSuccess: 'Perfil do usuário criado com sucesso!',
                updateSuccess: 'Perfil do usuário atualizado com sucesso!',
                deleteSuccess: 'Perfil do usuário excluído com sucesso!',
                deleteMultipleSuccess:
                    'Perfis do usuário excluídos com sucesso!',
                deactivateSuccess: 'Perfil do usuário desativado com sucesso!',
                activateSuccess: 'Perfil do usuário ativado com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do perfil do usuário',
                saveError: 'Erro ao salvar perfil do usuário',
                deleteError: 'Erro ao excluir perfil do usuário',
                deleteMultipleError: 'Erro ao excluir perfis do usuário',
                notFound: 'Perfil do usuário não encontrado',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        module: {
            title: 'Módulo',
            subtitle: 'Gerencie o módulo',
            list: 'Lista de módulos',
            createTitle: 'Novo módulo',
            editTitle: 'Editar módulo',
            form: {
                title: 'Formulário de Módulos'
            },
            messages: {
                createSuccess: 'Módulo criado com sucesso!',
                updateSuccess: 'Módulo atualizado com sucesso!',
                deleteSuccess: 'Módulos excluído com sucesso!',
                deleteMultipleSuccess: 'Módulos excluídos com sucesso!',
                deactivateSuccess: 'Módulos desativado com sucesso!',
                activateSuccess: 'Módulos ativado com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do módulos',
                saveError: 'Erro ao salvar módulos',
                deleteError: 'Erro ao excluir módulo',
                deleteMultipleError: 'Erro ao excluir módulos',
                notFound: 'Módulo não encontrado',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        position: {
            title: 'Cargo',
            subtitle: 'Gerencie o cargo',
            list: 'Lista de cargos',
            createTitle: 'Novo cargo',
            editTitle: 'Editar cargo',
            form: {
                title: 'Formulário de Cargos'
            },
            messages: {
                createSuccess: 'Cargo criado com sucesso!',
                updateSuccess: 'Cargo atualizado com sucesso!',
                deleteSuccess: 'Cargo excluído com sucesso!',
                deleteMultipleSuccess: 'Cargos excluídos com sucesso!',
                deactivateSuccess: 'Cargo desativado com sucesso!',
                activateSuccess: 'Cargo ativado com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do cargo',
                saveError: 'Erro ao salvar cargo',
                deleteError: 'Erro ao excluir cargo',
                deleteMultipleError: 'Erro ao excluir cargos',
                notFound: 'Cargo não encontrado',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        origin: {
            title: 'Origin',
            subtitle: 'Gerencie as origens do sistema',
            list: 'Lista de origens',
            createTitle: 'Nova origem',
            editTitle: 'Editar origem',
            form: {
                title: 'Formulário Origem'
            },
            messages: {
                createSuccess: 'Origem criado com sucesso!',
                updateSuccess: 'Origem atualizado com sucesso!',
                deleteSuccess: 'Origem excluído com sucesso!',
                deleteMultipleSuccess: 'Origins excluídos com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados da Origem',
                saveError: 'Erro ao salvar origem',
                deleteError: 'Erro ao excluir origem',
                deleteMultipleError: 'Erro ao excluir origens',
                notFound: 'Origem não encontrado',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        education: {
            title: 'Escolaridade',
            subtitle: 'Gerencie a escolaridade',
            list: 'Lista de escolaridades',
            createTitle: 'Nova escolaridade',
            editTitle: 'Editar escolaridade',
            form: {
                title: 'Formulário de Escolaridade'
            },
            messages: {
                createSuccess: 'Escolaridade criada com sucesso!',
                updateSuccess: 'Escolaridade atualizada com sucesso!',
                deleteSuccess: 'Escolaridade excluída com sucesso!',
                deleteMultipleSuccess: 'Escolaridades excluídas com sucesso!',
                deactivateSuccess: 'Escolaridade desativada com sucesso!',
                activateSuccess: 'Escolaridade ativada com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados da escolaridade',
                saveError: 'Erro ao salvar escolaridade',
                deleteError: 'Erro ao excluir escolaridade',
                deleteMultipleError: 'Erro ao excluir escolaridades',
                notFound: 'Escolaridade não encontrada',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        specialty: {
            title: 'Especialidade',
            subtitle: 'Gerencie a especialidade',
            list: 'Lista de especialidades',
            createTitle: 'Nova especialidade',
            editTitle: 'Editar especialidade',
            form: {
                title: 'Formulário de Especialidade'
            },
            messages: {
                createSuccess: 'Especialidade criada com sucesso!',
                updateSuccess: 'Especialidade atualizada com sucesso!',
                deleteSuccess: 'Especialidade excluída com sucesso!',
                deleteMultipleSuccess: 'Especialidades excluídas com sucesso!',
                deactivateSuccess: 'Especialidade desativada com sucesso!',
                activateSuccess: 'Especialidade ativada com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados da especialidade',
                saveError: 'Erro ao salvar especialidade',
                deleteError: 'Erro ao excluir especialidade',
                deleteMultipleError: 'Erro ao excluir especialidades',
                notFound: 'Especialidade não encontrada',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        skill: {
            title: 'Habilidade',
            subtitle: 'Gerencie a habilidade',
            list: 'Lista de habilidades',
            createTitle: 'Nova habilidade',
            editTitle: 'Editar habilidade',
            form: {
                title: 'Formulário de Habilidade'
            },
            messages: {
                createSuccess: 'Habilidade criada com sucesso!',
                updateSuccess: 'Habilidade atualizada com sucesso!',
                deleteSuccess: 'Habilidade excluída com sucesso!',
                deleteMultipleSuccess: 'Habilidades excluídas com sucesso!',
                deactivateSuccess: 'Habilidade desativada com sucesso!',
                activateSuccess: 'Habilidade ativada com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados da habilidade',
                saveError: 'Erro ao salvar habilidade',
                deleteError: 'Erro ao excluir habilidade',
                deleteMultipleError: 'Erro ao excluir habilidades',
                notFound: 'Habilidade não encontrada',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        department: {
            title: 'Departamento',
            subtitle: 'Gerencie o departamento',
            list: 'Lista de departamentos',
            createTitle: 'Novo departamento',
            editTitle: 'Editar departamento',
            form: {
                title: 'Formulário de Departamento'
            },
            messages: {
                createSuccess: 'Departamento criado com sucesso!',
                updateSuccess: 'Departamento atualizado com sucesso!',
                deleteSuccess: 'Departamento excluído com sucesso!',
                deleteMultipleSuccess: 'Departamentos excluídos com sucesso!',
                deactivateSuccess: 'Departamento desativado com sucesso!',
                activateSuccess: 'Departamento ativado com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados do departamento',
                saveError: 'Erro ao salvar departamento',
                deleteError: 'Erro ao excluir departamento',
                deleteMultipleError: 'Erro ao excluir departamentos',
                notFound: 'Departamento não encontrado',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        opportunity: {
            title: 'Oportunidade',
            subtitle: 'Gerencie a oportunidade',
            list: 'Lista de oportunidades',
            createTitle: 'Nova oportunidade',
            editTitle: 'Editar oportunidade',
            form: {
                title: 'Formulário de Oportunidade'
            },
            messages: {
                createSuccess: 'Oportunidade criada com sucesso!',
                updateSuccess: 'Oportunidade atualizada com sucesso!',
                deleteSuccess: 'Oportunidade excluída com sucesso!',
                deleteMultipleSuccess: 'Oportunidades excluídas com sucesso!',
                deactivateSuccess: 'Oportunidade desativada com sucesso!',
                activateSuccess: 'Oportunidade ativada com sucesso!'
            },
            errors: {
                loadError: 'Erro ao carregar dados da oportunidade',
                saveError: 'Erro ao salvar oportunidade',
                deleteError: 'Erro ao excluir oportunidade',
                deleteMultipleError: 'Erro ao excluir oportunidades',
                notFound: 'Oportunidade não encontrada',
                duplicateDescription:
                    'O campo descricao é único e já existe no banco de dados.'
            }
        },
        systemConfig: {
            themeBuilder: {
                title: 'Personalização de Tema',
                subtitle: 'Configure as cores e o modo de exibição do sistema',
                sections: {
                    mainColors: 'Cores Principais',
                    statusColors: 'Cores de Status',
                    darkMode: 'Modo Escuro',
                    presets: 'Temas Predefinidos'
                },
                colors: {
                    primary: 'Cor Primária',
                    secondary: 'Cor Secundária',
                    accent: 'Cor de Destaque',
                    positive: 'Sucesso',
                    negative: 'Erro',
                    warning: 'Aviso',
                    info: 'Informação',
                    dark: 'Fundo Escuro',
                    darkPage: 'Página Escura'
                },
                presets: {
                    default: 'Padrão',
                    ocean: 'Oceano',
                    forest: 'Floresta',
                    sunset: 'Sunset',
                    night: 'Noturno'
                },
                actions: {
                    save: 'Salvar Configurações',
                    reset: 'Restaurar Padrão'
                },
                messages: {
                    presetApplied: 'Preset "{name}" aplicado',
                    themeSaved: 'Tema salvo com sucesso!',
                    themeReset: 'Tema resetado para o padrão'
                }
            }
        }
    },
    errors: {
        loadTypesError: 'Erro ao carregar tipos de texto padronizado',
        validationError: 'Por favor, corrija os erros no formulário',
        requests: {
            generic: {
                400: 'Requisição Inválida. Verifique sua entrada e tente novamente.',
                401: 'Não Autorizado. Faça login.',
                403: 'Proibido. Você não tem permissão para realizar esta ação.',
                404: 'Não Encontrado. O recurso solicitado não pôde ser encontrado.',
                500: 'Erro Interno do Servidor. Tente novamente mais tarde.',
                503: 'Serviço Indisponível. Tente novamente mais tarde.',
                noCode: 'Erro Desconhecido. Tente novamente mais tarde.'
            },
            login: {
                400: 'E-mail ou senha inválidos.'
            },
            user: {
                401: 'Usuário não autenticado.'
            }
        }
    },
    pwa: {
        updateAvailable:
            'Uma nova versão do aplicativo está disponível. Recarregue a página para atualizar.',
        refresh: 'Recarregar'
    },
    user: {
        fullName: 'Nome completo',
        username: 'Nome de usuário',
        notAvailable: 'Não disponível',
        status: {
            label: 'Status',
            online: 'Online',
            offline: 'Offline',
            away: 'Ausente',
            busy: 'Ocupado',
            invisible: 'Invisível'
        }
    },
    notifications: {
        pleaseFixErrors: 'Por favor, corrija os erros antes de continuar',
        emailTestSuccess: 'Teste de conexão de e-mail realizado com sucesso!',
        featureNotImplemented: 'Funcionalidade ainda não implementada'
    },
    dialogs: {
        confirmDelete: {
            title: 'Confirmar exclusão',
            message: 'Tem certeza que deseja excluir "{item}"?'
        },
        confirmDeleteMultiple: {
            title: 'Confirmar exclusão múltipla',
            message:
                'Tem certeza que deseja excluir {count} itens selecionados?'
        },
        confirmStatusChange: {
            title: 'Confirmar alteração de status',
            message: 'Tem certeza que deseja {action} "{item}"?'
        }
    },
    validate: {
        requiredName: 'Nome é obrigatório',
        invalidName: 'Nome deve ter pelo menos 3 caracteres',
        requiredSurname: 'Sobrenome é obrigatório',
        invalidSurname: 'Sobrenome deve ter pelo menos 3 caracteres',
        invalidEmail: 'E-mail inválido',
        requiredEmail: 'E-mail é obrigatório',
        invalidPassword: 'Senha deve ter pelo menos 5 caracteres',
        requiredPassword: 'Senha é obrigatória',
        requiredUsername: 'Nome de usuário é obrigatório',
        invalidUsername: 'Nome de usuário deve ter pelo menos 3 caracteres',
        requiredRole: 'Função é obrigatória',
        requiredCompanyName: 'Razão social é obrigatória',
        invalidCompanyName: 'Razão social deve ter pelo menos 3 caracteres',
        docNaturalRequired: 'CPF é obrigatório',
        docNaturalInvalid: 'CPF inválido',
        docLegalRequired: 'CNPJ é obrigatório',
        docLegalInvalid: 'CNPJ inválido',
        requiredIndicatorStateEnrollmentRecipient:
            'Indicador de IE Destinatário é obrigatório',
        requiredFansyName: 'Nome fantasia é obrigatório',
        requiredPermissionProfileName:
            'Nome do perfil de permissão é obrigatório',
        invalidPermissionProfileName:
            'Nome do perfil de permissão deve ter pelo menos 3 caracteres',
        requiredPermissionGroupName: 'Nome do grupo de permissão é obrigatório',
        invalidPermissionGroupName:
            'Nome do grupo de permissão deve ter pelo menos 3 caracteres',
        requiredPermissionGroupCode:
            'Código do grupo de permissão é obrigatório',
        invalidPermissionGroupCode:
            'Código do grupo deve conter apenas letras minúsculas, números e underscores',
        maxObservations: 'Observações não podem exceder 500 caracteres',
        maxDescription: 'Descrição não pode exceder 250 caracteres',
        invalidPasswordExpirationDays:
            'Dias para expiração de senha deve ser um número positivo',
        requiredSeller: 'Vendedor é obrigatório',
        requiredBuyer: 'Comprador é obrigatório',
        requiredCashier: 'Caixa é obrigatório',
        requiredCompany: 'Empresa é obrigatória',
        requiredRepresentative: 'Representante é obrigatório',
        requiredSmtpServer: 'Servidor de E-mail é obrigatório',
        requiredSmtpPort: 'Porta SMTP é obrigatória',
        invalidSmtpPort: 'Porta SMTP inválida',
        requiredEmailPassword: 'Senha do E-mail é obrigatória',
        fieldRequired: 'O campo {field} é obrigatório',
        mustBeNumber: 'Este campo deve ser um número',
        minLength: 'Deve ter pelo menos {min} caracteres',
        invalidCommission: 'Comissão deve ser um número',
        minCommission: 'Comissão não pode ser menor que 0',
        maxCommission: 'Comissão não pode ser maior que 100',
        requiredCommission: 'Comissão é obrigatória',
        invalidDiscountPercentage: 'Percentual de desconto deve ser um número',
        minDiscountPercentage:
            'Percentual de desconto não pode ser menor que 0',
        maxDiscountPercentage:
            'Percentual de desconto não pode ser maior que 100',
        requiredDiscountPercentage: 'Percentual de desconto é obrigatório',
        invalidDiscountValue: 'Valor de desconto deve ser um número',
        minDiscountValue: 'Valor de desconto não pode ser menor que 0',
        requiredDiscountValue: 'Valor de desconto é obrigatório',
        invalidOrderPurchaseLimit:
            'Limite de compra do pedido deve ser um número',
        minOrderPurchaseLimit:
            'Limite de compra do pedido não pode ser menor que 0',
        requiredOrderPurchaseLimit: 'Limite de compra do pedido é obrigatório',
        invalidMonthlyPurchaseLimit:
            'Limite de compra do mês deve ser um número',
        minMonthlyPurchaseLimit:
            'Limite de compra do mês não pode ser menor que 0',
        requiredMonthlyPurchaseLimit: 'Limite de compra do mês é obrigatório',
        requiredCategory: 'Categoria é obrigatória',
        requiredPaymentCondition: 'Condição de pagamento é obrigatória',
        requiredPaymentModality: 'Modalidade de pagamento é obrigatória',
        requiredTransaction: 'Transação é obrigatória',
        requiredPriceTable: 'Tabela de preço é obrigatória',
        requiredFinancialAccount: 'Conta financeira é obrigatória',
        requiredDeliveryType: 'Tipo de entrega é obrigatório',
        requiredFreightIndicator: 'Indicador de frete é obrigatório',
        requiredCarrier: 'Transportadora é obrigatória',
        requiredCostCenter: 'Centro de custo é obrigatório',
        requiredPaymentMethodGroup: 'Grupo forma de pagamento é obrigatório',
        requiredDaysForDueNotice: 'Dias para aviso de vencimento é obrigatório',
        requiredDaysForCollectionNotice:
            'Dias para aviso de cobrança é obrigatório',
        requiredBillingDayLimit: 'Dia limite para faturamento é obrigatório',
        requiredMaxDiscountPercentage:
            'Percentual de desconto máximo é obrigatório',
        requiredMinimumBillingValue:
            'Valor do faturamento mínimo é obrigatório',
        requiredCreditLimit: 'Limite de crédito é obrigatório',
        minZero: 'Valor não pode ser menor que 0',
        minOne: 'Valor não pode ser menor que 1',
        maxThirtyOne: 'Valor não pode ser maior que 31',
        maxOneHundred: 'Valor não pode ser maior que 100',
        requiredMotherName: 'Nome da mãe é obrigatório',
        requiredAdmissionDate: 'Data de admissão é obrigatória',
        requiredPIS: 'PIS é obrigatório',
        requiredVoterCard: 'Título de eleitor é obrigatório',
        requiredWorkCard: 'Carteira de trabalho é obrigatória',
        requiredDepartment: 'Departamento é obrigatório',
        requiredSector: 'Setor é obrigatório',
        requiredPosition: 'Cargo é obrigatório',
        requiredHourCost: 'Valor custo hora é obrigatório',
        requiredAddressType: 'Tipo de endereço é obrigatório',
        requiredZipCode: 'CEP é obrigatório',
        requiredCountry: 'País é obrigatório',
        requiredState: 'Estado é obrigatório',
        requiredCity: 'Cidade é obrigatória',
        requiredStreet: 'Logradouro é obrigatório',
        requiredNumber: 'Número é obrigatório',
        requiredNeighborhood: 'Bairro é obrigatório',
        requiredDocumentType: 'Tipo de documento é obrigatório',
        requiredDocumentNumber: 'Número do documento é obrigatório',
        requiredOccupation: 'Ocupação é obrigatória',
        requiredBirthDate: 'Data de nascimento é obrigatória',
        requiredPhoneType: 'Tipo de telefone é obrigatório',
        requiredAreaCode: 'DDD é obrigatório',
        requiredPhoneNumber: 'Número de telefone é obrigatório',
        requiredEmployeeCount: 'Número de funcionários é obrigatório',
        requiredAnnualRevenue: 'Faturamento anual é obrigatório',
        requiredMainContact: 'Contato principal é obrigatório',
        requiredDescription: 'Descrição é obrigatória',
        requiredAbbreviation: 'Abreviação é obrigatória',
        requiredType: 'Tipo é obrigatório',
        requiredContent: 'Conteúdo é obrigatório',
        requiredTitle: 'Título é obrigatório',
        requiredModule: 'Módulo é obrigatório',
        requiredRoute: 'Rota é obrigatória',
        requiredModel: 'Modelo é obrigatório',
        requiredDifficultyLevel: 'Nível de dificuldade é obrigatório (1-10)',
        requiredBrand: 'Marca é obrigatória',
        minDifficultyLevel: 'Nível de dificuldade deve ser no mínimo 1',
        maxDifficultyLevel: 'Nível de dificuldade deve ser no máximo 10',
        maxFileSize: 'Arquivo muito grande. Máximo 2MB',
        invalidFileType: 'Tipo de arquivo inválido',
        requiredCode: 'Código é obrigatório',
        ncmCodeFormat: 'Código NCM deve ter o formato 0000.00.00',
        requiredStatus: 'Status é obrigatório',
        maxLength: 'Deve ter no máximo {max} caracteres'
    },
    cnpjSearch: {
        tooltip: 'Consultar CNPJ',
        title: 'Consulta de CNPJ',
        inputLabel: 'Digite o CNPJ',
        hint: 'Digite o CNPJ e pressione Enter ou clique na lupa para pesquisar',
        clear: 'Limpar campo',
        results: 'Resultados da Pesquisa',
        basicInfo: 'Informações Básicas',
        address: 'Endereço',
        contact: 'Informações de Contato',
        mainActivity: 'Atividade Principal',
        searching: 'Consultando CNPJ...',
        fields: {
            cnpj: 'CNPJ',
            companyName: 'Razão Social',
            tradeName: 'Nome Fantasia',
            status: 'Situação',
            street: 'Logradouro',
            zipCode: 'CEP',
            neighborhood: 'Bairro',
            city: 'Cidade',
            foundingDate: 'Data de Fundação',
            phone: 'Telefone',
            email: 'E-mail'
        },
        errors: {
            required: 'CNPJ é obrigatório',
            invalidFormat: 'CNPJ deve ter 14 dígitos',
            invalidCNPJ: 'CNPJ inválido',
            notFound: 'CNPJ não encontrado',
            searchError: 'Erro ao consultar CNPJ. Tente novamente.'
        }
    },
    cepSearch: {
        searching: 'Consultando CEP...',
        searchingAddress: 'Buscando endereço...',
        errors: {
            required: 'CEP é obrigatório',
            invalidFormat: 'CEP deve ter 8 dígitos',
            invalidCEP: 'CEP inválido',
            notFound: 'CEP não encontrado',
            searchError: 'Erro ao consultar CEP. Tente novamente.'
        },
        success: {
            addressFound: 'Endereço encontrado e preenchido automaticamente'
        },
        onlyBrazil: 'Pesquisa de CEP disponível apenas para o Brasil'
    },
    modals: {
        marcaModelo: {
            title: 'Marca x modelo',
            selectedBrand: 'Marca selecionada',
            manageModels: 'Gerencie os modelos vinculados a esta marca',
            form: {
                title: 'Vincular modelo à marca',
                editTitle: 'Editar vinculação marca-modelo'
            },
            table: {
                title: 'Modelos vinculados'
            },
            messages: {
                createSuccess: 'Modelo vinculado à marca com sucesso!',
                updateSuccess: 'Vinculação atualizada com sucesso!',
                deleteSuccess: 'Vinculação removida com sucesso!',
                deleteMultipleSuccess: 'Vinculações removidas com sucesso!',
                linkSuccess: 'Vinculação realizada com sucesso!'
            },
            errors: {
                saveError: 'Erro ao vincular modelo à marca',
                updateError: 'Erro ao atualizar vinculação',
                deleteError: 'Erro ao remover vinculação',
                deleteMultipleError: 'Erro ao remover vinculações',
                loadError: 'Erro ao carregar vinculações',
                loadModelosError: 'Erro ao carregar modelos',
                notFound: 'Vinculação não encontrada'
            }
        },
        vincularModelo: {
            title: 'Vincular modelo à marca'
        }
    },
    imageViewer: {
        title: 'Visualizador de imagem',
        zoomIn: 'Ampliar',
        zoomOut: 'Reduzir',
        fitScreen: 'Ajustar à tela',
        fullscreen: 'Tela cheia',
        loadError: 'Erro ao carregar imagem'
    },
    contactForm: {
        titles: {
            main: 'Formulário de Contato',
            generalInfo: 'Informações Gerais',
            phones: 'Telefones',
            emails: 'E-mails'
        },
        labels: {
            name: 'Nome Completo',
            occupation: 'Ocupação',
            department: 'Departamento',
            birthDate: 'Data de Nascimento',
            phoneType: 'Tipo de Telefone',
            countryCode: 'País',
            areaCode: 'DDD',
            phoneNumber: 'Número',
            extension: 'Ramal',
            email: 'E-mail',
            addPhone: 'Adicionar Telefone',
            addEmail: 'Adicionar E-mail',
            removePhone: 'Remover Telefone',
            removeEmail: 'Remover E-mail',
            whatsappMessages: 'Recebimento automático de mensagens via WhatsApp'
        },
        messages: {
            noPhones: 'Nenhum telefone cadastrado',
            noEmails: 'Nenhum e-mail cadastrado',
            noPhoneHint: 'Clique em "Adicionar Telefone" para começar',
            noEmailHint: 'Clique em "Adicionar E-mail" para começar',
            confirmRemovePhone: 'Deseja remover este telefone?',
            confirmRemoveEmail: 'Deseja remover este e-mail?',
            phoneAdded: 'Telefone adicionado com sucesso',
            emailAdded: 'E-mail adicionado com sucesso',
            phoneRemoved: 'Telefone removido com sucesso',
            emailRemoved: 'E-mail removido com sucesso'
        },
        validation: {
            requiredName: 'Nome é obrigatório',
            requiredOccupation: 'Ocupação é obrigatória',
            requiredDepartment: 'Departamento é obrigatório',
            requiredBirthDate: 'Data de nascimento é obrigatória',
            requiredPhoneType: 'Tipo de telefone é obrigatório',
            requiredCountryCode: 'País é obrigatório',
            requiredAreaCode: 'DDD é obrigatório',
            requiredPhoneNumber: 'Número de telefone é obrigatório',
            invalidExtension: 'Ramal deve ter pelo menos 2 dígitos',
            requiredEmail: 'E-mail é obrigatório',
            invalidEmail: 'E-mail inválido',
            minPhones: 'Pelo menos um telefone é obrigatório',
            minEmails: 'Pelo menos um e-mail é obrigatório'
        },
        occupations: {
            dev_frontend: 'Desenvolvedor Frontend',
            dev_backend: 'Desenvolvedor Backend',
            dev_fullstack: 'Desenvolvedor Full Stack',
            analista_sistemas: 'Analista de Sistemas',
            gerente_projetos: 'Gerente de Projetos',
            designer_ux_ui: 'Designer UX/UI',
            analista_dados: 'Analista de Dados',
            devops: 'DevOps Engineer',
            product_manager: 'Product Manager',
            scrum_master: 'Scrum Master'
        },
        departments: {
            ti: 'Tecnologia da Informação',
            rh: 'Recursos Humanos',
            financeiro: 'Financeiro',
            comercial: 'Comercial',
            marketing: 'Marketing',
            operacoes: 'Operações',
            juridico: 'Jurídico',
            compras: 'Compras'
        },
        phoneTypes: {
            celular: 'Celular',
            residencial: 'Residencial',
            comercial: 'Comercial',
            whatsapp: 'WhatsApp',
            outro: 'Outro'
        }
    },
    addressCollection: {
        titles: {
            main: 'Coleção de Endereços',
            addAddress: 'Adicionar Endereço',
            editAddress: 'Editar Endereço'
        },
        labels: {
            addAddress: 'Adicionar Endereço',
            editAddress: 'Editar Endereço',
            removeAddress: 'Remover Endereço',
            fullAddress: 'Endereço Completo',
            cityState: 'Cidade/Estado'
        },
        messages: {
            noAddresses: 'Nenhum endereço cadastrado',
            noAddressHint: 'Clique em "Adicionar Endereço" para começar',
            confirmRemoveAddress: 'Deseja remover este endereço?',
            addressAdded: 'Endereço adicionado com sucesso',
            addressUpdated: 'Endereço atualizado com sucesso',
            addressRemoved: 'Endereço removido com sucesso',
            saveError: 'Erro ao salvar endereço. Tente novamente.'
        },
        validation: {
            minAddresses: 'Pelo menos um endereço é obrigatório'
        }
    },
    contactCollection: {
        titles: {
            main: 'Coleção de Contatos',
            addContact: 'Adicionar Contato',
            editContact: 'Editar Contato'
        },
        labels: {
            addContact: 'Adicionar Contato',
            editContact: 'Editar Contato',
            removeContact: 'Remover Contato',
            contactInfo: 'Informações do Contato',
            location: 'Localização',
            contactMethods: 'Meios de Contato'
        },
        messages: {
            noContacts: 'Nenhum contato cadastrado',
            noContactHint: 'Clique em "Adicionar Contato" para começar',
            confirmRemoveContact: 'Deseja remover este contato?',
            contactAdded: 'Contato adicionado com sucesso',
            contactUpdated: 'Contato atualizado com sucesso',
            contactRemoved: 'Contato removido com sucesso',
            saveError: 'Erro ao salvar contato. Tente novamente.'
        },
        validation: {
            minContacts: 'Pelo menos um contato é obrigatório'
        }
    },
    richEditor: {
        bold: 'Negrito',
        italic: 'Itálico',
        underline: 'Sublinhado',
        strikethrough: 'Tachado',
        alignLeft: 'Alinhar à Esquerda',
        alignCenter: 'Centralizar',
        alignRight: 'Alinhar à Direita',
        bulletList: 'Lista com Marcadores',
        numberedList: 'Lista Numerada',
        insertLink: 'Inserir Link',
        clearFormat: 'Limpar Formatação',
        fontSize: 'Tamanho',
        fontFamily: 'Fonte',
        textColor: 'Cor do Texto',
        backgroundColor: 'Cor de Fundo',
        subscript: 'Subscrito',
        superscript: 'Sobrescrito',
        heading: 'Título',
        heading1: 'Título 1',
        heading2: 'Título 2',
        heading3: 'Título 3',
        heading4: 'Título 4',
        heading5: 'Título 5',
        heading6: 'Título 6',
        blockquote: 'Citação',
        code: 'Código Inline',
        codeBlock: 'Bloco de Código',
        insertImage: 'Inserir Imagem',
        table: 'Tabela',
        table2x2: 'Tabela 2x2',
        table3x3: 'Tabela 3x3',
        table4x4: 'Tabela 4x4',
        customTable: 'Tabela Personalizada',
        horizontalRule: 'Linha Horizontal',
        lineBreak: 'Quebra de Linha',
        indent: 'Aumentar Recuo',
        outdent: 'Diminuir Recuo',
        undo: 'Desfazer',
        redo: 'Refazer',
        selectAll: 'Selecionar tudo',
        visualMode: 'Modo visual',
        htmlMode: 'Modo HTML',
        enterUrl: 'Digite a URL do link:',
        enterImageUrl: 'Digite a URL da imagem:',
        enterImageAlt: 'Digite o texto alternativo da imagem:',
        enterRows: 'Número de linhas:',
        enterCols: 'Número de colunas:',
        characters: 'caracteres',
        words: 'palavras',
        placeholder: 'Digite seu texto aqui...'
    },
    dataTable: {
        manageColumns: 'Gerenciar Colunas',
        manageColumnsTooltip: 'Reorganizar e ocultar colunas',
        reset: 'Resetar',
        resetTooltip: 'Restaurar configuração padrão',
        dragToReorder: 'Arraste para reordenar as colunas',
        hideColumn: 'Ocultar coluna',
        showColumn: 'Mostrar coluna',
        pagination: {
            rowsPerPageLabel: 'Linhas por página:',
            paginationLabel: '{first}-{last} de {total}',
            selectedRowsLabel: {
                none: 'Nenhuma linha selecionada',
                one: '1 linha selecionada',
                many: '{count} linhas selecionadas'
            }
        },
        notifications: {
            loadError: 'Erro ao carregar configuração de colunas',
            saveError: 'Erro ao salvar configuração de colunas',
            configApplied: 'Configuração de colunas aplicada com sucesso',
            configReset: 'Configuração de colunas restaurada para o padrão'
        }
    },
    reports: {
        modal: {
            title: 'Relatórios',
            reportType: 'Tipo de Relatório',
            format: 'Formato',
            info: 'Informações',
            table: 'Tabela',
            columns: 'Colunas',
            columnsVisible: 'colunas visíveis',
            filters: 'Filtros',
            filtersApplied: 'Filtros aplicados',
            groupColumn: 'Coluna de Agrupamento',
            selectGroupColumn: 'Selecione a coluna para agrupar',
            summaryColumn: 'Coluna para Resumo',
            selectSummaryColumn: 'Selecione a coluna para resumir',
            generate: 'Gerar Relatório'
        },
        types: {
            simple: 'Simples',
            grouped: 'Simples Agrupado por Coluna',
            summary: 'Resumido Simples'
        },
        summary: {
            item: 'Item',
            quantity: 'Quantidade',
            percentage: '% Part.',
            total: 'TOTAL'
        },
        formats: {
            excel: 'Excel (.xls)',
            pdf: 'PDF'
        },
        errors: {
            fetchData: 'Erro ao buscar dados para o relatório',
            generateExcel: 'Erro ao gerar relatório Excel',
            generatePdf: 'Erro ao gerar relatório PDF',
            unsupportedType: 'Tipo de relatório não suportado',
            unsupportedFormat: 'Formato não suportado'
        },
        success: {
            generated: 'Relatório gerado com sucesso'
        }
    }
}
