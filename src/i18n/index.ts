import { enUS, enUSNumberFormat, enUSDateTimeFormat } from './en-US'
import { ptBR, ptBRNumberFormat, ptBRDateTimeFormat } from './pt-BR'
import { esPY, esPYNumberFormat, esPYDateTimeFormat } from './es-PY'

export const messages = {
    'pt-BR': ptBR,
    'en-US': enUS,
    'es-PY': esPY
}
export const datetimeFormats = {
    'pt-BR': ptBRDateTimeFormat,
    'en-US': enUSDateTimeFormat,
    'es-PY': esPYDateTimeFormat
}
export const numberFormats = {
    'pt-BR': ptBRNumberFormat,
    'en-US': enUSNumberFormat,
    'es-PY': esPYNumberFormat
}
