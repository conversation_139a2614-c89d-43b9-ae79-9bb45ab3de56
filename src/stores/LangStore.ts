import { computed, ref } from 'vue'
import type { ILocaleConfig } from 'src/interfaces/Lang'
import { defineStore } from 'pinia'
import { useI18n } from 'vue-i18n'
import { i18n } from 'src/boot/i18n'

export const useLocaleStore = defineStore('locale', () => {
    const { locale } = useI18n()
    const localeBoot = i18n.global.locale

    const languages = computed(
        () =>
            [
                {
                    code: 'en-US',
                    name: 'English',
                    flag: 'united-states-of-america.png'
                },
                {
                    code: 'pt-BR',
                    name: 'Português',
                    flag: 'brazil-.png'
                },
                {
                    code: 'es-PY',
                    name: 'Español',
                    flag: 'paraguai.png'
                }
            ] as ILocaleConfig[]
    )

    const selectedFlag = ref(languages.value?.[0]?.flag || '')

    const loadLocale = () => {
        const defaultLocale = getDefaultLocale()

        const storedLocale =
            localStorage.getItem('locale') &&
            localStorage.getItem('locale') !== '{}'
                ? localStorage.getItem('locale')
                : null

        const localeConfig: ILocaleConfig = storedLocale
            ? JSON.parse(storedLocale)
            : defaultLocale

        setLocale(localeConfig)
    }

    const setLocale = (localeConfig: ILocaleConfig) => {
        selectedFlag.value = localeConfig.flag

        locale.value = localeConfig.code
        localeBoot.value = localeConfig.code
        document.documentElement.lang = localeConfig.code?.split('-')[0] || ''
    }

    const saveLocale = (lang: ILocaleConfig) => {
        localStorage.setItem('locale', JSON.stringify(lang))
        selectedFlag.value = lang.flag
        locale.value = lang.code
        localeBoot.value = lang.code
    }

    const getDefaultLocale = () => {
        const defaultLocale = languages.value[0]
        const navLanguage = navigator.language.toLowerCase()
        const systemLocale =
            languages.value.find(
                lang => lang.code.toLowerCase() === navLanguage
            ) || defaultLocale

        return systemLocale
    }

    return {
        loadLocale,
        saveLocale,
        languages,
        selectedFlag,
        getDefaultLocale
    }
})
