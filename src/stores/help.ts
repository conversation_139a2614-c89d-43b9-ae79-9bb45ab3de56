import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
    HelpData,
    FormHelpData,
    HelpExibicao,
    HelpSearchParams
} from '@/interfaces/Help'

export const useHelpStore = defineStore('help', () => {
    // Estado
    const helps = ref<HelpData[]>([])
    const loading = ref(false)
    const error = ref<string | null>(null)

    // Mock data inicial
    const mockHelps: HelpData[] = [
        {
            id: 1,
            titulo: 'AJUDA DASHBOARD',
            conteudo: '<h1>Dashboard</h1><p>Esta é a página principal do sistema onde você pode visualizar informações importantes.</p>',
            modulo: 'Dashboard',
            rota: '/dashboard',
            ativo: true
        },
        {
            id: 2,
            titulo: 'AJUDA NCM',
            conteudo: '<h1>NCM - Nomenclatura Comum do Mercosul</h1><p>Aqui você pode gerenciar os códigos NCM dos produtos.</p>',
            modulo: 'Cadastro Geral',
            rota: '/cadastro-geral/ncm',
            ativo: true
        }
    ]

    // Inicializar dados mock
    const initializeMockData = () => {
        if (helps.value.length > 0) return

        // Carregar dados do localStorage ou usar mock
        const storedHelps = localStorage.getItem('mock-helps')

        if (storedHelps) {
            helps.value = JSON.parse(storedHelps)
        } else {
            helps.value = mockHelps
            localStorage.setItem('mock-helps', JSON.stringify(mockHelps))
        }
    }

    // Computed
    const helpsAtivos = computed(() => helps.value.filter(h => h.ativo))

    const helpsComInfo = computed(() => {
        return helpsAtivos.value.map(
            help =>
                ({
                    ...help,
                    moduloDescricao: help.modulo,
                    rotaDescricao: help.rota
                }) as HelpExibicao
        )
    })

    // Actions - CRUD
    const criarHelp = async (dados: FormHelpData): Promise<HelpData> => {
        loading.value = true
        error.value = null

        try {
            await new Promise(resolve => setTimeout(resolve, 500))

            const novoId = Math.max(...helps.value.map(h => h.id || 0), 0) + 1
            const novaHelp: HelpData = {
                id: novoId,
                titulo: dados.titulo,
                conteudo: dados.conteudo,
                modulo: dados.modulo,
                rota: dados.rota,
                ativo: dados.ativo
            }

            helps.value.push(novaHelp)
            localStorage.setItem('mock-helps', JSON.stringify(helps.value))

            return novaHelp
        } catch (err) {
            error.value = 'Erro ao criar ajuda'
            throw err
        } finally {
            loading.value = false
        }
    }

    const atualizarHelp = async (id: number, dados: FormHelpData): Promise<HelpData> => {
        loading.value = true
        error.value = null

        try {
            await new Promise(resolve => setTimeout(resolve, 500))

            const index = helps.value.findIndex(h => h.id === id)
            if (index === -1) {
                throw new Error('Ajuda não encontrada')
            }

            const helpExistente = helps.value[index]
            if (!helpExistente || !helpExistente.id) {
                throw new Error('Ajuda não encontrada')
            }

            const helpAtualizada: HelpData = {
                id: helpExistente.id,
                titulo: dados.titulo,
                conteudo: dados.conteudo,
                modulo: dados.modulo,
                rota: dados.rota,
                ativo: dados.ativo
            }

            helps.value[index] = helpAtualizada
            localStorage.setItem('mock-helps', JSON.stringify(helps.value))

            return helpAtualizada
        } catch (err) {
            error.value = 'Erro ao atualizar ajuda'
            throw err
        } finally {
            loading.value = false
        }
    }

    const excluirHelp = async (id: number): Promise<void> => {
        loading.value = true
        error.value = null

        try {
            await new Promise(resolve => setTimeout(resolve, 300))

            const index = helps.value.findIndex(h => h.id === id)
            if (index === -1) {
                throw new Error('Ajuda não encontrada')
            }

            helps.value.splice(index, 1)
            localStorage.setItem('mock-helps', JSON.stringify(helps.value))
        } catch (err) {
            error.value = 'Erro ao excluir ajuda'
            throw err
        } finally {
            loading.value = false
        }
    }

    // Actions - Busca
    const buscarHelpPorRota = (rota: string): HelpData | null => {
        const rotaNormalizada = rota.replace(/\/$/, '') || '/'
        return helpsAtivos.value.find(help => help.rota === rotaNormalizada) || null
    }

    const buscarHelps = async (params: HelpSearchParams): Promise<HelpData[]> => {
        loading.value = true
        error.value = null

        try {
            await new Promise(resolve => setTimeout(resolve, 300))

            let resultado = [...helps.value]

            if (params.modulo) {
                resultado = resultado.filter(h => h.modulo === params.modulo)
            }

            if (params.rota) {
                resultado = resultado.filter(h => h.rota.includes(params.rota!))
            }

            if (params.ativo !== undefined) {
                resultado = resultado.filter(h => h.ativo === params.ativo)
            }

            if (params.termo) {
                const termo = params.termo.toLowerCase()
                resultado = resultado.filter(
                    h =>
                        h.titulo.toLowerCase().includes(termo) ||
                        h.conteudo.toLowerCase().includes(termo)
                )
            }

            return resultado
        } catch (err) {
            error.value = 'Erro ao buscar ajudas'
            throw err
        } finally {
            loading.value = false
        }
    }

    const obterHelp = async (id: number): Promise<HelpData | null> => {
        loading.value = true
        error.value = null

        try {
            await new Promise(resolve => setTimeout(resolve, 200))

            return helps.value.find(h => h.id === id) || null
        } catch (err) {
            error.value = 'Erro ao obter ajuda'
            throw err
        } finally {
            loading.value = false
        }
    }

    // Actions - Utilitários
    const carregarHelps = async (): Promise<void> => {
        loading.value = true
        error.value = null

        try {
            await new Promise(resolve => setTimeout(resolve, 300))

            if (helps.value.length === 0) {
                initializeMockData()
            }
        } catch (err) {
            error.value = 'Erro ao carregar ajudas'
            throw err
        } finally {
            loading.value = false
        }
    }

    const limparErro = () => {
        error.value = null
    }

    const resetarStore = () => {
        helps.value = []
        loading.value = false
        error.value = null
        localStorage.removeItem('mock-helps')
    }

    // Verificar se uma rota tem ajuda disponível
    const rotaTemAjuda = (rota: string): boolean => {
        return buscarHelpPorRota(rota) !== null
    }

    // Inicializar dados mock na criação do store
    initializeMockData()

    return {
        // Estado
        helps,
        loading,
        error,

        // Computed
        helpsAtivos,
        helpsComInfo,

        // Actions - CRUD
        criarHelp,
        atualizarHelp,
        excluirHelp,

        // Actions - Busca
        buscarHelpPorRota,
        buscarHelps,
        obterHelp,
        rotaTemAjuda,

        // Actions - Utilitários
        carregarHelps,
        limparErro,
        resetarStore
    }
})
