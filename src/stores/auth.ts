import { ref } from 'vue'
import { defineStore } from 'pinia'
import http from '@/services/http'
import { i18n } from '@/boot/i18n'
import type {
    ICustomAxiosRequestConfig,
    IErrorsMessages,
    IHttpMethod
} from 'src/interfaces/Requests'
import type {
    IUserPermissions,
    IUserWithPermissions,
    IUserProfile
} from '@/interfaces/Permissions'
import { useFormUtils } from '@/composables/formUtils'
import { useRouter } from 'vue-router'

/**
 * @description
 * This store is responsible for managing the authentication state of the application.
 * It provides methods for authenticating users, checking authentication status, and logging out.
 *
 * @returns {Object} An object containing the token, user, setToken, authenticateUser, setUser, setProfile, logout, checkToken, and asyncRequest methods.
 */
export const useAuthStore = defineStore('auth', () => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { t } = i18n.global as any
    const router = useRouter()
    const { covertToFormData } = useFormUtils()

    const token = ref(localStorage.getItem('token') || '')

    // Inicializar user de forma mais segura
    const initializeUser = (): IUserWithPermissions => {
        try {
            const userData = localStorage.getItem('user')
            if (userData) {
                const parsed = JSON.parse(userData)
                // Verificar se tem as propriedades mínimas necessárias
                if (parsed && typeof parsed === 'object') {
                    return parsed as IUserWithPermissions
                }
            }
        } catch {
            //
        }
        return {} as IUserWithPermissions
    }

    const user = ref<IUserWithPermissions>(initializeUser())
    const isLoading = ref(false)

    async function authenticateUser(formLogin: {
        username: string
        password: string
    }) {
        isLoading.value = true
        try {
            const dataFormLogin = covertToFormData(formLogin)
            const { data } = await asyncRequest({
                endpoint: '/authenticate/token/',
                method: 'post',
                params: dataFormLogin,
                customErrorMessages: {
                    400: t('errors.requests.login.400')
                },
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            })

            return { token: data.access_token, user: data.user }
        } finally {
            isLoading.value = false
        }
    }

    function setToken(tokenValue: string) {
        if (!tokenValue) {
            return
        }
        localStorage.setItem('token', tokenValue)
        token.value = tokenValue
    }

    function setUser(userValue: IUserWithPermissions | object) {
        if (!userValue) {
            return
        }
        localStorage.setItem('user', JSON.stringify(userValue))
        user.value = userValue as IUserWithPermissions
    }

    function setProfile(profile: IUserProfile | object) {
        if (!profile) {
            return
        }
        const user = JSON.parse(localStorage.getItem('user') || '{}')
        user.profile = profile
        setUser(user)
    }

    async function checkToken() {
        try {
            const { data } = await asyncRequest({
                endpoint: '/auth/check-token',
                method: 'get'
            })
            return data.isAuth
        } catch (error) {
            throw new Error((error as Error).message)
        }
    }

    async function logout() {
        await asyncRequest({
            endpoint: '/api/corpsystem/produto-service/pessoa-usuario/logout',
            method: 'get'
        })
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        token.value = ''
        user.value = {} as IUserWithPermissions
        router.push('/login')
    }

    /**
     * Busca as permissões do usuário e salva no localStorage
     */
    async function fetchUserPermissions(
        userId: number
    ): Promise<IUserPermissions> {
        try {
            // Buscar permissões de módulos
            const modulesResponse = await asyncRequest({
                endpoint: '/api/corpsystem/produto-service/pn-usuario-modulo',
                method: 'get',
                params: {
                    id_pn_usuario: userId,
                    page: 1,
                    page_size: 10000
                }
            })

            // Buscar permissões de rotas
            const routesResponse = await asyncRequest({
                endpoint: '/api/corpsystem/produto-service/pn-usuario-rota',
                method: 'get',
                params: {
                    id_pn_usuario: userId,
                    page: 1,
                    page_size: 10000
                }
            })

            // Buscar permissões de ações
            const actionsResponse = await asyncRequest({
                endpoint:
                    '/api/corpsystem/produto-service/pn-usuario-acao-rota',
                method: 'get',
                params: {
                    id_pn_usuario: userId,
                    page: 1,
                    page_size: 10000
                }
            })

            const permissions: IUserPermissions = {
                modules: modulesResponse.data.results || [],
                routes: routesResponse.data.results || [],
                actions: actionsResponse.data.results || []
            }

            return permissions
        } catch {
            // Retornar permissões vazias em caso de erro
            return {
                modules: [],
                routes: [],
                actions: []
            }
        }
    }

    /**
     * Salva as permissões do usuário no localStorage
     */
    function setUserPermissions(permissions: IUserPermissions) {
        const currentUser = JSON.parse(localStorage.getItem('user') || '{}')
        currentUser.permissions = permissions
        setUser(currentUser)
    }

    /**
     * Obtém as permissões do usuário do localStorage
     */
    function getUserPermissions(): IUserPermissions | null {
        const currentUser = JSON.parse(localStorage.getItem('user') || '{}')
        return currentUser.permissions || null
    }

    /**
     * Verifica se o usuário é admin (acesso irrestrito a módulos/rotas/ações ativos)
     */
    function isAdmin(): boolean {
        const currentUser = JSON.parse(localStorage.getItem('user') || '{}')
        return currentUser.admin === true
    }

    /**
     * Verifica se o usuário é staff (sysadmin - acesso irrestrito + rotas exclusivas)
     */
    function isStaff(): boolean {
        const currentUser = JSON.parse(localStorage.getItem('user') || '{}')
        return currentUser.staff === true
    }

    /**
     * Verifica se o usuário tem privilégios especiais (admin ou staff)
     */
    function hasSpecialPrivileges(): boolean {
        return isAdmin() || isStaff()
    }

    /**
     * Recarrega as informações do usuário e suas permissões
     * Útil para atualizar dados após F5 ou reload da página
     */
    async function reloadUserData(): Promise<void> {
        if (!token.value || !user.value?.profile?.id) {
            return
        }

        isLoading.value = true
        try {
            // Recarregar permissões do usuário usando o profile.id
            const permissions = await fetchUserPermissions(
                user.value.profile.id
            )
            setUserPermissions(permissions)
        } catch {
            //
        } finally {
            isLoading.value = false
        }
    }

    /**
     * Verifica se o usuário tem permissão para acessar um módulo por ID
     */
    function hasModulePermission(moduleId: number): boolean {
        // Admin e Staff têm acesso irrestrito a módulos ativos
        if (hasSpecialPrivileges()) {
            return true // Será validado contra módulos ativos no composable
        }

        const permissions = getUserPermissions()
        if (!permissions) return false
        return permissions.modules.some(m => m.id_modulo === moduleId)
    }

    /**
     * Verifica se o usuário tem permissão para acessar um módulo por descrição
     */
    function hasModulePermissionByDescription(
        moduleDescription: string
    ): boolean {
        // Admin e Staff têm acesso irrestrito
        if (hasSpecialPrivileges()) {
            return true
        }

        const permissions = getUserPermissions()
        if (!permissions) return false

        return permissions.modules.some(
            module =>
                module.id_modulo_descricao?.toUpperCase() ===
                moduleDescription.toUpperCase()
        )
    }

    /**
     * Verifica se o usuário tem permissão para acessar uma rota por ID
     */
    function hasRoutePermission(routeId: number): boolean {
        // Admin e Staff têm acesso irrestrito a rotas ativas
        if (hasSpecialPrivileges()) {
            return true // Será validado contra rotas ativas no composable
        }

        const permissions = getUserPermissions()
        if (!permissions) return false
        return permissions.routes.some(r => r.id_rota === routeId)
    }

    /**
     * Verifica se o usuário tem permissão para acessar uma rota por descrição
     */
    function hasRoutePermissionByDescription(
        routeDescription: string
    ): boolean {
        // Admin e Staff têm acesso irrestrito
        if (hasSpecialPrivileges()) {
            return true
        }

        const permissions = getUserPermissions()
        if (!permissions) return false

        return permissions.routes.some(
            route =>
                route.id_rota_descricao?.toUpperCase() ===
                routeDescription.toUpperCase()
        )
    }

    /**
     * Verifica se o usuário tem permissão para uma ação
     */
    function hasActionPermission(actionId: number): boolean {
        // Admin e Staff têm acesso irrestrito a ações ativas
        if (hasSpecialPrivileges()) {
            return true // Será validado contra ações ativas no composable
        }

        const permissions = getUserPermissions()
        if (!permissions) return false
        return permissions.actions.some(a => a.id_acao_rota === actionId)
    }

    function asyncRequest({
        endpoint,
        method,
        params,
        customErrorMessages,
        axiosApi = http,
        headers
    }: {
        endpoint: string
        method: IHttpMethod
        params?: unknown
        customErrorMessages?: IErrorsMessages
        axiosApi?: typeof http
        headers?: Record<string, string>
    }) {
        const config: ICustomAxiosRequestConfig = {
            customErrorMessages
        }

        if (method === 'get') {
            return headers
                ? axiosApi.get(endpoint, {
                      params,
                      headers,
                      ...config
                  })
                : axiosApi.get(endpoint, {
                      params,
                      ...config
                  })
        }

        return headers
            ? axiosApi[method](endpoint, params, {
                  headers,
                  ...config
              })
            : axiosApi[method](endpoint, params, config)
    }

    return {
        token,
        user,
        isLoading,
        setToken,
        authenticateUser,
        setUser,
        setProfile,
        logout,
        checkToken,
        asyncRequest,
        // Funções de permissões
        fetchUserPermissions,
        setUserPermissions,
        getUserPermissions,
        hasModulePermission,
        hasModulePermissionByDescription,
        hasRoutePermission,
        hasRoutePermissionByDescription,
        hasActionPermission,
        // Funções de privilégios especiais
        isAdmin,
        isStaff,
        hasSpecialPrivileges,
        // Função de reload
        reloadUserData
    }
})
