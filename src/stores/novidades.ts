import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
    NovidadesData,
    NovidadeExibicao,
    NovidadeVista
} from '@/interfaces/Novidades'

// Interface para dados de criação/edição
interface NovidadeForm {
    titulo: string
    descricao: string
    modulo: string
    rota: string
    ativo: boolean
}

// Interface para filtros
interface NovidadesFiltros {
    status?: 'all' | 'new' | 'viewed' | null
    modulo?: string | null
    busca?: string | null
    ativo?: boolean
}

export const useNovidadesStore = defineStore('novidades', () => {
    // Estado
    const novidades = ref<NovidadesData[]>([])
    const novidadesVistas = ref<NovidadeVista[]>([])
    const loading = ref(false)
    const error = ref<string | null>(null)

    // Mock data inicial
    const initializeMockData = () => {
        const mockNovidades: NovidadesData[] = [
            {
                id: 1,
                titulo: 'NOVA FUNCIONALIDADE DE RELATÓRIOS',
                descricao: `
                    <h3>Nova Funcionalidade de Relatórios Avançados</h3>
                    <p>Implementamos uma nova funcionalidade de relatórios que permite gerar relatórios personalizados com filtros avançados e múltiplas opções de exportação.</p>

                    <h4>Principais recursos:</h4>
                    <ul>
                        <li><strong>Filtros por data:</strong> Selecione períodos específicos para análise</li>
                        <li><strong>Filtros por módulo:</strong> Gere relatórios específicos por área</li>
                        <li><strong>Exportação em PDF e Excel:</strong> Múltiplos formatos disponíveis</li>
                        <li><strong>Agendamento de relatórios:</strong> Configure relatórios automáticos</li>
                        <li><strong>Templates personalizados:</strong> Crie seus próprios modelos</li>
                    </ul>

                    <h4>Como usar:</h4>
                    <p>Acesse o menu <strong>Relatórios → Relatórios Personalizados</strong> e explore as novas opções disponíveis.</p>

                    <p><em>Esta funcionalidade está disponível para todos os usuários com permissão de relatórios.</em></p>
                `,
                modulo: 'SISTEMA',
                rota: '/relatorios',
                ativo: true,
                dataPublicacao: '2024-01-14',
                dataAtualizacao: '2024-01-14'
            },
            {
                id: 2,
                titulo: 'ATUALIZAÇÃO DO MÓDULO FINANCEIRO',
                descricao: `
                    <h3>Grandes Melhorias no Módulo Financeiro</h3>
                    <p>O módulo financeiro recebeu importantes atualizações para melhorar significativamente a gestão de contas a pagar e receber.</p>

                    <h4>Principais melhorias:</h4>
                    <ul>
                        <li><strong>Nova interface mais intuitiva:</strong> Design renovado e mais fácil de usar</li>
                        <li><strong>Integração com bancos:</strong> Conecte-se diretamente com seu banco</li>
                        <li><strong>Conciliação automática:</strong> Reconcilie transações automaticamente</li>
                        <li><strong>Dashboards financeiros:</strong> Visualize indicadores em tempo real</li>
                        <li><strong>Fluxo de caixa projetado:</strong> Planeje seu futuro financeiro</li>
                    </ul>

                    <h4>Novos recursos:</h4>
                    <ul>
                        <li>Centro de custos aprimorado</li>
                        <li>Categorização automática de despesas</li>
                        <li>Alertas de vencimento personalizáveis</li>
                        <li>Relatórios DRE automáticos</li>
                    </ul>

                    <p><strong>Importante:</strong> Recomendamos fazer backup dos dados antes de utilizar as novas funcionalidades.</p>
                `,
                modulo: 'FINANCEIRO',
                rota: '/financeiro',
                ativo: true,
                dataPublicacao: '2024-01-10',
                dataAtualizacao: '2024-01-10'
            },
            {
                id: 3,
                titulo: 'CORREÇÕES DE BUGS NO ESTOQUE',
                descricao: `
                    <h3>Correções Importantes no Módulo de Estoque</h3>
                    <p>Foram corrigidos diversos bugs críticos no módulo de estoque que estavam afetando o controle de movimentações e a precisão dos dados.</p>

                    <h4>Bugs corrigidos:</h4>
                    <ul>
                        <li><strong>Erro no cálculo de saldo:</strong> Corrigido problema que causava divergências no saldo atual</li>
                        <li><strong>Problema na importação de produtos:</strong> Resolvido erro ao importar planilhas com caracteres especiais</li>
                        <li><strong>Falha na geração de etiquetas:</strong> Corrigido problema de formatação nas etiquetas de código de barras</li>
                        <li><strong>Lentidão em relatórios:</strong> Otimizada consulta de movimentações para relatórios grandes</li>
                        <li><strong>Erro de duplicação:</strong> Corrigido bug que duplicava produtos em certas condições</li>
                    </ul>

                    <h4>Melhorias adicionais:</h4>
                    <ul>
                        <li>Performance melhorada em 40% nas consultas</li>
                        <li>Interface mais responsiva</li>
                        <li>Validações aprimoradas nos formulários</li>
                    </ul>

                    <p><em>Recomendamos verificar os saldos após a atualização para garantir a consistência dos dados.</em></p>
                `,
                modulo: 'ESTOQUE',
                rota: '/estoque',
                ativo: true,
                dataPublicacao: '2024-01-08',
                dataAtualizacao: '2024-01-08'
            },
            {
                id: 4,
                titulo: 'NOVA INTEGRAÇÃO COM E-COMMERCE',
                descricao: `
                    <h3>Integração Completa com Plataformas de E-commerce</h3>
                    <p>Agora você pode integrar seu ERP diretamente com as principais plataformas de e-commerce do mercado.</p>

                    <h4>Plataformas suportadas:</h4>
                    <ul>
                        <li>Shopify</li>
                        <li>WooCommerce</li>
                        <li>Magento</li>
                        <li>Mercado Livre</li>
                        <li>Amazon</li>
                    </ul>

                    <h4>Funcionalidades:</h4>
                    <ul>
                        <li>Sincronização automática de produtos</li>
                        <li>Controle de estoque em tempo real</li>
                        <li>Importação automática de pedidos</li>
                        <li>Atualização de preços sincronizada</li>
                    </ul>
                `,
                modulo: 'VENDAS',
                rota: '/vendas/ecommerce',
                ativo: true,
                dataPublicacao: '2024-01-05',
                dataAtualizacao: '2024-01-05'
            },
            {
                id: 5,
                titulo: 'MANUTENÇÃO PROGRAMADA - FINAL DE SEMANA',
                descricao: `
                    <h3>Manutenção Programada do Sistema</h3>
                    <p><strong>Data:</strong> Sábado, 20 de Janeiro de 2024</p>
                    <p><strong>Horário:</strong> 22:00 às 06:00 (horário de Brasília)</p>

                    <h4>O que será feito:</h4>
                    <ul>
                        <li>Atualização dos servidores</li>
                        <li>Otimização do banco de dados</li>
                        <li>Aplicação de patches de segurança</li>
                        <li>Backup completo do sistema</li>
                    </ul>

                    <p><strong>Impacto:</strong> O sistema ficará indisponível durante este período.</p>
                    <p><em>Recomendamos finalizar todas as operações importantes antes do horário de manutenção.</em></p>
                `,
                modulo: 'SISTEMA',
                rota: '/sistema/manutencao',
                ativo: true,
                dataPublicacao: '2024-01-03',
                dataAtualizacao: '2024-01-03'
            }
        ]

        // Carregar dados do localStorage ou usar mock
        const storedNovidades = localStorage.getItem('mock-novidades')
        const storedVistas = localStorage.getItem('mock-novidades-vistas')

        if (storedNovidades) {
            novidades.value = JSON.parse(storedNovidades)
        } else {
            novidades.value = mockNovidades
            localStorage.setItem(
                'mock-novidades',
                JSON.stringify(mockNovidades)
            )
        }

        if (storedVistas) {
            novidadesVistas.value = JSON.parse(storedVistas)
        } else {
            novidadesVistas.value = []
        }
    }

    // Computed
    const novidadesAtivas = computed(() => novidades.value.filter(n => n.ativo))

    const novidadesComStatus = computed(() => {
        const usuarioId = 1 // Mock - em produção viria do store de auth

        return novidadesAtivas.value.map(novidade => {
            const vista = novidadesVistas.value.find(
                v => v.novidadeId === novidade.id && v.usuarioId === usuarioId
            )

            return {
                ...novidade,
                vista: !!vista,
                dataVista: vista?.dataVista
            } as NovidadeExibicao
        })
    })

    const totalNovas = computed(
        () => novidadesComStatus.value.filter(n => !n.vista).length
    )

    // Actions - CRUD Novidades
    const criarNovidade = async (
        dados: NovidadeForm
    ): Promise<NovidadesData> => {
        loading.value = true
        error.value = null

        try {
            // Simular delay de API
            await new Promise(resolve => setTimeout(resolve, 500))

            const novoId = Math.max(...novidades.value.map(n => n.id || 0)) + 1
            const agora = new Date().toISOString().split('T')[0]

            const novaNovidade: NovidadesData = {
                id: novoId,
                titulo: dados.titulo,
                descricao: dados.descricao,
                modulo: dados.modulo,
                rota: dados.rota,
                ativo: dados.ativo,
                dataPublicacao: agora as string,
                dataAtualizacao: agora as string
            }

            novidades.value.unshift(novaNovidade)
            localStorage.setItem(
                'mock-novidades',
                JSON.stringify(novidades.value)
            )

            return novaNovidade
        } catch (err) {
            error.value = 'Erro ao criar novidade'
            throw err
        } finally {
            loading.value = false
        }
    }

    const atualizarNovidade = async (
        id: number,
        dados: Partial<NovidadesData>
    ): Promise<NovidadesData> => {
        loading.value = true
        error.value = null

        try {
            await new Promise(resolve => setTimeout(resolve, 500))

            const index = novidades.value.findIndex(n => n.id === id)
            if (index === -1) {
                throw new Error('Novidade não encontrada')
            }

            const novidadeAtualizada = {
                ...novidades.value[index],
                ...dados,
                dataAtualizacao: new Date().toISOString().split('T')[0]
            } as NovidadesData

            novidades.value[index] = novidadeAtualizada
            localStorage.setItem(
                'mock-novidades',
                JSON.stringify(novidades.value)
            )

            return novidadeAtualizada
        } catch (err) {
            error.value = 'Erro ao atualizar novidade'
            throw err
        } finally {
            loading.value = false
        }
    }

    const excluirNovidade = async (id: number): Promise<void> => {
        loading.value = true
        error.value = null

        try {
            await new Promise(resolve => setTimeout(resolve, 500))

            const index = novidades.value.findIndex(n => n.id === id)
            if (index === -1) {
                throw new Error('Novidade não encontrada')
            }

            novidades.value.splice(index, 1)
            localStorage.setItem(
                'mock-novidades',
                JSON.stringify(novidades.value)
            )

            // Remover também as visualizações desta novidade
            novidadesVistas.value = novidadesVistas.value.filter(
                v => v.novidadeId !== id
            )
            localStorage.setItem(
                'mock-novidades-vistas',
                JSON.stringify(novidadesVistas.value)
            )
        } catch (err) {
            error.value = 'Erro ao excluir novidade'
            throw err
        } finally {
            loading.value = false
        }
    }

    const excluirMultiplasNovidades = async (ids: number[]): Promise<void> => {
        loading.value = true
        error.value = null

        try {
            await new Promise(resolve => setTimeout(resolve, 800))

            novidades.value = novidades.value.filter(
                n => !ids.includes(n.id || 0)
            )
            localStorage.setItem(
                'mock-novidades',
                JSON.stringify(novidades.value)
            )

            // Remover visualizações das novidades excluídas
            novidadesVistas.value = novidadesVistas.value.filter(
                v => !ids.includes(v.novidadeId)
            )
            localStorage.setItem(
                'mock-novidades-vistas',
                JSON.stringify(novidadesVistas.value)
            )
        } catch (err) {
            error.value = 'Erro ao excluir novidades'
            throw err
        } finally {
            loading.value = false
        }
    }

    // Actions - Controle de Visualização
    const marcarComoVista = async (
        novidadeId: number,
        usuarioId: number = 1
    ): Promise<void> => {
        try {
            // Verificar se já foi vista
            const jaVista = novidadesVistas.value.find(
                v => v.novidadeId === novidadeId && v.usuarioId === usuarioId
            )

            if (jaVista) {
                return // Já foi vista
            }

            // Simular delay de API
            await new Promise(resolve => setTimeout(resolve, 200))

            const novaVisualizacao: NovidadeVista = {
                novidadeId,
                usuarioId,
                dataVista: new Date().toISOString()
            }

            novidadesVistas.value.push(novaVisualizacao)
            localStorage.setItem(
                'mock-novidades-vistas',
                JSON.stringify(novidadesVistas.value)
            )
        } catch (err) {
            error.value = 'Erro ao marcar novidade como vista'
            throw err
        }
    }

    const marcarComoNaoVista = async (
        novidadeId: number,
        usuarioId: number = 1
    ): Promise<void> => {
        try {
            await new Promise(resolve => setTimeout(resolve, 200))

            novidadesVistas.value = novidadesVistas.value.filter(
                v => !(v.novidadeId === novidadeId && v.usuarioId === usuarioId)
            )
            localStorage.setItem(
                'mock-novidades-vistas',
                JSON.stringify(novidadesVistas.value)
            )
        } catch (err) {
            error.value = 'Erro ao desmarcar novidade como vista'
            throw err
        }
    }

    const marcarTodasComoVistas = async (
        usuarioId: number = 1
    ): Promise<void> => {
        loading.value = true
        error.value = null

        try {
            await new Promise(resolve => setTimeout(resolve, 500))

            const novasVisualizacoes: NovidadeVista[] = []
            const agora = new Date().toISOString()

            novidadesAtivas.value.forEach(novidade => {
                const jaVista = novidadesVistas.value.find(
                    v =>
                        v.novidadeId === novidade.id &&
                        v.usuarioId === usuarioId
                )

                if (!jaVista && novidade.id) {
                    novasVisualizacoes.push({
                        novidadeId: novidade.id,
                        usuarioId,
                        dataVista: agora
                    })
                }
            })

            novidadesVistas.value.push(...novasVisualizacoes)
            localStorage.setItem(
                'mock-novidades-vistas',
                JSON.stringify(novidadesVistas.value)
            )
        } catch (err) {
            error.value = 'Erro ao marcar todas as novidades como vistas'
            throw err
        } finally {
            loading.value = false
        }
    }

    // Actions - Busca e Filtros
    const buscarNovidades = (
        filtros: NovidadesFiltros = {}
    ): NovidadeExibicao[] => {
        let resultado = [...novidadesComStatus.value]

        // Filtro por status
        if (filtros.status === 'new') {
            resultado = resultado.filter(n => !n.vista)
        } else if (filtros.status === 'viewed') {
            resultado = resultado.filter(n => n.vista)
        }

        // Filtro por módulo
        if (filtros.modulo) {
            resultado = resultado.filter(n => n.modulo === filtros.modulo)
        }

        // Filtro por busca (título e descrição)
        if (filtros.busca) {
            const termo = filtros.busca.toLowerCase()
            resultado = resultado.filter(
                n =>
                    n.titulo.toLowerCase().includes(termo) ||
                    n.descricao.toLowerCase().includes(termo)
            )
        }

        // Filtro por ativo
        if (filtros.ativo !== undefined) {
            resultado = resultado.filter(n => n.ativo === filtros.ativo)
        }

        // Ordenar por data de publicação (mais recentes primeiro)
        return resultado.sort(
            (a, b) =>
                new Date(b.dataPublicacao).getTime() -
                new Date(a.dataPublicacao).getTime()
        )
    }

    const obterNovidade = (id: number): NovidadesData | undefined => {
        return novidades.value.find(n => n.id === id)
    }

    const obterNovidadeComStatus = (
        id: number,
        usuarioId: number = 1
    ): NovidadeExibicao | undefined => {
        const novidade = obterNovidade(id)
        if (!novidade) return undefined

        const vista = novidadesVistas.value.find(
            v => v.novidadeId === id && v.usuarioId === usuarioId
        )

        return {
            ...novidade,
            vista: !!vista,
            dataVista: vista?.dataVista
        }
    }

    // Actions - Utilitários
    const carregarNovidades = async (): Promise<void> => {
        loading.value = true
        error.value = null

        try {
            // Simular carregamento de API
            await new Promise(resolve => setTimeout(resolve, 300))

            // Em produção, aqui faria a chamada para a API
            // const response = await api.get('/novidades')
            // novidades.value = response.data

            // Por enquanto, apenas inicializa os dados mock se necessário
            if (novidades.value.length === 0) {
                initializeMockData()
            }
        } catch (err) {
            error.value = 'Erro ao carregar novidades'
            throw err
        } finally {
            loading.value = false
        }
    }

    const limparErro = () => {
        error.value = null
    }

    const resetarStore = () => {
        novidades.value = []
        novidadesVistas.value = []
        loading.value = false
        error.value = null
        localStorage.removeItem('mock-novidades')
        localStorage.removeItem('mock-novidades-vistas')
    }

    // Inicializar dados mock na criação do store
    initializeMockData()

    return {
        // Estado
        novidades,
        novidadesVistas,
        loading,
        error,

        // Computed
        novidadesAtivas,
        novidadesComStatus,
        totalNovas,

        // Actions - CRUD
        criarNovidade,
        atualizarNovidade,
        excluirNovidade,
        excluirMultiplasNovidades,

        // Actions - Visualização
        marcarComoVista,
        marcarComoNaoVista,
        marcarTodasComoVistas,

        // Actions - Busca
        buscarNovidades,
        obterNovidade,
        obterNovidadeComStatus,

        // Actions - Utilitários
        carregarNovidades,
        limparErro,
        resetarStore
    }
})
