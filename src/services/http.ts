/* eslint-disable @typescript-eslint/ban-ts-comment */
import axios, { type AxiosError } from 'axios'
import { i18n } from '@/boot/i18n'
import type {
    ICustomAxiosRequestConfig,
    IHttpStatusCodeErrors
} from 'src/interfaces/Requests'

// Interface para resposta de erro de autenticação
interface AuthErrorResponse {
    detail?: string
}
import { Notify } from 'quasar'

// @ts-ignore
const t = i18n.global.t

import { useEnv } from '@/composables/useEnv'
const { getApiUrl } = useEnv()

const lang = i18n.global.locale.value
const baseURLLocal = getApiUrl()
const http = axios.create({
    baseURL: baseURLLocal,
    headers: {
        'Content-Type': 'application/json'
    }
})

http.interceptors.request.use(
    config => {
        const token = localStorage.getItem('token')
        if (token) {
            config.headers.Authorization = `Bearer ${token}`
            config.headers['Accept-Language'] =
                `${lang},${lang.slice(0, 2)};q=0.9,${lang.slice(
                    0,
                    2
                )};q=0.8,${lang};q=0.7`
        }
        return config
    },
    (error: AxiosError) => {
        return Promise.reject(error)
    }
)

http.interceptors.response.use(
    response => {
        return response
    },
    (error: AxiosError) => {
        const config = error.config as ICustomAxiosRequestConfig | undefined
        const statusCode =
            (error.response?.status as IHttpStatusCodeErrors) || 'noCode'

        // Verificar se é erro de autenticação específico
        const responseData = error.response?.data as AuthErrorResponse
        const isAuthenticationError =
            statusCode === 401 &&
            responseData?.detail ===
                'As credenciais de autenticação não foram fornecidas.'

        // Se for erro de autenticação, redirecionar para home
        if (isAuthenticationError) {
            // Limpar token do localStorage
            localStorage.removeItem('token')

            // Redirecionar para home
            window.location.href = '/login'

            // Não mostrar notificação para este caso específico
            return Promise.reject(error)
        }

        const genericErrors = {
            400: t('errors.requests.generic.400'),
            401: t('errors.requests.generic.401'),
            403: t('errors.requests.generic.403'),
            404: t('errors.requests.generic.404'),
            500: t('errors.requests.generic.500'),
            503: t('errors.requests.generic.503'),
            noCode: t('errors.requests.generic.noCode')
        }

        const errorMessage =
            config?.customErrorMessages?.[statusCode] ||
            genericErrors[statusCode]

        Notify.create({
            message: errorMessage,
            color: 'negative',
            position: 'bottom',
            timeout: 2000,
            actions: [{ label: 'Close', color: 'white' }]
        })
        return Promise.reject(error)
    }
)

export default http
