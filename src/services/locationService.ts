import { Country, State, City } from 'country-state-city'
import type { OptionItem } from '@/interfaces/FormContato'
import * as countries from 'i18n-iso-countries'

// Registrar idiomas suportados
import ptLocale from 'i18n-iso-countries/langs/pt.json'
import enLocale from 'i18n-iso-countries/langs/en.json'
import esLocale from 'i18n-iso-countries/langs/es.json'

countries.registerLocale(ptLocale)
countries.registerLocale(enLocale)
countries.registerLocale(esLocale)

// Interface para dados de localização
export interface LocationData {
    countries: OptionItem[]
    states: { [countryCode: string]: OptionItem[] }
    cities: { [stateCode: string]: OptionItem[] }
    [key: string]: OptionItem[] | { [key: string]: OptionItem[] }
}

// Cache para evitar reprocessamento
let locationCache: LocationData | null = null

// Mapeamento de locales do projeto para a biblioteca
const localeMap: { [key: string]: string } = {
    'pt-BR': 'pt',
    'en-US': 'en',
    'es-PY': 'es',
    pt: 'pt',
    en: 'en',
    es: 'es'
}

// Função para obter todos os países com nomes traduzidos
export const getCountries = (locale: string = 'pt-BR'): OptionItem[] => {
    const cacheKey = `countries_${locale}`

    if (locationCache && locationCache[cacheKey]) {
        return locationCache[cacheKey] as OptionItem[]
    }

    const targetLocale = localeMap[locale] || 'pt'
    const countriesData = Country.getAllCountries()

    const countryOptions = countriesData.map(country => {
        // Obter nome traduzido usando i18n-iso-countries
        const translatedName =
            countries.getName(country.isoCode, targetLocale) || country.name

        return {
            label: translatedName,
            value: country.isoCode,
            flag: country.flag,
            phonecode: country.phonecode
        }
    })

    // Ordenar por nome traduzido no idioma específico
    countryOptions.sort((a, b) => a.label.localeCompare(b.label, locale))

    if (!locationCache) {
        locationCache = { countries: [], states: {}, cities: {} }
    }

    // Armazenar no cache com chave específica do locale
    locationCache[cacheKey] = countryOptions

    return countryOptions
}

// Função para obter países (compatibilidade - usa português por padrão)
export const getCountriesDefault = (): OptionItem[] => {
    return getCountries('pt-BR')
}

// Função para obter estados de um país
export const getStatesByCountry = (countryCode: string): OptionItem[] => {
    if (locationCache?.states[countryCode]) {
        return locationCache.states[countryCode]
    }

    const states = State.getStatesOfCountry(countryCode)
    const stateOptions = states.map(state => ({
        label: state.name,
        value: state.isoCode,
        countryCode: state.countryCode
    }))

    if (!locationCache) {
        locationCache = { countries: [], states: {}, cities: {} }
    }
    if (!locationCache.states) {
        locationCache.states = {}
    }
    locationCache.states[countryCode] = stateOptions

    return stateOptions
}

// Função para obter cidades de um estado
export const getCitiesByState = (
    countryCode: string,
    stateCode: string
): OptionItem[] => {
    const cacheKey = `${countryCode}-${stateCode}`

    if (locationCache?.cities[cacheKey]) {
        return locationCache.cities[cacheKey]
    }

    const cities = City.getCitiesOfState(countryCode, stateCode)
    const cityOptions = cities.map(city => ({
        label: city.name,
        value: city.name,
        stateCode: city.stateCode,
        countryCode: city.countryCode
    }))

    if (!locationCache) {
        locationCache = { countries: [], states: {}, cities: {} }
    }
    if (!locationCache.cities) {
        locationCache.cities = {}
    }
    locationCache.cities[cacheKey] = cityOptions

    return cityOptions
}

// Função para obter países da América Latina com códigos telefônicos
export const getLatinAmericaCountries = (
    locale: string = 'pt-BR'
): OptionItem[] => {
    const latinAmericaCodes = [
        'AR',
        'BO',
        'BR',
        'CL',
        'CO',
        'CR',
        'CU',
        'EC',
        'SV',
        'GT',
        'HN',
        'MX',
        'NI',
        'PA',
        'PY',
        'PE',
        'DO',
        'UY',
        'VE'
    ]

    const allCountries = getCountries(locale)
    return allCountries
        .filter(country => latinAmericaCodes.includes(country.value))
        .map(country => ({
            ...country,
            label: `${country.label} (+${country.phonecode})`,
            codigoTelefone: `+${country.phonecode}`
        }))
}

// Função para obter máscara de telefone por país
export const getPhoneMaskByCountry = (countryCode: string): string => {
    const phoneMasks: { [key: string]: string } = {
        AR: '## #### ####', // Argentina
        BO: '# ### ####', // Bolívia
        BR: '## #####-####', // Brasil
        CL: '# #### ####', // Chile
        CO: '### ### ####', // Colômbia
        CR: '#### ####', // Costa Rica
        CU: '# ### ####', // Cuba
        EC: '## ### ####', // Equador
        SV: '#### ####', // El Salvador
        GT: '#### ####', // Guatemala
        HN: '#### ####', // Honduras
        MX: '## #### ####', // México
        NI: '#### ####', // Nicarágua
        PA: '#### ####', // Panamá
        PY: '### ### ###', // Paraguai
        PE: '### ### ###', // Peru
        DO: '### ####', // República Dominicana
        UY: '# ### ####', // Uruguai
        VE: '### ### ####' // Venezuela
    }

    return phoneMasks[countryCode] || '#####-####'
}

// Função para obter máscara de telefone por país (sem DDD)
export const getPhoneMaskByCountryWithoutDDD = (
    countryCode: string
): string => {
    const phoneMasks: { [key: string]: string } = {
        AR: '#### ####', // Argentina (sem DDD)
        BO: '### ####', // Bolívia (sem DDD)
        BR: '#####-####', // Brasil (sem DDD)
        CL: '#### ####', // Chile (sem DDD)
        CO: '### ####', // Colômbia (sem DDD)
        CR: '#### ####', // Costa Rica
        CU: '### ####', // Cuba (sem DDD)
        EC: '### ####', // Equador (sem DDD)
        SV: '#### ####', // El Salvador
        GT: '#### ####', // Guatemala
        HN: '#### ####', // Honduras
        MX: '#### ####', // México (sem DDD)
        NI: '#### ####', // Nicarágua
        PA: '#### ####', // Panamá
        PY: '### ###', // Paraguai (sem DDD)
        PE: '### ###', // Peru (sem DDD)
        DO: '### ####', // República Dominicana
        UY: '### ####', // Uruguai (sem DDD)
        VE: '### ####' // Venezuela (sem DDD)
    }

    return phoneMasks[countryCode] || '####-####'
}

// Função para limpar cache (útil para testes ou atualizações)
export const clearLocationCache = (): void => {
    locationCache = null
}

// Função para pré-carregar dados de países específicos (otimização)
export const preloadCountryData = (countryCodes: string[]): void => {
    countryCodes.forEach(countryCode => {
        getStatesByCountry(countryCode)
    })
}

// Função para obter nome traduzido de um país específico
export const getCountryNameTranslated = (
    countryCode: string,
    locale: string = 'pt-BR'
): string => {
    const targetLocale = localeMap[locale] || 'pt'
    const translatedName = countries.getName(countryCode, targetLocale)
    return translatedName || countryCode
}
