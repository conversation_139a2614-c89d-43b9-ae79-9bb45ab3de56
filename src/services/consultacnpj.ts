/* eslint-disable @typescript-eslint/ban-ts-comment */
import axios, { type AxiosError } from 'axios'
import { i18n } from '@/boot/i18n'
import type {
    ICustomAxiosRequestConfig,
    IHttpStatusCodeErrors
} from 'src/interfaces/Requests'
import { Notify } from 'quasar'

// @ts-ignore
const t = i18n.global.t

const axiosCnpj = axios.create({
    baseURL: 'https://open.cnpja.com/office/'
})

axiosCnpj.interceptors.response.use(
    response => response,
    (error: AxiosError) => {
        const config = error.config as ICustomAxiosRequestConfig | undefined
        const statusCode =
            (error.response?.status as IHttpStatusCodeErrors) || 'noCode'

        const genericErrors = {
            400: t('errors.requests.generic.400'),
            401: t('errors.requests.generic.401'),
            403: t('errors.requests.generic.403'),
            404: t('errors.requests.generic.404'),
            500: t('errors.requests.generic.500'),
            503: t('errors.requests.generic.503'),
            noCode: t('errors.requests.generic.noCode')
        }

        const errorMessage =
            config?.customErrorMessages?.[statusCode] ||
            genericErrors[statusCode]

        Notify.create({
            message: errorMessage,
            color: 'negative',
            position: 'bottom',
            timeout: 2000,
            actions: [{ label: 'Close', color: 'white' }]
        })
        return Promise.reject(error)
    }
)

export { axiosCnpj }
