{"editor.bracketPairColorization.enabled": true, "editor.guides.bracketPairs": true, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": ["source.fixAll.eslint"], "eslint.validate": ["javascript", "javascriptreact", "typescript", "vue"], "typescript.tsdk": "node_modules/typescript/lib", "i18n-ally.enabledParsers": ["js"], "i18n-ally.localesPaths": ["src/i18n", "coverage/src/i18n"]}