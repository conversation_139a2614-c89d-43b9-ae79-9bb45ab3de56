# Exemplo de Stack para Rancher
# Este arquivo mostra como configurar as variáveis de ambiente no Rancher

apiVersion: apps/v1
kind: Deployment
metadata:
  name: corperp-frontend
  namespace: default
  labels:
    app: corperp-frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: corperp-frontend
  template:
    metadata:
      labels:
        app: corperp-frontend
    spec:
      containers:
      - name: corperp-frontend
        image: corpsystemsolucoesti/corperpfront:latest
        ports:
        - containerPort: 80
        env:
        # ⚠️ CONFIGURE ESTAS VARIÁVEIS NO RANCHER
        - name: VITE_API_URL
          value: "https://api.corperp.com/api"
        - name: VITE_LOGO_SYSTEM
          value: "/src/assets/logos/logo-personalizado.png"
        - name: VITE_IMG_LOGIN_SYSTEM
          value: "/src/assets/logos/logo-corpsystem.png"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: corperp-frontend-service
  namespace: default
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
  selector:
    app: corperp-frontend

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: corperp-frontend-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: corperp.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: corperp-frontend-service
            port:
              number: 80
