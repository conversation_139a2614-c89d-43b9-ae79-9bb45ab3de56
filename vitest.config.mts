/* eslint-disable @typescript-eslint/ban-ts-comment */
import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import { quasar, transformAssetUrls } from '@quasar/vite-plugin'
import tsconfigPaths from 'vite-tsconfig-paths'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './src')
        }
    },
    test: {
        environment: 'jsdom',
        setupFiles: 'test/vitest/setup-file.ts',
        include: [
            'src/**/*.vitest.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
            'test/vitest/__tests__/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'
        ],
        coverage: {
            provider: 'v8',
            reporter: ['text', 'json', 'html'],
            exclude: [
                'node_modules',
                'test',
                'dist',
                'build',
                'coverage',
                'vite.config.ts',
                'vite.config.mts',
                'src/App.vue',
                'src/boot',
                'src/router/**',
                'src/stores/**',
                'src/interfaces/**'
            ],
            include: ['src/**']
        }
    },
    plugins: [
        // @ts-ignore
        vue({
            template: { transformAssetUrls }
        }),
        // @ts-ignore
        quasar({
            sassVariables: 'src/quasar-variables.scss'
        }),
        // @ts-ignore
        tsconfigPaths()
    ]
})
