# Estágio de dependências
FROM node:20-alpine AS deps
WORKDIR /app
COPY package.json ./
# Remover o package-lock.json existente e criar um novo
RUN rm -f package-lock.json && \
    npm install -g @quasar/cli && \
    npm config set fetch-retry-mintimeout 300000 && \
    npm config set fetch-retry-maxtimeout 300000 && \
    npm config set loglevel error
RUN npm install --no-package-lock --force --legacy-peer-deps --ignore-scripts

# Estágio de build
FROM node:20-alpine AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
ENV NODE_ENV=production
RUN npm run postinstall
RUN npm run build

# Estágio de produção
FROM nginx:stable-alpine AS production
COPY --from=builder /app/dist/pwa /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf
COPY entrypoint.sh /entrypoint.sh
RUN rm -f /etc/nginx/conf.d/default.conf.default || true
RUN chmod +x /entrypoint.sh
EXPOSE 80
ENTRYPOINT ["/entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]
