# 📚 Exemplos Práticos do Sistema de Permissões

## 🎯 Casos de Uso Reais

Este documento apresenta exemplos práticos de implementação do sistema de permissões em diferentes cenários.

## 🏪 Exemplo 1: Página de Produto Marca

### Configuração da Rota

```typescript
// src/composables/useRoutePermissions.ts
const routeModuleMapping: IRouteModuleMapping = {
    '/estoque/produto-marca': {
        moduleDescription: 'ESTOQUE',
        routeDescription: 'MARCA',
        modulePath: 'estoque'
    }
}
```

### Implementação Completa

```vue
<!-- src/pages/Estoque/ProdutoMarcaPage.vue -->
<template>
    <q-page class="q-pa-md">
        <!-- Header com ações principais -->
        <div class="page-header row justify-between items-center q-mb-md">
            <h1 class="text-h4">{{ $t('pages.titles.productBrand') }}</h1>
            
            <div class="header-actions row q-gutter-sm">
                <!-- <PERSON><PERSON><PERSON> criar - baseado em permissão -->
                <q-btn
                    v-if="permissions.canCreate"
                    :label="$t('buttons.add')"
                    color="primary"
                    icon="add"
                    @click="openCreateModal"
                />
                
                <!-- Botão admin/staff - baseado em privilégio -->
                <q-btn
                    v-if="isOnlyAdmin"
                    label="Configurações"
                    color="orange"
                    icon="admin_panel_settings"
                    @click="openAdminSettings"
                />
                
                <!-- Botão apenas staff - debug -->
                <q-btn
                    v-if="isOnlyStaff"
                    label="Debug"
                    color="purple"
                    icon="engineering"
                    @click="openDebugTools"
                />
            </div>
        </div>

        <!-- Tabela de dados -->
        <data-table
            :data="marcas"
            :columns="columns"
            :loading="loading"
            selection="multiple"
            v-model:selected="selected"
        >
            <!-- Coluna de ações -->
            <template #body-cell-actions="props">
                <q-td :props="props">
                    <div class="row q-gutter-xs">
                        <!-- Editar -->
                        <q-btn
                            v-if="permissions.canEdit"
                            icon="edit"
                            color="primary"
                            flat
                            round
                            dense
                            size="sm"
                            @click="openEditModal(props.row)"
                        >
                            <q-tooltip>{{ $t('buttons.edit') }}</q-tooltip>
                        </q-btn>
                        
                        <!-- Excluir -->
                        <q-btn
                            v-if="permissions.canDelete"
                            icon="delete"
                            color="negative"
                            flat
                            round
                            dense
                            size="sm"
                            @click="confirmDelete(props.row)"
                        >
                            <q-tooltip>{{ $t('buttons.delete') }}</q-tooltip>
                        </q-btn>
                        
                        <!-- Vincular modelo - sempre disponível -->
                        <q-btn
                            icon="link"
                            color="info"
                            flat
                            round
                            dense
                            size="sm"
                            @click="openMarcaModeloModal(props.row)"
                        >
                            <q-tooltip>{{ $t('buttons.linkModel') }}</q-tooltip>
                        </q-btn>
                    </div>
                </q-td>
            </template>
        </data-table>

        <!-- Ações em lote -->
        <action-collection :actions="batchActions" />

        <!-- Modais -->
        <modal-template v-model="showModal" :title="modalTitle">
            <form-produto-marca
                ref="formRef"
                :item="currentItem"
                :readonly="!canEditCurrent"
            />
            
            <template #actions>
                <q-btn
                    v-if="canEditCurrent"
                    :label="$t('buttons.save')"
                    color="primary"
                    @click="save"
                />
                <q-btn
                    :label="$t('buttons.cancel')"
                    @click="closeModal"
                />
            </template>
        </modal-template>
    </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { Dialog, Notify } from 'quasar'
import { useActionPermissions } from '@/composables/useActionPermissions'

// Composables
const { t: $t } = useI18n()
const { produtoMarcaPermissions, isOnlyAdmin, isOnlyStaff } = useActionPermissions()

// Estado
const marcas = ref([])
const selected = ref([])
const loading = ref(false)
const showModal = ref(false)
const currentItem = ref(null)
const isEditing = ref(false)

// Permissões
const permissions = produtoMarcaPermissions

// Computeds
const modalTitle = computed(() => 
    isEditing.value ? $t('forms.edit') : $t('forms.create')
)

const canEditCurrent = computed(() => 
    isEditing.value ? permissions.value.canEdit : permissions.value.canCreate
)

// Ações em lote
const batchActions = computed(() => [
    {
        label: $t('buttons.deleteSelected'),
        active: selected.value.length > 0 && permissions.value.canDelete,
        icon: 'delete',
        description: $t('buttons.deleteSelectedDescription'),
        action: confirmDeleteMultiple
    },
    {
        label: $t('buttons.linkModelToSelected'),
        active: selected.value.length === 1,
        icon: 'link',
        description: $t('buttons.linkModelDescription'),
        action: () => {
            if (selected.value.length === 1) {
                openMarcaModeloModal(selected.value[0])
            }
        }
    }
])

// Métodos
function openCreateModal() {
    currentItem.value = null
    isEditing.value = false
    showModal.value = true
}

function openEditModal(item: any) {
    currentItem.value = { ...item }
    isEditing.value = true
    showModal.value = true
}

function openAdminSettings() {
    Notify.create({
        message: '🔧 Configurações administrativas abertas!',
        color: 'orange',
        icon: 'admin_panel_settings'
    })
}

function openDebugTools() {
    Notify.create({
        message: '🛠️ Ferramentas de debug abertas!',
        color: 'purple',
        icon: 'engineering'
    })
}

// Inicialização
onMounted(() => {
    if (permissions.value.canView) {
        loadData()
    }
})
</script>
```

## 🏢 Exemplo 2: Página Administrativa

### Configuração

```typescript
// Rota exclusiva para admin e staff
'/admin/usuarios': {
    onlyAdmin: true,
    moduleDescription: 'ADMINISTRAÇÃO',
    routeDescription: 'USUÁRIOS'
}
```

### Implementação

```vue
<!-- src/pages/Admin/UsuariosPage.vue -->
<template>
    <q-page class="q-pa-md">
        <!-- Verificação de acesso -->
        <div v-if="!hasAccess" class="text-center q-pa-xl">
            <q-icon name="lock" size="4rem" color="negative" />
            <h2>Acesso Restrito</h2>
            <p>Esta página é exclusiva para administradores.</p>
        </div>

        <!-- Conteúdo para admin/staff -->
        <div v-else>
            <div class="page-header">
                <h1>Gerenciamento de Usuários</h1>
                
                <!-- Ações específicas por tipo -->
                <div class="admin-actions">
                    <!-- Ações para admin e staff -->
                    <q-btn
                        v-if="isOnlyAdmin"
                        label="Relatório Geral"
                        color="primary"
                        icon="assessment"
                        @click="generateReport"
                    />
                    
                    <!-- Ações apenas para staff -->
                    <q-btn
                        v-if="isOnlyStaff"
                        label="Logs do Sistema"
                        color="purple"
                        icon="list_alt"
                        @click="viewSystemLogs"
                    />
                    
                    <!-- Ações apenas para admin -->
                    <q-btn
                        v-if="isAdmin"
                        label="Configurar Licenças"
                        color="red"
                        icon="key"
                        @click="manageLicenses"
                    />
                </div>
            </div>

            <!-- Lista de usuários -->
            <user-management-table />
        </div>
    </q-page>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoutePermissions } from '@/composables/useRoutePermissions'
import { useActionPermissions } from '@/composables/useActionPermissions'

const { canAccessRoute } = useRoutePermissions()
const { isOnlyAdmin, isOnlyStaff, isAdmin } = useActionPermissions()

const hasAccess = computed(() => canAccessRoute('/admin/usuarios'))
</script>
```

## ⚙️ Exemplo 3: Página de Desenvolvimento

### Configuração

```typescript
// Rota exclusiva para staff
'/dev/tools': {
    staffOnly: true
}
```

### Implementação

```vue
<!-- src/pages/Dev/ToolsPage.vue -->
<template>
    <q-page class="q-pa-md">
        <!-- Só staff pode ver -->
        <div v-if="!isOnlyStaff" class="text-center q-pa-xl">
            <q-icon name="engineering" size="4rem" color="warning" />
            <h2>Área de Desenvolvimento</h2>
            <p>Esta página é exclusiva para a equipe de desenvolvimento.</p>
        </div>

        <div v-else>
            <h1>🛠️ Ferramentas de Desenvolvimento</h1>
            
            <div class="tools-grid">
                <!-- Debug de permissões -->
                <q-card class="tool-card">
                    <q-card-section>
                        <h3>Debug de Permissões</h3>
                        <q-btn @click="debugPermissions">
                            Mostrar Permissões
                        </q-btn>
                    </q-card-section>
                </q-card>
                
                <!-- Logs do sistema -->
                <q-card class="tool-card">
                    <q-card-section>
                        <h3>Logs do Sistema</h3>
                        <q-btn @click="viewLogs">
                            Ver Logs
                        </q-btn>
                    </q-card-section>
                </q-card>
            </div>
        </div>
    </q-page>
</template>

<script setup lang="ts">
import { useActionPermissions } from '@/composables/useActionPermissions'
import { useAuthStore } from '@/stores/auth'

const { isOnlyStaff } = useActionPermissions()
const authStore = useAuthStore()

function debugPermissions() {
    console.log('=== DEBUG DE PERMISSÕES ===')
    console.log('Usuário:', authStore.user)
    console.log('Permissões:', authStore.getUserPermissions())
    console.log('É Admin:', authStore.isAdmin())
    console.log('É Staff:', authStore.isStaff())
}
</script>
```

## 📋 Exemplo 4: Componente de Lista com Filtros

```vue
<!-- src/components/ProductList.vue -->
<template>
    <div class="product-list">
        <!-- Filtros baseados em permissões -->
        <div class="filters" v-if="hasAnyPermission">
            <q-select
                v-if="permissions.canView"
                v-model="statusFilter"
                :options="statusOptions"
                label="Status"
            />
            
            <q-select
                v-if="isOnlyAdmin"
                v-model="userFilter"
                :options="userOptions"
                label="Usuário"
            />
        </div>

        <!-- Lista de produtos -->
        <q-list v-if="permissions.canView">
            <q-item
                v-for="product in filteredProducts"
                :key="product.id"
                clickable
            >
                <q-item-section>
                    <q-item-label>{{ product.name }}</q-item-label>
                    <q-item-label caption>{{ product.description }}</q-item-label>
                </q-item-section>
                
                <q-item-section side>
                    <!-- Ações condicionais -->
                    <div class="row q-gutter-xs">
                        <q-btn
                            v-if="permissions.canEdit"
                            icon="edit"
                            @click="editProduct(product)"
                        />
                        
                        <q-btn
                            v-if="permissions.canDelete && canDeleteProduct(product)"
                            icon="delete"
                            @click="deleteProduct(product)"
                        />
                        
                        <!-- Ação especial para admin -->
                        <q-btn
                            v-if="isOnlyAdmin && product.status === 'pending'"
                            icon="check"
                            color="positive"
                            @click="approveProduct(product)"
                        />
                    </div>
                </q-item-section>
            </q-item>
        </q-list>

        <!-- Mensagem quando não tem permissão -->
        <div v-else class="no-permission">
            <q-banner class="text-negative">
                Você não tem permissão para visualizar produtos.
            </q-banner>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useActionPermissions } from '@/composables/useActionPermissions'

const { produtoMarcaPermissions, isOnlyAdmin } = useActionPermissions()
const permissions = produtoMarcaPermissions

// Verificar se tem alguma permissão
const hasAnyPermission = computed(() => 
    permissions.value.canView || 
    permissions.value.canCreate || 
    permissions.value.canEdit || 
    permissions.value.canDelete
)

// Lógica de negócio combinada com permissões
function canDeleteProduct(product: any): boolean {
    return product.status !== 'published' && 
           product.createdBy === currentUserId.value
}

function approveProduct(product: any) {
    // Ação que só admin pode fazer
    if (!isOnlyAdmin.value) return
    
    // Lógica de aprovação
}
</script>
```

## 🔄 Exemplo 5: Composable Personalizado

```typescript
// src/composables/useCompraPermissions.ts
import { computed } from 'vue'
import { useActionPermissions } from '@/composables/useActionPermissions'

export function useCompraPermissions() {
    const { hasActionPermissionInRoute, isOnlyAdmin, isOnlyStaff } = useActionPermissions()
    
    // Permissões básicas para módulo de compra
    const pedidoPermissions = computed(() => ({
        canCreate: hasActionPermissionInRoute('/compra/pedido', 'CRIAR'),
        canEdit: hasActionPermissionInRoute('/compra/pedido', 'EDITAR'),
        canDelete: hasActionPermissionInRoute('/compra/pedido', 'EXCLUIR'),
        canView: hasActionPermissionInRoute('/compra/pedido', 'VISUALIZAR')
    }))
    
    // Ações específicas do módulo de compra
    const canApprove = computed(() => 
        hasActionPermissionInRoute('/compra/pedido', 'APROVAR') ||
        isOnlyAdmin.value
    )
    
    const canReject = computed(() => 
        hasActionPermissionInRoute('/compra/pedido', 'REJEITAR') ||
        isOnlyAdmin.value
    )
    
    const canGenerateReport = computed(() => 
        hasActionPermissionInRoute('/compra/pedido', 'RELATORIO') ||
        isOnlyAdmin.value
    )
    
    // Ações administrativas
    const canManageSuppliers = computed(() => 
        isOnlyAdmin.value
    )
    
    const canViewSystemMetrics = computed(() => 
        isOnlyStaff.value
    )
    
    // Função para verificar se pode editar pedido específico
    function canEditPedido(pedido: any): boolean {
        if (!pedidoPermissions.value.canEdit) return false
        
        // Regras de negócio
        if (pedido.status === 'APROVADO') return false
        if (pedido.status === 'CANCELADO') return false
        
        // Admin pode editar qualquer pedido
        if (isOnlyAdmin.value) return true
        
        // Usuário normal só pode editar próprios pedidos em rascunho
        return pedido.status === 'RASCUNHO' && 
               pedido.createdBy === getCurrentUserId()
    }
    
    return {
        // Permissões básicas
        pedidoPermissions,
        
        // Ações específicas
        canApprove,
        canReject,
        canGenerateReport,
        
        // Ações administrativas
        canManageSuppliers,
        canViewSystemMetrics,
        
        // Funções específicas
        canEditPedido,
        
        // Privilégios
        isOnlyAdmin,
        isOnlyStaff
    }
}
```

### Uso do Composable

```vue
<!-- src/pages/Compra/PedidoPage.vue -->
<template>
    <q-page>
        <div class="toolbar">
            <q-btn
                v-if="permissions.canCreate"
                label="Novo Pedido"
                @click="createPedido"
            />
            
            <q-btn
                v-if="canGenerateReport"
                label="Relatório"
                @click="generateReport"
            />
            
            <q-btn
                v-if="canManageSuppliers"
                label="Gerenciar Fornecedores"
                @click="manageSuppliers"
            />
        </div>

        <q-table :data="pedidos">
            <template #body-cell-actions="props">
                <q-td>
                    <q-btn
                        v-if="canEditPedido(props.row)"
                        icon="edit"
                        @click="editPedido(props.row)"
                    />
                    
                    <q-btn
                        v-if="canApprove && props.row.status === 'PENDENTE'"
                        icon="check"
                        @click="approvePedido(props.row)"
                    />
                </q-td>
            </template>
        </q-table>
    </q-page>
</template>

<script setup lang="ts">
import { useCompraPermissions } from '@/composables/useCompraPermissions'

const {
    pedidoPermissions: permissions,
    canApprove,
    canGenerateReport,
    canManageSuppliers,
    canEditPedido
} = useCompraPermissions()
</script>
```

## 🎯 Padrões e Boas Práticas

### 1. Sempre Verificar Antes de Executar

```typescript
// ❌ Não fazer
function deleteItem(item: any) {
    api.delete(`/items/${item.id}`)
}

// ✅ Fazer
function deleteItem(item: any) {
    if (!permissions.value.canDelete) {
        Notify.create({
            message: 'Você não tem permissão para excluir',
            color: 'negative'
        })
        return
    }
    
    api.delete(`/items/${item.id}`)
}
```

### 2. Combinar Permissões com Regras de Negócio

```typescript
const canEditOrder = computed(() => 
    permissions.value.canEdit && 
    order.value?.status === 'DRAFT' &&
    order.value?.createdBy === currentUser.value?.id
)
```

### 3. Usar Fallbacks Gracefully

```typescript
const hasAnyAccess = computed(() => 
    permissions.value.canView || 
    permissions.value.canCreate || 
    isOnlyAdmin.value
)
```

---

[← Anterior: Configuração](configuration.md) | [Próximo: API Reference →](api-reference.md)
