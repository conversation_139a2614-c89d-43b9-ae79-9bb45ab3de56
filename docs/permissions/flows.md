# 🔄 Fluxos do Sistema de Permissões

## 📋 Visão Geral

Este documento detalha os fluxos de verificação e validação do sistema de permissões, desde o carregamento inicial até a verificação de ações específicas.

## 🚀 Fluxo de Inicialização

### 1. Login e Carregamento de Permissões

```mermaid
flowchart TD
    A[Usuário insere credenciais] --> B[Validar credenciais]
    B -->|Válidas| C[Obter token de autenticação]
    B -->|Inválidas| Z[Exibir erro]
    C --> D[Buscar dados do perfil]
    D --> E[Salvar perfil no store]
    E --> F[Buscar permissões do usuário]
    F --> G[Processar permissões]
    G --> H[Salvar no localStorage]
    H --> I[Redirecionar para home]
    Z --> A
```

**Código correspondente:**

```typescript
// src/pages/Login/LoginPage.vue
async function login(formLogin: { username: string; password: string }) {
    await setLoading(async () => {
        // 1. Autenticar usuário
        await authUser(formLogin)

        if (auth.token) {
            // 2. Verificar credenciais
            const { data } = await auth.asyncRequest({
                endpoint: '/api/corpsystem/produto-service/login',
                method: 'post',
                data: formLogin
            })

            // 3. Carregar perfil
            const profile = await auth.asyncRequest({
                endpoint: '/api/corpsystem/produto-service/pn-usuario',
                params: { id: data.user_id }
            })
            auth.setProfile(profile.data.results[0])

            // 4. Carregar permissões
            const userPermissions = await auth.fetchUserPermissions(
                profile.data.results[0].id
            )
            auth.setUserPermissions(userPermissions)

            // 5. Redirecionar
            if (data.message === 'Login realizado com sucesso.') {
                router.push('/')
            }
        }
    })
}
```

### 2. Recarregamento de Página (F5)

```mermaid
flowchart TD
    A[Usuário pressiona F5] --> B[Detectar recarregamento]
    B --> C{Usuário logado?}
    C -->|Não| D[Redirecionar para login]
    C -->|Sim| E{Tem dados do perfil?}
    E -->|Não| D
    E -->|Sim| F[Recarregar permissões]
    F --> G[Atualizar store]
    G --> H[Continuar navegação]
```

**Código correspondente:**

```typescript
// src/composables/usePageReload.ts
async function handlePageReload(): Promise<void> {
    if (
        isPageReloaded() &&
        authStore.token &&
        authStore.user?.profile?.id &&
        !authStore.isLoading
    ) {
        try {
            await authStore.reloadUserData()
        } catch (error) {
            console.error('Erro ao recarregar dados após F5:', error)
        }
    }
}
```

## 🛣️ Fluxo de Verificação de Rotas

### 1. Navegação entre Páginas

```mermaid
flowchart TD
    A[Usuário clica em link/navega] --> B[Router guard intercepta]
    B --> C{Rota pública?}
    C -->|Sim| D[✅ Permitir acesso]
    C -->|Não| E{Usuário logado?}
    E -->|Não| F[❌ Redirecionar para login]
    E -->|Sim| G[Verificar mapeamento da rota]
    G --> H{Rota mapeada?}
    H -->|Não| D
    H -->|Sim| I{staffOnly?}
    I -->|Sim| J{É staff?}
    J -->|Não| K[❌ Redirecionar para home]
    J -->|Sim| D
    I -->|Não| L{onlyAdmin?}
    L -->|Sim| M{É admin ou staff?}
    M -->|Não| K
    M -->|Sim| D
    L -->|Não| N{Admin ou Staff?}
    N -->|Sim| D
    N -->|Não| O[Verificar permissões específicas]
    O --> P{Tem permissão de módulo?}
    P -->|Não| K
    P -->|Sim| Q{Tem permissão de rota?}
    Q -->|Não| K
    Q -->|Sim| D
```

**Código correspondente:**

```typescript
// src/composables/useRoutePermissions.ts
function canAccessRoute(routePath: string): boolean {
    // Rotas sempre permitidas (públicas)
    const publicRoutes = ['/', '/login', '/profile']
    if (publicRoutes.includes(routePath)) {
        return true
    }

    // Verificar se o usuário está logado
    if (!authStore.token) {
        return false
    }

    // Buscar mapeamento da rota
    const mapping = routeModuleMapping[routePath]
    if (!mapping) {
        return true // Se não há mapeamento, permitir acesso
    }

    // Verificar se é rota exclusiva para staff
    if (mapping.staffOnly) {
        return authStore.isStaff()
    }

    // Verificar se é rota exclusiva para admin e staff
    if (mapping.onlyAdmin) {
        return authStore.hasSpecialPrivileges()
    }

    // Admin e Staff têm acesso irrestrito a rotas normais
    if (authStore.hasSpecialPrivileges()) {
        return true
    }

    // Verificar permissões específicas...
    return checkSpecificPermissions(mapping)
}
```

### 2. Filtro Dinâmico do Menu

```mermaid
flowchart TD
    A[Sistema carrega menu] --> B[Para cada item do menu]
    B --> C{Item tem rota?}
    C -->|Não| D[Manter item - será filtrado pelos subitens]
    C -->|Sim| E[Verificar acesso à rota]
    E --> F{Tem acesso?}
    F -->|Sim| G[Manter item no menu]
    F -->|Não| H[Remover item do menu]
    G --> I{Tem mais itens?}
    H --> I
    D --> I
    I -->|Sim| B
    I -->|Não| J[Menu filtrado pronto]
```

**Código correspondente:**

```typescript
// src/composables/useMenu.ts
const filterMenuItemByPermissions = (item: IMenuItem): boolean => {
    if (!item.route) {
        return true // Item pai - será filtrado pelos subitens
    }

    return canAccessRoute(item.route)
}

const menuItems = computed(() =>
    rawMenuItems.filter(filterMenuItemByPermissions)
)
```

## ⚡ Fluxo de Verificação de Ações

### 1. Verificação de Ação Específica

```mermaid
flowchart TD
    A[Componente verifica ação] --> B{Admin ou Staff?}
    B -->|Sim| C[✅ Permitir ação]
    B -->|Não| D[Buscar permissões do usuário]
    D --> E{Permissões carregadas?}
    E -->|Não| F[❌ Negar ação]
    E -->|Sim| G[Buscar ação na lista]
    G --> H[Para cada ação do usuário]
    H --> I{Descrição da ação confere?}
    I -->|Não| J{Mais ações?}
    I -->|Sim| K{Descrição da rota confere?}
    K -->|Não| J
    K -->|Sim| C
    J -->|Sim| H
    J -->|Não| F
```

**Código correspondente:**

```typescript
// src/composables/useActionPermissions.ts
function hasActionPermissionInRoute(
    routePath: string,
    actionDescription: string
): boolean {
    // Admin e Staff têm acesso irrestrito
    if (authStore.hasSpecialPrivileges()) {
        return true
    }

    const permissions = authStore.getUserPermissions()
    if (!permissions) return false

    // Verificar se existe uma ação com a descrição especificada para esta rota
    return permissions.actions.some(action => {
        // Verificar se a descrição da ação corresponde exatamente
        const actionMatches =
            action.id_acao_rota_id_acao_descricao?.toUpperCase() ===
            actionDescription.toUpperCase()

        // Verificar se pertence à rota correta
        let routeMatches = false
        if (routePath === '/estoque/produto-marca') {
            routeMatches =
                action.id_acao_rota_id_rota_descricao?.toUpperCase() === 'MARCA'
        } else if (routePath === '/estoque/modelo') {
            routeMatches =
                action.id_acao_rota_id_rota_descricao?.toUpperCase() ===
                'MODELO'
        }

        return actionMatches && routeMatches
    })
}
```

### 2. Renderização Condicional de Botões

```mermaid
flowchart TD
    A[Componente renderiza] --> B[Para cada botão/ação]
    B --> C{Usa privilégio especial?}
    C -->|Sim| D[Verificar isOnlyAdmin/isOnlyStaff]
    C -->|Não| E[Verificar permissão específica]
    D --> F{Tem privilégio?}
    E --> G{Tem permissão?}
    F -->|Sim| H[✅ Mostrar botão]
    F -->|Não| I[❌ Ocultar botão]
    G -->|Sim| H
    G -->|Não| I
    H --> J{Mais botões?}
    I --> J
    J -->|Sim| B
    J -->|Não| K[Interface renderizada]
```

**Código correspondente:**

```vue
<!-- src/pages/Estoque/ProdutoMarcaPage.vue -->
<template>
    <!-- Botão baseado em permissão específica -->
    <q-btn
        v-if="permissions.canCreate"
        :label="$t('buttons.add')"
        @click="openCreateModal"
    />

    <!-- Botão baseado em privilégio especial -->
    <q-btn
        v-if="isOnlyAdmin"
        label="Admin/Staff Only"
        @click="adminOnlyAction"
    />

    <!-- Botão apenas para staff -->
    <q-btn v-if="isOnlyStaff" label="Staff Only" @click="staffOnlyAction" />
</template>

<script setup>
const { produtoMarcaPermissions, isOnlyAdmin, isOnlyStaff } =
    useActionPermissions()
const permissions = produtoMarcaPermissions
</script>
```

## 🔄 Fluxos de Atualização

### 1. Mudança de Permissões em Tempo Real

```mermaid
flowchart TD
    A[Admin altera permissões] --> B[Backend atualiza banco]
    B --> C[Sistema notifica mudança]
    C --> D[Cliente recebe notificação]
    D --> E[Recarregar permissões]
    E --> F[Atualizar store]
    F --> G[Reatividade atualiza UI]
    G --> H[Usuário vê mudanças]
```

### 2. Logout e Limpeza

```mermaid
flowchart TD
    A[Usuário faz logout] --> B[Limpar token]
    B --> C[Limpar dados do usuário]
    C --> D[Limpar permissões]
    D --> E[Limpar localStorage]
    E --> F[Redirecionar para login]
    F --> G[Resetar estado da aplicação]
```

**Código correspondente:**

```typescript
// src/stores/auth.ts
function logout(): void {
    // Limpar estado
    token.value = ''
    user.value = {} as IUserWithPermissions

    // Limpar localStorage
    localStorage.removeItem('token')
    localStorage.removeItem('user')

    // Redirecionar
    router.push('/login')
}
```

## 🎯 Cenários Especiais

### 1. Primeiro Acesso (Usuário Novo)

```mermaid
flowchart TD
    A[Usuário novo faz login] --> B[Carregar perfil]
    B --> C{Tem permissões?}
    C -->|Não| D[Mostrar mensagem de acesso limitado]
    C -->|Sim| E[Carregar permissões]
    E --> F[Verificar se tem acesso a alguma rota]
    F --> G{Tem acesso?}
    G -->|Não| D
    G -->|Sim| H[Redirecionar para primeira rota acessível]
```

### 2. Sessão Expirada

```mermaid
flowchart TD
    A[Usuário tenta acessar recurso] --> B[Token expirado]
    B --> C[API retorna 401]
    C --> D[Interceptor detecta erro]
    D --> E[Limpar sessão]
    E --> F[Redirecionar para login]
    F --> G[Mostrar mensagem de sessão expirada]
```

### 3. Erro de Carregamento de Permissões

```mermaid
flowchart TD
    A[Tentar carregar permissões] --> B[Erro na API]
    B --> C[Retry automático]
    C --> D{Sucesso?}
    D -->|Sim| E[Continuar normalmente]
    D -->|Não| F[Modo degradado]
    F --> G[Acesso apenas a rotas públicas]
    G --> H[Notificar usuário do problema]
```

## 📊 Métricas e Monitoramento

### Pontos de Medição

1. **Tempo de carregamento de permissões**
2. **Taxa de sucesso de verificações**
3. **Número de tentativas de acesso negadas**
4. **Performance de verificações em lote**
5. **Frequência de recarregamentos**

### Logs Importantes

```typescript
// Exemplos de logs para monitoramento
console.log('Permissões carregadas:', performance.now() - startTime)
console.warn('Acesso negado:', { user: userId, route: routePath })
console.error('Erro ao carregar permissões:', error)
```

---

[← Anterior: Arquitetura](architecture.md) | [Próximo: Implementação →](implementation.md)
