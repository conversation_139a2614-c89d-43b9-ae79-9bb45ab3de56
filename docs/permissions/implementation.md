# 🛠️ Implementação do Sistema de Permissões

## 📋 Guia de Implementação

Este documento mostra como implementar verificações de permissões em componentes Vue, desde casos simples até cenários complexos.

## 🎯 Uso Básico em Componentes

### 1. Verificação de Rotas

```vue
<template>
    <div v-if="canAccess">
        <!-- Conte<PERSON><PERSON> da página -->
        <h1>Página Produto Marca</h1>
    </div>
    <div v-else>
        <q-banner class="text-negative">
            Você não tem permissão para acessar esta página.
        </q-banner>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoutePermissions } from '@/composables/useRoutePermissions'

const { canAccessRoute } = useRoutePermissions()

const canAccess = computed(() => canAccessRoute('/estoque/produto-marca'))
</script>
```

### 1.1. Verificação de Status de Módulo

```vue
<template>
    <div v-if="moduleActive">
        <!-- Conteúdo quando módulo está ativo -->
        <div v-if="canAccess">
            <h1>Página Produto Marca</h1>
        </div>
        <div v-else>
            <q-banner class="text-warning">
                Você não tem permissão para acessar esta funcionalidade.
            </q-banner>
        </div>
    </div>
    <div v-else>
        <q-banner class="text-negative">
            <q-icon name="block" class="q-mr-sm" />
            Este módulo foi desativado pelo administrador.
        </q-banner>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoutePermissions } from '@/composables/useRoutePermissions'
import { usePermissions } from '@/composables/usePermissions'

const { canAccessRoute } = useRoutePermissions()
const { isModuleActiveByDescription } = usePermissions()

const moduleActive = computed(() => isModuleActiveByDescription('ESTOQUE'))

const canAccess = computed(
    () => moduleActive.value && canAccessRoute('/estoque/produto-marca')
)
</script>
```

### 2. Verificação de Ações CRUD

```vue
<template>
    <q-page>
        <!-- Toolbar com botões condicionais -->
        <div class="toolbar">
            <q-btn
                v-if="permissions.canCreate"
                :label="$t('buttons.add')"
                color="primary"
                icon="add"
                @click="openCreateModal"
            />

            <q-btn
                v-if="permissions.canView"
                :label="$t('buttons.refresh')"
                color="secondary"
                icon="refresh"
                @click="loadData"
            />
        </div>

        <!-- Tabela com ações por linha -->
        <q-table>
            <template #body-cell-actions="props">
                <q-td :props="props">
                    <q-btn
                        v-if="permissions.canEdit"
                        icon="edit"
                        @click="edit(props.row)"
                    />
                    <q-btn
                        v-if="permissions.canDelete"
                        icon="delete"
                        @click="confirmDelete(props.row)"
                    />
                </q-td>
            </template>
        </q-table>
    </q-page>
</template>

<script setup lang="ts">
import { useActionPermissions } from '@/composables/useActionPermissions'

const { produtoMarcaPermissions } = useActionPermissions()
const permissions = produtoMarcaPermissions
</script>
```

### 3. Privilégios Especiais

```vue
<template>
    <q-page>
        <!-- Botões normais baseados em permissões -->
        <q-btn
            v-if="permissions.canCreate"
            label="Criar Marca"
            @click="create"
        />

        <!-- Botões especiais para Admin/Staff -->
        <q-btn
            v-if="isOnlyAdmin"
            label="Configurações Avançadas"
            color="orange"
            icon="admin_panel_settings"
            @click="openAdminSettings"
        />

        <!-- Botões exclusivos para Staff -->
        <q-btn
            v-if="isOnlyStaff"
            label="Debug Tools"
            color="purple"
            icon="engineering"
            @click="openDebugTools"
        />

        <!-- Botões apenas para Admin (não staff) -->
        <q-btn
            v-if="isAdmin"
            label="Business Settings"
            color="red"
            icon="supervisor_account"
            @click="openBusinessSettings"
        />
    </q-page>
</template>

<script setup lang="ts">
import { useActionPermissions } from '@/composables/useActionPermissions'

const { produtoMarcaPermissions, isOnlyAdmin, isOnlyStaff, isAdmin } =
    useActionPermissions()

const permissions = produtoMarcaPermissions

function openAdminSettings() {
    // Ação que admin e staff podem executar
}

function openDebugTools() {
    // Ação que apenas staff pode executar
}

function openBusinessSettings() {
    // Ação que apenas admin pode executar
}
</script>
```

## 🔧 Implementações Avançadas

### 1. Verificação Dinâmica de Ações

```vue
<template>
    <q-page>
        <!-- Lista de ações dinâmicas -->
        <div v-for="action in availableActions" :key="action.name">
            <q-btn
                :label="action.label"
                :color="action.color"
                :icon="action.icon"
                @click="action.handler"
            />
        </div>
    </q-page>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useActionPermissions } from '@/composables/useActionPermissions'

const { hasActionPermissionInRoute, isOnlyAdmin } = useActionPermissions()

const availableActions = computed(() => {
    const actions = []

    // Ações baseadas em permissões específicas
    if (hasActionPermissionInRoute('/estoque/produto-marca', 'CRIAR')) {
        actions.push({
            name: 'create',
            label: 'Criar',
            color: 'primary',
            icon: 'add',
            handler: () => create()
        })
    }

    if (hasActionPermissionInRoute('/estoque/produto-marca', 'EDITAR')) {
        actions.push({
            name: 'edit',
            label: 'Editar',
            color: 'secondary',
            icon: 'edit',
            handler: () => edit()
        })
    }

    // Ações baseadas em privilégios especiais
    if (isOnlyAdmin.value) {
        actions.push({
            name: 'admin',
            label: 'Admin Action',
            color: 'orange',
            icon: 'admin_panel_settings',
            handler: () => adminAction()
        })
    }

    return actions
})
</script>
```

### 2. Componente de Ações em Lote

```vue
<template>
    <action-collection :actions="batchActions" />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useActionPermissions } from '@/composables/useActionPermissions'
import ActionCollection from '@/components/ActionCollection.vue'

const { produtoMarcaPermissions } = useActionPermissions()
const permissions = produtoMarcaPermissions

// Estado de seleção (vem do componente pai)
const selected = defineModel<any[]>('selected', { default: () => [] })

const batchActions = computed(() => [
    {
        label: 'Excluir Selecionados',
        active: selected.value.length > 0 && permissions.value.canDelete,
        icon: 'delete',
        description: 'Excluir os itens selecionados',
        action: () => deleteSelected()
    },
    {
        label: 'Exportar Selecionados',
        active: selected.value.length > 0 && permissions.value.canView,
        icon: 'download',
        description: 'Exportar os itens selecionados',
        action: () => exportSelected()
    },
    {
        label: 'Vincular à Modelo',
        active: selected.value.length === 1, // Sempre disponível para 1 item
        icon: 'link',
        description: 'Vincular modelos à marca selecionada',
        action: () => linkToModel()
    }
])
</script>
```

### 3. Guard de Rota Personalizado

```typescript
// src/router/guards/permissionGuard.ts
import { useRoutePermissions } from '@/composables/useRoutePermissions'
import { useAuthStore } from '@/stores/auth'

export function createPermissionGuard() {
    const { canAccessRoute } = useRoutePermissions()
    const authStore = useAuthStore()

    return (to: any, from: any, next: any) => {
        // Verificar se usuário está logado
        if (!authStore.token) {
            next('/login')
            return
        }

        // Verificar acesso à rota
        if (canAccessRoute(to.path)) {
            next()
        } else {
            // Redirecionar para página de acesso negado ou home
            next('/')
        }
    }
}

// src/router/index.ts
import { createPermissionGuard } from './guards/permissionGuard'

const router = createRouter({
    // ... configuração do router
})

router.beforeEach(createPermissionGuard())
```

### 4. Composable Personalizado para Rota Específica

```typescript
// src/composables/useProdutoMarcaPermissions.ts
import { computed } from 'vue'
import { useActionPermissions } from '@/composables/useActionPermissions'

export function useProdutoMarcaPermissions() {
    const { hasActionPermissionInRoute, isOnlyAdmin, isOnlyStaff } =
        useActionPermissions()

    const routePath = '/estoque/produto-marca'

    // Permissões CRUD básicas
    const canCreate = computed(() =>
        hasActionPermissionInRoute(routePath, 'CRIAR')
    )

    const canEdit = computed(() =>
        hasActionPermissionInRoute(routePath, 'EDITAR')
    )

    const canDelete = computed(() =>
        hasActionPermissionInRoute(routePath, 'EXCLUIR')
    )

    const canView = computed(() =>
        hasActionPermissionInRoute(routePath, 'VISUALIZAR')
    )

    // Ações especiais
    const canLink = computed(
        () => canView.value // Vincular requer apenas visualização
    )

    const canExport = computed(() => canView.value)

    const canImport = computed(() => canCreate.value)

    // Ações administrativas
    const canManageSettings = computed(() => isOnlyAdmin.value)

    const canDebug = computed(() => isOnlyStaff.value)

    return {
        // CRUD básico
        canCreate,
        canEdit,
        canDelete,
        canView,

        // Ações especiais
        canLink,
        canExport,
        canImport,

        // Ações administrativas
        canManageSettings,
        canDebug,

        // Privilégios
        isOnlyAdmin,
        isOnlyStaff
    }
}
```

## 📱 Implementação em Diferentes Tipos de Componente

### 1. Página Completa

```vue
<!-- src/pages/Estoque/ProdutoMarcaPage.vue -->
<template>
    <q-page class="q-pa-md">
        <!-- Header com permissões -->
        <div class="page-header">
            <h1>{{ $t('pages.titles.productBrand') }}</h1>
            <div class="header-actions">
                <q-btn
                    v-if="permissions.canCreate"
                    :label="$t('buttons.add')"
                    color="primary"
                    icon="add"
                    @click="openCreateModal"
                />
            </div>
        </div>

        <!-- Conteúdo principal -->
        <data-table
            :data="data"
            :loading="loading"
            @selection-change="onSelectionChange"
        >
            <template #body-cell-actions="props">
                <q-td :props="props">
                    <div class="row q-gutter-xs">
                        <q-btn
                            v-if="permissions.canEdit"
                            icon="edit"
                            @click="edit(props.row)"
                        />
                        <q-btn
                            v-if="permissions.canDelete"
                            icon="delete"
                            @click="confirmDelete(props.row)"
                        />
                    </div>
                </q-td>
            </template>
        </data-table>

        <!-- Ações em lote -->
        <action-collection :actions="batchActions" />
    </q-page>
</template>

<script setup lang="ts">
import { useProdutoMarcaPermissions } from '@/composables/useProdutoMarcaPermissions'

const permissions = useProdutoMarcaPermissions()
</script>
```

### 2. Modal/Dialog

```vue
<!-- src/components/modals/ProdutoMarcaModal.vue -->
<template>
    <modal-template v-model="show" :title="modalTitle">
        <form-produto-marca
            ref="formRef"
            :item="currentItem"
            :readonly="!canEdit"
        />

        <template #actions>
            <q-btn
                v-if="canEdit"
                :label="$t('buttons.save')"
                color="primary"
                @click="save"
            />
            <q-btn :label="$t('buttons.close')" @click="close" />
        </template>
    </modal-template>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useProdutoMarcaPermissions } from '@/composables/useProdutoMarcaPermissions'

const permissions = useProdutoMarcaPermissions()

const props = defineProps<{
    item?: any
    mode: 'create' | 'edit' | 'view'
}>()

const canEdit = computed(() => {
    if (props.mode === 'create') return permissions.canCreate.value
    if (props.mode === 'edit') return permissions.canEdit.value
    return false
})
</script>
```

### 3. Componente de Lista

```vue
<!-- src/components/lists/ProdutoMarcaList.vue -->
<template>
    <div class="produto-marca-list">
        <div v-if="!permissions.canView" class="no-permission">
            <q-banner class="text-negative">
                Você não tem permissão para visualizar esta lista.
            </q-banner>
        </div>

        <div v-else>
            <q-list>
                <q-item
                    v-for="item in items"
                    :key="item.id"
                    clickable
                    @click="viewItem(item)"
                >
                    <q-item-section>
                        <q-item-label>{{ item.descricao }}</q-item-label>
                    </q-item-section>

                    <q-item-section side>
                        <div class="row q-gutter-xs">
                            <q-btn
                                v-if="permissions.canEdit"
                                icon="edit"
                                size="sm"
                                @click.stop="editItem(item)"
                            />
                            <q-btn
                                v-if="permissions.canDelete"
                                icon="delete"
                                size="sm"
                                @click.stop="deleteItem(item)"
                            />
                        </div>
                    </q-item-section>
                </q-item>
            </q-list>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useProdutoMarcaPermissions } from '@/composables/useProdutoMarcaPermissions'

const permissions = useProdutoMarcaPermissions()
</script>
```

## 🎯 Boas Práticas

### 1. Sempre Verificar Permissões

```typescript
// ❌ Não fazer
function deleteItem(item: any) {
    // Deletar sem verificar
    api.delete(`/items/${item.id}`)
}

// ✅ Fazer
function deleteItem(item: any) {
    if (!permissions.canDelete.value) {
        console.warn('Usuário não tem permissão para deletar')
        return
    }

    api.delete(`/items/${item.id}`)
}
```

### 2. Usar Computeds para Reatividade

```typescript
// ❌ Não fazer
const canEdit = hasActionPermissionInRoute('/rota', 'EDITAR')

// ✅ Fazer
const canEdit = computed(() => hasActionPermissionInRoute('/rota', 'EDITAR'))
```

### 3. Combinar Verificações

```typescript
// Combinar permissão específica com estado do item
const canEditItem = computed(
    () => permissions.canEdit.value && item.value?.status === 'DRAFT'
)

// Combinar privilégio com condição de negócio
const canApprove = computed(
    () => isOnlyAdmin.value && item.value?.status === 'PENDING'
)
```

---

[← Anterior: Fluxos](flows.md) | [Próximo: Configuração →](configuration.md)
