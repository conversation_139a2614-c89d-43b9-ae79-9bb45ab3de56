# 📖 API Reference - Sistema de Permissões

## 📋 Referência Completa das APIs

Esta documentação apresenta todas as funções, composables e interfaces disponíveis no sistema de permissões.

## 🏪 AuthStore (`src/stores/auth.ts`)

### Estado

```typescript
interface AuthStore {
    token: Ref<string>
    user: Ref<IUserWithPermissions>
    isLoading: Ref<boolean>
}
```

### Métodos de Autenticação

#### `login(credentials: LoginCredentials): Promise<void>`

Realiza login do usuário e carrega permissões.

```typescript
await authStore.login({
    username: 'usuario',
    password: 'senha'
})
```

#### `logout(): void`

Realiza logout e limpa dados do usuário.

```typescript
authStore.logout()
```

#### `reloadUserData(): Promise<void>`

Recarrega dados do usuário (usado no F5).

```typescript
await authStore.reloadUserData()
```

### Métodos de Permissões

#### `fetchUserPermissions(userId: number): Promise<IUserPermissions>`

Busca permissões do usuário no backend.

```typescript
const permissions = await authStore.fetchUserPermissions(123)
```

#### `setUserPermissions(permissions: IUserPermissions): void`

Define permissões do usuário no store.

```typescript
authStore.setUserPermissions(permissions)
```

#### `getUserPermissions(): IUserPermissions | null`

Retorna permissões do usuário atual.

```typescript
const permissions = authStore.getUserPermissions()
```

### Verificações por ID (Compatibilidade)

#### `hasModulePermission(moduleId: number): boolean`

Verifica permissão de módulo por ID.

```typescript
const canAccess = authStore.hasModulePermission(5) // ESTOQUE
```

#### `hasRoutePermission(routeId: number): boolean`

Verifica permissão de rota por ID.

```typescript
const canAccess = authStore.hasRoutePermission(61) // MARCA
```

#### `hasActionPermission(actionId: number): boolean`

Verifica permissão de ação por ID.

```typescript
const canAccess = authStore.hasActionPermission(123)
```

### Verificações por Descrição (Recomendado)

#### `hasModulePermissionByDescription(description: string): boolean`

Verifica permissão de módulo por descrição.

```typescript
const canAccess = authStore.hasModulePermissionByDescription('ESTOQUE')
```

#### `hasRoutePermissionByDescription(description: string): boolean`

Verifica permissão de rota por descrição.

```typescript
const canAccess = authStore.hasRoutePermissionByDescription('MARCA')
```

### Privilégios Especiais

#### `isAdmin(): boolean`

Verifica se usuário é admin.

```typescript
const isAdmin = authStore.isAdmin()
```

#### `isStaff(): boolean`

Verifica se usuário é staff.

```typescript
const isStaff = authStore.isStaff()
```

#### `hasSpecialPrivileges(): boolean`

Verifica se usuário tem privilégios especiais (admin OU staff).

```typescript
const hasPrivileges = authStore.hasSpecialPrivileges()
```

## 🛣️ useRoutePermissions (`src/composables/useRoutePermissions.ts`)

### Funções Principais

#### `canAccessRoute(routePath: string): boolean`

Verifica se usuário pode acessar uma rota.

```typescript
const { canAccessRoute } = useRoutePermissions()

if (canAccessRoute('/estoque/produto-marca')) {
    // Usuário pode acessar
}
```

#### `canAccessModule(modulePath: string): boolean`

Verifica se usuário pode acessar um módulo.

```typescript
const canAccess = canAccessModule('estoque')
```

#### `requiresAuth(routePath: string): boolean`

Verifica se rota requer autenticação.

```typescript
const needsAuth = requiresAuth('/admin/users')
```

### Funções Utilitárias

#### `getAccessibleRoutes(): string[]`

Retorna lista de rotas acessíveis pelo usuário.

```typescript
const routes = getAccessibleRoutes()
// ['/', '/estoque', '/estoque/marca', ...]
```

#### `getAccessibleModules(): string[]`

Retorna lista de módulos acessíveis pelo usuário.

```typescript
const modules = getAccessibleModules()
// ['estoque', 'compra', ...]
```

#### `getRouteMapping(routePath: string): IRouteModuleMapping[string] | undefined`

Retorna configuração de uma rota específica.

```typescript
const mapping = getRouteMapping('/estoque/marca')
// { moduleDescription: 'ESTOQUE', routeDescription: 'MARCA' }
```

### Funções de Configuração

#### `addRouteMapping(...): void`

Adiciona mapeamento de rota dinamicamente.

```typescript
addRouteMapping(
    '/nova-rota',
    undefined, // moduleId
    undefined, // routeId
    'nova-rota', // modulePath
    false, // staffOnly
    true // onlyAdmin
)
```

#### `configureRouteIds(routePath: string, moduleId?: number, routeId?: number): void`

Configura IDs para rota existente.

```typescript
configureRouteIds('/estoque/marca', 5, 61)
```

## ⚡ useActionPermissions (`src/composables/useActionPermissions.ts`)

### Computeds de Privilégios

#### `isOnlyAdmin: ComputedRef<boolean>`

Computed que verifica se usuário tem privilégios de admin ou staff.

```vue
<template>
    <q-btn v-if="isOnlyAdmin" @click="adminAction" />
</template>

<script setup>
const { isOnlyAdmin } = useActionPermissions()
</script>
```

#### `isOnlyStaff: ComputedRef<boolean>`

Computed que verifica se usuário é staff.

```vue
<template>
    <q-btn v-if="isOnlyStaff" @click="staffAction" />
</template>
```

#### `isAdmin: ComputedRef<boolean>`

Computed que verifica se usuário é admin.

```vue
<template>
    <q-btn v-if="isAdmin" @click="adminOnlyAction" />
</template>
```

### Verificações de Ações

#### `hasActionPermission(actionId: number): boolean`

Verifica permissão de ação por ID.

```typescript
const canCreate = hasActionPermission(123)
```

#### `hasActionPermissionInRoute(routePath: string, actionDescription: string): boolean`

Verifica permissão de ação específica em uma rota.

```typescript
const canCreate = hasActionPermissionInRoute('/estoque/marca', 'CRIAR')
const canEdit = hasActionPermissionInRoute('/estoque/marca', 'EDITAR')
const canDelete = hasActionPermissionInRoute('/estoque/marca', 'EXCLUIR')
const canView = hasActionPermissionInRoute('/estoque/marca', 'VISUALIZAR')
```

### Permissões Pré-configuradas

#### `produtoMarcaPermissions: ComputedRef<CrudPermissions>`

Permissões CRUD para produto marca.

```typescript
const { produtoMarcaPermissions } = useActionPermissions()

// Uso
produtoMarcaPermissions.value.canCreate
produtoMarcaPermissions.value.canEdit
produtoMarcaPermissions.value.canDelete
produtoMarcaPermissions.value.canView
```

#### `produtoModeloPermissions: ComputedRef<CrudPermissions>`

Permissões CRUD para produto modelo.

```typescript
const { produtoModeloPermissions } = useActionPermissions()
```

### Função Genérica

#### `getCrudPermissions(routePath: string): ComputedRef<CrudPermissions>`

Gera permissões CRUD para qualquer rota.

```typescript
const permissions = getCrudPermissions('/nova-rota')

// Uso
permissions.value.canCreate
permissions.value.canEdit
permissions.value.canDelete
permissions.value.canView
```

## 🔧 usePermissions (`src/composables/usePermissions.ts`)

### Verificações de Status de Módulo

#### `isModuleActiveByDescription(moduleDescription: string): boolean`

Verifica se um módulo está ativo pela descrição.

```typescript
const { isModuleActiveByDescription } = usePermissions()

if (isModuleActiveByDescription('ESTOQUE')) {
    // Módulo ESTOQUE está ativo
}
```

#### `isModuleActiveByPath(modulePath: string): boolean`

Verifica se um módulo está ativo pelo path.

```typescript
const { isModuleActiveByPath } = usePermissions()

if (isModuleActiveByPath('estoque')) {
    // Módulo estoque está ativo
}
```

### Carregamento de Dados

#### `fetchSystemModules(): Promise<ISystemModule[]>`

Carrega módulos do sistema.

```typescript
const { fetchSystemModules } = usePermissions()

await fetchSystemModules()
```

### Estado

#### `systemModules: Ref<ISystemModule[]>`

Lista de módulos do sistema.

```typescript
const { systemModules } = usePermissions()

console.log('Módulos:', systemModules.value)
```

## 🍽️ useMenu (`src/composables/useMenu.ts`)

### Propriedades

#### `menuItems: ComputedRef<IMenuItem[]>`

Menu filtrado baseado em permissões e status de módulos.

```typescript
const { menuItems } = useMenu()

// Menu é automaticamente filtrado por permissões E módulos ativos
```

#### `shouldShowMenu: ComputedRef<boolean>`

Controla quando o menu deve ser exibido.

```typescript
const { shouldShowMenu } = useMenu()

// true quando módulos estão carregados ou usuário não está logado
```

### Funções

#### `filterMenuItemByPermissions(item: IMenuItem): boolean`

Filtra item do menu baseado em permissões.

```typescript
const canShow = filterMenuItemByPermissions(menuItem)
```

#### `isActiveParent(route: string): boolean`

Verifica se rota pai está ativa.

```typescript
const isActive = isActiveParent('/estoque')
```

## 📊 Interfaces e Tipos

### `IUserPermissions`

```typescript
interface IUserPermissions {
    modules: Array<{
        id_modulo: number
        id_modulo_descricao: string
    }>
    routes: Array<{
        id_rota: number
        id_rota_descricao: string
    }>
    actions: Array<{
        id_acao_rota: number
        id_acao_rota_id_acao_descricao: string
        id_acao_rota_id_rota_descricao: string
    }>
}
```

### `IRouteModuleMapping`

```typescript
interface IRouteModuleMapping {
    [routePath: string]: {
        // Identificação por descrição (recomendado)
        moduleDescription?: string
        routeDescription?: string

        // Identificação por ID (compatibilidade)
        moduleId?: number
        routeId?: number

        // Configurações
        staffOnly?: boolean
        onlyAdmin?: boolean
        requiresRoutePermission?: boolean
        modulePath?: string
    }
}
```

### `CrudPermissions`

```typescript
interface CrudPermissions {
    canCreate: boolean
    canEdit: boolean
    canDelete: boolean
    canView: boolean
}
```

### `IUserWithPermissions`

```typescript
interface IUserWithPermissions {
    id: number
    username: string
    nome: string
    sobrenome: string
    email: string
    admin?: boolean
    staff?: boolean
    profile?: {
        id: number
        cargo?: string
        [key: string]: unknown
    }
    permissions?: IUserPermissions
    [key: string]: unknown
}
```

## 🔧 Utilitários

### Constantes

```typescript
// Ações CRUD padrão
const CRUD_ACTIONS = {
    CREATE: 'CRIAR',
    EDIT: 'EDITAR',
    DELETE: 'EXCLUIR',
    VIEW: 'VISUALIZAR'
} as const

// Rotas públicas
const PUBLIC_ROUTES = ['/', '/login', '/profile'] as const
```

### Helpers

#### `checkRouteMatch(action: any, routePath: string): boolean`

Verifica se ação corresponde à rota.

```typescript
const matches = checkRouteMatch(action, '/estoque/marca')
```

#### `checkSpecificPermissions(mapping: any): boolean`

Verifica permissões específicas baseadas no mapeamento.

```typescript
const hasPermission = checkSpecificPermissions(routeMapping)
```

## 📱 Exemplos de Uso Completo

### Verificação Completa em Componente

```vue
<script setup lang="ts">
import { useRoutePermissions } from '@/composables/useRoutePermissions'
import { useActionPermissions } from '@/composables/useActionPermissions'
import { useAuthStore } from '@/stores/auth'

// Composables
const { canAccessRoute } = useRoutePermissions()
const {
    produtoMarcaPermissions,
    isOnlyAdmin,
    isOnlyStaff,
    hasActionPermissionInRoute
} = useActionPermissions()
const authStore = useAuthStore()

// Verificações
const canAccessPage = canAccessRoute('/estoque/marca')
const permissions = produtoMarcaPermissions
const canApprove = hasActionPermissionInRoute('/estoque/marca', 'APROVAR')

// Privilégios
const isAdmin = authStore.isAdmin()
const isStaff = authStore.isStaff()
const hasSpecialPrivileges = authStore.hasSpecialPrivileges()
</script>
```

---

[← Anterior: Exemplos](examples.md) | [Voltar ao Índice](README.md)
