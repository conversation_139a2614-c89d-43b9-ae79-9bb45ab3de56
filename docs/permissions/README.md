# 🔐 Sistema de Permissões - ERP CorpSystem

## 📋 Documentação Completa

Esta documentação cobre todo o sistema de permissões do ERP, incluindo arquitetura, fluxos, implementação e exemplos práticos.

## 📚 Índice da Documentação

### 📖 Documentos Principais

1. **[Visão Geral](overview.md)** - Introdução e conceitos fundamentais
2. **[Arquitetura](architecture.md)** - Estrutura técnica e componentes
3. **[Fluxos](flows.md)** - Fluxos de verificação e validação
4. **[Implementação](implementation.md)** - Como implementar em componentes
5. **[Configuração](configuration.md)** - Como configurar rotas e permissões
6. **[Exemplos](examples.md)** - Exemplos práticos de uso
7. **[API Reference](api-reference.md)** - Referência completa das APIs

## 🎯 Guia Rápido

### Para Desenvolvedores

- **Novo no projeto?** Comece com [Visão Geral](overview.md)
- **Implementar permissões?** Veja [Implementação](implementation.md)
- **Adicionar nova rota?** Consulte [Configuração](configuration.md)
- **Exemplos práticos?** Acesse [Exemplos](examples.md)

### Para Arquitetos

- **Entender a estrutura?** Leia [Arquitetura](architecture.md)
- **Fluxos de dados?** Consulte [Fluxos](flows.md)
- **APIs disponíveis?** Veja [API Reference](api-reference.md)

## 🔑 Conceitos Principais

### Hierarquia de Permissões

```
📦 Sistema ERP
├── 🏢 Módulos (ESTOQUE, COMPRA, CADASTRO GERAL)
│   ├── ✅ Status (ATIVO/INATIVO) ← Nova funcionalidade
│   ├── 📄 Rotas (MARCA, MODELO, PEDIDO)
│   │   └── ⚡ Ações (CRIAR, EDITAR, EXCLUIR, VISUALIZAR)
└── 🔐 Privilégios Especiais (Admin/Staff)
```

### Tipos de Usuários

- **👑 Admin**: Contratante com acesso irrestrito
- **⚙️ Staff**: Mantenedor com acesso total + ferramentas
- **👤 Usuário**: Acesso baseado em permissões específicas

### Propriedades Especiais de Rotas

- **`staffOnly: true`**: Apenas staff pode acessar
- **`onlyAdmin: true`**: Admin e staff podem acessar
- **Sem propriedades**: Verificação normal de permissões

## 🚀 Início Rápido

### 1. Verificar Acesso a Rota

```typescript
import { useRoutePermissions } from '@/composables/useRoutePermissions'

const { canAccessRoute } = useRoutePermissions()

if (canAccessRoute('/estoque/produto-marca')) {
    // Usuário pode acessar
}
```

### 2. Verificar Permissão de Ação

```typescript
import { useActionPermissions } from '@/composables/useActionPermissions'

const { hasActionPermissionInRoute } = useActionPermissions()

if (hasActionPermissionInRoute('/estoque/produto-marca', 'CRIAR')) {
    // Usuário pode criar
}
```

### 3. Usar Privilégios Especiais

```vue
<template>
    <!-- Botão para Admin/Staff -->
    <q-btn v-if="isOnlyAdmin" @click="adminAction" />

    <!-- Botão apenas para Staff -->
    <q-btn v-if="isOnlyStaff" @click="staffAction" />
</template>

<script setup>
const { isOnlyAdmin, isOnlyStaff } = useActionPermissions()
</script>
```

## 📊 Matriz de Acesso Rápida

| Tipo de Usuário | Módulos                | Rotas                  | Ações                  | `staffOnly` | `onlyAdmin` |
| --------------- | ---------------------- | ---------------------- | ---------------------- | ----------- | ----------- |
| **Admin**       | ✅ Apenas ativos       | ✅ Apenas ativas       | ✅ Apenas ativas       | ❌          | ✅          |
| **Staff**       | ✅ Apenas ativos       | ✅ Apenas ativas       | ✅ Apenas ativas       | ✅          | ✅          |
| **Usuário**     | 🔍 Verificação + Ativo | 🔍 Verificação + Ativa | 🔍 Verificação + Ativa | ❌          | ❌          |

> **⚠️ Importante**: Módulos inativos são **completamente inacessíveis** para todos os usuários, incluindo Admin e Staff.

## 🔄 Fluxo Básico

1. **Usuário faz login** → Carrega permissões
2. **Navega para rota** → Verifica acesso ao módulo/rota
3. **Interage com ação** → Verifica permissão específica
4. **Sistema permite/nega** → Baseado nas regras

## 📁 Arquivos Principais

```
src/
├── stores/
│   └── auth.ts                 # Store central de autenticação
├── composables/
│   ├── useRoutePermissions.ts  # Verificação de rotas
│   ├── useActionPermissions.ts # Verificação de ações
│   └── useMenu.ts             # Filtro de menu
├── interfaces/
│   └── Permissions.ts         # Tipos e interfaces
└── pages/
    └── */                     # Implementação em páginas
```

## 🆕 Novidades v2.1

- ✅ **Controle por Status de Módulo**: Módulos inativos são completamente inacessíveis
- ✅ **Estado Global Singleton**: Sincronização entre composables
- ✅ **Loading UX**: Menu vazio durante carregamento para evitar flash
- ✅ **Verificação Absoluta**: Nem Admin/Staff acessam módulos inativos
- ✅ **Descrições em vez de IDs**: Consistência entre ambientes
- ✅ **Propriedade `onlyAdmin`**: Rotas para admin e staff
- ✅ **Computeds de privilégios**: `isOnlyAdmin`, `isOnlyStaff`, `isAdmin`
- ✅ **Recarregamento automático**: Suporte ao F5
- ✅ **Compatibilidade**: Mantém IDs como fallback

## 🔍 Busca Rápida

- **Como adicionar nova rota?** → [Configuração](configuration.md#adicionando-nova-rota)
- **Como usar em componente?** → [Implementação](implementation.md#uso-em-componentes)
- **Como funciona a verificação?** → [Fluxos](flows.md#verificacao-de-rotas)
- **Quais APIs estão disponíveis?** → [API Reference](api-reference.md)

---

**Versão:** 2.1.0
**Última atualização:** Dezembro 2024
**Compatibilidade:** Vue 3 + TypeScript + Pinia
