# 🔐 Sistema de Permissões - Visão Geral

## 🎯 Introdução

O sistema de permissões do ERP CorpSystem implementa um controle de acesso granular baseado em três níveis hierárquicos: **Módulos**, **Rotas** e **Ações**. Alé<PERSON> disso, possui privilégios especiais para usuários **Admin** e **Staff**.

## 🏗️ Conceitos Fundamentais

### Estrutura Hierárquica

```
📦 Sistema ERP
├── 🏢 Módulos (ex: ESTOQUE, COMPRA, CADASTRO GERAL)
│   ├── 📄 Rotas (ex: MARCA, MODELO, PEDIDO)
│   │   └── ⚡ Ações (ex: CRIAR, EDITAR, EXCLUIR, VISUALIZAR)
│   └── 👥 Usuários com Permissões
└── 🔐 Privilégios Especiais (Admin/Staff)
```

### Princípios do Sistema

1. **Granularidade**: Controle fino sobre cada ação específica
2. **Hierarquia**: Permissões organizadas em níveis lógicos
3. **Flexibilidade**: Suporte a diferentes tipos de usuários
4. **Consistência**: Funcionamento uniforme entre ambientes (dev/homolog/prod)
5. **Performance**: Verificações otimizadas e cache inteligente
6. **Segurança**: Princípio do menor privilégio por padrão

## 👥 Tipos de Usuários

### 1. 👑 Admin (Contratante)

- **Descrição**: Cliente contratante do sistema
- **Identificação**: `admin: true` no perfil do usuário
- **Acesso**: Irrestrito a todos os módulos/rotas/ações **ativos**
- **Características**:
    - Ignora verificações de permissões normais
    - Não acessa rotas `staffOnly`
    - Acessa rotas `onlyAdmin`
    - Foco em funcionalidades de negócio

**Exemplo de perfil:**

```json
{
    "id": 123,
    "nome": "João Silva",
    "admin": true,
    "staff": false
}
```

### 2. ⚙️ Staff (Mantenedor do Sistema)

- **Descrição**: Equipe de manutenção e desenvolvimento
- **Identificação**: `staff: true` no perfil do usuário
- **Acesso**: Irrestrito + rotas exclusivas de staff
- **Características**:
    - Maior nível de privilégio no sistema
    - Acesso a ferramentas de desenvolvimento
    - Acessa rotas `staffOnly` e `onlyAdmin`
    - Foco em manutenção técnica

**Exemplo de perfil:**

```json
{
    "id": 456,
    "nome": "Maria Santos",
    "admin": false,
    "staff": true
}
```

### 3. 👤 Usuário Normal

- **Descrição**: Usuário padrão do sistema
- **Identificação**: `admin: false` e `staff: false`
- **Acesso**: Baseado em permissões específicas
- **Características**:
    - Verificação granular de módulos/rotas/ações
    - Sem privilégios especiais
    - Acesso controlado pelo sistema de permissões
    - Foco em operações do dia a dia

**Exemplo de perfil:**

```json
{
    "id": 789,
    "nome": "Carlos Oliveira",
    "admin": false,
    "staff": false
}
```

## 📊 Matriz de Acesso

| Tipo de Usuário    | Módulos         | Rotas           | Ações           | `staffOnly` | `onlyAdmin` |
| ------------------ | --------------- | --------------- | --------------- | ----------- | ----------- |
| **Admin**          | ✅ Todos ativos | ✅ Todas ativas | ✅ Todas ativas | ❌          | ✅          |
| **Staff**          | ✅ Todos        | ✅ Todas        | ✅ Todas        | ✅          | ✅          |
| **Usuário Normal** | 🔍 Verificação  | 🔍 Verificação  | 🔍 Verificação  | ❌          | ❌          |

**Legenda:**

- ✅ Acesso irrestrito
- ❌ Acesso negado
- 🔍 Verificação baseada em permissões específicas

## 🔑 Níveis de Permissão

### 1. 🏢 Módulos

- **Definição**: Grandes áreas funcionais do sistema
- **Exemplos**: ESTOQUE, COMPRA, CADASTRO GERAL, FINANCEIRO
- **Controle**: Acesso ao módulo como um todo
- **Identificação**: Por `id_modulo_descricao` (recomendado) ou `id_modulo`

### 2. 📄 Rotas

- **Definição**: Páginas específicas dentro de um módulo
- **Exemplos**: MARCA, MODELO, PEDIDO, FORNECEDOR
- **Controle**: Acesso a páginas específicas
- **Identificação**: Por `id_rota_descricao` (recomendado) ou `id_rota`

### 3. ⚡ Ações

- **Definição**: Operações específicas que podem ser executadas
- **Exemplos**: CRIAR, EDITAR, EXCLUIR, VISUALIZAR, APROVAR
- **Controle**: Granular sobre cada operação
- **Identificação**: Por `id_acao_rota_id_acao_descricao` e `id_acao_rota_id_rota_descricao`

## 🎯 Casos de Uso Comuns

### Cenário 1: Usuário de Estoque

```
👤 Usuário: Carlos (Operador de Estoque)
🏢 Módulo: ESTOQUE ✅
📄 Rotas: MARCA ✅, MODELO ❌
⚡ Ações em MARCA: CRIAR ❌, EDITAR ✅, EXCLUIR ❌, VISUALIZAR ✅
```

### Cenário 2: Gerente Administrativo

```
👑 Admin: João (Gerente)
🏢 Módulos: TODOS ✅
📄 Rotas: TODAS ✅
⚡ Ações: TODAS ✅
🔐 Especiais: onlyAdmin ✅, staffOnly ❌
```

### Cenário 3: Desenvolvedor

```
⚙️ Staff: Maria (Desenvolvedora)
🏢 Módulos: TODOS ✅
📄 Rotas: TODAS ✅
⚡ Ações: TODAS ✅
🔐 Especiais: onlyAdmin ✅, staffOnly ✅
```

## 🔄 Fluxo Básico de Verificação

1. **Usuário tenta acessar uma funcionalidade**
2. **Sistema verifica tipo de usuário**
    - Se Admin/Staff → Acesso direto (com exceções)
    - Se usuário normal → Continua verificação
3. **Sistema verifica permissões específicas**
    - Módulo → Rota → Ação
4. **Sistema permite ou nega acesso**

## 🚀 Benefícios do Sistema

### Para Administradores

- **Controle Total**: Visibilidade e controle sobre todos os acessos
- **Flexibilidade**: Configuração granular por usuário
- **Segurança**: Princípio do menor privilégio
- **Auditoria**: Rastreabilidade de acessos

### Para Desenvolvedores

- **Simplicidade**: APIs claras e intuitivas
- **Consistência**: Padrões uniformes em todo o sistema
- **Manutenibilidade**: Código organizado e documentado
- **Extensibilidade**: Fácil adição de novas funcionalidades

### Para Usuários Finais

- **Interface Limpa**: Só veem o que podem usar
- **Performance**: Carregamento otimizado
- **Experiência**: Navegação intuitiva
- **Segurança**: Proteção contra acessos indevidos

## 📚 Próximos Passos

1. [**Arquitetura**](architecture.md) - Entenda a estrutura técnica
2. [**Fluxos**](flows.md) - Veja os fluxos de verificação
3. [**Implementação**](implementation.md) - Aprenda a implementar
4. [**Exemplos**](examples.md) - Veja exemplos práticos

---

[← Voltar ao Índice](../README.md) | [Próximo: Arquitetura →](architecture.md)
