# ⚙️ Configuração do Sistema de Permissões

## 📋 Guia de Configuração

Este documento explica como configurar rotas, permissões e privilégios especiais no sistema.

## 🛣️ Configuração de Rotas

### 1. Mapeamento Básico de Rotas

O mapeamento de rotas é feito no arquivo `src/composables/useRoutePermissions.ts`:

```typescript
const routeModuleMapping: IRouteModuleMapping = {
    // Rotas públicas (sem verificação)
    '/': {},
    '/login': {},
    '/profile': {},

    // Rotas com verificação de módulo
    '/estoque': {
        moduleDescription: 'ESTOQUE',
        modulePath: 'estoque'
    },

    // Rotas com verificação de módulo + rota específica
    '/estoque/produto-marca': {
        moduleDescription: 'ESTOQUE',
        routeDescription: 'MARCA',
        modulePath: 'estoque'
    },

    // Rotas com privilégios especiais
    '/admin': { 
        onlyAdmin: true 
    },
    '/dev': { 
        staffOnly: true 
    }
}
```

### 2. Propriedades de Configuração

```typescript
interface IRouteModuleMapping {
    [routePath: string]: {
        // Identificação por descrição (RECOMENDADO)
        moduleDescription?: string    // Ex: "ESTOQUE"
        routeDescription?: string     // Ex: "MARCA"
        
        // Identificação por ID (COMPATIBILIDADE)
        moduleId?: number            // Ex: 5
        routeId?: number             // Ex: 61
        
        // Configurações de acesso
        staffOnly?: boolean          // Apenas staff
        onlyAdmin?: boolean          // Admin + staff
        requiresRoutePermission?: boolean
        modulePath?: string          // Para navegação
    }
}
```

### 3. Adicionando Nova Rota

#### Rota Simples (apenas módulo):
```typescript
'/nova-secao': {
    moduleDescription: 'NOVA_SECAO',
    modulePath: 'nova-secao'
}
```

#### Rota Específica (módulo + rota):
```typescript
'/nova-secao/item': {
    moduleDescription: 'NOVA_SECAO',
    routeDescription: 'ITEM',
    modulePath: 'nova-secao'
}
```

#### Rota Administrativa:
```typescript
'/admin/configuracoes': {
    onlyAdmin: true,
    moduleDescription: 'ADMINISTRAÇÃO',
    routeDescription: 'CONFIGURAÇÕES'
}
```

#### Rota de Desenvolvimento:
```typescript
'/dev/debug': {
    staffOnly: true
}
```

## 🔐 Tipos de Acesso

### 1. Rotas Públicas
```typescript
// Sem configuração = acesso livre
'/': {},
'/login': {},
'/sobre': {}
```

### 2. Rotas com Verificação Normal
```typescript
// Verifica permissão de módulo
'/estoque': {
    moduleDescription: 'ESTOQUE'
}

// Verifica permissão de módulo + rota
'/estoque/marca': {
    moduleDescription: 'ESTOQUE',
    routeDescription: 'MARCA'
}
```

### 3. Rotas Administrativas (`onlyAdmin`)
```typescript
// Admin e Staff podem acessar
'/admin/usuarios': {
    onlyAdmin: true,
    moduleDescription: 'ADMINISTRAÇÃO'
}
```

**Matriz de acesso:**
| Usuário | Acesso |
|---------|--------|
| Admin | ✅ |
| Staff | ✅ |
| Normal | ❌ |

### 4. Rotas de Staff (`staffOnly`)
```typescript
// Apenas Staff pode acessar
'/dev/tools': {
    staffOnly: true
}
```

**Matriz de acesso:**
| Usuário | Acesso |
|---------|--------|
| Admin | ❌ |
| Staff | ✅ |
| Normal | ❌ |

## ⚡ Configuração de Ações

### 1. Ações CRUD Padrão

As ações são verificadas automaticamente usando as descrições:
- `CRIAR` - Criar novos registros
- `EDITAR` - Modificar registros existentes
- `EXCLUIR` - Remover registros
- `VISUALIZAR` - Ver registros

### 2. Uso em Componentes

```typescript
// Verificação automática por rota
const { produtoMarcaPermissions } = useActionPermissions()

// Acesso às permissões
produtoMarcaPermissions.value.canCreate    // CRIAR
produtoMarcaPermissions.value.canEdit      // EDITAR
produtoMarcaPermissions.value.canDelete    // EXCLUIR
produtoMarcaPermissions.value.canView      // VISUALIZAR
```

### 3. Ações Personalizadas

```typescript
// Verificação de ação específica
const canApprove = computed(() => 
    hasActionPermissionInRoute('/compra/pedido', 'APROVAR')
)

const canReject = computed(() => 
    hasActionPermissionInRoute('/compra/pedido', 'REJEITAR')
)
```

### 4. Criando Permissões para Nova Rota

```typescript
// src/composables/useActionPermissions.ts

// Adicionar permissões específicas para nova rota
const novaRotaPermissions = computed(() => ({
    canCreate: hasActionPermissionInRoute('/nova-rota', 'CRIAR'),
    canEdit: hasActionPermissionInRoute('/nova-rota', 'EDITAR'),
    canDelete: hasActionPermissionInRoute('/nova-rota', 'EXCLUIR'),
    canView: hasActionPermissionInRoute('/nova-rota', 'VISUALIZAR'),
    // Ações específicas
    canApprove: hasActionPermissionInRoute('/nova-rota', 'APROVAR'),
    canExport: hasActionPermissionInRoute('/nova-rota', 'EXPORTAR')
}))

// Exportar no return
return {
    // ... outras permissões
    novaRotaPermissions
}
```

## 🎯 Privilégios Especiais

### 1. Computeds Disponíveis

```typescript
const { isOnlyAdmin, isOnlyStaff, isAdmin } = useActionPermissions()

// isOnlyAdmin: true se for Admin OU Staff
// isOnlyStaff: true se for Staff
// isAdmin: true se for Admin
```

### 2. Uso em Templates

```vue
<template>
    <!-- Para Admin e Staff -->
    <q-btn v-if="isOnlyAdmin" @click="adminStaffAction" />
    
    <!-- Apenas para Staff -->
    <q-btn v-if="isOnlyStaff" @click="staffOnlyAction" />
    
    <!-- Apenas para Admin -->
    <q-btn v-if="isAdmin" @click="adminOnlyAction" />
</template>
```

### 3. Combinando com Permissões

```typescript
// Ação que requer permissão específica OU privilégio especial
const canExecuteSpecialAction = computed(() => 
    hasActionPermissionInRoute('/rota', 'ACAO_ESPECIAL') || 
    isOnlyAdmin.value
)

// Ação que requer permissão E privilégio
const canExecuteAdminAction = computed(() => 
    hasActionPermissionInRoute('/rota', 'EDITAR') && 
    isOnlyAdmin.value
)
```

## 📊 Configuração de Menu

### 1. Menu Automático

O menu é filtrado automaticamente baseado nas permissões:

```typescript
// src/composables/useMenu.ts
const menuItems = computed(() => 
    rawMenuItems.filter(filterMenuItemByPermissions)
)

const filterMenuItemByPermissions = (item: IMenuItem): boolean => {
    if (!item.route) return true // Item pai
    return canAccessRoute(item.route)
}
```

### 2. Estrutura do Menu

```typescript
// Exemplo de estrutura de menu
const rawMenuItems: IMenuItem[] = [
    {
        label: 'Estoque',
        icon: 'inventory',
        subItens: [
            {
                label: 'Marca',
                route: '/estoque/produto-marca',  // Filtrado automaticamente
                icon: 'label'
            },
            {
                label: 'Modelo',
                route: '/estoque/modelo',         // Filtrado automaticamente
                icon: 'category'
            }
        ]
    },
    {
        label: 'Administração',
        icon: 'admin_panel_settings',
        subItens: [
            {
                label: 'Usuários',
                route: '/admin/usuarios',         // onlyAdmin
                icon: 'people'
            }
        ]
    }
]
```

## 🔧 Configuração Avançada

### 1. Fallback de IDs para Descrições

```typescript
// Configuração com fallback
'/rota-legada': {
    // Prioridade 1: Descrições (recomendado)
    moduleDescription: 'ESTOQUE',
    routeDescription: 'MARCA',
    
    // Prioridade 2: IDs (fallback)
    moduleId: 5,
    routeId: 61
}
```

### 2. Configuração Dinâmica

```typescript
// Adicionar mapeamento dinamicamente
function addRouteMapping(
    routePath: string,
    moduleId?: number,
    routeId?: number,
    modulePath?: string,
    staffOnly?: boolean,
    onlyAdmin?: boolean
) {
    const mapping: any = {}
    
    if (moduleId !== undefined) mapping.moduleId = moduleId
    if (routeId !== undefined) mapping.routeId = routeId
    if (modulePath !== undefined) mapping.modulePath = modulePath
    if (staffOnly !== undefined) mapping.staffOnly = staffOnly
    if (onlyAdmin !== undefined) mapping.onlyAdmin = onlyAdmin
    
    routeModuleMapping[routePath] = mapping
}

// Uso
addRouteMapping('/nova-rota', undefined, undefined, 'nova-rota', false, true)
```

### 3. Configuração por Ambiente

```typescript
// Diferentes configurações por ambiente
const routeModuleMapping: IRouteModuleMapping = {
    '/estoque/marca': {
        // Produção: usar descrições
        ...(process.env.NODE_ENV === 'production' ? {
            moduleDescription: 'ESTOQUE',
            routeDescription: 'MARCA'
        } : {
            // Desenvolvimento: usar IDs para compatibilidade
            moduleId: 5,
            routeId: 61
        })
    }
}
```

## 📋 Checklist de Configuração

### ✅ Para Nova Rota

1. **Adicionar mapeamento** em `routeModuleMapping`
2. **Definir tipo de acesso** (público, normal, onlyAdmin, staffOnly)
3. **Usar descrições** em vez de IDs quando possível
4. **Testar acesso** com diferentes tipos de usuário
5. **Adicionar ao menu** se necessário
6. **Criar permissões específicas** se precisar de ações customizadas

### ✅ Para Nova Ação

1. **Verificar se ação existe** no backend
2. **Usar descrição padrão** (CRIAR, EDITAR, EXCLUIR, VISUALIZAR)
3. **Criar computed específico** se necessário
4. **Implementar verificação** no componente
5. **Testar com usuário sem permissão**

### ✅ Para Privilégio Especial

1. **Definir se é onlyAdmin ou staffOnly**
2. **Configurar na rota** se for acesso à página
3. **Usar computed apropriado** se for ação específica
4. **Documentar o comportamento**
5. **Testar com admin, staff e usuário normal**

## 🔍 Debugging e Troubleshooting

### 1. Verificar Permissões do Usuário

```typescript
// No console do navegador
console.log('Permissões:', authStore.getUserPermissions())
console.log('É Admin:', authStore.isAdmin())
console.log('É Staff:', authStore.isStaff())
```

### 2. Verificar Mapeamento de Rota

```typescript
// Verificar se rota está mapeada
console.log('Mapeamento:', routeModuleMapping['/sua-rota'])

// Verificar acesso
console.log('Pode acessar:', canAccessRoute('/sua-rota'))
```

### 3. Verificar Ação Específica

```typescript
// Verificar ação específica
console.log('Pode criar:', hasActionPermissionInRoute('/rota', 'CRIAR'))
```

---

[← Anterior: Implementação](implementation.md) | [Próximo: Exemplos →](examples.md)
