{"name": "erp-corpsystem", "version": "0.0.1", "description": "CorpERP - Sistema ERP desenvolvido com Quasar Framework", "productName": "CorpERP", "author": "Corpsystem", "type": "module", "private": true, "homepage": ".", "keywords": ["erp", "quasar", "vue", "pwa", "corpsystem"], "scripts": {"lint": "eslint -c ./eslint.config.js \"./src*/**/*.{ts,js,cjs,mjs,vue}\"", "lint:fix": "eslint -c ./eslint.config.js \"./src*/**/*.{ts,js,cjs,mjs,vue}\" --fix", "format": "prettier --write \"**/*.{js,ts,vue,scss,html,md,json}\" --ignore-path .gitignore", "dev": "quasar dev -m pwa", "build": "quasar build -m pwa", "build:prod": "NODE_ENV=production quasar build -m pwa", "build:analyze": "quasar build -m pwa --analyze", "preview": "quasar serve dist/pwa --port 8080 --history", "postinstall": "quasar prepare", "clean": "rm -rf dist node_modules/.cache", "docker:build": "docker build -t corperpfront .", "docker:run": "docker run -p 8080:80 corperpfront", "test:script": "echo \"See package.json => scripts for available tests.\" && exit 0", "test": "vitest --ui", "test:run": "vitest run", "test:cov": "vitest run --coverage", "test:watch": "vitest", "cy:open": "cross-env NODE_ENV=test start-test \"quasar dev\" http-get://127.0.0.1:5123 \"cypress open --e2e\"", "test:open:ci": "cross-env NODE_ENV=test start-test \"quasar dev\" http-get://127.0.0.1:5123 \"cypress run --e2e\"", "test:component": "cross-env NODE_ENV=test cypress open --component", "test:component:ci": "cross-env NODE_ENV=test cypress run --component", "k8s:deploy": "kubectl apply -f k8s/", "k8s:delete": "kubectl delete -f k8s/"}, "dependencies": {"@formkit/drag-and-drop": "^0.5.3", "@quasar/extras": "^1.16.4", "@vee-validate/i18n": "^4.13.2", "@vee-validate/rules": "^4.13.2", "axios": "^1.2.1", "country-state-city": "^3.2.1", "dompurify": "^3.2.6", "exceljs": "^4.4.0", "i18n-iso-countries": "^7.14.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "marked": "^16.0.0", "pinia": "^2.0.11", "quasar": "^2.16.0", "quill": "^2.0.3", "register-service-worker": "^1.7.2", "vee-validate": "^4.13.2", "vue": "^3.4.18", "vue-i18n": "^9.2.2", "vue-router": "^4.0.12", "yup": "^1.4.0"}, "devDependencies": {"@eslint/js": "^9.14.0", "@intlify/unplugin-vue-i18n": "^2.0.0", "@quasar/app-vite": "^2.0.0", "@quasar/quasar-app-extension-testing-e2e-cypress": "^6.1.0", "@quasar/quasar-app-extension-testing-unit-vitest": "^1.1.0", "@types/dompurify": "^3.0.5", "@types/exceljs": "^0.5.3", "@types/jspdf": "^1.3.3", "@types/marked": "^5.0.2", "@types/node": "^20.5.9", "@types/quill": "^2.0.14", "@vitejs/plugin-vue": "^5.2.3", "@vitest/coverage-v8": "2.1.8", "@vitest/ui": "^2.0.5", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.3", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.2", "cypress": "^13.6.6", "eslint": "^9.14.0", "eslint-plugin-cypress": "^2.15.1", "eslint-plugin-vue": "^9.30.0", "globals": "^15.12.0", "jsdom": "^26.0.0", "prettier": "^3.3.3", "typescript": "~5.5.3", "vite-plugin-checker": "^0.8.0", "vitest": "^2.0.5", "vue-tsc": "^2.0.29", "workbox-build": "^7.0.0", "workbox-cacheable-response": "^7.0.0", "workbox-core": "^7.0.0", "workbox-expiration": "^7.0.0", "workbox-precaching": "^7.0.0", "workbox-routing": "^7.0.0", "workbox-strategies": "^7.0.0"}, "engines": {"node": ">=20.0.0", "npm": ">= 8.0.0"}, "browserslist": ["last 10 Chrome versions", "last 10 Firefox versions", "last 4 Edge versions", "last 7 Safari versions", "last 8 Android versions", "last 8 ChromeAndroid versions", "last 8 FirefoxAndroid versions", "last 10 iOS versions", "last 5 Opera versions"]}