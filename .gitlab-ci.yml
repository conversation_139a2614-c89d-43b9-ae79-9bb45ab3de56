image: docker:stable

stages:
    - build

build-docker:
    services:
        - docker:dind
    retry: 2
    before_script:
        - docker info
        - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD

    stage: build
    script:
        - docker build -t corperpfront .
        - docker tag corperpfront corpsystemsolucoesti/corperpfront:$CI_COMMIT_TAG
        - docker push corpsystemsolucoesti/corperpfront:$CI_COMMIT_TAG
    only:
        - tags
