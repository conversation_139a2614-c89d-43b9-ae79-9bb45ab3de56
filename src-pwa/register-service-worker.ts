/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/ban-ts-comment */

import { register } from 'register-service-worker'
import { Notify } from 'quasar'
import { i18n } from '@/boot/i18n'

// @ts-ignore
const t = i18n.global.t
// The ready(), registered(), cached(), updatefound() and updated()
// events passes a ServiceWorkerRegistration instance in their arguments.
// ServiceWorkerRegistration: https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerRegistration

register(process.env.SERVICE_WORKER_FILE, {
    // The registrationOptions object will be passed as the second argument
    // to ServiceWorkerContainer.register()
    // https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/register#Parameter

    // registrationOptions: { scope: './' },

    ready(/* registration */) {
        // console.log('Service worker is active.')
    },

    registered(/* registration */) {
        // console.log('Service worker has been registered.')
    },

    cached(/* registration */) {
        // console.log('Content has been cached for offline use.')
    },

    updatefound(/* registration */) {
        // console.log('New content is downloading.')
    },

    updated(/* registration */) {
        // console.log('New content is available; please refresh.')
        Notify.create({
            message: t('pwa.updateAvailable'),
            icon: 'fa-solid fa-download',
            closeBtn: t('buttons.refresh'),
            timeout: 10000,
            onDismiss() {
                ;(location as any).reload()
            }
        })
    },

    offline() {
        // console.log('No internet connection found. App is running in offline mode.')
        Notify.create({
            message:
                'No internet connection found. App is running in offline mode.',
            icon: 'fa-solid fa-wifi-slash',
            timeout: 10000
        })
    },

    error(/* err */) {
        // console.error('Error during service worker registration:', err)
    }
})
